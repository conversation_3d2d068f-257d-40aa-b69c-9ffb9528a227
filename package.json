{"name": "test", "version": "0.1.0", "private": true, "dependencies": {"@ag-grid-community/core": "^31.3.2", "@ag-grid-community/react": "^31.3.2", "@anaralabs/lector": "^3.4.0", "@annotationhub/react-golden-layout": "^2.3.2", "@blueprintjs/core": "^5.16.6", "@blueprintjs/datetime": "^5.3.1", "@blueprintjs/datetime2": "^2.3.1", "@blueprintjs/icons": "^5.20.0", "@blueprintjs/select": "^5.0.10", "@blueprintjs/table": "^5.0.10", "@djthoms/pretty-checkbox": "^3.1.0", "@fontsource/jetbrains-mono": "^5.2.5", "@fortawesome/fontawesome-free": "^6.6.0", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@google-cloud/local-auth": "^3.0.1", "@mantine/core": "^7.2.1", "@mantine/hooks": "^7.2.1", "@nivo/bar": "^0.99.0", "@nivo/calendar": "^0.99.0", "@nivo/core": "^0.99.0", "@nivo/line": "^0.99.0", "@nivo/radar": "^0.99.0", "@nivo/treemap": "^0.99.0", "@pdf-viewer/react": "^1.0.1", "@pdftron/pdfjs-express": "^8.7.4", "@pdftron/pdfjs-express-viewer": "^8.7.4", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/selection-mode": "^3.12.0", "@react-pdf/renderer": "^3.1.12", "@reduxjs/toolkit": "^2.2.1", "@sasza/react-panzoom": "^1.18.0", "@silevis/reactgrid": "^4.1.6", "@tanstack/react-table": "^8.17.3", "@tanstack/react-virtual": "^3.10.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@use-gesture/react": "^10.3.1", "@wojtekmaj/react-hooks": "^1.21.0", "@xyflow/react": "^12.4.4", "ag-grid-community": "^30.2.0", "ag-grid-react": "^30.2.1", "antd": "^5.11.0", "axios": "^1.5.0", "d3-collection": "^1.0.7", "dayjs": "^1.11.10", "dockview": "^4.3.1", "dockview-react": "^3.0.2", "elementary-circuits-directed-graph": "^1.3.1", "firebase": "^10.14.1", "flexlayout-react": "^0.7.15", "fuse.js": "^7.1.0", "geist": "^1.3.1", "golden-layout": "^2.6.0", "google-auth-library": "^9.15.0", "googleapis": "^144.0.0", "jodit-react": "^4.1.2", "jspreadsheet-ce": "^4.13.4", "jsuites": "^5.0.26", "jwt-decode": "^3.1.2", "konva": "^9.2.0", "ldrs": "^1.0.1", "lodash": "^4.17.21", "lodash.throttle": "^4.1.1", "lucide-react": "^0.438.0", "moment": "^2.30.1", "pako": "^2.1.0", "pdfjs-dist": "^3.11.174", "plotly.js": "^3.0.0", "plotly.js-dist": "^3.0.0", "plotly.js-dist-min": "^3.0.0", "pretty-checkbox-react": "^3.2.0", "primeicons": "^6.0.1", "primereact": "^10.0.9", "qs": "^6.11.2", "react": "^18.2.0", "react-data-grid": "^7.0.0-beta.39", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-easy-panzoom": "^0.4.4", "react-golden-layout": "^1.0.6", "react-google-login": "^5.2.2", "react-grid-layout": "^1.4.4", "react-grid-system": "^8.1.9", "react-hotkeys-hook": "^4.4.1", "react-icons": "^4.11.0", "react-image-pan-zoom-rotate": "^1.6.0", "react-input-checkbox": "^1.1.0", "react-intersection-observer": "^9.13.1", "react-konva": "^18.2.10", "react-map-interaction": "^2.1.0", "react-medium-image-zoom": "^5.2.8", "react-multi-select-component": "^4.3.4", "react-new-improved-window": "^0.2.9", "react-pan-and-zoom-hoc": "^2.1.8", "react-pdf": "^7.7.3", "react-pdf-annotations": "^3.2.8", "react-pdf-headless": "^0.7.0", "react-pdf-highlighter": "^8.0.0-rc.0", "react-plotly.js": "^2.6.0", "react-popout": "^3.0.4", "react-quick-pinch-zoom": "^5.1.0", "react-redux": "^8.1.3", "react-resizable": "^3.0.5", "react-resizable-panels": "^2.0.12", "react-router-dom": "^6.23.0", "react-scripts": "^5.0.1", "react-select": "^5.8.0", "react-sizeme": "^3.0.2", "react-split-pane": "^0.1.92", "react-spreadsheet": "^0.9.4", "react-virtual": "^2.10.4", "react-virtualized": "^9.22.6", "react-virtualized-auto-sizer": "^1.0.24", "react-virtuoso": "^4.10.3", "react-window": "^1.8.11", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.15.3", "redux": "^4.2.1", "resize-observer-polyfill": "^1.5.1", "rough-notation": "^0.5.1", "split-pane-react": "^0.1.3", "styled-components": "^6.0.7", "svg-pan-zoom": "^3.6.1", "use-clipboard-copy": "^0.2.0", "use-gesture": "^1.0.0", "use-pan-and-zoom": "^0.6.5", "use-zoom-pan": "^1.0.2", "uuid": "^9.0.1", "uuidv4": "^6.2.13", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build && npm run move-static", "build:in": "dotenv -e .env.in react-scripts build  && mv build build_in", "build:us": "dotenv -e .env.us react-scripts build  && mv build build_us", "build:test": "dotenv -e .env.test react-scripts build  && mv build build_test", "test": "react-scripts test", "eject": "react-scripts eject", "move-static": "cp -a ./node_modules/@pdftron/pdfjs-express-viewer/public/. ./dist/public/pdfjsexpress"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"ajv": "^8.12.0", "ajv-keywords": "^5.1.0", "dotenv-cli": "^7.4.4", "netlify-cli": "^17.16.2", "schema-utils": "^4.2.0"}, "resolutions": {"pdfjs-dist": "4.4.168"}}