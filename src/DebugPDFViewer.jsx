import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

// Import required styles
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

// Debug logging helper
const log = (message, data) => {
  console.log(`[DEBUG PDF] ${message}`, data || '');
};

const DebugPDFViewer = ({ fileId, filePath }) => {
  const [pdf, setPdf] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [debugInfo, setDebugInfo] = useState({});

  useEffect(() => {
    const fetchPdf = async () => {
      setLoading(true);
      setError(null);
      setPdf(null);

      if (!fileId) {
        setError('No file ID provided');
        setLoading(false);
        return;
      }

      try {
        const apiUrl = `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`;
        log('API URL', apiUrl);
        log('Session ID', localStorage.getItem('session_id'));

        // First try to validate the endpoint with a HEAD request
        try {
          const headResponse = await fetch(apiUrl, {
            method: 'HEAD',
            headers: { 'session-id': localStorage.getItem('session_id') }
          });
          
          log('HEAD response', {
            status: headResponse.status,
            ok: headResponse.ok,
            contentType: headResponse.headers.get('content-type'),
            contentLength: headResponse.headers.get('content-length')
          });
          
          setDebugInfo(prev => ({
            ...prev, 
            head: {
              status: headResponse.status,
              contentType: headResponse.headers.get('content-type')
            }
          }));
          
          if (!headResponse.ok) {
            throw new Error(`Server returned ${headResponse.status}`);
          }
        } catch (headErr) {
          log('HEAD request failed', headErr.message);
          // Continue anyway, might be blocked by CORS
        }

        // Now try to get the actual file
        log('Starting GET request');
        const response = await fetch(apiUrl, {
          headers: { 'session-id': localStorage.getItem('session_id') }
        });
        
        if (!response.ok) {
          throw new Error(`Server returned status ${response.status}`);
        }
        
        log('GET response', {
          status: response.status,
          contentType: response.headers.get('content-type'),
          contentLength: response.headers.get('content-length')
        });

        // Log headers for debugging
        const headers = {};
        response.headers.forEach((value, key) => {
          headers[key] = value;
        });
        log('Response headers', headers);
        
        setDebugInfo(prev => ({
          ...prev,
          get: { 
            status: response.status,
            headers
          }
        }));

        // Get the data as blob
        log('Reading response as blob');
        const blob = await response.blob();
        log('Blob received', {
          size: blob.size,
          type: blob.type
        });

        // Create object URL
        const url = URL.createObjectURL(blob);
        log('Created blob URL', url);
        
        // Set the PDF data
        setPdf(url);
        setLoading(false);
        
        setDebugInfo(prev => ({
          ...prev,
          blob: { 
            size: blob.size,
            type: blob.type,
            url: url.substring(0, 30) + '...' // Truncate for UI
          }
        }));
      } catch (err) {
        log('Error fetching PDF', err);
        setError(`Error: ${err.message}`);
        setLoading(false);
        
        setDebugInfo(prev => ({
          ...prev,
          error: err.message
        }));
      }
    };

    fetchPdf();

    // Cleanup function
    return () => {
      if (pdf && pdf.startsWith('blob:')) {
        log('Revoking blob URL', pdf);
        URL.revokeObjectURL(pdf);
      }
    };
  }, [fileId]);

  const onDocumentLoadSuccess = ({ numPages }) => {
    log('PDF document loaded successfully', { numPages });
    setNumPages(numPages);
    setDebugInfo(prev => ({
      ...prev,
      document: { 
        numPages,
        loaded: true
      }
    }));
  };

  const onDocumentLoadError = (error) => {
    log('Error loading PDF document', error);
    setError(`Document error: ${error.message}`);
    setDebugInfo(prev => ({
      ...prev,
      documentError: error.message
    }));
  };

  // Simple debug display
  const DebugPanel = () => (
    <div style={{ 
      backgroundColor: '#f5f5f5', 
      padding: '10px', 
      borderRadius: '4px',
      fontSize: '12px',
      marginBottom: '10px',
      fontFamily: 'monospace'
    }}>
      <h3>PDF Viewer Debug Info</h3>
      <div>File ID: {fileId || 'none'}</div>
      <div>Loading: {loading ? 'true' : 'false'}</div>
      <div>Error: {error || 'none'}</div>
      <div>PDF URL: {pdf ? (pdf.substring(0, 30) + '...') : 'none'}</div>
      <div>API URL: {process.env.REACT_APP_FILES_API ? `${process.env.REACT_APP_FILES_API}/get-file/${fileId}` : 'Environment variable missing'}</div>
      
      <h4>Response Details:</h4>
      <pre style={{ maxHeight: '100px', overflow: 'auto' }}>
        {JSON.stringify(debugInfo, null, 2)}
      </pre>
    </div>
  );

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <DebugPanel />
      
      <div style={{ 
        flex: 1, 
        overflowY: 'auto', 
        padding: '10px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}>
        {loading ? (
          <div>Loading PDF...</div>
        ) : error ? (
          <div style={{ color: 'red' }}>
            <h3>Error Loading PDF</h3>
            <p>{error}</p>
          </div>
        ) : (
          <Document
            file={pdf}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            options={{
              cMapUrl: 'https://unpkg.com/pdfjs-dist@2.12.313/cmaps/',
              cMapPacked: true,
              standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@2.12.313/standard_fonts/'
            }}
          >
            {numPages && 
              Array.from(new Array(numPages), (_, index) => (
                <div key={`page_${index + 1}`} style={{ margin: '10px 0' }}>
                  <Page 
                    pageNumber={index + 1} 
                    width={500}
                    renderTextLayer={true}
                    renderAnnotationLayer={true}
                  />
                </div>
              ))
            }
          </Document>
        )}
      </div>
    </div>
  );
};

export default DebugPDFViewer;
