import React, { useState, useEffect } from "react";





// Function to fetch the raw block data from the API
function BlockDataLoader({ pdfFileName }) {
  const [blocksData, setBlocksData] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`http://0.0.0.0:8000/get-block-data/${pdfFileName}`);
        const jsonData = await response.json();
        setBlocksData(jsonData);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchData();
  }, [pdfFileName]);

  return blocksData;
}

// Function to process the raw data and extract BlockType, Page, and BoundingBox
function getBlockLayers(rawData) {
  if (!rawData.Blocks) {
    return [];
  }

  return rawData.Blocks.map(block => ({
    BlockType: block.BlockType,
    Page: block.Page,
    BoundingBox: block.Geometry?.BoundingBox,
    Text: block.Text || null // Set Text to null when it's not available
  }));
}

export { BlockDataLoader, getBlockLayers };