import React, { useCallback, useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import store from './redux/store';
import { useDispatch } from 'react-redux';
import { setIsFilesHandlerOpen, setLayoutModel } from './redux/appConfigurationSlice';
import { PortalProvider, OverlaysProvider } from '@blueprintjs/core';

const copyStyles = (source, target) => {
  const headFrag = target.createDocumentFragment();

  Array.from(source.styleSheets).forEach((styleSheet) => {
    let rules;
    try {
      rules = styleSheet.cssRules;
    } catch (err) {
      console.error(err);
    }

    if (rules) {
      const ruleText = [];
      Array.from(rules).forEach((cssRule) => {
        const { type } = cssRule;

        if (type === CSSRule.UNKNOWN_RULE) {
          return;
        }

        let returnText = '';
        if (type === CSSRule.KEYFRAMES_RULE) {
          returnText = getKeyFrameText(cssRule);
        } else if ([CSSRule.IMPORT_RULE, CSSRule.FONT_FACE_RULE].includes(type)) {
          returnText = fixUrlForRule(cssRule);
        } else {
          returnText = cssRule.cssText;
        }
        ruleText.push(returnText);
      });

      const newStyleEl = target.createElement('style');
      newStyleEl.textContent = ruleText.join('\n');
      headFrag.appendChild(newStyleEl);
    } else if (styleSheet.href) {
      const newLinkEl = target.createElement('link');
      newLinkEl.rel = 'stylesheet';
      newLinkEl.href = styleSheet.href;
      headFrag.appendChild(newLinkEl);
    }
  });

  target.head.appendChild(headFrag);
};

function getKeyFrameText(cssRule) {
  const tokens = ['@keyframes', cssRule.name, '{'];
  Array.from(cssRule.cssRules).forEach((rule) => {
    tokens.push(rule.keyText, '{', rule.style.cssText, '}');
  });
  tokens.push('}');
  return tokens.join(' ');
}

function fixUrlForRule(cssRule) {
  return cssRule.cssText
    .split('url(')
    .map((line) => {
      if (line[1] === '/') {
        return `${line.slice(0, 1)}${window.location.origin}${line.slice(1)}`;
      }
      return line;
    })
    .join('url(');
}

export const useNewWindow = (children, onClose, setPopupRef) => {
  const dispatch = useDispatch();

  const openNewWindow = useCallback(() => {
    dispatch(setIsFilesHandlerOpen(true));
    dispatch(setLayoutModel('collapsed'));

    const newWindow = window.open(
      '',
      '_blank',
      'left=500,top=100,width=1500,height=1500,popup=yes'
    );

    setPopupRef(newWindow);

    const div = newWindow.document.createElement('div');
    newWindow.document.body.appendChild(div);
    const root = createRoot(div);

    const handleBeforeUnload = () => {
      root.unmount();
      newWindow.removeEventListener('beforeunload', handleBeforeUnload);
      onClose();
      dispatch(setIsFilesHandlerOpen(false));
      dispatch(setLayoutModel('initial'));
    };

    const enhancedChildren = React.cloneElement(children, { ownerDocument: newWindow.document });

    root.render(
      <OverlaysProvider>
        <PortalProvider>
          <Provider store={store}>
            {enhancedChildren}
          </Provider>
        </PortalProvider>
      </OverlaysProvider>
    );

    // Copy styles from the main document to the new window
    setTimeout(() => copyStyles(document, newWindow.document), 0);

    newWindow.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      handleBeforeUnload();
    };
  }, [children, onClose, dispatch, setPopupRef]);

  useEffect(() => {
    const observer = new ResizeObserver(() => {});
    observer.observe(document.body);
    return () => observer.disconnect();
  }, []);

  return { openNewWindow };
};

export default useNewWindow;
