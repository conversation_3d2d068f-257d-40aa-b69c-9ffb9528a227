import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import axios from 'axios';
import { pdfjs } from 'pdfjs-dist/legacy/build/pdf';
import { <PERSON>, Spinner, Button as Bp<PERSON><PERSON><PERSON>, ButtonGroup, HTMLSelect } from "@blueprintjs/core";
import { Input, InputNumber } from 'antd';
import { RotateCcw, RotateCw, ZoomIn, ZoomOut, Search, X, ArrowRight } from "lucide-react";
import { useDispatch, useSelector } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import Draggable from 'react-draggable';
import { throttle } from 'lodash';
import {
  PdfLoader,
  Tip,
  Highlight,
  Popup,
  AreaHighlight
} from "react-pdf-highlighter";
import FixedPdfHighlighter from "./FixedPdfHighlighter";

// Import Redux actions
import {
  updatePdfViewer,
  setViewerVisibility,
  setFocusedViewer,
} from './redux/currentPdfSlice';

// Import CSS
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "react-pdf-highlighter/dist/style.css";
import "./pdf-viewer.css";

// Set up the worker for PDF.js - use the same version as installed in package.json
// to avoid version conflicts with other PDF components
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js`;

// Constants
const ZOOM_LEVELS = [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3];
const PAGE_PADDING = 10;

// SearchWindow component
const SearchWindow = ({ isOpen, onClose, searchQuery, onSearchChange, onSearch, searchResults, currentSearchIndex, onNavigateSearch, isDarkTheme, totalOccurrences }) => {
  if (!isOpen) return null;

  return (
    <Draggable handle=".search-window-header">
      <div
        className={`search-window ${isDarkTheme ? 'bp5-dark' : ''}`}
        style={{
          position: 'absolute',
          top: '100px',
          right: '20px',
          width: '300px',
          backgroundColor: isDarkTheme ? '#30404D' : 'white',
          border: `1px solid ${isDarkTheme ? '#394B59' : '#E1E8ED'}`,
          borderRadius: '3px',
          boxShadow: '0 0 10px rgba(0, 0, 0, 0.2)',
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}
      >
        <div
          className="search-window-header"
          style={{
            padding: '8px 10px',
            backgroundColor: isDarkTheme ? '#394B59' : '#F5F8FA',
            borderBottom: `1px solid ${isDarkTheme ? '#30404D' : '#E1E8ED'}`,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            cursor: 'move'
          }}
        >
          <span style={{ fontWeight: 'bold' }}>Search</span>
          <BpButton
            icon={<X size={16} />}
            minimal={true}
            small={true}
            onClick={onClose}
          />
        </div>
        <div style={{ padding: '10px' }}>
          <div style={{ marginBottom: '10px' }}>
            <Input
              placeholder="Search..."
              value={searchQuery}
              onChange={onSearchChange}
              onPressEnter={onSearch}
              style={{ width: '100%' }}
              className={isDarkTheme ? 'bp5-dark' : ''}
            />
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
            <span>
              {searchResults.length > 0 ? (
                <>
                  {currentSearchIndex + 1} of {searchResults.length} results
                  {totalOccurrences > 0 && ` (${totalOccurrences} occurrences)`}
                </>
              ) : (
                'No results'
              )}
            </span>
            <div>
              <BpButton
                icon={<ArrowRight size={16} style={{ transform: 'rotate(180deg)' }} />}
                minimal={true}
                small={true}
                disabled={searchResults.length === 0}
                onClick={() => onNavigateSearch('prev')}
              />
              <BpButton
                icon={<ArrowRight size={16} />}
                minimal={true}
                small={true}
                disabled={searchResults.length === 0}
                onClick={() => onNavigateSearch('next')}
              />
            </div>
          </div>
          {searchResults.length > 0 && currentSearchIndex >= 0 && (
            <div
              style={{
                padding: '8px',
                backgroundColor: isDarkTheme ? '#394B59' : '#F5F8FA',
                borderRadius: '3px',
                fontSize: '12px',
                maxHeight: '100px',
                overflow: 'auto',
                whiteSpace: 'pre-wrap', // Preserve line breaks
                wordBreak: 'break-word'
              }}
            >
              <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                Page {searchResults[currentSearchIndex].pageNumber}
                {searchResults[currentSearchIndex].isMultiLine &&
                  ` (${searchResults[currentSearchIndex].lines} lines)`}
              </div>
              <div>
                {searchResults[currentSearchIndex].text}
              </div>
            </div>
          )}
        </div>
      </div>
    </Draggable>
  );
};

// GoToWindow component
const GoToWindow = ({ isOpen, onClose, onGoTo, totalPages, isDarkTheme }) => {
  const [pageInput, setPageInput] = useState(1);

  if (!isOpen) return null;

  const handleGoTo = () => {
    if (pageInput >= 1 && pageInput <= totalPages) {
      onGoTo(pageInput);
      onClose();
    }
  };

  return (
    <Draggable handle=".goto-window-header">
      <div
        className={`goto-window ${isDarkTheme ? 'bp5-dark' : ''}`}
        style={{
          position: 'absolute',
          top: '100px',
          left: '50%',
          transform: 'translateX(-50%)',
          width: '250px',
          backgroundColor: isDarkTheme ? '#30404D' : 'white',
          border: `1px solid ${isDarkTheme ? '#394B59' : '#E1E8ED'}`,
          borderRadius: '3px',
          boxShadow: '0 0 10px rgba(0, 0, 0, 0.2)',
          zIndex: 1000,
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden'
        }}
      >
        <div
          className="goto-window-header"
          style={{
            padding: '8px 10px',
            backgroundColor: isDarkTheme ? '#394B59' : '#F5F8FA',
            borderBottom: `1px solid ${isDarkTheme ? '#30404D' : '#E1E8ED'}`,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            cursor: 'move'
          }}
        >
          <span style={{ fontWeight: 'bold' }}>Go to Page</span>
          <BpButton
            icon={<X size={16} />}
            minimal={true}
            small={true}
            onClick={onClose}
          />
        </div>
        <div style={{ padding: '15px' }}>
          <div style={{ marginBottom: '15px', display: 'flex', alignItems: 'center' }}>
            <span style={{ marginRight: '10px' }}>Page:</span>
            <InputNumber
              min={1}
              max={totalPages}
              value={pageInput}
              onChange={(value) => setPageInput(value)}
              style={{ width: '100%' }}
              onPressEnter={handleGoTo}
            />
            <span style={{ marginLeft: '10px' }}>of {totalPages}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
            <BpButton
              text="Cancel"
              minimal={true}
              onClick={onClose}
              style={{ marginRight: '10px' }}
            />
            <BpButton
              text="Go"
              intent="primary"
              onClick={handleGoTo}
            />
          </div>
        </div>
      </div>
    </Draggable>
  );
};

// Main PDF Viewer component
const PDFJsViewer = ({ fileId, filePath, isDarkTheme }) => {
  // State for PDF
  const [pdfUrl, setPdfUrl] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [zoomLevel, setZoomLevel] = useState(1.0);
  const [rotation, setRotation] = useState(0);

  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isGoToOpen, setIsGoToOpen] = useState(false);

  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [totalOccurrences, setTotalOccurrences] = useState(0);

  // Refs
  const containerRef = useRef(null);
  const pagesRef = useRef({});
  const highlighterRef = useRef(null);

  // Redux and visibility
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  const currentViewerState = useSelector(state => state.currentPdf?.viewers?.[fileId]);

  // Scroll to page function - define this before using it in other functions
  const scrollToPage = useCallback((pageNum) => {
    if (containerRef.current && pagesRef.current[pageNum]) {
      pagesRef.current[pageNum].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, []);

  // Fetch PDF file
  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();
    let objectUrl = null;

    // Reset states
    setPdfUrl(null);
    setLoading(true);
    setError(null);
    setSearchResults([]);
    setCurrentSearchIndex(-1);

    const fetchFile = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: { "session-id": localStorage.getItem("session_id") },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        if (isMounted) {
          objectUrl = URL.createObjectURL(response.data);
          setPdfUrl(objectUrl);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(err)) {
            console.log("Request canceled:", err.message);
          } else {
            console.error("Failed to fetch the PDF:", err);
            setError(`Failed to load the PDF. Please try again.`);
          }
        }
      }
    };

    if (fileId) {
      fetchFile();
    } else {
      setLoading(false);
      setError('No file ID provided. Cannot load the PDF.');
    }

    // Cleanup function
    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [fileId]);

  // Redux/Visibility effects
  useEffect(() => {
    dispatch(setViewerVisibility({ fileId, isVisible: inView }));
    if (inView) {
      dispatch(setFocusedViewer(fileId));
    }
  }, [inView, fileId, dispatch, filePath]);

  // Update Redux store when page changes
  useEffect(() => {
    if (numPages > 0) {
      dispatch(updatePdfViewer({
        fileId,
        filePath,
        page: pageNumber,
        isVisible: inView
      }));
    }
  }, [pageNumber, fileId, filePath, inView, dispatch, numPages]);

  // Document load handlers
  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    console.log(`Document loaded with ${numPages} pages`);
    setNumPages(numPages);
    setLoading(false);
    setPageNumber(1);
  }, []);

  const onDocumentLoadError = useCallback((error) => {
    console.error('Error loading PDF:', error);
    setError(`Failed to load PDF: ${error.message}`);
    setLoading(false);
  }, []);

  // Navigation functions
  const goToPrevPage = useCallback(() => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  }, []);

  const goToNextPage = useCallback(() => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  }, [numPages]);

  const handleGoTo = useCallback((page) => {
    if (page >= 1 && page <= numPages) {
      setPageNumber(page);
      scrollToPage(page);
    }
  }, [numPages]);

  // Zoom functions
  const zoomIn = useCallback(throttle(() => {
    setZoomLevel(prevZoom => {
      const currentIndex = ZOOM_LEVELS.findIndex(zl => zl >= prevZoom);
      const nextIndex = Math.min(currentIndex + 1, ZOOM_LEVELS.length - 1);
      return ZOOM_LEVELS[nextIndex];
    });
  }, 200), []);

  const zoomOut = useCallback(throttle(() => {
    setZoomLevel(prevZoom => {
      const currentIndex = ZOOM_LEVELS.findIndex(zl => zl >= prevZoom);
      const prevIndex = Math.max(currentIndex - 1, 0);
      return ZOOM_LEVELS[prevIndex];
    });
  }, 200), []);

  // Search functions
  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleSearch = useCallback(async () => {
    if (!pdfUrl || !searchQuery.trim()) {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      setTotalOccurrences(0);
      return;
    }

    try {
      const pdfDocument = await pdfjs.getDocument(pdfUrl).promise;
      const results = [];
      let totalOccurrencesCount = 0;

      for (let i = 1; i <= numPages; i++) {
        const page = await pdfDocument.getPage(i);
        const textContent = await page.getTextContent();

        // Group text items by their y-position to maintain line structure
        const lineMap = new Map();
        textContent.items.forEach(item => {
          // Round y position to account for slight variations in same line
          const yPos = Math.round(item.transform[5] * 100) / 100;
          if (!lineMap.has(yPos)) {
            lineMap.set(yPos, []);
          }
          lineMap.get(yPos).push(item);
        });

        // Sort lines by y-position (top to bottom)
        const sortedLines = Array.from(lineMap.entries())
          .sort((a, b) => b[0] - a[0]) // Reverse order (top to bottom)
          .map(([_, items]) => {
            // Sort items in each line by x-position (left to right)
            return items.sort((a, b) => a.transform[4] - b.transform[4]);
          });

        // Create a structured text representation with proper line breaks
        let structuredText = '';
        sortedLines.forEach(line => {
          const lineText = line.map(item => item.str).join('');
          structuredText += lineText + '\n';
        });

        // Search for matches in the structured text
        const regex = new RegExp(searchQuery, 'gim'); // Added 'm' flag for multi-line
        let match;

        while ((match = regex.exec(structuredText)) !== null) {
          totalOccurrencesCount++;

          // Find the position of the match in the page
          // This is a simplified approach - in a real implementation, you would
          // need to calculate the exact coordinates of the match on the page
          const matchPosition = {
            boundingRect: {
              x1: 50,  // These are placeholder values
              y1: 50,
              x2: 500,
              y2: 100,
              width: 450,
              height: 50,
              pageNumber: i
            },
            rects: [
              {
                x1: 50,
                y1: 50,
                x2: 500,
                y2: 100,
                width: 450,
                height: 50,
                pageNumber: i
              }
            ],
            pageNumber: i
          };

          results.push({
            pageNumber: i,
            text: match[0],
            matchIndex: match.index,
            isMultiLine: match[0].includes('\n'),
            lines: match[0].split('\n').filter(Boolean).length,
            position: matchPosition
          });
        }
      }

      setSearchResults(results);
      setTotalOccurrences(totalOccurrencesCount);

      if (results.length > 0) {
        setCurrentSearchIndex(0);
        setPageNumber(results[0].pageNumber);

        // Use the highlighter to scroll to the first result after a short delay
        // to allow the highlights to be created
        setTimeout(() => {
          if (highlighterRef.current) {
            const firstHighlight = {
              id: 'search-0',
              position: results[0].position
            };
            highlighterRef.current.scrollTo(firstHighlight);
          }
        }, 100);
      } else {
        setCurrentSearchIndex(-1);
      }

      setIsSearchOpen(true);
    } catch (error) {
      console.error('Error during search:', error);
    }
  }, [pdfUrl, searchQuery, numPages, setSearchResults, setCurrentSearchIndex, setTotalOccurrences, setPageNumber, setIsSearchOpen]);

  const navigateSearch = useCallback((direction) => {
    if (searchResults.length === 0) return;

    const newIndex = direction === 'next'
      ? (currentSearchIndex + 1) % searchResults.length
      : (currentSearchIndex - 1 + searchResults.length) % searchResults.length;

    setCurrentSearchIndex(newIndex);
    setPageNumber(searchResults[newIndex].pageNumber);

    // Scroll to the highlight using the highlighter's scrollTo method
    if (highlighterRef.current) {
      const highlight = {
        id: `search-${newIndex}`,
        position: searchResults[newIndex].position
      };
      highlighterRef.current.scrollTo(highlight);
    }
  }, [searchResults, currentSearchIndex]);



  // Convert search results to highlights for react-pdf-highlighter
  const searchHighlights = useMemo(() => {
    return searchResults.map((result, index) => ({
      id: `search-${index}`,
      content: {
        text: result.text
      },
      position: {
        boundingRect: {
          x1: 0,
          y1: 0,
          x2: 0,
          y2: 0,
          width: 0,
          height: 0,
          pageNumber: result.pageNumber
        },
        rects: [],
        pageNumber: result.pageNumber
      },
      comment: '',
      isMultiLine: result.isMultiLine,
      lines: result.lines
    }));
  }, [searchResults]);

  // Scroll to highlight
  const scrollToHighlight = useCallback((highlight) => {
    if (highlight && highlighterRef.current) {
      highlighterRef.current.scrollTo(highlight);
    }
  }, []);

  // Handle highlight click
  const handleHighlightClick = useCallback((highlight) => {
    const index = searchHighlights.findIndex(h => h.id === highlight.id);
    if (index !== -1) {
      setCurrentSearchIndex(index);
    }
  }, [searchHighlights]);

  // Get scale for the PDF highlighter
  const getScaledZoom = useCallback(() => {
    return zoomLevel;
  }, [zoomLevel]);

  // Get rotation for the PDF highlighter
  const getRotation = useCallback(() => {
    return rotation;
  }, [rotation]);

  return (
    <div ref={ref} className={isDarkTheme ? 'bp5-dark' : ''} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      {/* Toolbar */}
      <Card style={{ padding: '5px', marginBottom: '5px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Navigation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <ButtonGroup>
              <BpButton icon="chevron-left" onClick={goToPrevPage} disabled={pageNumber <= 1} />
              <div style={{ padding: '0 10px', display: 'flex', alignItems: 'center' }}>
                Page {pageNumber} of {numPages || '?'}
              </div>
              <BpButton icon="chevron-right" onClick={goToNextPage} disabled={pageNumber >= numPages} />
              <BpButton icon="locate" onClick={() => setIsGoToOpen(true)} />
            </ButtonGroup>
          </div>

          {/* Zoom and rotation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <ButtonGroup>
              <BpButton icon={<RotateCcw size={16} />} onClick={() => setRotation(prev => (prev - 90 + 360) % 360)} />
              <BpButton icon={<RotateCw size={16} />} onClick={() => setRotation(prev => (prev + 90) % 360)} />
            </ButtonGroup>

            <ButtonGroup>
              <BpButton icon={<ZoomOut size={16} />} onClick={zoomOut} />
              <HTMLSelect
                options={ZOOM_LEVELS.map(level => ({ label: `${Math.round(level * 100)}%`, value: level }))}
                value={zoomLevel}
                onChange={e => setZoomLevel(parseFloat(e.target.value))}
                style={{ width: '100px' }}
              />
              <BpButton icon={<ZoomIn size={16} />} onClick={zoomIn} />
            </ButtonGroup>
          </div>

          {/* Search controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <Input
              placeholder="Search..."
              value={searchQuery}
              onChange={handleSearchChange}
              onPressEnter={handleSearch}
              style={{ width: '200px' }}
            />
            <BpButton icon={<Search size={16} />} onClick={handleSearch} />
          </div>
        </div>
      </Card>

      {/* PDF Viewer */}
      <div ref={containerRef} style={{ flex: 1, overflow: 'auto', position: 'relative' }}>
        {error ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
            <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>
            <BpButton intent="primary" onClick={() => window.location.reload()}>Retry</BpButton>
          </div>
        ) : loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Spinner size={50} />
          </div>
        ) : (
          <PdfLoader
            url={pdfUrl}
            beforeLoad={<Spinner size={50} />}
            onError={onDocumentLoadError}
          >
            {(pdfDocument) => {
              // Set number of pages when document loads
              if (numPages === 0 && pdfDocument.numPages) {
                onDocumentLoadSuccess({ numPages: pdfDocument.numPages });
              }

              return (
                <div style={{ height: '100%' }}>
                  <FixedPdfHighlighter
                    ref={highlighterRef}
                    pdfDocument={pdfDocument}
                    enableAreaSelection={false}
                    highlights={searchHighlights}
                    onScrollChange={() => {}}
                    scrollRef={containerRef}
                    onSelectionFinished={() => {}}
                    highlightTransform={(highlight, index, setTip, hideTip, viewportToScaled, screenshot, isScrolledTo) => {
                      const isSearchResult = highlight.id.startsWith('search-');
                      const highlightIndex = parseInt(highlight.id.split('-')[1], 10);
                      const isCurrent = highlightIndex === currentSearchIndex;

                      return (
                        <Highlight
                          key={highlight.id}
                          position={highlight.position}
                          onClick={() => handleHighlightClick(highlight)}
                          isScrolledTo={isScrolledTo}
                          className={`Highlight ${isSearchResult ? 'search-highlight' : ''} ${highlight.isMultiLine ? 'multi-line' : ''} ${isCurrent ? 'current' : ''}`}
                        />
                      );
                    }}
                    getPageWidth={(pageNumber) => {
                      return Math.floor(800 * getScaledZoom());
                    }}
                    getPageHeight={(pageNumber) => {
                      return Math.floor(1100 * getScaledZoom());
                    }}
                    getScale={() => getScaledZoom()}
                    getRotation={() => getRotation()}
                  />
                </div>
              );
            }}
          </PdfLoader>
        )}

        {/* Search Window */}
        <SearchWindow
          isOpen={isSearchOpen}
          onClose={() => setIsSearchOpen(false)}
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          onSearch={handleSearch}
          searchResults={searchResults}
          currentSearchIndex={currentSearchIndex}
          onNavigateSearch={navigateSearch}
          isDarkTheme={isDarkTheme}
          totalOccurrences={totalOccurrences}
        />

        {/* Go To Window */}
        <GoToWindow
          isOpen={isGoToOpen}
          onClose={() => setIsGoToOpen(false)}
          onGoTo={handleGoTo}
          totalPages={numPages}
          isDarkTheme={isDarkTheme}
        />
      </div>
    </div>
  );
};

export default PDFJsViewer;
