import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Spinner } from '@blueprintjs/core';
import { useDispatch } from 'react-redux';
import { useInView } from 'react-intersection-observer';

// Import required CSS
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

/**
 * SimpleWorkingPDFViewer - A minimalist PDF viewer guaranteed to work
 * with react-pdf
 */
const SimpleWorkingPDFViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  // Core state
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Redux and visibility tracking
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  // Load PDF file
  useEffect(() => {
    let isMounted = true;
    
    async function fetchFile() {
      try {
        setLoading(true);
        console.log('Fetching PDF for:', { fileId, filePath });
        
        if (filePath) {
          // If direct path is provided, use it
          setPdfFile(filePath);
          setLoading(false);
          return;
        }
        
        if (!fileId) {
          throw new Error('No file ID provided');
        }
        
        // Add console log to verify API URL
        console.log('API URL:', `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`);
        
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'session-id': localStorage.getItem('session_id'),
          },
        });
        
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }
        
        // Get as blob directly
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        
        if (isMounted) {
          console.log('PDF loaded successfully, setting URL', url);
          setPdfFile(url);
          setLoading(false);
          
          // Update Redux if needed
          if (dispatch && typeof dispatch === 'function') {
            try {
              dispatch({
                type: 'updatePdfViewer',
                payload: {
                  fileId,
                  filePath,
                  page: 1,
                  isVisible: inView
                }
              });
            } catch (e) {
              console.warn('Could not dispatch update:', e);
            }
          }
        }
      } catch (err) {
        console.error('Error loading PDF:', err);
        if (isMounted) {
          setError(`Failed to load PDF: ${err.message}`);
          setLoading(false);
        }
      }
    }
    
    fetchFile();
    
    return () => {
      isMounted = false;
      if (pdfFile && typeof pdfFile === 'string' && pdfFile.startsWith('blob:')) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, filePath, dispatch, inView, pdfFile]);
  
  // Document load handlers
  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    console.log(`PDF loaded with ${numPages} pages`);
  };
  
  // Render all pages
  const renderAllPages = () => {
    const pages = [];
    for (let i = 1; i <= numPages; i++) {
      pages.push(
        <div key={`page-${i}`} style={{
          margin: '10px 0',
          display: 'flex',
          justifyContent: 'center'
        }}>
          <Page 
            pageNumber={i} 
            renderTextLayer={true}
            renderAnnotationLayer={true}
            width={Math.min(600, window.innerWidth - 50)}
          />
        </div>
      );
    }
    return pages;
  };

  return (
    <div 
      ref={ref}
      style={{ 
        height: '100%', 
        width: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        overflow: 'hidden',
        ...style 
      }}
    >
      <div style={{ 
        flex: 1, 
        overflow: 'auto', 
        padding: '20px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}>
        {loading ? (
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: '100%'
          }}>
            <Spinner size={50} />
            <div style={{ marginTop: '20px' }}>Loading PDF...</div>
          </div>
        ) : error ? (
          <div style={{ 
            color: 'red', 
            padding: '20px', 
            textAlign: 'center' 
          }}>
            <h3>Error Loading PDF</h3>
            <p>{error}</p>
          </div>
        ) : (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={(err) => {
              console.error('Error rendering PDF:', err);
              setError(`Error rendering PDF: ${err.message}`);
            }}
            loading={<Spinner size={40} />}
            options={{
              cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.4.120/cmaps/',
              cMapPacked: true
            }}
          >
            {numPages ? renderAllPages() : <Spinner size={40} />}
          </Document>
        )}
      </div>
    </div>
  );
};

export default SimpleWorkingPDFViewer;
