import React, { useState, useEffect, useRef } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { 
  Button, 
  ButtonGroup, 
  Navbar, 
  Spinner, 
  NonIdealState 
} from '@blueprintjs/core';
import axios from 'axios';

// Set the worker source using unpkg CDN with the exact version
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

const SimplePDFViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  const [pdfUrl, setPdfUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [numPages, setNumPages] = useState(0);
  const containerRef = useRef(null);

  // Fetch the PDF from the API
  useEffect(() => {
    // If filePath is provided directly, use it
    if (filePath) {
      console.log(`Using direct filePath: ${filePath}`);
      setPdfUrl(filePath);
      setLoading(false);
      return;
    }

    // If fileId is provided, fetch from API
    if (fileId) {
      const fetchPdf = async () => {
        setLoading(true);
        setError(null);
        
        try {
          console.log(`Fetching PDF for fileId: ${fileId}`);
          const response = await axios.get(
            `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
            {
              headers: {
                "session-id": localStorage.getItem("session_id"),
              },
              responseType: "blob",
            }
          );

          const pdfBlob = response.data;
          const url = URL.createObjectURL(pdfBlob);
          console.log(`Created blob URL: ${url}`);
          setPdfUrl(url);
          setLoading(false);
        } catch (err) {
          console.error("Failed to fetch PDF:", err);
          setError(err.message || "Failed to fetch PDF data");
          setLoading(false);
        }
      };
      
      fetchPdf();
    } else {
      setError("No PDF source provided");
      setLoading(false);
    }

    // Cleanup function to revoke object URL
    return () => {
      if (pdfUrl && pdfUrl.startsWith('blob:')) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [fileId, filePath]);

  // Handle zoom in/out
  const zoomIn = () => setScale(prev => Math.min(prev + 0.2, 3));
  const zoomOut = () => setScale(prev => Math.max(prev - 0.2, 0.5));
  
  // Handle page navigation
  const goToPrevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));
  const goToNextPage = () => setCurrentPage(prev => Math.min(prev + 1, numPages));

  // Container styles
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa',
    color: isDarkTheme ? '#f5f8fa' : '#182026',
    ...style
  };

  // Navbar styles
  const navbarStyle = {
    padding: '0 10px',
    backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  };

  // PDF content area styles
  const contentStyle = {
    flexGrow: 1,
    overflow: 'auto',
    padding: '20px',
    display: 'flex',
    justifyContent: 'center'
  };

  // PDF page container styles
  const pageContainerStyle = {
    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
    backgroundColor: 'white'
  };

  // If loading or error, show appropriate UI
  if (loading) {
    return (
      <div style={containerStyle}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
          <Spinner size={50} />
          <span style={{ marginLeft: '10px' }}>Loading PDF...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={containerStyle}>
        <NonIdealState
          icon="error"
          title="Error Loading PDF"
          description={error}
        />
      </div>
    );
  }

  // Function to handle document load success
  const onDocumentLoadSuccess = ({ numPages: nextNumPages }) => {
    setNumPages(nextNumPages);
    setLoading(false);
  };

  // Function to handle document load error
  const onDocumentLoadError = (err) => {
    console.error('Error loading PDF:', err);
    setError(err.message || 'Failed to load PDF');
    setLoading(false);
  };

  return (
    <div style={containerStyle}>
      {/* Control Navbar */}
      <Navbar style={navbarStyle}>
        <div style={{ display: 'flex', alignItems: 'center', padding: '5px 0' }}>
          {/* Page Navigation */}
          <ButtonGroup>
            <Button 
              icon="chevron-left" 
              onClick={goToPrevPage} 
              disabled={currentPage <= 1}
              minimal
              small
            />
            <span style={{ padding: '0 10px' }}>
              Page {currentPage} of {numPages || '?'}
            </span>
            <Button 
              icon="chevron-right" 
              onClick={goToNextPage} 
              disabled={currentPage >= numPages}
              minimal
              small
            />
          </ButtonGroup>
          
          {/* Zoom Controls */}
          <ButtonGroup style={{ marginLeft: '20px' }}>
            <Button 
              icon="zoom-out" 
              onClick={zoomOut} 
              minimal
              small
            />
            <span style={{ padding: '0 10px' }}>
              {Math.round(scale * 100)}%
            </span>
            <Button 
              icon="zoom-in" 
              onClick={zoomIn} 
              minimal
              small
            />
          </ButtonGroup>
        </div>
      </Navbar>

      {/* PDF Content */}
      <div style={contentStyle} ref={containerRef}>
        {pdfUrl && (
          <Document
            file={pdfUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={<div style={{ padding: '20px' }}><Spinner size={30} /></div>}
            error={<div style={{ color: 'red' }}>Could not load PDF</div>}
          >
            <Page
              key={`page_${currentPage}_scale_${scale}`}
              pageNumber={currentPage}
              scale={scale}
              renderTextLayer={true}
              renderAnnotationLayer={true}
              loading={<div style={{ padding: '20px' }}><Spinner size={30} /></div>}
              error={<div style={{ color: 'red' }}>Error rendering page</div>}
              style={pageContainerStyle}
            />
          </Document>
        )}
      </div>
    </div>
  );
};

export default SimplePDFViewer;
