@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

.genai-results-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0 0 15px 0;
  background-color: var(--bp5-app-background-color);
  font-family: 'JetBrains Mono', monospace;
}

.genai-results-header {
  padding: 12px 16px;
  margin-bottom: 15px;
  border-radius: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--bp5-card-background-color);
}

.genai-results-title {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.genai-results-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  color: var(--bp5-heading-color);
  line-height: 1.2;
}

.file-name {
  font-size: 14px;
  color: var(--bp5-text-color);
  margin-top: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.file-name strong {
  margin-right: 6px;
  color: var(--bp5-intent-primary);
}

.genai-results-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  min-width: 32px;
  margin: 2px;
  justify-content: center;
  text-align: center;
}

.genai-results-content {
  flex: 1;
  overflow: auto;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

/* Table styling */
.table-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.table-section {
  background-color: var(--bp5-card-background-color);
  border-radius: 3px;
  overflow: hidden;
  box-shadow: var(--bp5-elevation-shadow-1);
}

.section-header {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border-bottom: 1px solid var(--bp5-divider-black);
  transition: background-color 0.2s ease;
}

.bp5-dark .section-header {
  border-bottom: 1px solid var(--bp5-divider-white);
}

.section-header:hover {
  background-color: var(--bp5-hover-background-color);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title h4 {
  margin: 0;
  font-size: 15px;
  font-weight: 700;
  color: var(--bp5-heading-color);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table td {
  padding: 7px 16px;
  vertical-align: top;
}

.data-table tr:hover {
  background-color: rgba(138, 155, 168, 0.05);
}

.bp5-dark .data-table tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.field-label {
  width: 150px;
  color: var(--bp5-text-color-muted);
  font-weight: 600;
  font-size: 13px;
}

.field-label-container {
  display: flex;
  align-items: center;
  gap: 6px;
}

.field-icon {
  color: var(--bp5-text-color-muted);
}

.indented {
  padding-left: 30px !important;
}

.subheader {
  background-color: var(--bp5-app-background-color);
  border-top: 1px solid var(--bp5-divider-black);
  border-bottom: 1px solid var(--bp5-divider-black);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 16px !important;
}

.bp5-dark .subheader {
  background-color: var(--bp5-dark-app-background-color);
  border-top: 1px solid var(--bp5-divider-white);
  border-bottom: 1px solid var(--bp5-divider-white);
}

.subheader-text {
  font-weight: 600;
  font-size: 13px;
  color: var(--bp5-heading-color);
}

/* Field values styling */
.field-value-container {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.field-value {
  padding: 3px 0;
  font-size: 14px;
  word-break: break-word;
}

.issue-description {
  font-weight: 700;
  color: var(--bp5-intent-danger);
}

.not-found {
  font-style: italic;
  color: var(--bp5-text-color-muted);
  font-size: 13px;
}

/* Boolean field tags */
.boolean-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px 2px 6px !important;
  border-radius: 3px;
  font-size: 13px;
  font-weight: 500;
}

.recommendation-tag {
  padding: 3px 8px !important;
  border-radius: 3px;
  font-size: 13px;
  line-height: 1.3;
  display: inline-block;
  max-width: 100%;
  font-weight: 500;
  background-color: rgba(217, 130, 43, 0.15) !important;
  color: var(--bp5-intent-warning) !important;
}

.bp5-dark .recommendation-tag {
  background-color: rgba(217, 130, 43, 0.2) !important;
}

.recommendation-text {
  color: var(--bp5-intent-warning);
  font-weight: 600;
}

.bp5-dark .recommendation-text {
  color: var(--bp5-intent-warning);
}

/* Non-conformance section styling */
.non-conformance-section {
  border-left: 3px solid var(--bp5-intent-warning);
}

.non-conformance-section .section-header {
  background-color: rgba(217, 130, 43, 0.1);
}

.bp5-dark .non-conformance-section .section-header {
  background-color: rgba(217, 130, 43, 0.15);
}

.non-conformance-section .section-title h4 {
  color: var(--bp5-intent-warning);
}

/* Page references */
.quote-container {
  margin: 8px 0;
  padding: 8px 12px;
  background-color: rgba(138, 155, 168, 0.1);
  border-left: 3px solid var(--bp5-intent-primary);
  border-radius: 3px;
  font-size: 13px;
}

.quote-label {
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--bp5-text-color-muted);
  font-size: 12px;
}

.quote-text blockquote {
  margin: 0;
  padding: 0;
  font-style: italic;
  color: var(--bp5-text-color);
  line-height: 1.4;
}

.bp5-dark .quote-container {
  background-color: rgba(16, 22, 26, 0.3);
}

.page-references {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 5px;
  margin-top: 5px;
}

.page-ref-label {
  font-size: 12px;
  color: var(--bp5-text-color-muted);
}

.page-number-button {
  min-width: 24px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  font-size: 12px;
  padding: 1px 4px !important;
}

/* Loading spinner */
.spinner-only-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'JetBrains Mono', monospace;
}

.spinner-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.spinner-text {
  font-size: 15px;
  color: var(--bp5-text-color-muted);
}

/* Method header styling with delete button */
.method-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.delete-method-button {
  opacity: 0.6;
  transition: opacity 0.2s ease, transform 0.1s ease;
}

.delete-method-button:hover {
  opacity: 1;
  transform: scale(1.1);
}

.method-header:hover .delete-method-button {
  opacity: 0.9;
}

.issue-header:hover .delete-issue-button {
  opacity: 0.9;
}

/* Method styling */
.method-header {
  background-color: #2A313B;
  padding: 7px 16px !important;
  font-weight: 600;
}

.method-header-text {
  font-size: 14px;
  font-weight: 700;
  color: var(--bp5-heading-color);
}

.method-separator {
  height: 1px;
  background-color: var(--bp5-divider-black);
  padding: 0 !important;
}

.next-inspection-date {
  background-color: rgba(50, 55, 65, 0.5);
}

.bp5-dark .next-inspection-date {
  background-color: rgba(50, 55, 65, 0.5);
}

/* Issue styling */
.issue-header {
  background-color: #2A313B;
  padding: 7px 16px !important;
  font-weight: 600;
}

.issue-header-text {
  font-size: 14px;
  font-weight: 700;
  color: var(--bp5-heading-color);
}

.delete-issue-button {
  opacity: 0.6;
  transition: opacity 0.2s ease, transform 0.1s ease;
}

.delete-issue-button:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Field dropdown styling */
.field-dropdown {
  margin-top: 10px;
  margin-bottom: 5px;
}

/* Style for dark mode theme */
.bp5-dark .field-dropdown .ant-select-selector {
  background-color: #252A31 !important;
  border-color: #383E47 !important;
}

/* Style for dropdown items in dark mode */
.bp5-dark .ant-select-dropdown {
  background-color: #252A31 !important;
}

.bp5-dark .ant-select-item {
  background-color: #252A31;
  color: #f5f8fa;
}

.bp5-dark .ant-select-item-option-active {
  background-color: #2F343C;
}

.bp5-dark .ant-select-item-option-selected {
  background-color: #383E47;
}

/* Ensure dropdown menus stay above other content */
.ant-select-dropdown {
  z-index: 1050 !important;
}

/* Dark mode specific overrides */
.bp5-dark .genai-results-container {
  background-color: var(--bp5-dark-app-background-color);
}

.bp5-dark .genai-results-header,
.bp5-dark .table-section {
  background-color: var(--bp5-dark-card-background-color);
}

.bp5-dark .not-found {
  color: var(--bp5-dark-text-color-muted);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .genai-results-container {
    padding: 10px;
  }

  .field-label {
    width: 140px;
  }

  .genai-results-title h3 {
    font-size: 15px;
  }

  .section-title h4 {
    font-size: 14px;
  }
}

/* Responsive button handling */
.genai-results-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  min-width: 32px;
  margin: 2px;
  justify-content: center;
  text-align: center;
}

/* Responsive adjustments for smaller screens and tabs */
@media (max-width: 768px) {
  .filename-text {
    max-width: 150px;
  }
  
  .genai-results-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .genai-results-actions {
    margin-top: 10px;
    width: 100%;
    justify-content: flex-start;
  }
}

/* Very small screens or tabs */
@media (max-width: 480px) {
  .filename-text {
    max-width: 100px;
  }
  
  .action-button {
    padding: 5px !important;
  }
}

/* For tab containers with limited width */
.tab-container-small .genai-results-actions .action-button span {
  display: none;
}

.tab-container-small .genai-results-actions .action-button {
  min-width: 32px;
  padding: 5px !important;
}

/* Method Modal Styles */
.method-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
}

.method-modal {
  background-color: var(--bp5-card-background-color);
  border-radius: 6px;
  padding: 20px;
  width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.method-modal h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: var(--bp5-heading-color);
  font-size: 16px;
  font-weight: 600;
}

.method-modal-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* Breadcrumbs styling */
.file-breadcrumbs {
  display: flex;
  align-items: center;
  margin-top: 6px;
  max-width: 100%;
  overflow: hidden;
}

.source-file-label {
  color: var(--bp5-text-color);
  font-weight: 500;
  margin-right: 6px;
  font-size: 13px;
}

.file-breadcrumbs .bp5-breadcrumbs {
  font-size: 13px;
  display: flex;
  align-items: center;
}

.file-breadcrumbs .bp5-breadcrumb {
  color: var(--bp5-text-color-muted);
}

.file-breadcrumbs .current-breadcrumb {
  color: var(--bp5-intent-primary);
  font-weight: 500;
  display: flex;
  align-items: center;
}

.file-breadcrumbs .current-breadcrumb .bp5-icon {
  margin-right: 0px;
  color: var(--bp5-gold5);
}

/* File tag styling */
.file-tag {
  display: flex;
  align-items: center;
  max-width: 100%;
  overflow: hidden;
}

.file-tag .bp5-icon {
  color: var(--bp5-text-color-muted);
}

.filename-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px; /* Default for larger screens */
  margin-left: 5px;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .filename-text {
    max-width: 150px;
  }
}

@media (max-width: 480px) {
  .filename-text {
    max-width: 100px;
  }
}

/* Section tag styling */
.section-tag {
  margin-left: 8px;
  font-size: 12px;
}

.section-title {
  display: flex;
  align-items: center;
}

.section-title h4 {
  margin: 0 0 0 8px;
  font-size: 15px;
  font-weight: 600;
}

.genai-results-actions .action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  box-shadow: var(--bp5-elevation-shadow-1);
  transition: transform 0.1s ease, box-shadow 0.1s ease;
}

.genai-results-actions .action-button:hover {
  transform: translateY(-1px);
  box-shadow: var(--bp5-elevation-shadow-2);
}

.genai-results-actions .action-button:active {
  transform: translateY(0);
  box-shadow: var(--bp5-elevation-shadow-1);
}

.genai-results-actions .action-button .bp5-icon {
  margin: 0;
  line-height: 0;
  
}

.genai-results-actions .bp5-button-group {
  display: flex;
  align-items: center;
  gap: 3px;  /* Add small gap between buttons in group */
}

/* Ensure buttons in button group don't have individual shadows */
.genai-results-actions .bp5-button-group .action-button {
  box-shadow: none;
  margin: 0;
}

/* Add shadow to the entire button group instead */
.genai-results-actions .bp5-button-group {
  box-shadow: var(--bp5-elevation-shadow-1);
}

.genai-results-actions .bp5-button-group:hover {
  box-shadow: var(--bp5-elevation-shadow-2);
}
