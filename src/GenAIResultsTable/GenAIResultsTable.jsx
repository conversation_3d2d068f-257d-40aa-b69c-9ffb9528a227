import React, { useState, useEffect, useRef, useContext } from "react";
import { useSelector, useDispatch } from "react-redux";
import { addAction } from "../redux/actionHistorySlice";
import {
  Card,
  Spinner,
  NonIdealState,
  Button,
  ButtonGroup,
  InputGroup,
  Tag,
  Icon,
  Collapse,
  Elevation,
  Intent,
  Tooltip,
  Position,
  HTMLTable,
  Toaster,
  Dialog,
  Classes
} from "@blueprintjs/core";
import { Select } from "antd";
import { AppContext } from "../AppContextProvider";
import "./GenAIResultsTable.css";
import axios from "axios";
import { LucideNotepadText } from "lucide-react";

// Create a toaster instance outside of the component
const AIResultsToaster = Toaster.create({
  position: Position.TOP,
});

// Get filename from path
const getFileName = (path) => {
  if (!path) return "";
  const parts = path.split('/');
  return parts[parts.length - 1];
};

const GenAIResultsTable = ({ results, isDarkTheme, isLoading, fileId, filePath, hasError, onPageNavigation, activeTab }) => {
  const [editedResults, setEditedResults] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    details: true,
    inspectors: true,
    methods: true,
    issues: true,
  });
  const [selectedInspectionType, setSelectedInspectionType] = useState(null);
  const [selectedMethods, setSelectedMethods] = useState({});
  const [selectedIssueRanks, setSelectedIssueRanks] = useState({});
  const [selectedIssueCategories, setSelectedIssueCategories] = useState({});
  const [isCreatingRecord, setIsCreatingRecord] = useState(false);
  const [isMethodModalOpen, setIsMethodModalOpen] = useState(false);
  const [newMethodValue, setNewMethodValue] = useState('');
  const [isResetConfirmOpen, setIsResetConfirmOpen] = useState(false);
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [dropdownHistory, setDropdownHistory] = useState([]);
  const containerRef = useRef(null);
  const [isSmallContainer, setIsSmallContainer] = useState(false);

  const { lookupValues } = useContext(AppContext);
  
  // Get user from Redux state
  const { user } = useSelector((state) => state.auth);
  // Get active tab from Redux state
  const activeTabFromRedux = useSelector((state) => state.filesHandler.activeTab);
  // Get dispatch function for Redux actions
  const dispatch = useDispatch();
  
  // Store the latest parameters and functions globally for the reload mechanism
  useEffect(() => {
    // Make these available globally for the reload function
    window.latestAssetClickParams = window.latestAssetClickParams || {};
    
    // Log the current latestAssetClickParams for debugging
    console.log("Current latestAssetClickParams in GenAIResultsTable:", window.latestAssetClickParams);
  }, []);

  useEffect(() => {
    if (results) {
      // Deep clone to avoid reference issues
      const newResults = JSON.parse(JSON.stringify(results));
      
      console.log('Original AI results:', newResults);
      
      // Add better data validation and initialization
      const ensureSection = (section) => {
        if (!newResults[section]) {
          console.log(`Initializing missing section: ${section}`);
          newResults[section] = {};
        }
      };
      
      // Ensure core sections exist
      ensureSection('Details');
      ensureSection('Inspectors');
      
      // Initialize Inspectors array if it doesn't exist or is not an array
      if (!Array.isArray(newResults.Inspectors)) {
        console.log('Converting Inspectors to array or initializing empty array');
        newResults.Inspectors = Array.isArray(newResults.Inspectors) ? newResults.Inspectors : [];
      }
      
      // Initialize empty issues array if it doesn't exist or is not an array
      if (!Array.isArray(newResults["Non-Conformance"])) {
        console.log('Converting Non-Conformance to array or initializing empty array');
        newResults["Non-Conformance"] = Array.isArray(newResults["Non-Conformance"]) ? newResults["Non-Conformance"] : [];
      }
      
      // Initialize Methods array if it doesn't exist or is not an array
      if (!Array.isArray(newResults["Inspection Methods"])) {
        console.log('Converting Inspection Methods to array or initializing empty array');
        newResults["Inspection Methods"] = Array.isArray(newResults["Inspection Methods"]) ? newResults["Inspection Methods"] : [];
        
        // If we have no methods but have details, try to create a method from the inspection type
        if (newResults["Inspection Methods"].length === 0 && newResults.Details && newResults.Details["Inspection Type"]) {
          console.log('Creating default method from inspection type');
          newResults["Inspection Methods"].push({
            "Method": {
              value: newResults.Details["Inspection Type"].value || "Visual Inspection",
              page: newResults.Details["Inspection Type"].page || []
            },
            "Next Inspection Date": {
              value: "",
              page: []
            }
          });
        }
      }
      
      console.log('Processed results with methods:', newResults["Inspection Methods"]);
      
      setEditedResults(newResults);
      
      // Initialize history with original state
      setHistory([newResults]);
      setHistoryIndex(0);
      setDropdownHistory([{
        inspectionType: selectedInspectionType,
        methods: {...selectedMethods},
        issueRanks: {...selectedIssueRanks},
        issueCategories: {...selectedIssueCategories}
      }]);
    }
  }, [results, selectedInspectionType, selectedMethods, selectedIssueRanks, selectedIssueCategories]);

  useEffect(() => {
    if (results && lookupValues) {
      // Check for Inspection Type match
      const inspectionTypeValue = results.Details?.["Inspection Type"]?.value;
      if (inspectionTypeValue && lookupValues?.["Inspection Type"]) {
        const exactMatch = lookupValues["Inspection Type"].find(
          option => option.toLowerCase() === inspectionTypeValue.toLowerCase()
        );
        if (exactMatch) {
          setSelectedInspectionType(exactMatch);
        }
      }
      
      // Check for Inspection Methods matches
      if (results["Inspection Methods"] && lookupValues?.["Inspection Methods"]) {
        const methodMatches = {};
        results["Inspection Methods"].forEach((method, index) => {
          const methodValue = method.Method?.value;
          if (methodValue) {
            const exactMatch = lookupValues["Inspection Methods"].find(
              option => option.toLowerCase() === methodValue.toLowerCase()
            );
            if (exactMatch) {
              methodMatches[index] = exactMatch;
            }
          }
        });
        setSelectedMethods(methodMatches);
      }
      
      // Check for Issue Ranks matches
      if (results["Non-Conformance"] && lookupValues?.["Rank"]) {
        const issueRankMatches = {};
        results["Non-Conformance"].forEach((issue, index) => {
          const issueRankValue = issue.Rank?.value;
          if (issueRankValue) {
            const exactMatch = lookupValues["Rank"].find(
              option => option.toLowerCase() === issueRankValue.toLowerCase()
            );
            if (exactMatch) {
              issueRankMatches[index] = exactMatch;
            }
          }
        });
        setSelectedIssueRanks(issueRankMatches);
      }
      
      // Check for Issue Categories matches
      if (results["Non-Conformance"] && lookupValues?.["NCR Category"]) {
        const issueCategoryMatches = {};
        results["Non-Conformance"].forEach((issue, index) => {
          const issueCategoryValue = issue.Category?.value;
          if (issueCategoryValue) {
            const exactMatch = lookupValues["NCR Category"].find(
              option => option.toLowerCase() === issueCategoryValue.toLowerCase()
            );
            if (exactMatch) {
              issueCategoryMatches[index] = exactMatch;
            }
          }
        });
        setSelectedIssueCategories(issueCategoryMatches);
      }
    }
  }, [results, lookupValues]);

  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        setIsSmallContainer(width < 800);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Add to history when making changes
  const addToHistory = (newState, dropdownState = null) => {
    // If we're not at the end of the history, remove future states
    if (historyIndex < history.length - 1) {
      setHistory(history.slice(0, historyIndex + 1));
      setDropdownHistory(dropdownHistory.slice(0, historyIndex + 1));
    }
    
    // Add new state to history
    const newHistory = [...history, JSON.parse(JSON.stringify(newState))];
    setHistory(newHistory);
    
    // Add dropdown state to history
    const newDropdownHistory = [...dropdownHistory, dropdownState || {
      inspectionType: selectedInspectionType,
      methods: {...selectedMethods},
      issueRanks: {...selectedIssueRanks},
      issueCategories: {...selectedIssueCategories}
    }];
    setDropdownHistory(newDropdownHistory);
    
    // Update history index to point to the new state
    setHistoryIndex(newHistory.length - 1);
  };

  // Handle undo operation
  const handleUndo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      setEditedResults(JSON.parse(JSON.stringify(history[newIndex])));
      
      // Restore dropdown states
      const dropdownState = dropdownHistory[newIndex];
      setSelectedInspectionType(dropdownState.inspectionType);
      setSelectedMethods({...dropdownState.methods});
      setSelectedIssueRanks({...dropdownState.issueRanks});
      setSelectedIssueCategories({...dropdownState.issueCategories});
    }
  };

  // Handle redo operation
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      setEditedResults(JSON.parse(JSON.stringify(history[newIndex])));
      
      // Restore dropdown states
      const dropdownState = dropdownHistory[newIndex];
      setSelectedInspectionType(dropdownState.inspectionType);
      setSelectedMethods({...dropdownState.methods});
      setSelectedIssueRanks({...dropdownState.issueRanks});
      setSelectedIssueCategories({...dropdownState.issueCategories});
    }
  };

  // Check if undo/redo are available
  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  // Function to reset to original AI results
  const resetToOriginal = () => {
    if (!results) return;
    
    // Deep clone to avoid reference issues
    const originalResults = JSON.parse(JSON.stringify(results));
    setEditedResults(originalResults);
    
    // Reset selected values
    setSelectedMethods({});
    setSelectedIssueRanks({});
    setSelectedIssueCategories({});
    
    // Reset history
    setHistory([originalResults]);
    setHistoryIndex(0);
    
    // Close dialog
    setIsResetConfirmOpen(false);
    
    // Show success message
    AIResultsToaster.show({
      message: "Results have been reset to original AI output",
      intent: Intent.SUCCESS,
      icon: "refresh",
      timeout: 3000
    });
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="spinner-only-container">
        <div className="spinner-content">
          <Spinner size={50} />
          <div className="spinner-text">Analyzing document...</div>
        </div>
      </div>
    );
  }

  // Render error state
  if (hasError) {
    return (
      <div className={`genai-results-container ${isDarkTheme ? "bp5-dark" : ""}`}>
        <NonIdealState
          icon="error"
          title="Analysis Failed"
          description="There was an error analyzing this document. Please try again later."
        />
      </div>
    );
  }

  // Render no results state
  if (!results || !editedResults) {
    return (
      <div className={`genai-results-container ${isDarkTheme ? "bp5-dark" : ""}`}>
        <div className="genai-results-header">
          <h2>AI Analysis Results</h2>
          <div className="file-name">{getFileName(filePath)}</div>
        </div>
        <NonIdealState
          icon="search"
          title="No Results"
          description="No analysis results available for this document."
        />
      </div>
    );
  }

  // Handle value changes with history tracking
  const handleValueChange = (section, value, field, index = null) => {
    const newResults = JSON.parse(JSON.stringify(editedResults));
    
    // Check if the section exists
    if (!newResults[section]) {
      console.error(`Section ${section} does not exist in editedResults`);
      return;
    }
    
    if (index !== null) {
      // Handle array items like methods or issues
      if (newResults[section][index] && newResults[section][index][field]) {
        newResults[section][index][field].value = value;
      } else {
        console.error(`Cannot set value for ${section}[${index}][${field}]`);
      }
    } else {
      // Handle regular fields
      if (!newResults[section][field]) {
        // Create the field if it doesn't exist
        newResults[section][field] = { value: value, page: [] };
      } else {
        newResults[section][field].value = value;
      }
    }
    
    setEditedResults(newResults);
    addToHistory(newResults);
  };

  // Handle dropdown selection changes with history tracking
  const handleInspectionTypeChange = (value) => {
    setSelectedInspectionType(value);
    
    // Add to dropdown history
    const newDropdownState = {
      inspectionType: value,
      methods: {...selectedMethods},
      issueRanks: {...selectedIssueRanks},
      issueCategories: {...selectedIssueCategories}
    };
    addToHistory(editedResults, newDropdownState);
  };
  
  const handleMethodChange = (index, value) => {
    const newSelectedMethods = {...selectedMethods, [index]: value};
    setSelectedMethods(newSelectedMethods);
    
    // Add to dropdown history
    const newDropdownState = {
      inspectionType: selectedInspectionType,
      methods: newSelectedMethods,
      issueRanks: {...selectedIssueRanks},
      issueCategories: {...selectedIssueCategories}
    };
    addToHistory(editedResults, newDropdownState);
  };

  const handleIssueRankChange = (index, value) => {
    const newSelectedIssueRanks = {...selectedIssueRanks, [index]: value};
    setSelectedIssueRanks(newSelectedIssueRanks);
    
    // Add to dropdown history
    const newDropdownState = {
      inspectionType: selectedInspectionType,
      methods: {...selectedMethods},
      issueRanks: newSelectedIssueRanks,
      issueCategories: {...selectedIssueCategories}
    };
    addToHistory(editedResults, newDropdownState);
  };

  const handleIssueCategoryChange = (index, value) => {
    const newSelectedIssueCategories = {...selectedIssueCategories, [index]: value};
    setSelectedIssueCategories(newSelectedIssueCategories);
    
    // Add to dropdown history
    const newDropdownState = {
      inspectionType: selectedInspectionType,
      methods: {...selectedMethods},
      issueRanks: {...selectedIssueRanks},
      issueCategories: newSelectedIssueCategories
    };
    addToHistory(editedResults, newDropdownState);
  };

  const toggleEditing = () => {
    setIsEditing(!isEditing);
  };

  const saveChanges = () => {
    // Here you would implement saving the changes back to the server
    console.log("Saving changes:", editedResults);
    setIsEditing(false);
  };

  const resetChanges = () => {
    setEditedResults(JSON.parse(JSON.stringify(results))); // Reset to original
    setIsEditing(false);
  };

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Function to handle deletion of a method
  const handleDeleteMethod = (index) => {
    // Create a copy of the current results
    const newResults = { ...editedResults };
    
    // Remove the method at the specified index
    if (newResults["Inspection Methods"] && newResults["Inspection Methods"].length > index) {
      newResults["Inspection Methods"].splice(index, 1);
      setEditedResults(newResults);
      addToHistory(newResults);
    }
  };

  // Function to handle deletion of an issue
  const handleDeleteIssue = (index) => {
    // Create a copy of the current results
    const newResults = { ...editedResults };
    
    // Remove the issue at the specified index
    if (newResults["Non-Conformance"] && newResults["Non-Conformance"].length > index) {
      newResults["Non-Conformance"].splice(index, 1);
      setEditedResults(newResults);
      addToHistory(newResults);
    }
  };

  // Function to add a new inspection method
  const handleAddMethod = () => {
    setNewMethodValue('');
    setIsMethodModalOpen(true);
  };

  // Function to actually add the method after selection
  const confirmAddMethod = () => {
    if (!editedResults || !newMethodValue) return;
    
    const newResults = JSON.parse(JSON.stringify(editedResults));
    
    // Ensure the methods array exists
    if (!Array.isArray(newResults["Inspection Methods"])) {
      newResults["Inspection Methods"] = [];
    }
    
    // Add the new method with the selected value
    newResults["Inspection Methods"].push({
      "Method": {
        value: newMethodValue,
        page: []
      },
      "Next Inspection Date": {
        value: "",
        page: []
      }
    });
    
    // Update the selected methods state
    const newIndex = newResults["Inspection Methods"].length - 1;
    setSelectedMethods(prev => ({
      ...prev,
      [newIndex]: newMethodValue
    }));
    
    setEditedResults(newResults);
    addToHistory(newResults);
    setIsMethodModalOpen(false);
  };

  // Render page references as individual page buttons with badges
  // Helper function to render the quote field if available
  const renderQuote = (field) => {
    if (!field || !field.quote) return null;
    
    return (
      <div className="quote-container">
        <div className="quote-label">Source Quote:</div>
        <div className="quote-text">
          <blockquote>
            {field.quote}
          </blockquote>
        </div>
      </div>
    );
  };

  const renderPageReferences = (pageNumbers, field) => {
    if (!pageNumbers || pageNumbers.length === 0) return null;
    
    // Ensure page numbers are integers and one-indexed (PDF viewers typically use 1-indexed pages)
    const sanitizedPageNumbers = pageNumbers.map(num => {
      // Convert to integer if it's a string
      const pageNum = typeof num === 'string' ? parseInt(num, 10) : num;
      // Ensure it's valid
      return !isNaN(pageNum) ? pageNum : null;
    }).filter(Boolean); // Remove null/undefined values
    
    if (sanitizedPageNumbers.length === 0) return null;
    
    // Get the source quote from the field if available
    const sourceQuote = field && field.quote ? field.quote : null;
    
    // Handler for page navigation that works with iframe interaction
    const handlePageClick = (pageNum) => {
      try {
        const pageNumber = parseInt(pageNum, 10);
        console.log(`GenAIResultsTable - Initiating navigation to page ${pageNumber}`);
        
        // First try the direct callback if provided
        if (onPageNavigation && typeof onPageNavigation === 'function') {
          onPageNavigation(pageNumber, sourceQuote);
        }
        
        // Always dispatch the custom event for PDF viewer to catch
        const event = new CustomEvent('pdf-page-navigation', {
          detail: { 
            pageNumber: pageNumber,
            timestamp: Date.now(),
            source: 'GenAIResultsTable',
            sourceQuote: sourceQuote
          }
        });
        window.dispatchEvent(event);
        console.log(`Dispatched custom event pdf-page-navigation to page ${pageNumber} with quote: ${sourceQuote ? sourceQuote.substring(0, 30) + '...' : 'none'}`);
        
        // Also send a postMessage directly to all iframes in case they're listening
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach(iframe => {
          if (iframe.contentWindow) {
            try {
              console.log(`Sending postMessage to iframe for page ${pageNumber}`);
              iframe.contentWindow.postMessage({
                type: 'setPage',
                pageNumber: pageNumber,
                timestamp: Date.now(),
                source: 'GenAIResultsTable',
                action: 'forceNavigate',
                sourceQuote: sourceQuote
              }, '*');
            } catch (e) {
              console.warn('Could not postMessage to iframe:', e);
            }
          }
        });
        
        // Add a listener for confirmation from the PDF viewer
        const navigationConfirmationHandler = (event) => {
          if (event.data && 
              event.data.type === 'pageNavigationComplete' && 
              event.data.source === 'pdfjs' &&
              event.data.pageNumber === pageNumber) {
            console.log(`Navigation to page ${pageNumber} completed successfully`);
            // Remove this listener after receiving confirmation
            window.removeEventListener('message', navigationConfirmationHandler);
          }
        };
        window.addEventListener('message', navigationConfirmationHandler);
        
        // Set a timeout to remove the listener if we don't get confirmation
        setTimeout(() => {
          window.removeEventListener('message', navigationConfirmationHandler);
        }, 3000);
        
        console.log(`Completed all navigation attempts to page ${pageNumber}`);
      } catch (error) {
        console.error("Error navigating to page:", error);
      }
    };
    
    return (
      <div className="page-references">
        <span className="page-ref-label">Pages: </span>
        {sanitizedPageNumbers.map((pageNum, index) => (
          <Tooltip 
            key={`page-${pageNum}-${index}`}
            content={`Go to page ${pageNum}`} 
            position={Position.TOP}
          >
            <Button
              intent="primary"
              minimal
              small
              onClick={() => handlePageClick(pageNum)}
              className="page-number-button"
              aria-label={`Go to page ${pageNum}`}
              tabIndex={0}
              role="button"
            >
              {pageNum}
            </Button>
          </Tooltip>
        ))}
      </div>
    );
  };

  const renderSectionHeader = (title, icon, section) => (
    <div className="section-header" onClick={() => toggleSection(section)}>
      <div className="section-title">
        {icon === "notepad-text" ? <LucideNotepadText size={16} /> : <Icon icon={icon} size={16} />}
        <h4>{title}</h4>
      </div>
      <Button
        icon={expandedSections[section] ? "chevron-up" : "chevron-down"}
        minimal
        small
        onClick={(e) => {
          e.stopPropagation();
          toggleSection(section);
        }}
      />
    </div>
  );

  const renderField = (key, label, icon) => {
    const sectionKey = key.split('.')[0];
    const fieldKey = key.split('.')[1] || key;
    
    // Handle nested fields with better validation
    const field = sectionKey !== fieldKey 
      ? (editedResults[sectionKey] && editedResults[sectionKey][fieldKey]) 
      : editedResults[key];
    
    // Skip rendering if the entire section is missing
    if (!field) {
      console.log(`Field not found in results: ${key}`);
      return (
        <tr>
          <td className="field-label">
            <div className="field-label-container">
              {icon && <Icon icon={icon} size={12} className="field-icon" />}
              <span>{label}</span>
            </div>
          </td>
          <td>
            <div className="field-value-container">
              <div className="field-value">
                <span className="not-found">Data not available</span>
              </div>
            </div>
          </td>
        </tr>
      );
    }

    return (
      <tr>
        <td className="field-label">
          <div className="field-label-container">
            {icon && <Icon icon={icon} size={12} className="field-icon" />}
            <span>{label}</span>
          </div>
        </td>
        <td>
          {isEditing ? (
            <InputGroup
              id={`field-${key}`}
              value={field.value || ''}
              onChange={(e) => handleValueChange(sectionKey !== fieldKey ? sectionKey : key, e.target.value, sectionKey !== fieldKey ? fieldKey : undefined)}
              fill
            />
          ) : (
            <div className="field-value-container">
              <div className="field-value">
                {field.value ? field.value : <span className="not-found">Not found</span>}
              </div>
              {renderQuote(field)}
              {field.page && renderPageReferences(field.page, field)}
            </div>
          )}
        </td>
      </tr>
    );
  };

  const renderBooleanField = (key, label, icon) => {
    const sectionKey = key.split('.')[0];
    const fieldKey = key.split('.')[1] || key;
    
    // Handle nested fields with better validation
    const field = sectionKey !== fieldKey 
      ? (editedResults[sectionKey] && editedResults[sectionKey][fieldKey]) 
      : editedResults[key];
    
    // Skip rendering if the entire section is missing
    if (!field) {
      console.log(`Field not found in results: ${key}`);
      return (
        <tr>
          <td className="field-label">
            <div className="field-label-container">
              {icon && <Icon icon={icon} size={12} className="field-icon" />}
              <span>{label}</span>
            </div>
          </td>
          <td>
            <div className="field-value-container">
              <div className="field-value">
                <span className="not-found">Data not available</span>
              </div>
            </div>
          </td>
        </tr>
      );
    }

    return (
      <tr>
        <td className="field-label">
          <div className="field-label-container">
            {icon && <Icon icon={icon} size={12} className="field-icon" />}
            <span>{label}</span>
          </div>
        </td>
        <td>
          {isEditing ? (
            <div className="bp5-select">
              <select
                id={`field-${key}`}
                value={field.value.toString()}
                onChange={(e) => handleValueChange(sectionKey !== fieldKey ? sectionKey : key, e.target.value === 'true', sectionKey !== fieldKey ? fieldKey : undefined)}
              >
                <option value="true">Yes</option>
                <option value="false">No</option>
              </select>
            </div>
          ) : (
            <div className="field-value-container">
              <Tag 
                icon={field.value ? "tick-circle" : "cross-circle"}
                intent={field.value ? Intent.SUCCESS : Intent.DANGER} 
                minimal 
                className="boolean-tag"
              >
                {field.value ? 'Yes' : 'No'}
              </Tag>
              {renderQuote(field)}
              {field.page && renderPageReferences(field.page, field)}
            </div>
          )}
        </td>
      </tr>
    );
  };

  const renderInspectors = () => {
    const inspectors = editedResults.Inspectors;
    if (!inspectors || inspectors.length === 0) return null;

    return (
      <>
        {inspectors.map((inspector, index) => (
          <tr key={index}>
            <td className="field-label">
              <div className="field-label-container">
                <Icon icon="person" size={12} className="field-icon" />
                <span>Inspector {index + 1}</span>
              </div>
            </td>
            <td>
              {isEditing ? (
                <InputGroup
                  id={`inspector-${index}`}
                  value={inspector.name.value || ''}
                  onChange={(e) => handleValueChange('Inspectors', e.target.value, 'name', index)}
                  fill
                />
              ) : (
                <div className="field-value-container">
                  <div className="field-value">
                    {inspector.name.value || <span className="not-found">Not specified</span>}
                  </div>
                  {renderQuote(inspector.name)}
                  {renderPageReferences(inspector.name.page, inspector.name)}
                </div>
              )}
            </td>
          </tr>
        ))}
      </>
    );
  };

  const renderInspectionMethods = () => {
    const methods = editedResults["Inspection Methods"];
    if (!methods || methods.length === 0) return null;

    return (
      <>
        {methods.map((method, index) => (
          <React.Fragment key={`method-${index}`}>
            <tr className="method-header-row">
              <td colSpan="2" className="method-header">
                <div className="method-header-content">
                  <span className="method-header-text">Method {index + 1}</span>
                  <Button 
                    icon="cross" 
                    minimal 
                    small 
                    intent={Intent.DANGER}
                    onClick={() => handleDeleteMethod(index)}
                    className="delete-method-button"
                  />
                </div>
              </td>
            </tr>
            <tr>
              <td className="field-label indented">Method Name</td>
              <td>
                {isEditing ? (
                  <Select
                    placeholder="Select Method"
                    style={{ minWidth: 200 }}
                    value={method.Method.value || selectedMethods[index] || ''}
                    onChange={(value) => {
                      handleMethodChange(index, value);
                      handleValueChange('Inspection Methods', value, 'Method', index);
                    }}
                    options={(lookupValues?.["Inspection Methods"] || []).map(option => ({
                      value: option,
                      label: option
                    }))}
                    dropdownStyle={{ zIndex: 1050 }}
                    optionFilterProp="label"
                    showSearch
                    allowClear
                  />
                ) : (
                  <div className="field-value-container">
                    <div className="field-value">
                      {method.Method.value || <span className="not-found">Not specified</span>}
                      <div className="field-dropdown">
                        <Select
                          placeholder="Select normalized value"
                          style={{ minWidth: 200 }}
                          value={selectedMethods[index]}
                          onChange={(value) => handleMethodChange(index, value)}
                          options={(lookupValues?.["Inspection Methods"] || []).map(option => ({
                            value: option,
                            label: option
                          }))}
                          dropdownStyle={{ zIndex: 1050 }}
                          optionFilterProp="label"
                          showSearch
                          allowClear
                        />
                      </div>
                    </div>
                    {renderQuote(method.Method)}
                    {renderPageReferences(method.Method.page, method.Method)}
                  </div>
                )}
              </td>
            </tr>
            <tr>
              <td className="field-label indented">Next Inspection Date</td>
              <td>
                {isEditing ? (
                  <InputGroup
                    id={`method-date-${index}`}
                    value={method["Next Inspection Date"]?.value || ''}
                    onChange={(e) => handleValueChange('Inspection Methods', e.target.value, 'Next Inspection Date', index)}
                    fill
                  />
                ) : (
                  <div className="field-value-container">
                    <div className="field-value">
                      {method["Next Inspection Date"]?.value 
                        ? method["Next Inspection Date"].value
                        : <span className="not-found">Not specified</span>
                      }
                    </div>
                    {renderQuote(method["Next Inspection Date"])}
                    {renderPageReferences(method["Next Inspection Date"]?.page, method["Next Inspection Date"])}
                  </div>
                )}
              </td>
            </tr>
            {index < methods.length - 1 && (
              <tr>
                <td colSpan="2" className="method-separator"></td>
              </tr>
            )}
          </React.Fragment>
        ))}
      </>
    );
  };

  const renderIssues = () => {
    const issues = editedResults["Non-Conformance"];
    console.log('Non-Conformance issues:', issues);
    
    if (!issues || issues.length === 0) return null;

    return (
      <>
        {issues.map((issue, index) => {
          console.log(`Issue ${index}:`, issue);
          return (
          <React.Fragment key={`issue-${index}`}>
            <tr className="issue-header-row">
              <td colSpan="2" className="issue-header">
                <div className="method-header-content">
                  <span className="issue-header-text">Issue {index + 1}</span>
                  <Button 
                    icon="cross" 
                    minimal 
                    small 
                    intent={Intent.DANGER}
                    onClick={() => handleDeleteIssue(index)}
                    className="delete-issue-button"
                  />
                </div>
              </td>
            </tr>
            <tr>
              <td className="field-label indented">Description</td>
              <td>
                {isEditing ? (
                  <InputGroup
                    id={`issue-desc-${index}`}
                    value={issue.Issue?.value || ''}
                    onChange={(e) => handleValueChange('Non-Conformance', e.target.value, 'Issue', index)}
                    fill
                  />
                ) : (
                  <div className="field-value-container">
                    <div className="field-value issue-description">
                      {issue.Issue?.value || <span className="not-found">Not specified</span>}
                    </div>
                    {renderQuote(issue.Issue)}
                    {renderPageReferences(issue.Issue?.page, issue.Issue)}
                  </div>
                )}
              </td>
            </tr>
            <tr>
              <td className="field-label indented">Category</td>
              <td>
                {isEditing ? (
                  <Select
                    placeholder="Select Category"
                    style={{ minWidth: 200 }}
                    value={selectedIssueCategories[index]}
                    onChange={(value) => handleIssueCategoryChange(index, value)}
                    options={(lookupValues?.["NCR Category"] || []).map(option => ({
                      value: option,
                      label: option
                    }))}
                    dropdownStyle={{ zIndex: 1050 }}
                    optionFilterProp="label"
                    showSearch
                    allowClear
                  />
                ) : (
                  <div className="field-value-container">
                    <div className="field-value">
                      {issue.Category?.value || <span className="not-found">Not specified</span>}
                      <div className="field-dropdown">
                        <Select
                          placeholder="Select Category"
                          style={{ minWidth: 200 }}
                          value={selectedIssueCategories[index]}
                          onChange={(value) => handleIssueCategoryChange(index, value)}
                          options={(lookupValues?.["NCR Category"] || ["Mechanical", "Electrical", "Structural", "Safety", "Operational"]).map(option => ({
                            value: option,
                            label: option
                          }))}
                          dropdownStyle={{ zIndex: 1050 }}
                          optionFilterProp="label"
                          showSearch
                          allowClear
                        />
                      </div>
                    </div>
                    {renderPageReferences(issue.Category?.page, issue.Category)}
                  </div>
                )}
              </td>
            </tr>
            <tr>
              <td className="field-label indented">Rank</td>
              <td>
                {isEditing ? (
                  <Select
                    placeholder="Select Rank"
                    style={{ minWidth: 200 }}
                    value={selectedIssueRanks[index]}
                    onChange={(value) => handleIssueRankChange(index, value)}
                    options={(lookupValues?.["Rank"] || []).map(option => ({
                      value: option,
                      label: option
                    }))}
                    dropdownStyle={{ zIndex: 1050 }}
                    optionFilterProp="label"
                    showSearch
                    allowClear
                  />
                ) : (
                  <div className="field-value-container">
                    <div className="field-value">
                      {issue.Rank?.value || <span className="not-found">Not specified</span>}
                      <div className="field-dropdown">
                        <Select
                          placeholder="Select Rank"
                          style={{ minWidth: 200 }}
                          value={selectedIssueRanks[index]}
                          onChange={(value) => handleIssueRankChange(index, value)}
                          options={(lookupValues?.["Rank"] || ["Low", "Medium", "High", "Critical"]).map(option => ({
                            value: option,
                            label: option
                          }))}
                          dropdownStyle={{ zIndex: 1050 }}
                          optionFilterProp="label"
                          showSearch
                          allowClear
                        />
                      </div>
                    </div>
                    {renderPageReferences(issue.Rank?.page, issue.Rank)}
                  </div>
                )}
              </td>
            </tr>
            <tr>
              <td className="field-label indented">Recommendation</td>
              <td>
                {isEditing ? (
                  <InputGroup
                    id={`issue-rec-${index}`}
                    value={issue.Recommendation?.value || ''}
                    onChange={(e) => handleValueChange('Non-Conformance', e.target.value, 'Recommendation', index)}
                    fill
                  />
                ) : (
                  <div className="field-value-container">
                    <div className="field-value">
                      {issue.Recommendation?.value 
                        ? <span className="recommendation-text">{issue.Recommendation.value}</span>
                        : <span className="not-found">Not specified</span>
                      }
                    </div>
                    {renderQuote(issue.Recommendation)}
                    {renderPageReferences(issue.Recommendation?.page, issue.Recommendation)}
                  </div>
                )}
              </td>
            </tr>
            {index < issues.length - 1 && (
              <tr>
                <td colSpan="2" className="method-separator"></td>
              </tr>
            )}
          </React.Fragment>
        )})}
      </>
    );
  };

  const renderDetails = () => {
    const details = editedResults.Details || {};
    return (
      <>
        {Object.entries(details).map(([key, value], index) => {
          // Skip rendering this field if the value is null or empty or if it's Inspection Type (handled separately)
          if (!value || (typeof value === 'object' && !value.value) || key === "Inspection Type") return null;
          
          return (
            <React.Fragment key={`detail-${index}`}>
              <tr>
                <td className="field-label">{key}</td>
                <td>
                  {isEditing ? (
                    <InputGroup
                      id={`details-${key}`}
                      value={value.value || ''}
                      onChange={(e) => handleValueChange('Details', e.target.value, key)}
                      fill
                    />
                  ) : (
                    <div className="field-value-container">
                      <div className="field-value">
                        {value.value || <span className="not-found">Not specified</span>}
                      </div>
                      {renderPageReferences(value.page, value)}
                    </div>
                  )}
                </td>
              </tr>
            </React.Fragment>
          );
        })}
      </>
    );
  };

  // Custom renderField function for Inspection Type with dropdown
  const renderInspectionTypeField = () => {
    if (!editedResults?.Details?.["Inspection Type"]) return null;
    const value = editedResults.Details["Inspection Type"];
    
    return (
      <tr>
        <td className="field-label">Inspection Type</td>
        <td>
          {isEditing ? (
            <InputGroup
              id="details-Inspection Type"
              value={value.value || ''}
              onChange={(e) => handleValueChange('Details', e.target.value, 'Inspection Type')}
              fill
            />
          ) : (
            <div className="field-value-container">
              <div className="field-value">
                {value.value || <span className="not-found">Not specified</span>}
              </div>
              <div className="field-dropdown">
                <Select
                  placeholder="Select normalized value"
                  style={{ width: "100%" }}
                  value={selectedInspectionType}
                  onChange={handleInspectionTypeChange}
                  options={(lookupValues?.["Inspection Type"] || []).map(option => ({
                    value: option,
                    label: option
                  }))}
                  optionFilterProp="label"
                  showSearch
                  allowClear
                />
              </div>
              {renderPageReferences(value.page, value)}
            </div>
          )}
        </td>
      </tr>
    );
  };

  // Function to create a new inspection record using the AI-extracted data
  const createPIRecord = async () => {
    // Initialize default values in case editedResults is not available
    let assetInfo = {};
    
    // Check if user is authenticated
    if (!user || !user.email) {
      AIResultsToaster.show({
        message: "User authentication required",
        intent: "danger",
      });
      return;
    }
    
    // Get asset info from latestAssetClickParams if available
    if (window.latestAssetClickParams) {
      console.log("Using latestAssetClickParams:", window.latestAssetClickParams);
      assetInfo = {
        plant: window.latestAssetClickParams["Plant"] || "",
        unit: window.latestAssetClickParams["Unit"] || "",
        system: window.latestAssetClickParams["System"] || "",
        tag_name: window.latestAssetClickParams["Tag Name"] || "",
        asset_classification: window.latestAssetClickParams["Asset Classification"] || "",
        equipment_type: window.latestAssetClickParams["Equipment Type"] || ""
      };
      
      // Log the extracted asset info for debugging
      console.log("Extracted asset info:", assetInfo);
    } else if (!editedResults) {
      AIResultsToaster.show({
        message: "No asset information available to create a record",
        intent: "warning",
      });
      return;
    }
    
    // Check if user has selected inspection methods
    const hasSelectedMethods = Object.keys(selectedMethods).length > 0;
    if (!hasSelectedMethods) {
      AIResultsToaster.show({
        message: "Please select at least one inspection method before creating a record",
        intent: "warning",
      });
      return;
    }

    setIsCreatingRecord(true);

    try {
      // Format the data for the API
      const inspectionType = selectedInspectionType || (editedResults?.Details?.["Inspection Type"]?.value) || "In Service Inspection";
      
      // Use tag name from latestAssetClickParams
      let tagName = "";
      if (window.latestAssetClickParams && window.latestAssetClickParams["Tag Name"]) {
        tagName = String(window.latestAssetClickParams["Tag Name"]);
      } else if (editedResults?.Details?.["Tag Name"]?.value) {
        tagName = String(editedResults.Details["Tag Name"].value);
      }
      
      // Extract date from AI results or use current date as fallback
      let inspectionDate = "";
      if (editedResults?.Details?.["Date"]?.value) {
        inspectionDate = String(editedResults.Details["Date"].value);
      } else if (editedResults?.Details?.["Inspection Date"]?.value) {
        inspectionDate = String(editedResults.Details["Inspection Date"].value);
      } else {
        // Use current date as fallback in MM/DD/YYYY format
        const today = new Date();
        inspectionDate = `${(today.getMonth()+1).toString().padStart(2, '0')}/${today.getDate().toString().padStart(2, '0')}/${today.getFullYear()}`;
      }
      const inspectorCompany = "Advanced Tube Inspection";
      
      console.log("Using values:");
      console.log("- Tag Name (from latestAssetClickParams):", tagName);
      console.log("- Inspection Date (from AI results):", inspectionDate);
      console.log("- Inspector Company (hardcoded):", inspectorCompany);
      
      // Format inspectors as comma-separated string
      let inspectorNames = "";
      if (editedResults?.Inspectors && editedResults.Inspectors.length > 0) {
        inspectorNames = editedResults.Inspectors
          .map(inspector => inspector.name?.value || "")
          .filter(name => name.trim() !== "")
          .join(", ");
      }
      
      // Format inspection methods as JSON string
      let inspectionMethods = [];
      if (
        editedResults["Inspection Methods"] &&
        editedResults["Inspection Methods"].length > 0
      ) {
        inspectionMethods = editedResults["Inspection Methods"].map((method, index) => {
          // Use the user-selected method if available
          const methodName = selectedMethods[index] || method.Method?.value || "";
          return {
            "Method": methodName,
            "Findings": method.Findings?.value || "",
            "Recommendations": method.Recommendations?.value || ""
          };
        });
      }
      const methodsJson = JSON.stringify(inspectionMethods);

      // Format non-conformance issues as JSON string
      let nonConformanceIssues = [];
      if (editedResults["Non-Conformance"] && editedResults["Non-Conformance"].length > 0) {
        // First, filter out deleted issues
        const filteredIssues = editedResults["Non-Conformance"].filter(issue => !issue.isDeleted);
        
        // Create a mapping of non-deleted issues to their original indices
        // This ensures we get the correct selectedIssueRanks and selectedIssueCategories
        const originalIndices = {};
        let filteredIndex = 0;
        
        editedResults["Non-Conformance"].forEach((issue, index) => {
          if (!issue.isDeleted) {
            originalIndices[filteredIndex] = index;
            filteredIndex++;
          }
        });
        
        console.log("Original indices mapping:", originalIndices);
        
        // Now map the filtered issues with the correct selected values
        nonConformanceIssues = filteredIssues.map((issue, index) => {
          const originalIndex = originalIndices[index];
          // Use the user-selected rank and category if available
          const rank = selectedIssueRanks[originalIndex] || issue.Rank?.value || "";
          const category = selectedIssueCategories[originalIndex] || issue.Category?.value || "";
          
          console.log(`Issue ${index} (original index ${originalIndex}):`, {
            rank: rank,
            selectedRank: selectedIssueRanks[originalIndex],
            category: category,
            selectedCategory: selectedIssueCategories[originalIndex]
          });
          
          return {
            "Issue": issue.Issue?.value || "",
            "Rank": rank,
            "Category": category,
            "Recommendation": issue.Recommendation?.value || ""
          };
        });
      }
      const issuesJson = JSON.stringify(nonConformanceIssues);

      // Format file paths as JSON string
      const filesJson = JSON.stringify([
        {
          "file_path": String(filePath),
          "file_id": String(fileId)
        }
      ]);

      // Get session ID from local storage
      const sessionId = localStorage.getItem('session_id');

      // Call the API to create a new inspection record
      const response = await axios.get(
        `${process.env.REACT_APP_DATA_API}/data/new_inspection/`,
        {
          params: {
            plant: String(assetInfo.plant),
            unit: String(assetInfo.unit),
            system: String(assetInfo.system),
            tag_name: String(assetInfo.tag_name || tagName),
            asset_classification: String(assetInfo.asset_classification),
            equipment_type: String(assetInfo.equipment_type),
            components: "ALL", // Default value as specified in the API
            user: String(user.email),
            inspection_type: String(inspectionType),
            inspection_date: String(inspectionDate),
            inspector: String(inspectorNames),
            company: String(inspectorCompany), 
            inspection_methods: methodsJson,
            non_conformance_issues: issuesJson,
            files: filesJson,
          },
          headers: {
            "Content-Type": "application/json",
            "session-id": sessionId,
          },
        }
      );

      // If we have non-conformance issues, create NCRs for each one
      if (nonConformanceIssues.length > 0) {
        console.log(`Creating ${nonConformanceIssues.length} NCRs for non-deleted issues`);
        
        // Create NCRs sequentially with a delay between each
        for (let i = 0; i < nonConformanceIssues.length; i++) {
          const issue = nonConformanceIssues[i];
          
          // Wait 2 seconds between NCR creation to avoid overwhelming the API
          if (i > 0) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
          
          console.log("Creating NCR with values:", {
            plant: assetInfo.plant,
            unit: assetInfo.unit,
            system: assetInfo.system,
            tag_name: assetInfo.tag_name || tagName,
            asset_classification: assetInfo.asset_classification,
            equipment_type: assetInfo.equipment_type,
            rank: issue.Rank,
            category: issue.Category
          });
          
          // Create NCR using the same asset info from latestAssetClickParams
          await axios.get(
            `${process.env.REACT_APP_DATA_API}/data/new_ncr/`,
            {
              params: {
                plant: String(assetInfo.plant),
                unit: String(assetInfo.unit),
                system: String(assetInfo.system),
                tag_name: String(assetInfo.tag_name || tagName),
                asset_classification: String(assetInfo.asset_classification),
                equipment_type: String(assetInfo.equipment_type),
                user: String(user.email),
                inspection_date: String(inspectionDate),
                rank: String(issue.Rank),
                status: "Pending",
                category: String(issue.Category),
                details: String(issue.Issue),
                recommendations: String(issue.Recommendation)
              },
              headers: {
                "Content-Type": "application/json",
                "session-id": sessionId,
              },
            }
          );
          
          console.log(`Created NCR ${i+1} of ${nonConformanceIssues.length}`);
        }
      }

      if (response.data.message) {
        console.log("Record created successfully:", response.data.message);
        
        const successMessage = nonConformanceIssues.length > 0 
          ? `PI Record and ${nonConformanceIssues.length} NCRs created successfully` 
          : "PI Record created successfully";
        
        // Show success toast
        AIResultsToaster.show({
          message: successMessage,
          intent: "success",
        });
        
        // Add to action history
        dispatch(addAction({
          type: 'CREATE_RECORD',
          status: 'SUCCESS',
          message: successMessage,
        }));
        
        // Trigger data reload after a short delay
        setTimeout(() => {
          // If we have a parent component with a reload function, call it
          if (window.reloadPreviousInspectionsData) {
            window.reloadPreviousInspectionsData();
          }
          
          // Simulate clicking on the row again to refresh the data
          if (window.handleFocusAssetClick && window.latestAssetClickParams) {
            console.log("Simulating click on asset row to refresh data");
            window.handleFocusAssetClick(window.latestAssetClickParams);
          }
        }, 1800);
      } else {
        console.error("Error creating record:", response.data.error);
        const errorMessage = `Error creating record: ${response.data.error || "Unknown error"}`;
        
        // Show error toast
        AIResultsToaster.show({
          message: errorMessage,
          intent: "danger",
        });
        
        // Add to action history
        dispatch(addAction({
          type: 'DANGER',
          message: errorMessage,
          source: 'AI Analysis'
        }));
      }
    } catch (error) {
      console.error("API call failed:", error.message);
      if (error.response) {
        console.error("Response status:", error.response.status);
        console.error("Response data:", error.response.data);
      }
      
      const errorMessage = `Error creating record: ${error.message}`;
      
      // Show error toast
      AIResultsToaster.show({
        message: errorMessage,
        intent: "danger",
      });
      
      // Add to action history
      dispatch(addAction({
        type: 'DANGER',
        message: errorMessage,
        source: 'AI Analysis'
      }));
    } finally {
      setIsCreatingRecord(false);
    }
  };

  return (
    <div className={`genai-results-container ${isDarkTheme ? 'bp5-dark' : ''} ${isSmallContainer ? 'tab-container-small' : ''}`}>
      <Card elevation={Elevation.TWO} className="genai-results-header">
        <div className="genai-results-title">
          <h3>AI Analysis Results</h3>
          <div className="file-breadcrumbs">
            <Tooltip content={getFileName(filePath)} position={Position.TOP}>
              <Tag minimal className="file-tag">
                <Icon icon="document" />
                <span className="filename-text">{getFileName(filePath)}</span>
              </Tag>
            </Tooltip>
          </div>
          {isLoading && <Spinner size={16} intent="primary" className="header-spinner" />}
        </div>
        <div className="genai-results-actions">
          {isEditing ? (
            <>
              <ButtonGroup minimal={false}>
                <Tooltip content="Save Changes" position={Position.TOP}>
                  <Button
                    icon="tick"
                    intent="success"
                    onClick={saveChanges}
                    className="action-button"
                  />
                </Tooltip>
                <Tooltip content="Cancel Editing" position={Position.TOP}>
                  <Button
                    icon="cross"
                    intent="danger"
                    onClick={resetChanges}
                    className="action-button"
                  />
                </Tooltip>
                <Tooltip content="Undo" position={Position.TOP}>
                  <Button
                    icon="undo"
                    disabled={!canUndo}
                    onClick={handleUndo}
                    className="action-button"
                  />
                </Tooltip>
                <Tooltip content="Redo" position={Position.TOP}>
                  <Button
                    icon="redo"
                    disabled={!canRedo}
                    onClick={handleRedo}
                    className="action-button"
                  />
                </Tooltip>
              </ButtonGroup>
            </>
          ) : (
            <>
              <ButtonGroup minimal={false}>
                <Tooltip content="Edit Results" position={Position.TOP}>
                  <Button
                    icon="edit"
                    intent="primary"
                    onClick={toggleEditing}
                    className="action-button"
                  />
                </Tooltip>
                <Tooltip content="Reset to Original" position={Position.TOP}>
                  <Button
                    icon="refresh"
                    intent="warning"
                    onClick={() => setIsResetConfirmOpen(true)}
                    className="action-button"
                  />
                </Tooltip>
                <Tooltip content="Create PI Record" position={Position.TOP}>
                  <Button
                    icon="add"
                    intent="success"
                    onClick={createPIRecord}
                    className="action-button"
                    loading={isCreatingRecord}
                    disabled={isCreatingRecord}
                  />
                </Tooltip>
              </ButtonGroup>
            </>
          )}
        </div>
      </Card>
      
      <div className="genai-results-content" ref={containerRef}>
        <div className="table-container">
          <div className="table-section">
            {renderSectionHeader("Inspection Details", "clipboard", "details")}
            <Collapse isOpen={expandedSections.details}>
              <HTMLTable striped condensed className="data-table">
                <tbody>
                  {renderInspectionTypeField()}
                  {renderDetails()}
                  {renderField("Tag Name", "Tag Name", "tag")}
                  {renderField("Inspection Date", "Inspection Date", "calendar")}
                  {renderField("Inspector Company", "Inspector Company", "office")}
                  {renderBooleanField("NDE Data", "NDE Data Present", "database")}
                </tbody>
              </HTMLTable>
            </Collapse>
          </div>

          {editedResults.Inspectors && editedResults.Inspectors.length > 0 && (
            <div className="table-section">
              {renderSectionHeader("Inspectors", "people", "inspectors")}
              <Collapse isOpen={expandedSections.inspectors}>
                <HTMLTable striped condensed className="data-table">
                  <tbody>
                    {renderInspectors()}
                  </tbody>
                </HTMLTable>
              </Collapse>
            </div>
          )}

          {editedResults["Inspection Methods"] && editedResults["Inspection Methods"].length > 0 && (
            <div className="table-section">
              {renderSectionHeader("Inspection Methods", "notepad-text", "methods")}
              <Collapse isOpen={expandedSections.methods}>
                <HTMLTable striped condensed className="data-table">
                  <tbody>
                    {renderInspectionMethods()}
                  </tbody>
                </HTMLTable>
                {isEditing && (
                  <Button
                    icon="plus"
                  
                    small
                    intent={Intent.PRIMARY}
                    onClick={handleAddMethod}
                    className="add-button"
                  />
                )}
              </Collapse>
            </div>
          )}

          {editedResults["Non-Conformance"] && editedResults["Non-Conformance"].length > 0 && (
            <div className="table-section non-conformance-section">
              {renderSectionHeader("Non-Conformance Issues", "warning-sign", "issues")}
              <Collapse isOpen={expandedSections.issues}>
                <HTMLTable striped condensed className="data-table">
                  <tbody>
                    {renderIssues()}
                  </tbody>
                </HTMLTable>
              </Collapse>
            </div>
          )}
        </div>
      </div>
      {isMethodModalOpen && (
        <div className="method-modal-overlay">
          <div className="method-modal">
            <h4>Select Inspection Method</h4>
            <Select
              placeholder="Select Method"
              style={{ width: '100%', marginBottom: '15px' }}
              value={newMethodValue}
              onChange={(value) => setNewMethodValue(value)}
              options={(lookupValues?.["Inspection Methods"] || []).map(option => ({
                value: option,
                label: option
              }))}
              dropdownStyle={{ zIndex: 1050 }}
              optionFilterProp="label"
              showSearch
              allowClear
            />
            <div className="method-modal-buttons">
              <Button
                icon="tick"
                intent="success"
                onClick={confirmAddMethod}
                disabled={!newMethodValue}
                className="action-button"
              >
                Add Method
              </Button>
              <Button
                icon="cross"
                intent="danger"
                onClick={() => setIsMethodModalOpen(false)}
                className="action-button"
                style={{ marginLeft: '10px' }}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
      <Dialog
        isOpen={isResetConfirmOpen}
        onClose={() => setIsResetConfirmOpen(false)}
        title="Reset to Original AI Results?"
        className={isDarkTheme ? Classes.DARK : ''}
      >
        <div className={Classes.DIALOG_BODY}>
          <p>
            This will discard all your changes and restore the original AI-generated results. 
            Any deleted NCRs or methods will be restored, and any edits will be lost. This
            action cannot be undone.
          </p>
        </div>
        <div className={Classes.DIALOG_FOOTER}>
          <div className={Classes.DIALOG_FOOTER_ACTIONS}>
            <Button 
              intent="none" 
              onClick={() => setIsResetConfirmOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              intent="primary" 
              onClick={resetToOriginal}
            >
              Yes, Reset Everything
            </Button>
          </div>
        </div>
      </Dialog>
    </div>
  );
};

export default GenAIResultsTable;
