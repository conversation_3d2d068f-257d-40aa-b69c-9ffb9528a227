import React from 'react';

const NozzleMario = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    viewBox="0 0 455.71 581.43"
    version="1.0"
    style={{ width: "35px", height: "50px" }} 
  >
    <defs>
      <linearGradient id="linearGradient3152">
        <stop id="stop3154" style={{ stopColor: '#009a11' }} offset="0" />
        <stop id="stop3156" style={{ stopColor: '#00cb17' }} offset="1" />
      </linearGradient>
      <linearGradient
        id="linearGradient3158"
        y2="139.51"
        xlinkHref="#linearGradient3152"
        gradientUnits="userSpaceOnUse"
        x2="-286.43"
        y1="139.51"
        x1="-742.14"
      />
      <linearGradient
        id="linearGradient3166"
        y2="139.51"
        xlinkHref="#linearGradient3152"
        gradientUnits="userSpaceOnUse"
        x2="-286.43"
        gradientTransform="matrix(.76923 0 0 2.5351 -118.68 69.419)"
        y1="139.51"
        x1="-742.14"
      />
    </defs>
    <g transform="translate(742.14 -53.076)">
      <rect
        id="rect3164"
        style={{ strokeLinejoin: 'round', stroke: '#005e04', strokeWidth: 10, fill: 'url(#linearGradient3166)' }}
        height="412.86"
        width="342.86"
        y="216.65"
        x="-685.71"
      />
      <rect
        id="rect2380"
        style={{ strokeLinejoin: 'round', stroke: '#005e04', strokeWidth: 10, fill: 'url(#linearGradient3158)' }}
        height="162.86"
        width="445.71"
        y="58.076"
        x="-737.14"
      />
      <rect
        id="rect3160"
        style={{ fill: '#00d818' }}
        height="152.5"
        width="68.596"
        y="63.076"
        x="-467.87"
      />
      <rect
        id="rect3162"
        style={{ fill: '#00d818' }}
        height="152.5"
        width="27.168"
        y="63.076"
        x="-381.3"
      />
      <rect
        id="rect3168"
        style={{ fill: '#00d818' }}
        height="396.79"
        width="68.596"
        y="226.29"
        x="-492.16"
      />
      <rect
        id="rect3170"
        style={{ fill: '#00d818' }}
        height="396.79"
        width="27.168"
        y="226.29"
        x="-405.58"
      />
      <rect
        id="rect3172"
        style={{ fill: '#00820e' }}
        height="152.66"
        width="51.429"
        y="63.097"
        x="-731.79"
      />
      <rect
        id="rect3174"
        style={{ fill: '#00820e' }}
        height="398.37"
        width="51.429"
        y="225.95"
        x="-680.36"
      />
      <rect
        id="rect3178"
        style={{ fill: '#005e04' }}
        height="156.57"
        width="16.162"
        y="59.382"
        x="-739.43"
      />
      <rect
        id="rect3180"
        style={{ fill: '#005e04' }}
        height="403.05"
        width="16.162"
        y="224.04"
        x="-689.93"
      />
      <rect
        id="rect3182"
        style={{ fill: '#00820e' }}
        height="152.66"
        width="7.992"
        y="63.097"
        x="-667.79"
      />
      <rect
        id="rect3184"
        style={{ fill: '#00820e' }}
        height="398.63"
        width="7.992"
        y="225.73"
        x="-618.04"
      />
    </g>
  </svg>
);

export default NozzleMario;
