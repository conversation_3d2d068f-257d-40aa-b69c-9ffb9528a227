import React from 'react';

const Nozzle = ({ isDarkTheme, width = '100%', height = '100%' }) => {
  const fillColor = isDarkTheme ? "#FFFFFF" : "#000000";
  const strokeColor = isDarkTheme ? "#FFFFFF" : "#000000";

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 1210 1164"  // This ensures the SVG scales correctly
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="xMidYMid meet"  // This ensures it is centered
    >
      <g transform="translate(-2218 -586)">
        <g>
          <path
            d="M2227 1496.5C2227 1359.81 2337.81 1249 2474.5 1249 2611.19 1249 2722 1359.81 2722 1496.5 2722 1633.19 2611.19 1744 2474.5 1744 2337.81 1744 2227 1633.19 2227 1496.5Z"
            stroke={strokeColor}
            strokeWidth="10.3125"
            strokeLinecap="butt"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            fill={isDarkTheme ? "none" : fillColor}
          />
          <path
            d="M2289 1503.5C2289 1404.92 2369.36 1325 2468.5 1325 2567.64 1325 2648 1404.92 2648 1503.5 2648 1602.08 2567.64 1682 2468.5 1682 2369.36 1682 2289 1602.08 2289 1503.5Z"
            stroke={strokeColor}
            strokeWidth="10.3125"
            strokeLinecap="butt"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            fill="none"
          />
          <path
            d="M0 0 697.509 653.92"
            stroke={strokeColor}
            strokeWidth="10.3125"
            strokeLinecap="butt"
            strokeLinejoin="miter"
            strokeMiterlimit="8"
            fill="none"
            transform="matrix(1 0 0 -1 2299 1321.92)"
          />
          <path
            d="M0 0 655.02 680.953"
            stroke={strokeColor}
            strokeWidth="10.3125"
            strokeLinecap="butt"
            strokeLinejoin="miter"
            strokeMiterlimit="8"
            fill="none"
            transform="matrix(1 0 0 -1 2679 1636.95)"
          />
          <path
            d="M2993.74 667.193C3106.18 595.419 3247.14 643.705 3308.58 775.042 3335.14 831.8 3343.44 897.78 3331.98 961.062"
            stroke={strokeColor}
            strokeWidth="10.3125"
            strokeLinecap="butt"
            strokeLinejoin="miter"
            strokeMiterlimit="8"
            fill="none"
          />
          <circle
            cx="3406.23"
            cy="615.656"
            r="7.08978"
     
          />
        </g>
      </g>
    </svg>
  );
};

export default Nozzle;
