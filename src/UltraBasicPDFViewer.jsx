import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Spinner } from '@blueprintjs/core';

// Import required CSS
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

const UltraBasicPDFViewer = ({ fileId, filePath }) => {
  const [pdfData, setPdfData] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load PDF
  useEffect(() => {
    const loadPdf = async () => {
      try {
        setLoading(true);
        console.log('Trying to load PDF with fileId:', fileId);
        
        // Direct GET request with blob response
        const apiUrl = `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`;
        console.log('API URL:', apiUrl);
        
        const response = await fetch(apiUrl, {
          headers: {
            'session-id': localStorage.getItem('session_id')
          }
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }
        
        const data = await response.blob();
        const url = URL.createObjectURL(data);
        setPdfData(url);
        setLoading(false);
      } catch (err) {
        console.error('Failed to load PDF:', err);
        setError(err.message);
        setLoading(false);
      }
    };

    if (fileId) {
      loadPdf();
    }
    
    // Cleanup
    return () => {
      if (pdfData && typeof pdfData === 'string' && pdfData.startsWith('blob:')) {
        URL.revokeObjectURL(pdfData);
      }
    };
  }, [fileId, pdfData]);

  function onDocumentLoadSuccess({ numPages }) {
    setNumPages(numPages);
  }

  // Simple renderer for all pages
  const renderPages = () => {
    const pages = [];
    for (let i = 1; i <= numPages; i++) {
      pages.push(
        <div key={`page-${i}`} style={{ margin: '10px 0' }}>
          <Page pageNumber={i} />
        </div>
      );
    }
    return pages;
  };

  return (
    <div style={{ height: '100%', overflow: 'auto', padding: '10px' }}>
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
          <Spinner />
        </div>
      ) : error ? (
        <div style={{ color: 'red', padding: '20px' }}>
          <h3>Error Loading PDF</h3>
          <p>{error}</p>
        </div>
      ) : (
        <Document
          file={pdfData}
          onLoadSuccess={onDocumentLoadSuccess}
          loading={<Spinner />}
          options={{
            cMapUrl: 'https://unpkg.com/pdfjs-dist@2.12.313/cmaps/',
            cMapPacked: true,
          }}
        >
          {numPages && renderPages()}
        </Document>
      )}
    </div>
  );
};

export default UltraBasicPDFViewer;
