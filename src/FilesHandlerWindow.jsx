
import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import SplitPane from "react-split-pane";
import {
  Navbar,
  NavbarGroup,
  Card,
  Button,
  NonIdealState,
} from "@blueprintjs/core";
import PDFJsViewer from "./PDFJsViewer2";
import { Tag } from "primereact/tag";
import "primereact/resources/themes/fluent-light/theme.css";
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";
import { BiUnlink } from "react-icons/bi";
import FilesTable from "./FilesTable/FilesTable";
import { setSelectedFile, setIsFileLinked } from "./filesHandlerSlice";
import "@blueprintjs/core/lib/css/blueprint.css";
import "./Styles/App.css";
const LinkFileIcon = ({ style, className }) => {
    return (
      <svg
        className={`svg-icon ${className}`}
        style={{
          width: "1em",
          height: "1em",
          verticalAlign: "middle",
          fill: "currentColor",
          overflow: "hidden",
          ...style,
        }}
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M729.421868 592.96194c-48.383964 0-90.440825 26.126031-113.861234 64.768119L394.33309 537.957185c17.308201-22.50762 28.02017-50.344618 28.02017-80.886215 0-25.245987-7.436366-48.599881-19.651572-68.744687l209.729675-123.097596c22.592554 41.771359 66.267263 70.521147 116.990504 70.521147 73.617671 0 133.512806-59.878762 133.512806-133.512806S803.039539 68.724221 729.421868 68.724221 595.910085 128.60196 595.910085 202.236004c0 10.903328 1.682315 21.366633 4.15974 31.519878L382.027833 361.727485c-24.084535-23.533996-56.92857-38.169321-93.187379-38.169321-73.617671 0-133.512806 59.878762-133.512806 133.512806s59.895135 133.512806 133.512806 133.512806c30.53034 0 58.359152-10.707876 80.878029-28.016077l232.395908 125.803217c-3.651157 12.157901-6.205329 24.77322-6.205329 38.104853 0 73.634044 59.895135 133.512806 133.512806 133.512806s133.512806-59.878762 133.512806-133.512806S803.039539 592.96194 729.421868 592.96194zM729.421868 135.479601c36.81651 0 66.756403 29.955242 66.756403 66.756403s-29.938869 66.756403-66.756403 66.756403c-36.81651 0-66.756403-29.955242-66.756403-66.756403S692.605358 135.479601 729.421868 135.479601zM288.840454 523.82635c-36.81651 0-66.756403-29.955242-66.756403-66.756403s29.938869-66.756403 66.756403-66.756403 66.756403 29.955242 66.756403 66.756403S325.657988 523.82635 288.840454 523.82635zM729.421868 793.231149c-36.81651 0-66.756403-29.955242-66.756403-66.756403 0-36.801161 29.938869-66.756403 66.756403-66.756403 36.81651 0 66.756403 29.955242 66.756403 66.756403C796.178271 763.274884 766.239402 793.231149 729.421868 793.231149z" />
      </svg>
    );
  };

function FilesHandlerWindow({ isDarkTheme }) {
  const dispatch = useDispatch();
  const selectedFile = useSelector((state) => state.filesHandler.selectedFile);
  const isFileLinked = useSelector((state) => state.filesHandler.isFileLinked);
  const currentInspectionFiles = useSelector(
    (state) => state.filesHandler.currentInspectionFiles
  );
  const activeTab = useSelector((state) => state.filesHandler.activeTab);
  const inspectionsGridApi = useSelector(
    (state) => state.filesHandler.inspectionsGridApi
  );

  useEffect(() => {
    if (activeTab === "assetFiles") {
      dispatch(setIsFileLinked(false));
    }
  }, [activeTab, dispatch]);

  useEffect(() => {
    const selectedNodes = inspectionsGridApi
      ? inspectionsGridApi.getSelectedNodes()
      : [];
    if (selectedNodes.length === 0) {
      dispatch(setIsFileLinked(false));
    }
    console.log("inspectionGridAPI:", inspectionsGridApi);
  }, [inspectionsGridApi, dispatch]);

  const onPrimeButtonClick = () => {
    if (activeTab === "previousInspections") {
      dispatch(setIsFileLinked(!isFileLinked));
      // Add logic for handling link action for previous inspections
    } else if (activeTab === "assetFiles") {
      dispatch(setIsFileLinked(!isFileLinked));
      // Add logic for handling link action for asset files
    }
  };

  const handleFileClick = (fileId, filename, path) => {
    dispatch(setSelectedFile({ id: fileId, name: filename, path: path }));
  };

  useEffect(() => {
    console.log("rowFiles", currentInspectionFiles);
    if (selectedFile && currentInspectionFiles.includes(selectedFile.path)) {
      dispatch(setIsFileLinked(true));
      console.log("File is linked");
    } else {
      dispatch(setIsFileLinked(false));
      console.log("File is Not linked");
    }
  }, [currentInspectionFiles, selectedFile, dispatch]);

  return (
    <div className={isDarkTheme ? "bp5-dark" : ""} style={{ position: "relative", height: "100%", width: "100%" }}>
      <SplitPane
        split="vertical"
        minSize={100}
        maxSize={window.innerWidth - 200}
        defaultSize="67%"
        size="67%"
        onChange={() => {
          window.dispatchEvent(new Event("resize"));
        }}
      >
        <div className="main-content" style={{ marginTop: "2px" }}>
          {selectedFile && selectedFile.id ? (
            <>
              <Navbar>
                <NavbarGroup
                  className="centered-items"
                  style={{ paddingLeft: "25px" }}
                >
                  <Tag
                    style={{
                      background: "transparent",
                      fontSize: "11px",
                      color: isDarkTheme ? "white" : "black",
                    }}
                    value={selectedFile.path}
                    rounded
                  ></Tag>

                  <Navbar.Divider />
                  <Button
                    onClick={onPrimeButtonClick}
                    className={`link-button ${
                      isFileLinked ? "my-prime-button-break" : ""
                    }`}
                  >
                    {isFileLinked ? <BiUnlink /> : <LinkFileIcon />}
                  </Button>
                </NavbarGroup>
              </Navbar>
              <Card className="no-padding-card" style={{ height: "100%" }}>
                <PDFJsViewer
                  fileId={selectedFile.id}
                  isDarkTheme={isDarkTheme}
                  style={{ padding: "15px 15px 15px 15px" }}
                />
              </Card>
            </>
          ) : (
            <div className="centered-non-ideal" style={{ height: "100%" }}>
              <NonIdealState
                icon="document-open"
                title="No File Selected"
                description="Please select a file to be displayed"
              />
            </div>
          )}
        </div>

        <div className="fileviewer-sidebar">
          <FilesTable onFileClick={handleFileClick} isDarkTheme={isDarkTheme} />
        </div>
      </SplitPane>
    </div>
  );
}

export default FilesHandlerWindow;