import React, {
  useEffect,
  useRef,
  useState,
  use<PERSON><PERSON>back,
  useMemo,
} from "react";
import axios from "axios";
import { Document, Page, pdfjs } from "react-pdf";
import {
  <PERSON>,
  Spinner,
  <PERSON><PERSON>,
  HTMLSelect,
  Popover,
  InputGroup,
} from "@blueprintjs/core";
import {
  RotateCcw,
  RotateCw,
  ZoomIn,
  ZoomOut,
  Maximize,
  Search,
  MousePointer,
  Hand,
} from "lucide-react";
import { useIntersectionObserver } from "@wojtekmaj/react-hooks";
import PanZoom from "react-easy-panzoom";
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const ZOOM_LEVELS = [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3, 3.5, 4];

const observerConfig = {
  threshold: [0.1], // Trigger when 10% of the page is visible
};

const PageWithObserver = React.memo(
  ({
    pageNumber,
    setPageVisibility,
    scale,
    rotation,
    customTextRenderer,
    orientation,
  }) => {
    const [page, setPage] = useState();

    const onIntersectionChange = useCallback(
      ([entry]) => {
        setPageVisibility(pageNumber, entry.isIntersecting);
      },
      [pageNumber, setPageVisibility]
    );

    useIntersectionObserver(page, observerConfig, onIntersectionChange);

    const pageWidth = orientation === "landscape" ? 1200 : 800;
    const pageHeight = orientation === "landscape" ? 800 : 1200;

    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          marginBottom: "10px",
        }}
      >
        <Page
          canvasRef={setPage}
          pageNumber={pageNumber}
          scale={scale}
          width={pageWidth}
          height={pageHeight}
          rotate={rotation}
          customTextRenderer={customTextRenderer}
          style={{ display: "block", margin: "auto" }}
        />
      </div>
    );
  }
);

const PDFJsViewer = ({ fileId, isDarkTheme, ownerDocument, panZoomRef }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [searchText, setSearchText] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [isSearchPopoverVisible, setIsSearchPopoverVisible] = useState(false);
  const [isPanZoomMode, setIsPanZoomMode] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [enableZoom, setEnableZoom] = useState(false);
  const [orientations, setOrientations] = useState([]);

  const pdfContainerRef = useRef(null);
  const parentClassName = isDarkTheme ? "bp5-dark" : "";

  useEffect(() => {
    let cancelSource = axios.CancelToken.source();

    const fetchFile = async () => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: {
              "session-id": localStorage.getItem("session_id"),
            },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        const pdfUrl = URL.createObjectURL(response.data);
        setPdfFile(pdfUrl);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        if (axios.isCancel(error)) {
          console.log("Request canceled:", error.message);
        } else {
          console.error("Failed to fetch the PDF:", error);
        }
      }
    };

    if (fileId) {
      fetchFile();
    }

    return () => {
      cancelSource.cancel("Operation canceled by the user.");
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId]);

  useEffect(() => {
    const getOrientation = async () => {
      if (!pdfFile) return;
      const pdf = await pdfjs.getDocument(pdfFile).promise;
      const orientations = [];

      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const viewport = page.getViewport({ scale: 1 });
        orientations.push(
          viewport.width > viewport.height ? "landscape" : "portrait"
        );
      }

      setOrientations(orientations);
    };

    getOrientation();
  }, [pdfFile]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === "Control" || event.key === "Meta") {
        setEnableZoom(true);
      }
    };

    const handleKeyUp = (event) => {
      if (event.key === "Control" || event.key === "Meta") {
        setEnableZoom(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setPageNumber(1);
  };

  const setPageVisibility = useCallback((pageNumber, isIntersecting) => {
    if (isIntersecting) {
      setPageNumber(pageNumber);
    }
  }, []);

  const updateZoom = (newZoomLevel) => {
    setZoomLevel(newZoomLevel);
    setScale(newZoomLevel);
    if (panZoomRef.current && panZoomRef.current.setTransformState) {
      panZoomRef.current.setTransformState(0, 0, newZoomLevel);
    }
  };

  const zoomIn = () => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex < ZOOM_LEVELS.length - 1) {
      const newZoomLevel = ZOOM_LEVELS[currentIndex + 1];
      updateZoom(newZoomLevel);
    }
  };

  const zoomOut = () => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex > 0) {
      const newZoomLevel = ZOOM_LEVELS[currentIndex - 1];
      updateZoom(newZoomLevel);
    }
  };

  const rotateLeft = () => {
    setRotation((prevRotation) => (prevRotation - 90 + 360) % 360);
  };

  const rotateRight = () => {
    setRotation((prevRotation) => (prevRotation + 90) % 360);
  };

  const handleFitToWidth = () => {
    if (pdfContainerRef.current) {
      const containerWidth = pdfContainerRef.current.clientWidth;
      const pageWidth = pdfContainerRef.current.querySelector(
        ".react-pdf__Page__canvas"
      )?.width;
      if (pageWidth) {
        const newScale = containerWidth / pageWidth;
        updateZoom(newScale);
      }
    }
  };

  const resetView = () => {
    updateZoom(1.0);
    setRotation(0);
    if (panZoomRef.current && panZoomRef.current.reset) {
      panZoomRef.current.reset();
    }
  };

  const searchDocument = async () => {
    if (searchText.trim() === "") return;
    const results = [];
    for (let i = 1; i <= numPages; i++) {
      const page = await pdfjs
        .getDocument(pdfFile)
        .promise.then((pdf) => pdf.getPage(i));
      const textContent = await page.getTextContent();
      const textItems = textContent.items.map((item) => ({
        str: item.str,
        transform: item.transform,
        width: item.width,
        height: item.height,
        page: i,
      }));

      textItems.forEach((item) => {
        let match;
        const regex = new RegExp(searchText, "gi");
        while ((match = regex.exec(item.str)) !== null) {
          results.push({
            page: item.page,
            str: match[0],
            transform: item.transform,
            width: item.width,
            height: item.height,
          });
        }
      });
    }

    setSearchResults(results);
    setCurrentSearchIndex(0);
    if (results.length > 0) {
      navigateToSearchResult(results[0].page);
    }
  };

  const navigateToSearchResult = (pageNum) => {
    setPageNumber(pageNum);
    pdfContainerRef.current
      .querySelector(`.react-pdf__Page[data-page-number="${pageNum}"]`)
      ?.scrollIntoView({ behavior: "smooth" });
  };

  const nextSearchResult = () => {
    if (searchResults.length === 0) return;

    const newIndex = (currentSearchIndex + 1) % searchResults.length;
    setCurrentSearchIndex(newIndex);
    navigateToSearchResult(searchResults[newIndex].page);
  };

  const prevSearchResult = () => {
    if (searchResults.length === 0) return;

    const newIndex =
      (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
    setCurrentSearchIndex(newIndex);
    navigateToSearchResult(searchResults[newIndex].page);
  };

  const clearSearch = () => {
    setSearchText("");
    setSearchResults([]);
    setIsSearchPopoverVisible(false);
  };

  const memoizedOptions = useMemo(
    () => ({
      ownerDocument: ownerDocument || document,
      cMapUrl: "https://unpkg.com/pdfjs-dist@2.9.359/cmaps/",
      cMapPacked: true,
    }),
    [ownerDocument]
  );

  const highlightPattern = useCallback(
    (text, pattern) =>
      pattern
        ? text.replace(
            new RegExp(`(${pattern})`, "gi"),
            '<mark style="background-color: gold; padding: 0; margin: 0; border-radius: 2px;">$1</mark>'
          )
        : text,
    []
  );

  const textRenderer = useCallback(
    (textItem) => highlightPattern(textItem.str, searchText),
    [searchText, highlightPattern]
  );

  const togglePanZoomMode = () => {
    setIsPanZoomMode((prev) => {
      if (!prev) {
        if (panZoomRef.current && panZoomRef.current.autoCenter) {
          setTimeout(() => {
            panZoomRef.current.autoCenter();
          }, 0);
        }
      }
      return !prev;
    });
  };

  const searchContent = (
    <div style={{ padding: "10px", height: "calc(100vh-250px)" }}>
      <InputGroup
        value={searchText}
        onChange={(e) => setSearchText(e.target.value)}
        placeholder="Enter search text"
        rightElement={
          <Button
            icon={<Search size={16} />}
            minimal={true}
            onClick={searchDocument}
          />
        }
      />
      <div style={{ marginTop: "10px" }}>
        <Button
          icon="arrow-left"
          onClick={prevSearchResult}
          disabled={searchResults.length === 0}
        >
          Previous
        </Button>
        <Button
          icon="arrow-right"
          onClick={nextSearchResult}
          disabled={searchResults.length === 0}
        >
          Next
        </Button>
        <Button
          icon="cross"
          onClick={clearSearch}
          style={{ marginLeft: "5px" }}
        >
          Close
        </Button>
      </div>
      {searchResults.length > 0 && (
        <div style={{ marginTop: "10px" }}>
          Match {currentSearchIndex + 1} of {searchResults.length}
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <div
        style={{
          height: "100vh",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spinner intent="primary" />
      </div>
    );
  }

  if (!pdfFile) {
    return (
      <div
        style={{
          height: "calc(100vh-250px)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Spinner intent="primary" />
      </div>
    );
  }
  return (
    <div className={parentClassName} style={{ height: "calc(100hv-250px)" }}>
      <Card
        style={{
          marginBottom: 0,
          padding: "2px",
          display: "flex",
          alignItems: "center",
          gap: "10px",
          background: isDarkTheme ? "#1C2127" : "white",
        }}
      >
        <Popover
          content={searchContent}
          isOpen={isSearchPopoverVisible}
          placement="bottom"
          onInteraction={(state) => setIsSearchPopoverVisible(state)}
        >
          <Button icon={<Search size={16} />} minimal={true} />
        </Popover>
        <HTMLSelect
          style={{
            background: isDarkTheme ? "#1C2127" : "white",
            color: isDarkTheme ? "white" : "black",
          }}
          value={Math.round(zoomLevel * 100)}
          onChange={(e) => {
            const newScale = parseFloat(e.target.value) / 100;
            updateZoom(newScale);
          }}
        >
          {ZOOM_LEVELS.map((level) => (
            <option key={level} value={level * 100}>
              {Math.round(level * 100)}%
            </option>
          ))}
        </HTMLSelect>
        <Button icon={<ZoomIn size={16} />} onClick={zoomIn} minimal={true} />
        <Button icon={<ZoomOut size={16} />} onClick={zoomOut} minimal={true} />
        <Button
          icon={<Maximize size={16} />}
          onClick={handleFitToWidth}
          minimal={true}
        />
        <Button
          icon={<RotateCcw size={16} />}
          onClick={rotateLeft}
          minimal={true}
        />
        <Button
          icon={<RotateCw size={16} />}
          onClick={rotateRight}
          minimal={true}
        />
        <div style={{ display: "flex", alignItems: "center" }}>
          <Button
            icon={<MousePointer size={16} />}
            onClick={togglePanZoomMode}
            minimal={isPanZoomMode}
            intent={!isPanZoomMode ? "primary" : "none"}
          />
          <Button
            icon={<Hand size={16} />}
            onClick={togglePanZoomMode}
            minimal={!isPanZoomMode}
            intent={isPanZoomMode ? "primary" : "none"}
          />
        </div>
        <Button text="Reset View" onClick={resetView} minimal={true} />
        <span style={{ marginLeft: "10px" }}>
          Page {pageNumber} of {numPages}
        </span>
      </Card>

      <div
        ref={pdfContainerRef}
        className="pdf-container"
        style={{
          height: "calc(100vh - 200px)",
          marginTop: "10px",
          overflow: "auto",
        }}
      >
        {isPanZoomMode ? (
          <PanZoom
            ref={panZoomRef}
            zoom={zoomLevel}
            minZoom={0.5}
            maxZoom={4}
            enableBoundingBox={true}
            boundaryRatioHorizontal={1.3}
            boundaryRatioVertical={1.3}
            disableScrollZoom={true}
            disableDoubleClickZoom={true}
            style={{ border: "none" }}
          >
            <Document
              file={pdfFile}
              onLoadSuccess={onDocumentLoadSuccess}
              loading={<Spinner />}
              options={memoizedOptions}
              className="pdf-container"
            >
              {Array.from(new Array(numPages), (el, index) => (
                <PageWithObserver
                  key={`page_${index + 1}`}
                  pageNumber={index + 1}
                  setPageVisibility={setPageVisibility}
                  scale={scale}
                  rotation={rotation}
                  customTextRenderer={textRenderer}
                  orientation={orientations[index]}
                />
              ))}
            </Document>
          </PanZoom>
        ) : (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            loading={<Spinner />}
            options={memoizedOptions}
            className="pdf-container"
          >
            {Array.from(new Array(numPages), (el, index) => (
              <PageWithObserver
                key={`page_${index + 1}`}
                pageNumber={index + 1}
                setPageVisibility={setPageVisibility}
                scale={scale}
                rotation={rotation}
                customTextRenderer={textRenderer}
                orientation={orientations[index]}
              />
            ))}
          </Document>
        )}
      </div>
    </div>
  );
};

export default PDFJsViewer;
