import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Spinner, SpinnerSize } from "@blueprintjs/core";
import { useInView } from 'react-intersection-observer';
import {
  RPProvider,
  RPDefaultLayout,
  RPPages,
  RPTheme,
  useRotationContext,
  useDarkModeContext
} from "@pdf-viewer/react";
import {
  updatePdfViewer,
  setViewerVisibility,
  setFocusedViewer,
  selectVisiblePDFs
} from './redux/currentPdfSlice';

// PDF cache to prevent reloading
const pdfCache = {};

/**
 * Dark Mode Handler component
 * This component synchronizes the PDF viewer's dark mode with the app's isDarkTheme prop
 */
const DarkModeHandler = ({ isDarkTheme }) => {
  const { setDarkMode } = useDarkModeContext();
  
  // Sync the dark mode with the isDarkTheme prop
  useEffect(() => {
    console.log('Setting PDF viewer dark mode to:', isDarkTheme);
    setDarkMode(isDarkTheme);
  }, [isDarkTheme, setDarkMode]);
  
  // This component doesn't render anything
  return null;
};

/**
 * PDFViewerNew component
 * 
 * This component is responsible for rendering a PDF file using the @pdf-viewer/react library.
 * It handles loading, error handling, and provides a clean UI.
 * 
 * @param {string} pdfUrl - The URL of the PDF file to load
 * @param {boolean} isDarkTheme - Whether to use a dark theme
 */
export default function PDFViewerNew({ pdfUrl, isDarkTheme }) {
  console.log('PDFViewerNew rendering with props:', { pdfUrl, isDarkTheme });
  
  const [isLoading, setIsLoading] = useState(true);
  const [pdfError, setPdfError] = useState(null);
  const layoutRef = useRef(null);

  // Create rotation buttons
  const rotateClockwiseButton = useMemo(() => {
    const button = document.createElement('button');
    button.innerHTML = '↻';
    button.title = 'Rotate clockwise';
    button.style.background = 'none';
    button.style.border = '1px solid transparent';
    button.style.borderRadius = '3px';
    button.style.cursor = 'pointer';
    button.style.padding = '4px 8px';
    button.style.fontSize = '18px';
    button.style.color = isDarkTheme ? '#BFCCD6' : '#5C7080';
    button.style.transition = 'all 0.2s ease';
    button.style.margin = '0 2px';
    button.onmouseover = () => {
      button.style.backgroundColor = isDarkTheme ? '#394B59' : '#E1E8ED';
    };
    button.onmouseout = () => {
      button.style.backgroundColor = 'transparent';
    };
    return button;
  }, [isDarkTheme]);

  const rotateCounterClockwiseButton = useMemo(() => {
    const button = document.createElement('button');
    button.innerHTML = '↺';
    button.title = 'Rotate counterclockwise';
    button.style.background = 'none';
    button.style.border = '1px solid transparent';
    button.style.borderRadius = '3px';
    button.style.cursor = 'pointer';
    button.style.padding = '4px 8px';
    button.style.fontSize = '18px';
    button.style.color = isDarkTheme ? '#BFCCD6' : '#5C7080';
    button.style.transition = 'all 0.2s ease';
    button.style.margin = '0 2px';
    button.onmouseover = () => {
      button.style.backgroundColor = isDarkTheme ? '#394B59' : '#E1E8ED';
    };
    button.onmouseout = () => {
      button.style.backgroundColor = 'transparent';
    };
    return button;
  }, [isDarkTheme]);

  // Create hand tool button
  const handToolButton = useMemo(() => {
    const button = document.createElement('button');
    button.innerHTML = '✋';
    button.title = 'Hand tool (toggle)';
    button.style.background = 'none';
    button.style.border = '1px solid transparent';
    button.style.borderRadius = '3px';
    button.style.cursor = 'pointer';
    button.style.padding = '4px 8px';
    button.style.fontSize = '18px';
    button.style.color = isDarkTheme ? '#BFCCD6' : '#5C7080';
    button.style.transition = 'all 0.2s ease';
    button.style.margin = '0 2px';
    button.onmouseover = () => {
      if (button.getAttribute('data-active') !== 'true') {
        button.style.backgroundColor = isDarkTheme ? '#394B59' : '#E1E8ED';
      }
    };
    button.onmouseout = () => {
      if (button.getAttribute('data-active') !== 'true') {
        button.style.backgroundColor = 'transparent';
      }
    };
    return button;
  }, [isDarkTheme]);

  // Handle viewer loaded event
  const handleLoaded = useCallback(() => {
    // Get the toolbar element
    const toolbar = layoutRef.current?.querySelector('[data-rp="topBarRight"]');
    if (!toolbar) return;

    // Add rotation buttons to toolbar
    toolbar.prepend(rotateClockwiseButton);
    toolbar.prepend(rotateCounterClockwiseButton);
    
    // Add hand tool button to toolbar
    toolbar.prepend(handToolButton);
    
    // Add event listeners for rotation
    rotateClockwiseButton.addEventListener('click', () => {
      // Visual feedback on click
      rotateClockwiseButton.style.backgroundColor = isDarkTheme ? '#30404D' : '#D8E1E8';
      setTimeout(() => {
        rotateClockwiseButton.style.backgroundColor = 'transparent';
      }, 200);
      
      // Use direct DOM manipulation for rotation
      const viewer = layoutRef.current?.querySelector('[data-rp="viewer"]');
      if (viewer) {
        const currentRotation = parseInt(viewer.getAttribute('data-rotation') || '0', 10);
        const newRotation = (currentRotation + 90) % 360;
        viewer.setAttribute('data-rotation', newRotation.toString());
        
        // Find all pages and apply rotation
        const pages = viewer.querySelectorAll('[data-rp="page"]');
        pages.forEach(page => {
          page.style.transform = `rotate(${newRotation}deg)`;
          page.style.transition = 'transform 0.3s ease';
        });
        
        console.log(`Rotated document clockwise to ${newRotation} degrees`);
      }
    });
    
    rotateCounterClockwiseButton.addEventListener('click', () => {
      // Visual feedback on click
      rotateCounterClockwiseButton.style.backgroundColor = isDarkTheme ? '#30404D' : '#D8E1E8';
      setTimeout(() => {
        rotateCounterClockwiseButton.style.backgroundColor = 'transparent';
      }, 200);
      
      // Use direct DOM manipulation for rotation
      const viewer = layoutRef.current?.querySelector('[data-rp="viewer"]');
      if (viewer) {
        const currentRotation = parseInt(viewer.getAttribute('data-rotation') || '0', 10);
        const newRotation = (currentRotation - 90 + 360) % 360;
        viewer.setAttribute('data-rotation', newRotation.toString());
        
        // Find all pages and apply rotation
        const pages = viewer.querySelectorAll('[data-rp="page"]');
        pages.forEach(page => {
          page.style.transform = `rotate(${newRotation}deg)`;
          page.style.transition = 'transform 0.3s ease';
        });
        
        console.log(`Rotated document counter-clockwise to ${newRotation} degrees`);
      }
    });
    
    handToolButton.addEventListener('click', () => {
      const viewer = layoutRef.current?.querySelector('[data-rp="viewer"]');
      if (viewer) {
        // Toggle hand tool mode
        const isHandToolActive = viewer.getAttribute('data-hand-tool') === 'true';
        const newHandToolState = !isHandToolActive;
        viewer.setAttribute('data-hand-tool', newHandToolState.toString());
        
        // Update button appearance for active state
        if (newHandToolState) {
          handToolButton.style.backgroundColor = isDarkTheme ? '#30404D' : '#D8E1E8';
          handToolButton.style.border = `1px solid ${isDarkTheme ? '#5C7080' : '#BFCCD6'}`;
          handToolButton.setAttribute('data-active', 'true');
        } else {
          handToolButton.style.backgroundColor = 'transparent';
          handToolButton.style.border = '1px solid transparent';
          handToolButton.setAttribute('data-active', 'false');
        }
        
        // Change cursor style
        if (newHandToolState) {
          viewer.style.cursor = 'grab';
          // When active and mouse is down, change to grabbing cursor
          viewer.onmousedown = () => { viewer.style.cursor = 'grabbing'; };
          viewer.onmouseup = () => { viewer.style.cursor = 'grab'; };
        } else {
          viewer.style.cursor = 'auto';
          viewer.onmousedown = null;
          viewer.onmouseup = null;
        }
        
        console.log(`Hand tool ${newHandToolState ? 'activated' : 'deactivated'}`);
      }
    });
    
  }, [rotateClockwiseButton, rotateCounterClockwiseButton, handToolButton, isDarkTheme]);

  // Cleanup function
  const cleanupOnLoaded = useCallback(() => {
    if (rotateClockwiseButton) {
      rotateClockwiseButton.remove();
    }
    if (rotateCounterClockwiseButton) {
      rotateCounterClockwiseButton.remove();
    }
    if (handToolButton) {
      handToolButton.remove();
    }
  }, [rotateClockwiseButton, rotateCounterClockwiseButton, handToolButton]);

  /**
   * Handle document loading
   * 
   * @param {object} e - The event object
   */
  const handleDocumentLoaded = (e) => {
    console.log('PDF document loaded:', e);
    setIsLoading(false);
  };

  if (!pdfUrl) {
    return (
      <div 
        className="pdf-error-container" 
        style={{ 
          display: 'flex', 
          flexDirection: 'column',
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100%', 
          width: '100%'
        }}
      >
        <div>No PDF URL provided</div>
      </div>
    );
  }

  console.log('Rendering PDF viewer with URL:', pdfUrl);
  console.log('Dark theme enabled:', isDarkTheme);
  
  // Define Blueprint 5 colors for light and dark themes
  const bp5LightColors = {
    '--rp-toolbar-background': '#F5F8FA',
    '--rp-text-color': '#182026',
    '--rp-icon-color': '#5C7080',
    '--rp-icon-hover-color': '#182026',
    '--rp-separator-color': '#D8E1E8',
    '--rp-input-background': '#FFFFFF',
    '--rp-input-border': '#BFCCD6',
    '--rp-dropdown-background': '#FFFFFF',
    '--rp-dropdown-shadow': 'rgba(16, 22, 26, 0.1)',
    '--rp-dialog-background': '#FFFFFF',
    '--rp-dialog-shadow': 'rgba(16, 22, 26, 0.2)',
    '--rp-tooltip-background': '#394B59',
    '--rp-tooltip-text': '#FFFFFF'
  };

  const bp5DarkColors = {
    '--rp-toolbar-background': '#1C2127',
    '--rp-text-color': '#F5F8FA',
    '--rp-icon-color': '#BFCCD6',
    '--rp-icon-hover-color': '#FFFFFF',
    '--rp-separator-color': '#394B59',
    '--rp-input-background': '#394B59',
    '--rp-input-border': '#5C7080',
    '--rp-dropdown-background': '#30404D',
    '--rp-dropdown-shadow': 'rgba(0, 0, 0, 0.2)',
    '--rp-dialog-background': '#1C2127',
    '--rp-dialog-shadow': 'rgba(0, 0, 0, 0.3)',
    '--rp-tooltip-background': '#5C7080',
    '--rp-tooltip-text': '#FFFFFF',
    '--rp-dark-gray2': '#252A31',
    '--rp-dark-gray3': '#2F343C',
    '--rp-dark-gray4': '#383E47',
    '--rp-dark-gray5': '#404854'
  };
  
  return (
    <div style={{ 
      width: '100%', 
      height: '100%', 
      position: 'relative'
    }}>
      {isLoading && (
        <div 
          className="spinner-container" 
          style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            height: '100%', 
            width: '100%',
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 10,
            backgroundColor: isDarkTheme ? 'rgba(16, 22, 26, 0.7)' : 'rgba(255, 255, 255, 0.7)'
          }}
        >
          <Spinner
            className="spinner-center"
            intent="primary"
            size={SpinnerSize.STANDARD}
          />
        </div>
      )}
      <RPProvider 
        src={pdfUrl}
        withCredentials={false}
        onDocumentLoad={handleDocumentLoaded}
        onError={(error) => {
          console.error("Error in PDF viewer:", error);
          setPdfError("Error displaying the PDF. Please try again.");
        }}
      >
        <DarkModeHandler isDarkTheme={isDarkTheme} />
        <RPTheme
          customVariables={bp5LightColors}
          customDarkVariables={bp5DarkColors}
        >
          <RPDefaultLayout 
            ref={layoutRef}
            onLoaded={handleLoaded}
            cleanupOnLoaded={cleanupOnLoaded}
            style={{ 
              width: "100%", 
              height: "100%",
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              display: "flex",
              flexDirection: "column"
            }}
            slots={{
              downloadTool: false,
              printTool: false,
              openFileTool: false,
              themeSwitcher: false,
              rotateTool: false // Disable the default rotation tool
            }}
          >
            <RPPages 
              style={{
                flexGrow: 1,
                overflow: "auto",
                height: "100%",
                width: "100%"
              }}
            />
          </RPDefaultLayout>
        </RPTheme>
      </RPProvider>
    </div>
  );
}
