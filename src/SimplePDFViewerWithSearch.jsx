import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { Card, Spinner, <PERSON>ton, ButtonGroup, HTMLSelect } from "@blueprintjs/core";
import { Input } from 'antd';
import { RotateCcw, RotateCw, ZoomIn, ZoomOut, Search, X } from "lucide-react";
import { useDispatch, useSelector } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import Draggable from 'react-draggable';
import { throttle } from 'lodash';

// Import Redux actions
import {
  updatePdfViewer,
  setViewerVisibility,
  setFocusedViewer,
} from './redux/currentPdfSlice';

// Import CSS
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "./pdf-viewer.css";

// Set up the worker for PDF.js
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

// Constants
const ZOOM_LEVELS = [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3];
const PAGE_PADDING = 10;

// SearchWindow component
const SearchWindow = ({ isOpen, onClose, searchQuery, onSearchChange, onSearch, searchResults, currentSearchIndex, onNavigateSearch }) => {
  if (!isOpen) return null;
  
  return (
    <Draggable handle=".search-window-header">
      <div className="search-window" style={{
        position: 'absolute',
        top: '50px',
        right: '20px',
        width: '300px',
        backgroundColor: '#FFFFFF',
        border: '1px solid #ccc',
        borderRadius: '4px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        zIndex: 1000,
      }}>
        <div className="search-window-header" style={{
          padding: '5px 10px',
          backgroundColor: '#F5F8FA',
          borderBottom: '1px solid #ccc',
          cursor: 'move',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '30px',
        }}>
          <span>Search PDF</span>
          <Button icon={<X size={14} />} minimal={true} small={true} onClick={onClose} />
        </div>
        <div style={{ padding: '10px' }}>
          <Input.Search
            placeholder="Enter search term"
            value={searchQuery}
            onChange={onSearchChange}
            onSearch={onSearch}
            style={{ marginBottom: "16px" }}
          />
          {searchResults.length > 0 ? (
            <div>
              <p style={{ fontSize: '12px', marginBottom: '8px' }}>
                Result {currentSearchIndex + 1} of {searchResults.length}
              </p>
              <div style={{ 
                padding: '8px', 
                backgroundColor: '#F5F8FA', 
                borderRadius: '3px',
                marginBottom: '8px',
                fontSize: '12px'
              }}>
                <div style={{ fontWeight: 'bold' }}>Page {searchResults[currentSearchIndex].page}</div>
                <div>{searchResults[currentSearchIndex].text}</div>
              </div>
              <Button 
                onClick={() => onNavigateSearch('prev')} 
                style={{ marginRight: "8px" }} 
                small={true} 
                disabled={searchResults.length <= 1}
              >
                Previous
              </Button>
              <Button 
                onClick={() => onNavigateSearch('next')} 
                small={true} 
                disabled={searchResults.length <= 1}
              >
                Next
              </Button>
            </div>
          ) : (
            searchQuery && <p style={{ fontSize: '12px', marginBottom: '8px' }}>No results found.</p>
          )}
        </div>
      </div>
    </Draggable>
  );
};

// Main PDF Viewer component
const SimplePDFViewerWithSearch = ({ fileId, filePath, isDarkTheme }) => {
  // State for PDF
  const [pdfUrl, setPdfUrl] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  
  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  
  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  
  // Refs
  const containerRef = useRef(null);
  const pagesRef = useRef({});
  
  // Redux and visibility
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  // Fetch PDF file
  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();
    let objectUrl = null;
    
    // Reset states
    setPdfUrl(null);
    setLoading(true);
    setError(null);
    setSearchResults([]);
    setCurrentSearchIndex(-1);
    
    const fetchFile = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: { "session-id": localStorage.getItem("session_id") },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );
        
        if (isMounted) {
          objectUrl = URL.createObjectURL(response.data);
          setPdfUrl(objectUrl);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(err)) {
            console.log("Request canceled:", err.message);
          } else {
            console.error("Failed to fetch the PDF:", err);
            setError(`Failed to load the PDF. Please try again.`);
          }
        }
      }
    };
    
    if (fileId) {
      fetchFile();
    } else {
      setLoading(false);
      setError('No file ID provided. Cannot load the PDF.');
    }
    
    // Cleanup function
    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [fileId]);
  
  // Redux/Visibility effects
  useEffect(() => {
    dispatch(setViewerVisibility({ fileId, isVisible: inView }));
    if (inView) {
      dispatch(setFocusedViewer(fileId));
    }
  }, [inView, fileId, dispatch, filePath]);
  
  // Update Redux store when page changes
  useEffect(() => {
    if (numPages > 0) {
      dispatch(updatePdfViewer({
        fileId,
        filePath,
        page: pageNumber,
        isVisible: inView
      }));
    }
  }, [pageNumber, fileId, filePath, inView, dispatch, numPages]);
  
  // Document load handlers
  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    console.log(`Document loaded with ${numPages} pages`);
    setNumPages(numPages);
    setLoading(false);
    setPageNumber(1);
  }, []);
  
  const onDocumentLoadError = useCallback((error) => {
    console.error('Error loading PDF:', error);
    setError(`Failed to load PDF: ${error.message}`);
    setLoading(false);
  }, []);
  
  // Navigation functions
  const goToPrevPage = useCallback(() => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  }, []);
  
  const goToNextPage = useCallback(() => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  }, [numPages]);
  
  const goToPage = useCallback((page) => {
    if (page >= 1 && page <= numPages) {
      setPageNumber(page);
      scrollToPage(page);
    }
  }, [numPages]);
  
  // Scroll to page function
  const scrollToPage = useCallback((pageNum) => {
    if (containerRef.current && pagesRef.current[pageNum]) {
      pagesRef.current[pageNum].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, []);
  
  // Zoom functions
  const zoomIn = useCallback(throttle(() => {
    setScale(prevScale => {
      const currentIndex = ZOOM_LEVELS.findIndex(level => level >= prevScale);
      const nextIndex = Math.min(currentIndex + 1, ZOOM_LEVELS.length - 1);
      return ZOOM_LEVELS[nextIndex];
    });
  }, 200), []);
  
  const zoomOut = useCallback(throttle(() => {
    setScale(prevScale => {
      const currentIndex = ZOOM_LEVELS.findIndex(level => level >= prevScale);
      const prevIndex = Math.max(currentIndex - 1, 0);
      return ZOOM_LEVELS[prevIndex];
    });
  }, 200), []);
  
  // Search functions
  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);
  
  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim() || !pdfUrl) {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      return;
    }
    
    try {
      setIsSearchOpen(true);
      
      // Load the PDF document
      const loadingTask = pdfjs.getDocument(pdfUrl);
      const pdf = await loadingTask.promise;
      
      const results = [];
      
      // Search through each page
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const text = textContent.items.map(item => item.str).join(' ');
        
        // Simple search for the query in the text
        let searchIndex = text.toLowerCase().indexOf(searchQuery.toLowerCase());
        while (searchIndex !== -1) {
          // Get some context around the match
          const startIndex = Math.max(0, searchIndex - 40);
          const endIndex = Math.min(text.length, searchIndex + searchQuery.length + 40);
          const contextText = text.substring(startIndex, endIndex);
          
          // Add the result
          results.push({
            page: i,
            text: '...' + contextText + '...',
            index: searchIndex
          });
          
          // Find the next occurrence
          searchIndex = text.toLowerCase().indexOf(searchQuery.toLowerCase(), searchIndex + 1);
        }
      }
      
      setSearchResults(results);
      
      if (results.length > 0) {
        setCurrentSearchIndex(0);
        setPageNumber(results[0].page);
        scrollToPage(results[0].page);
        
        // Highlight the text on the page
        setTimeout(() => {
          highlightTextOnPage(results[0].page, searchQuery);
        }, 500);
      } else {
        setCurrentSearchIndex(-1);
      }
    } catch (error) {
      console.error('Error during search:', error);
    }
  }, [searchQuery, pdfUrl, scrollToPage]);
  
  const navigateSearch = useCallback((direction) => {
    if (searchResults.length === 0) return;
    
    const newIndex = direction === 'next'
      ? (currentSearchIndex + 1) % searchResults.length
      : (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
    
    setCurrentSearchIndex(newIndex);
    
    // Go to the page with the search result
    const page = searchResults[newIndex].page;
    setPageNumber(page);
    scrollToPage(page);
    
    // Highlight the text on the page
    setTimeout(() => {
      highlightTextOnPage(page, searchQuery);
    }, 500);
  }, [searchResults, currentSearchIndex, scrollToPage, searchQuery]);
  
  // Function to highlight text on a page
  const highlightTextOnPage = useCallback((page, text) => {
    if (!text || !pagesRef.current[page]) return;
    
    // Find the text layer in the page
    const textLayer = pagesRef.current[page].querySelector('.react-pdf__Page__textContent');
    if (!textLayer) return;
    
    // Clear previous highlights
    const existingHighlights = document.querySelectorAll('.pdf-text-highlight');
    existingHighlights.forEach(el => el.classList.remove('pdf-text-highlight'));
    
    // Find and highlight text nodes containing the search text
    const textNodes = Array.from(textLayer.querySelectorAll('span'));
    const lowerText = text.toLowerCase();
    
    textNodes.forEach(node => {
      const nodeText = node.textContent.toLowerCase();
      if (nodeText.includes(lowerText)) {
        node.classList.add('pdf-text-highlight');
      }
    });
  }, []);
  
  // Render pages
  const renderPages = useCallback(() => {
    const pages = [];
    
    for (let i = 1; i <= numPages; i++) {
      pages.push(
        <div 
          key={`page-container-${i}`}
          ref={el => pagesRef.current[i] = el}
          style={{
            margin: `${PAGE_PADDING}px 0`,
            display: 'flex',
            justifyContent: 'center'
          }}
        >
          <Page
            key={`page-${i}-${scale}-${rotation}`}
            pageNumber={i}
            scale={scale}
            rotate={rotation}
            renderTextLayer={true}
            renderAnnotationLayer={true}
            className={`pdf-page ${i === pageNumber ? 'current-page' : ''}`}
            loading={
              <div style={{ width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <Spinner size={30} />
              </div>
            }
            error={
              <div style={{ padding: '20px', color: 'red', textAlign: 'center' }}>
                Error loading page {i}
              </div>
            }
            onRenderSuccess={() => {
              // If this is the current page and we have search results, highlight the text
              if (i === pageNumber && searchResults.length > 0 && currentSearchIndex >= 0) {
                highlightTextOnPage(i, searchQuery);
              }
            }}
          />
        </div>
      );
    }
    
    return pages;
  }, [numPages, scale, rotation, pageNumber, searchResults, currentSearchIndex, searchQuery, highlightTextOnPage]);
  
  return (
    <div ref={ref} className={isDarkTheme ? 'bp5-dark' : ''} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      {/* Toolbar */}
      <Card style={{ padding: '5px', marginBottom: '5px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Navigation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <ButtonGroup>
              <Button icon="chevron-left" onClick={goToPrevPage} disabled={pageNumber <= 1} />
              <div style={{ padding: '0 10px', display: 'flex', alignItems: 'center' }}>
                Page {pageNumber} of {numPages || '?'}
              </div>
              <Button icon="chevron-right" onClick={goToNextPage} disabled={pageNumber >= numPages} />
              <Button icon="locate" onClick={() => {
                const page = prompt(`Enter page number (1-${numPages}):`, pageNumber);
                if (page) {
                  const pageNum = parseInt(page, 10);
                  if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= numPages) {
                    goToPage(pageNum);
                  }
                }
              }} />
            </ButtonGroup>
          </div>
          
          {/* Zoom and rotation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <ButtonGroup>
              <Button icon={<RotateCcw size={16} />} onClick={() => setRotation(prev => (prev - 90 + 360) % 360)} />
              <Button icon={<RotateCw size={16} />} onClick={() => setRotation(prev => (prev + 90) % 360)} />
            </ButtonGroup>
            
            <ButtonGroup>
              <Button icon={<ZoomOut size={16} />} onClick={zoomOut} />
              <HTMLSelect
                options={ZOOM_LEVELS.map(level => ({ label: `${Math.round(level * 100)}%`, value: level }))}
                value={scale}
                onChange={e => setScale(parseFloat(e.target.value))}
                style={{ width: '100px' }}
              />
              <Button icon={<ZoomIn size={16} />} onClick={zoomIn} />
            </ButtonGroup>
          </div>
          
          {/* Search controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <Input
              placeholder="Search..."
              value={searchQuery}
              onChange={handleSearchChange}
              onPressEnter={handleSearch}
              style={{ width: '200px' }}
            />
            <Button icon={<Search size={16} />} onClick={handleSearch} />
          </div>
        </div>
      </Card>
      
      {/* PDF Viewer */}
      <div ref={containerRef} style={{ flex: 1, overflow: 'auto', position: 'relative' }}>
        {error ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
            <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>
            <Button intent="primary" onClick={() => window.location.reload()}>Retry</Button>
          </div>
        ) : loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Spinner size={50} />
          </div>
        ) : (
          <Document
            file={pdfUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <Spinner size={50} />
              </div>
            }
          >
            {renderPages()}
          </Document>
        )}
        
        {/* Search Window */}
        <SearchWindow
          isOpen={isSearchOpen}
          onClose={() => setIsSearchOpen(false)}
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          onSearch={handleSearch}
          searchResults={searchResults}
          currentSearchIndex={currentSearchIndex}
          onNavigateSearch={navigateSearch}
        />
      </div>
    </div>
  );
};

export default SimplePDFViewerWithSearch;
