import React, { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { Card, <PERSON>ner, <PERSON>ton, ButtonGroup, HTMLSelect } from "@blueprintjs/core";
import { Input } from 'antd';
import { ZoomIn, ZoomOut, RotateCcw, RotateCw, Search } from "lucide-react";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "./pdf-viewer.css";

// Set up the worker for PDF.js
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.js`;

const ZOOM_LEVELS = [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3];

const SimplePDFViewer = ({ filePath, fileId, isDarkTheme }) => {
  // State for PDF rendering
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for search
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);

  // Refs
  const containerRef = useRef(null);
  const pageRefs = useRef({});

  // Handle document load success
  const onDocumentLoadSuccess = ({ numPages }) => {
    console.log(`Document loaded with ${numPages} pages`);
    setNumPages(numPages);
    setLoading(false);
  };

  // Handle document load error
  const onDocumentLoadError = (error) => {
    console.error('Error loading PDF:', error);
    setError(`Failed to load PDF: ${error.message}`);
    setLoading(false);

    // Log additional information for debugging
    console.log('PDF loading failed with:', {
      fileId,
      filePath,
      error: error.message
    });
  };

  // Create a URL for the PDF file
  const [pdfUrl, setPdfUrl] = useState(null);

  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();
    let objectUrl = null;

    // Reset states
    setPdfUrl(null);
    setLoading(true);
    setError(null);

    console.log('Fetching PDF file:', { fileId, filePath });

    const fetchFile = async () => {
      try {
        // This is the exact same approach used in PDFJsViewer2.jsx
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: { "session-id": localStorage.getItem("session_id") },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        if (isMounted) {
          objectUrl = URL.createObjectURL(response.data);
          console.log('Created blob URL:', objectUrl);
          setPdfUrl(objectUrl);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(err)) {
            console.log("Request canceled:", err.message);
          } else {
            console.error("Failed to fetch the PDF:", err);
            setError(`Failed to load the PDF: ${err.message}. Please try again.`);
          }
        }
      }
    };

    if (fileId) {
      fetchFile();
    } else if (filePath && filePath.startsWith('blob:')) {
      // If it's already a blob URL, use it directly
      setPdfUrl(filePath);
      setLoading(false);
    } else {
      setLoading(false);
      setError('No file ID provided. Cannot load the PDF.');
    }

    // Cleanup function
    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [fileId, filePath]);

  // Navigation functions
  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  };

  const goToNextPage = () => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  };

  // Zoom functions
  const zoomIn = () => {
    setScale(prev => {
      const currentIndex = ZOOM_LEVELS.findIndex(level => level >= prev);
      const nextIndex = Math.min(currentIndex + 1, ZOOM_LEVELS.length - 1);
      return ZOOM_LEVELS[nextIndex];
    });
  };

  const zoomOut = () => {
    setScale(prev => {
      const currentIndex = ZOOM_LEVELS.findIndex(level => level >= prev);
      const prevIndex = Math.max(currentIndex - 1, 0);
      return ZOOM_LEVELS[prevIndex];
    });
  };

  // Rotation functions
  const rotateClockwise = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const rotateCounterClockwise = () => {
    setRotation(prev => (prev - 90 + 360) % 360);
  };

  // Search functions
  const handleSearch = async () => {
    if (!searchText.trim()) {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      return;
    }

    try {
      const results = [];

      for (let i = 1; i <= numPages; i++) {
        const textContent = await getPageText(i);
        const regex = new RegExp(searchText, 'gi');
        let match;

        while ((match = regex.exec(textContent)) !== null) {
          results.push({
            pageNumber: i,
            matchText: match[0],
            matchIndex: match.index
          });
        }
      }

      setSearchResults(results);
      if (results.length > 0) {
        setCurrentSearchIndex(0);
        setPageNumber(results[0].pageNumber);
      } else {
        setCurrentSearchIndex(-1);
      }
    } catch (error) {
      console.error('Search error:', error);
    }
  };

  const getPageText = async (pageNum) => {
    try {
      const pdf = await pdfjs.getDocument(filePath).promise;
      const page = await pdf.getPage(pageNum);
      const textContent = await page.getTextContent();
      return textContent.items.map(item => item.str).join(' ');
    } catch (error) {
      console.error(`Error getting text for page ${pageNum}:`, error);
      return '';
    }
  };

  const navigateSearchResults = (direction) => {
    if (searchResults.length === 0) return;

    const newIndex = direction === 'next'
      ? (currentSearchIndex + 1) % searchResults.length
      : (currentSearchIndex - 1 + searchResults.length) % searchResults.length;

    setCurrentSearchIndex(newIndex);
    setPageNumber(searchResults[newIndex].pageNumber);
  };

  // Render pages
  const renderPages = () => {
    const pages = [];

    for (let i = 1; i <= numPages; i++) {
      pages.push(
        <div key={`page-${i}`} style={{ marginBottom: '10px' }}>
          <Page
            pageNumber={i}
            scale={scale}
            rotate={rotation}
            renderTextLayer={true}
            renderAnnotationLayer={true}
            inputRef={el => { pageRefs.current[i] = el; }}
            loading={<div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
              <Spinner size={30} />
            </div>}
          />
        </div>
      );
    }

    return pages;
  };

  return (
    <div className={isDarkTheme ? 'bp5-dark' : ''} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Toolbar */}
      <Card style={{ padding: '5px', marginBottom: '5px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Navigation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <ButtonGroup>
              <Button icon="chevron-left" onClick={goToPrevPage} disabled={pageNumber <= 1} />
              <div style={{ padding: '0 10px', display: 'flex', alignItems: 'center' }}>
                Page {pageNumber} of {numPages || '?'}
              </div>
              <Button icon="chevron-right" onClick={goToNextPage} disabled={pageNumber >= numPages} />
            </ButtonGroup>
          </div>

          {/* Zoom and rotation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <ButtonGroup>
              <Button icon={<RotateCcw size={16} />} onClick={rotateCounterClockwise} />
              <Button icon={<RotateCw size={16} />} onClick={rotateClockwise} />
            </ButtonGroup>

            <ButtonGroup>
              <Button icon={<ZoomOut size={16} />} onClick={zoomOut} />
              <HTMLSelect
                options={ZOOM_LEVELS.map(level => ({ label: `${level * 100}%`, value: level }))}
                value={scale}
                onChange={e => setScale(parseFloat(e.target.value))}
                style={{ width: '100px' }}
              />
              <Button icon={<ZoomIn size={16} />} onClick={zoomIn} />
            </ButtonGroup>
          </div>

          {/* Search controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <Input
              placeholder="Search..."
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              style={{ width: '200px' }}
            />
            <Button icon={<Search size={16} />} onClick={handleSearch} />
            {searchResults.length > 0 && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
                <Button icon="arrow-left" onClick={() => navigateSearchResults('prev')} />
                <span>{currentSearchIndex + 1} of {searchResults.length}</span>
                <Button icon="arrow-right" onClick={() => navigateSearchResults('next')} />
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* PDF Viewer */}
      <div ref={containerRef} style={{ flex: 1, overflow: 'auto', padding: '10px' }}>
        {error ? (
          <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
            <h3>Error Loading PDF</h3>
            <p>{error}</p>
            <div style={{ marginTop: '20px', display: 'flex', gap: '10px', justifyContent: 'center', flexWrap: 'wrap' }}>
              <Button intent="primary" onClick={() => window.location.reload()}>Retry</Button>
              <Button intent="warning" onClick={() => {
                console.log('Debug info:', { fileId, filePath, pdfUrl, error });
                alert(`File ID: ${fileId}\nFile Path: ${filePath}\nPDF URL: ${pdfUrl || 'none'}\nError: ${error}`);
              }}>Debug Info</Button>
              <label htmlFor="pdf-upload" style={{ marginTop: '10px', width: '100%', textAlign: 'center' }}>
                <div>Try uploading the PDF directly:</div>
                <input
                  id="pdf-upload"
                  type="file"
                  accept=".pdf"
                  style={{ display: 'none' }}
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) {
                      const url = URL.createObjectURL(file);
                      setPdfUrl(url);
                      setError(null);
                      setLoading(false);
                      console.log('Loaded PDF from file upload:', url);
                    }
                  }}
                />
                <Button intent="success" onClick={() => document.getElementById('pdf-upload').click()}>Upload PDF</Button>
              </label>
            </div>
          </div>
        ) : loading ? (
          <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column' }}>
            <Spinner size={50} />
            <div style={{ marginTop: '20px' }}>Loading PDF...</div>
            <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>{filePath}</div>
            <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>File ID: {fileId}</div>
            <div style={{ marginTop: '20px', display: 'flex', gap: '10px', justifyContent: 'center', flexDirection: 'column', alignItems: 'center' }}>
              <Button onClick={() => {
                console.log('Debug info:', { fileId, filePath, pdfUrl });
                alert(`File ID: ${fileId}\nFile Path: ${filePath}\nPDF URL: ${pdfUrl || 'none'}`);
              }}>Show Debug Info</Button>

              <div style={{ marginTop: '20px' }}>
                <div>Try uploading the PDF directly:</div>
                <input
                  id="pdf-upload-loading"
                  type="file"
                  accept=".pdf"
                  style={{ display: 'none' }}
                  onChange={(e) => {
                    const file = e.target.files[0];
                    if (file) {
                      const url = URL.createObjectURL(file);
                      setPdfUrl(url);
                      setLoading(false);
                      console.log('Loaded PDF from file upload:', url);
                    }
                  }}
                />
                <Button intent="success" onClick={() => document.getElementById('pdf-upload-loading').click()}>Upload PDF</Button>
              </div>
            </div>
          </div>
        ) : (
          <Document
            file={pdfUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={<div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column' }}>
              <Spinner size={50} />
              <div style={{ marginTop: '20px' }}>Loading PDF...</div>
              <div style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>File ID: {fileId}</div>
            </div>}
          >
            {renderPages()}
          </Document>
        )}
      </div>
    </div>
  );
};

export default SimplePDFViewer;
