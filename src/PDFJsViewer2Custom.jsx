import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { Select, Button as AntButton, Card, Input, Popover, message } from 'antd';
import { FaRedoAlt, FaUndoAlt, FaArrowLeft, FaArrowRight, FaSearchPlus, FaSearchMinus, FaArrowsAltH, FaSearch } from 'react-icons/fa';
import { Spinner } from "@blueprintjs/core";
import "@blueprintjs/core/lib/css/blueprint.css";
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { setPdfFile, setNumPages, setPageNumber, setScale, setRotation, setSearchText, setSearchResults, setCurrentSearchIndex } from './redux/pdfViewerSlice';

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const { Option } = Select;

const PDFJsViewer = ({ fileId, isDarkTheme, ownerDocument }) => {
  const dispatch = useDispatch();
  const pdfFile = useSelector((state) => state.pdfViewer.pdfFile);
  const numPages = useSelector((state) => state.pdfViewer.numPages);
  const pageNumber = useSelector((state) => state.pdfViewer.pageNumber);
  const scale = useSelector((state) => state.pdfViewer.scale);
  const rotation = useSelector((state) => state.pdfViewer.rotation);
  const searchText = useSelector((state) => state.pdfViewer.searchText);
  const searchResults = useSelector((state) => state.pdfViewer.searchResults);
  const currentSearchIndex = useSelector((state) => state.pdfViewer.currentSearchIndex);

  const pdfContainerRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [isSearchPopoverVisible, setIsSearchPopoverVisible] = useState(false);
  const [debouncedSearchText, setDebouncedSearchText] = useState('');
  const [error, setError] = useState(null);
  const [visiblePages, setVisiblePages] = useState([]);

  const MIN_SCALE = 0.1;
  const MAX_SCALE = 10;
  const ZOOM_STEP = 0.1;

  // ... (keep the existing useEffect hooks for file fetching and search debouncing)

  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    console.log('PDF loaded successfully. Number of pages:', numPages);
    dispatch(setNumPages(numPages));
    dispatch(setPageNumber(1));
    setVisiblePages([1, 2, 3]); // Initially render first 3 pages
  }, [dispatch]);

  const changePage = useCallback((offset) => {
    dispatch(setPageNumber((prevPageNumber) => {
      const newPageNumber = Math.min(Math.max(prevPageNumber + offset, 1), numPages);
      updateVisiblePages(newPageNumber);
      return newPageNumber;
    }));
  }, [dispatch, numPages]);

  const changePageTo = useCallback((page) => {
    dispatch(setPageNumber(page));
    updateVisiblePages(page);
  }, [dispatch]);

  const updateVisiblePages = useCallback((centerPage) => {
    const visibleRange = 2; // Number of pages to render on each side of the current page
    const start = Math.max(1, centerPage - visibleRange);
    const end = Math.min(numPages, centerPage + visibleRange);
    setVisiblePages(Array.from({length: end - start + 1}, (_, i) => start + i));
  }, [numPages]);

  const zoomIn = useCallback(() => {
    dispatch(setScale((prevScale) => {
      const newScale = Math.min(prevScale + ZOOM_STEP, MAX_SCALE);
      console.log('Zooming in. New scale:', newScale);
      return Number(newScale.toFixed(2));
    }));
  }, [dispatch]);

  const zoomOut = useCallback(() => {
    dispatch(setScale((prevScale) => {
      const newScale = Math.max(prevScale - ZOOM_STEP, MIN_SCALE);
      console.log('Zooming out. New scale:', newScale);
      return Number(newScale.toFixed(2));
    }));
  }, [dispatch]);

  const handleZoomChange = useCallback((newScale) => {
    console.log('Zoom changed. New scale:', newScale);
    dispatch(setScale(Number(newScale)));
  }, [dispatch]);

  // ... (keep the existing rotation, fit to width, and search functions)

  const handleScroll = useCallback(() => {
    if (!pdfContainerRef.current) return;
    
    const container = pdfContainerRef.current;
    const containerHeight = container.clientHeight;
    const scrollPosition = container.scrollTop;

    // Estimate which page is currently in view
    const estimatedPage = Math.floor(scrollPosition / (containerHeight * scale)) + 1;
    updateVisiblePages(estimatedPage);
  }, [scale, updateVisiblePages]);

  useEffect(() => {
    const container = pdfContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);

  // ... (keep the existing memoized options, highlight pattern, and text renderer)

  const zoomOptions = useMemo(() => [
    { value: 0.5, label: '50%' },
    { value: 1, label: '100%' },
    { value: 2, label: '200%' },
    { value: 4, label: '400%' },
    { value: 6, label: '600%' },
    { value: 8, label: '800%' },
    { value: 10, label: '1000%' },
  ], []);

  // ... (keep the existing search content JSX)

  return (
    <div className={isDarkTheme ? "bp5-dark" : ""}>
      <Card style={{ marginBottom: 16, height: '70px', display: 'flex', alignItems: 'center' }}>
        <Select
          value={scale}
          onChange={handleZoomChange}
          style={{ width: 120 }}
        >
          {zoomOptions.map(option => (
            <Option key={option.value} value={option.value}>{option.label}</Option>
          ))}
        </Select>
        {/* ... (keep the existing navigation buttons) */}
        <AntButton onClick={zoomIn} disabled={scale >= MAX_SCALE}>
          <FaSearchPlus />
        </AntButton>
        <AntButton onClick={zoomOut} disabled={scale <= MIN_SCALE}>
          <FaSearchMinus />
        </AntButton>
        {/* ... (keep the existing rotation and search buttons) */}
      </Card>

      <div
        ref={pdfContainerRef}
        style={{ overflowY: 'scroll', height: '90vh', marginTop: '10px', display: 'flex', justifyContent: 'center' }}
      >
        <div>
          {loading ? (
            <div className="spinner-container">
              <Spinner className="spinner-center" intent="primary" size={Spinner.SIZE_STANDARD} />
            </div>
          ) : error ? (
            <div>Error: {error}</div>
          ) : (
            pdfFile ? (
              <Document
                file={pdfFile}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                loading={<Spinner />}
                options={memoizedOptions}
              >
                {visiblePages.map((pageNum) => (
                  <div key={`page_${pageNum}`} style={{ position: 'relative', borderBottom: '10px solid #ccc', textAlign: 'center' }}>
                    <Page
                      pageNumber={pageNum}
                      scale={scale}
                      rotate={rotation}
                      width={pdfContainerRef.current ? pdfContainerRef.current.clientWidth : undefined}
                      customTextRenderer={textRenderer}
                    />
                  </div>
                ))}
              </Document>
            ) : (
              <div>No PDF file loaded</div>
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default PDFJsViewer;