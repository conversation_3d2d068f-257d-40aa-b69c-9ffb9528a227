import React, { useState, useEffect, useRef } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Card, Button as BPB<PERSON>on, Navbar, Alignment } from "@blueprintjs/core";
import { Select as AntSelect, Input as AntInput } from 'antd';
import "antd/dist/reset.css";

import "@blueprintjs/core/lib/css/blueprint.css";
import 'react-pdf/dist/Page/TextLayer.css';
import 'react-pdf/dist/Page/AnnotationLayer.css';

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const zoomLevels = [25, 50, 100, 125, 150, 175, 200, 225, 300];

function PDFReader({fileId}) {

  const [numPages, setNumPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [pdfFile, setPdfFile] = useState(null);
  const [scale, setScale] = useState(1.0);
  const [inputValue, setInputValue] = useState(currentPage.toString());
  const containerRef = useRef(null);

  useEffect(() => {
    const fetchFile = async () => {
      try {
        const response = await fetch(`${process.env.REACT_APP_FILE_API}/get-file/${fileId}`);
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        const blob = await response.blob();
        const pdfUrl = URL.createObjectURL(blob);
        setPdfFile(pdfUrl);
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
      }
    };

    if (fileId) {
      fetchFile();
    }
  }, [fileId]);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setCurrentPage(1);
  };

  const handleZoomChange = (value) => {
    setScale(value / 100);
  };

  const handleScroll = () => {
    if (containerRef.current) {
      const pages = containerRef.current.querySelectorAll('.react-pdf__Page');
      const scrollTop = containerRef.current.scrollTop;
      let newPage = 1;

      for (let page of pages) {
        if (page.offsetTop + page.clientHeight / 2 > scrollTop) {
          break;
        }
        newPage++;
      }

      setCurrentPage(newPage);
      setInputValue(newPage.toString()); // Keep input value in sync with current page
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('scroll', handleScroll);
      }
    };
  }, [numPages]);

  const scrollToPage = (pageNumber) => {
    const pages = containerRef.current.querySelectorAll('.react-pdf__Page');
    const targetPage = pages[pageNumber - 1];
    if (targetPage) {
      containerRef.current.scrollTop = targetPage.offsetTop;
    }
  };

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  const handleInputKeyPress = (e) => {
    if (e.key === 'Enter') {
      const newPage = parseInt(inputValue, 10);
      if (!isNaN(newPage) && newPage >= 1 && newPage <= numPages) {
        setCurrentPage(newPage);
        scrollToPage(newPage);
      } else {
        // Reset input value to the current page
        setInputValue(currentPage.toString());
      }
    }
  };

  const handleInputBlur = () => {
    const newPage = parseInt(inputValue, 10);
    if (!isNaN(newPage) && newPage >= 1 && newPage <= numPages) {
      setCurrentPage(newPage);
      scrollToPage(newPage);
    } else {
      // Reset input value to the current page
      setInputValue(currentPage.toString());
    }
  };

  const goToPrevPage = () => {
    setCurrentPage(currentPage => {
      const newPage = Math.max(currentPage - 1, 1);
      scrollToPage(newPage);
      return newPage;
    });
  };

  const goToNextPage = () => {
    setCurrentPage(currentPage => {
      const newPage = Math.min(currentPage + 1, numPages);
      scrollToPage(newPage);
      return newPage;
    });
  };

  return (
    <div>
      <Card style={{ margin: '0px', width: '100%' }}>
        <Navbar>
          <Navbar.Group align={Alignment.LEFT}>
            <AntSelect defaultValue={100} style={{ width: 120 }} onChange={handleZoomChange}>
              {zoomLevels.map(level => (
                <AntSelect.Option key={level} value={level}>{`${level}%`}</AntSelect.Option>
              ))}
            </AntSelect>
            <BPButton icon="zoom-in" onClick={() => handleZoomChange(scale * 100 + 25)} minimal />
            <BPButton icon="zoom-out" onClick={() => handleZoomChange(scale * 100 - 25)} minimal />
            <BPButton icon="arrow-up" onClick={goToPrevPage} minimal disabled={currentPage === 1} />
            <BPButton icon="arrow-down" onClick={goToNextPage} minimal disabled={currentPage === numPages} />
            <div style={{ display: 'flex', alignItems: 'center' }}>
              Page&nbsp;
              <AntInput
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleInputKeyPress}
                onBlur={handleInputBlur}
                style={{ width: 50, marginRight: 5 }}
              />
              &nbsp;of {numPages}
            </div>
          </Navbar.Group>
        </Navbar>
        <div
          ref={containerRef}
          style={{
            maxHeight: '85vh',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
          }}
        >
          {pdfFile && (
            <Document file={pdfFile} onLoadSuccess={onDocumentLoadSuccess}>
              {Array.from({ length: numPages }, (_, index) => (
                <div key={`page_${index + 1}`} style={{ display: 'flex', justifyContent: 'center' }}>
                  {index % 2 === 0 ? (
                    <Page key={`page_${index + 1}`} pageNumber={index + 1} scale={scale} />
                  ) : (
                    <div
                      style={{
                        width: '400px', // Set your landscape page width here
                        height: '800px', // Set your landscape page height here
                      }}
                    >
                      <Page key={`page_${index + 1}`} pageNumber={index + 1} scale={scale} />
                    </div>
                  )}
                </div>
              ))}
            </Document>
          )}
        </div>
      </Card>
    </div>
  );
}

export default PDFReader;
