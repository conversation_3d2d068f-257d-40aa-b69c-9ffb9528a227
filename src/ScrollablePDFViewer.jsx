import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Spinner } from '@blueprintjs/core';
import { useDispatch } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import { Virtuoso } from 'react-virtuoso';

// Import required CSS
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

/**
 * ScrollablePDFViewer - Continuous scroll PDF viewer
 * Shows all pages in a scrollable container
 */
const ScrollablePDFViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  // Core state
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [loading, setLoading] = useState(true);
  const [scale, setScale] = useState(1.2);
  const [error, setError] = useState(null);
  
  // Redux and visibility tracking
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  // Load PDF file using your exact method
  useEffect(() => {
    async function fetchFile() {
      if (hasLoaded) return;
      
      setPdfFile(null);
      setDownloadProgress(0);
      setLoading(true);

      try {
        console.log('Fetching PDF for:', { fileId, filePath });
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });
        
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          if (contentLength) {
            setDownloadProgress(receivedLength / parseInt(contentLength, 10));
          }
        }

        const blob = new Blob(chunks);
        const pdfUrl = URL.createObjectURL(blob);
        setPdfFile(pdfUrl);
        setHasLoaded(true);
        setLoading(false);

        // If you need to dispatch state updates
        if (dispatch && typeof dispatch === 'function') {
          try {
            dispatch({
              type: 'UPDATE_PDF_VIEWER',
              payload: {
                fileId,
                filePath,
                page: 1,
                isVisible: inView
              }
            });
          } catch (e) {
            console.warn('Could not dispatch update:', e);
          }
        }

        console.log('PDF Loaded:', {
          fileId,
          filePath,
          initialPage: 1,
          isVisible: inView
        });
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
        setError(error.message || "Failed to load PDF");
        setLoading(false);
      }
    }

    // Only fetch the file if we're not on the PDF viewer URL directly
    const currentPath = window.location.pathname;
    if (!currentPath.includes('/pdfjs-3.11.174-dist/web/')) {
      fetchFile();
    }

    return () => {
      if (pdfFile && typeof pdfFile === 'string' && pdfFile.startsWith('blob:')) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, filePath, dispatch, hasLoaded, inView, pdfFile]);

  // Document load success handler
  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    console.log(`PDF loaded with ${numPages} pages`);
  };
  
  // Custom loading indicator with progress
  const LoadingIndicator = () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center',
      height: '100%',
      padding: '20px'
    }}>
      <Spinner size={50} />
      {downloadProgress > 0 && downloadProgress < 1 && (
        <div style={{ width: '80%', marginTop: '20px' }}>
          <div 
            style={{ 
              height: '4px', 
              width: `${downloadProgress * 100}%`, 
              backgroundColor: '#2B95D6', 
              borderRadius: '2px' 
            }} 
          />
          <div style={{ marginTop: '8px', textAlign: 'center' }}>
            {Math.round(downloadProgress * 100)}%
          </div>
        </div>
      )}
      <div style={{ marginTop: '20px' }}>Loading PDF...</div>
    </div>
  );

  // Page renderer for the virtualized list
  const renderPage = (index) => {
    const pageNumber = index + 1;
    return (
      <div 
        key={`page-${pageNumber}`}
        style={{
          margin: '20px 0',
          padding: '10px',
          backgroundColor: 'white',
          boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
          display: 'flex',
          justifyContent: 'center'
        }}
      >
        <Page
          pageNumber={pageNumber}
          scale={scale}
          renderTextLayer={true}
          renderAnnotationLayer={true}
          loading={<Spinner size={20} />}
        />
      </div>
    );
  };

  return (
    <div 
      ref={ref}
      style={{ 
        height: '100%', 
        width: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        background: '#f5f5f5',
        ...style 
      }}
    >
      {loading ? (
        <LoadingIndicator />
      ) : error ? (
        <div style={{ 
          padding: '20px', 
          textAlign: 'center', 
          color: 'red' 
        }}>
          <h3>Error Loading PDF</h3>
          <p>{error}</p>
        </div>
      ) : (
        <Document
          file={pdfFile}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={(err) => {
            console.error('Error rendering PDF:', err);
            setError(`Error rendering PDF: ${err.message}`);
          }}
          options={{
            cMapUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/cmaps/',
            cMapPacked: true,
            standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/standard_fonts/'
          }}
        >
          {numPages && (
            <Virtuoso
              style={{ height: '100%', width: '100%' }}
              totalCount={numPages}
              itemContent={index => renderPage(index)}
              overscan={2}
            />
          )}
        </Document>
      )}
      
      {/* Note for Lector integration */}
      <div style={{ 
        padding: '10px', 
        fontSize: '12px', 
        textAlign: 'center', 
        backgroundColor: '#f0f0f0',
        borderTop: '1px solid #ddd'
      }}>
        Note: To use Lector, install: npm install @anaralabs/lector pdfjs-dist
      </div>
    </div>
  );
};

export default ScrollablePDFViewer;
