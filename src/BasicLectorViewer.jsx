import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>s, Root, TextLayer } from "@anaralabs/lector";
import { Spinner } from "@blueprintjs/core";

/**
 * BasicLectorViewer - A simple implementation of PDF viewer using Lector
 *
 * This follows the basic Lector implementation pattern while supporting
 * file loading from your API and dark mode.
 */
const BasicLectorViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  const [pdfSource, setPdfSource] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Load the PDF file from API
  useEffect(() => {
    const fetchFile = async () => {
      try {
        setLoading(true);
        setError(null);

        // If filePath is provided directly, use it
        if (filePath && (filePath.startsWith('http') || filePath.startsWith('blob:'))) {
          console.log(`Using direct filePath: ${filePath}`);
          setPdfSource(filePath);
          setLoading(false);
          return;
        }

        // Otherwise fetch from API using fileId
        if (!fileId) {
          throw new Error("No file ID or valid path provided");
        }

        console.log(`Fetching PDF for fileId: ${fileId}`);
        const sessionId = localStorage.getItem('session_id');

        // Use the fetch API with a reader to track progress
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': sessionId,
          },
        });

        if (!response.ok) {
          throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
        }

        console.log(`Fetch response status: ${response.status}`);

        // Get the blob directly
        const blob = await response.blob();
        console.log(`Fetched PDF data size: ${blob.size} bytes, type: ${blob.type}`);

        // Create a blob URL
        const url = URL.createObjectURL(blob);
        console.log(`Created blob URL: ${url}`);

        // Set the source to the blob URL
        setPdfSource(url);
        setLoading(false);
      } catch (err) {
        console.error("Error loading PDF:", err);
        setError(err.message || "Failed to load PDF");
        setLoading(false);
      }
    };

    fetchFile();

    // Cleanup function
    return () => {
      if (pdfSource && typeof pdfSource === 'string' && pdfSource.startsWith('blob:')) {
        console.log(`Revoking blob URL: ${pdfSource}`);
        URL.revokeObjectURL(pdfSource);
      }
    };
  }, [fileId, filePath]);

  // Loading indicator
  const LoadingIndicator = () => (
    <div className="p-4 flex items-center justify-center flex-col">
      <Spinner size={40} />
      <div className="mt-3">Loading PDF...</div>
    </div>
  );

  // Error display
  if (error) {
    return (
      <div className="p-4 text-center text-red-500">
        <h3 className="font-semibold">Error Loading PDF</h3>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%', ...style }}>
      <Root
        source={pdfSource}
        className={`w-full h-full border overflow-auto rounded-lg ${
          isDarkTheme ? 'bg-gray-800' : 'bg-white'
        }`}
        loader={<LoadingIndicator />}
        onLoad={() => console.log("Lector successfully loaded PDF")}
        onError={(err) => {
          console.error("Lector error:", err);
          setError(`Failed to render PDF: ${err.message}`);
        }}
      >
        <Pages
          className={isDarkTheme ?
            'invert-[94%] hue-rotate-180 brightness-[80%] contrast-[228%]' :
            ''}
        >
          <Page>
            <CanvasLayer />
            <TextLayer />
          </Page>
        </Pages>
      </Root>
    </div>
  );
};

export default BasicLectorViewer;
