/* Styles for the FileViewer and its inner components */

.file-viewer {
    display: flex;
    flex-direction: column;
    height: 100vh;  /* adjust as needed, this assumes you want the viewer to take the full viewport height */
}

.navbar {
    /* any specific styles for the navbar if required */
}

.viewer-content {
    display: flex;
    flex-direction: row;
    flex: 1;  /* take the remaining height after navbar */
}

.viewer-sidebar {
    width: 10%;  /* adjust as required */
    overflow-y: auto;

    height: 100%;
}

.viewer-main {
    width: 90%;  /* adjust as required */
    overflow-y: auto;
    padding: 15px; /* optional padding */
    height: 100%;
}

 /* any specific styles for the sidebar if required */

.file-viewer .viewer-content {
    display: flex;
}

.viewer-left-sidebar,
.viewer-sidebar,
.viewer-main,
.viewer-right-sidebar {
    flex: 1;
}

.viewer-left-sidebar,
.viewer-right-sidebar {
    max-width: 50px;
}

.viewer-main {
    flex: 2;
}
.nav-sidebar {
    width: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 5px;
}
.nav-sidebar.bp5-navbar .nav-sidebar-button {
    /* These are just placeholders, you might need to add/adjust styles based on actual behavior */
    display: block;
    width: 100%;
    text-align: center;
    margin-bottom: 5px;  /* Add some spacing between buttons */
}
.nav-sidebar.bp5-navbar {
    /* Add any styles that are needed to make the sidebar look as you want */
    border: 1px solid #e0e0e0;  /* This is an example border */
    padding: 5px;
}

.nav-sidebar-buttons > .bp5-button {
    /* Override styles for buttons, if necessary */
    display: block;
    width: 100%;
    margin-bottom: 5px;
}