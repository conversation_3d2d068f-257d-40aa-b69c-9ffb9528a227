import React from 'react';
import { Document, Page } from "react-pdf";

function Sidebar({ actualFile, numPages }) {  // Make sure to destructure numPages from props
    return (
        <div className="sidebar">
            <Document file={actualFile}>
                {Array.from(new Array(numPages), (_, index) => (
                    <Page key={`thumb_${index + 1}`} pageNumber={index + 1} scale={0.15} />
                ))}
            </Document>
        </div>
    );
}

export default Sidebar;
