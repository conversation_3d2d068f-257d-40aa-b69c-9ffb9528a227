import React, { useState, useEffect } from 'react';
import { Document, Page } from "react-pdf";

function PDFFileDisplay({ fileId, setActualFile }) {
    const [pdfFile, setPdfFile] = useState(null);
    const [numPages, setNumPages] = useState(null);

    useEffect(() => {
        async function fetchFile() {
            try {
                const response = await fetch(`http://localhost:8020/get-file/${fileId}`);
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }

                const blob = await response.blob();
                const pdfUrl = URL.createObjectURL(blob);
                setPdfFile(pdfUrl);
                setActualFile(pdfUrl);  // Set the actual file in the parent component
            } catch (error) {
                console.error("Failed to fetch the PDF:", error);
            }
        }
        fetchFile();
    }, [fileId, setActualFile]);

    return (
        <div className="pdf-display">
            <Document file={pdfFile} onLoadSuccess={(pdfData) => setNumPages(pdfData.numPages)}>
                {Array.from(new Array(numPages), (_, index) => (
                    <Page key={`page_${index + 1}`} pageNumber={index + 1} />
                ))}
            </Document>
        </div>
    );
}

export default PDFFileDisplay;
