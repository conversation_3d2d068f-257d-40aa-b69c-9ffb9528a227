import React from 'react';
import { Button } from '@blueprintjs/core';

function NavSidebar({ buttons }) {
    return (
        <div className="nav-sidebar bp5-navbar">
            <div className="nav-sidebar-buttons">
                {buttons.map((buttonProps, index) => (
                    <Button key={index} {...buttonProps} />
                ))}
            </div>
        </div>
    );
}

export default NavSidebar;
