import React, { useState } from 'react';
import Navbar from './NavBar';
import Sidebar from './Sidebar';
import PDFFileDisplay from './PDFFileDisplay';
import NavSidebar from './NavSidebar';
import './FileViewer.css';

function FileViewer({ fileId }) {
    const [actualFile, setActualFile] = useState(null);
    const [numPages, setNumPages] = useState(null);

    

    return (
        <div className="file-viewer">
            <Navbar />
            <div className="viewer-content">
                <div className="viewer-sidebar">
                    <Sidebar actualFile={actualFile} numPages={numPages} />
                </div>
                <div className="viewer-main">
                    <PDFFileDisplay fileId={fileId} setActualFile={setActualFile} setNumPages={setNumPages} />
                </div>
              
            </div>
        </div>
    );
}

export default FileViewer;
