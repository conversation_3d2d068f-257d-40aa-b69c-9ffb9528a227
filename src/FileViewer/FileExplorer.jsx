import React, { useState, useEffect, useMemo, useRef } from "react";
import { useSelector } from "react-redux";
import { Card, Spinner, Tag, NonIdealState, Button } from "@blueprintjs/core";
import { ChevronRight, ChevronDown } from "lucide-react";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaF<PERSON>er<PERSON><PERSON>, FaGoogleDrive } from "react-icons/fa";
import { Copy, FileText, FolderX, RotateCcw } from "lucide-react";
import { useVirtualizer } from '@tanstack/react-virtual';
import { Dropdown, Input, Select } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import axios from "axios";

// Import existing file type icons
import IconPDF from "../FilesTable/IconPDF";
import IconDWG from "../FilesTable/IconDWG";
import IconEXCEL from "../FilesTable/IconEXCEL";
import IconWORD from "../FilesTable/IconWORD";
import IconIMG from "../FilesTable/IconIMG";
import IconDefault from "../FilesTable/IconDefault";

import "../Styles/VSCodeTreeExplorer.css";

function VSCodeTreeExplorer({ onFileClick, isDarkTheme, openFiles = [], onResetViewedFiles }) {
  const [searchQuery, setSearchQuery] = useState("");
  const [expandedFolders, setExpandedFolders] = useState(new Set());
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalFiles, setTotalFiles] = useState(0);
  const [fileStatusFilter, setFileStatusFilter] = useState([]);
  const [showAttachedOnly, setShowAttachedOnly] = useState(false);
  const [isToggling, setIsToggling] = useState(false);

  const apiCallInitiated = useRef(false);

  // Get Redux state for file status indicators
  const currentAssetFiles = useSelector((state) => state.filesHandler.currentAssetFiles);
  const currentInspectionFilesPath = useSelector((state) => state.filesHandler.currentInspectionFilesPath);
  const currentGeneralFile = useSelector((state) => state.filesHandler.currentGeneralFile);
  const activeTab = useSelector((state) => state.filesHandler.activeTab);
  const selectedRowData = useSelector((state) => state.selectedRows["inspections-grid"]);

  // Get asset selection for auto-filtering (same as FilesTable)
  const selectedAsset = useSelector((state) => state.appConfiguration.selectedAsset);
  const selectedAssetSearchKey = useSelector((state) => state.appConfiguration.selectedAssetSearchKey);

  // API retry logic (same as FilesTable)
  const makeApiCallWithRetry = async (url, options, maxTotalTime = 180000) => {
    let totalWaitTime = 0;
    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
    const fixedIncrement = 5000;

    while (true) {
      try {
        const response = await axios(url, options);
        return response;
      } catch (error) {
        if (totalWaitTime >= maxTotalTime) {
          throw error;
        }
        console.log(`Request failed, retrying in ${fixedIncrement}ms...`);
        await delay(fixedIncrement);
        totalWaitTime += fixedIncrement;
      }
    }
  };

  // Load discarded files status
  const loadDiscardedFiles = async () => {
    try {
      const response = await axios.get(`${process.env.REACT_APP_DATA_API}/files/discarded`, {
        headers: {
          "Content-Type": "application/json",
          "session-id": localStorage.getItem("session_id"),
        }
      });

      // Update data with discarded status
      setData(prevData => {
        return prevData.map(file => {
          const isDiscarded = response.data.some(discardedFile => discardedFile.path === file.path);
          return {
            ...file,
            Mapped: isDiscarded ? 'DISCARDED' : file.Mapped
          };
        });
      });

    } catch (error) {
      console.error('Error loading discarded files:', error);
    }
  };

  // Load data from API (same as FilesTable)
  const loadInitialData = async () => {
    if (!apiCallInitiated.current) {
      apiCallInitiated.current = true;
      setLoading(true);

      try {
        const response = await makeApiCallWithRetry(
          `${process.env.REACT_APP_DATA_API}/list-files-static/`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "session-id": localStorage.getItem("session_id"),
            },
          }
        );

        console.log("VSCode Tree - API response:", response.data.files?.length || 0, "files");
        console.log("Sample file data:", response.data.files?.[0]);
        setData(response.data.files || []);

        // Load discarded status after files are loaded
        setTimeout(() => {
          loadDiscardedFiles();
          setLoading(false);
        }, 100);

      } catch (error) {
        console.error("Failed to fetch files:", error);
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    loadInitialData();
  }, []);

  // Get file type from mimeType and filename
  const getFileType = (mimeType, fileName = "") => {
    // First try mimeType
    switch (mimeType) {
      case "application/pdf":
        return "PDF";
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      case "application/vnd.ms-excel":
        return "Excel";
      case "application/msword":
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        return "Word";
      case "image/vnd.dwg":
        return "DWG";
      case "image/jpeg":
      case "image/png":
      case "image/tif":
      case "image/tiff":
        return "Image";
    }

    // Fallback to file extension
    const extension = fileName.toLowerCase().split('.').pop();
    switch (extension) {
      case "pdf":
        return "PDF";
      case "dwg":
        return "DWG";
      case "xlsx":
      case "xls":
        return "Excel";
      case "docx":
      case "doc":
        return "Word";
      case "jpg":
      case "jpeg":
      case "png":
      case "tif":
      case "tiff":
      case "gif":
      case "bmp":
        return "Image";
      default:
        return "File";
    }
  };

  // File type icon component
  const FileIcon = ({ fileType, fileName, size = 17 }) => {
    const getFileTypeFromName = (name) => {
      if (!name) return 'default';
      const extension = name.split('.').pop()?.toLowerCase();

      switch (extension) {
        case 'pdf':
          return 'PDF';
        case 'dwg':
        case 'dxf':
          return 'DWG';
        case 'xlsx':
        case 'xls':
          return 'Excel';
        case 'docx':
        case 'doc':
          return 'Word';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
        case 'svg':
          return 'Image';
        default:
          return 'default';
      }
    };

    const type = fileType || getFileTypeFromName(fileName);

    switch (type) {
      case "PDF":
        return <IconPDF size={size} />;
      case "DWG":
        return <IconDWG size={size} />;
      case "Excel":
        return <IconEXCEL size={size} />;
      case "Word":
        return <IconWORD size={size} />;
      case "Image":
        return <IconIMG size={size} />;
      default:
        return <IconDefault size={size} />;
    }
  };

  // Transform flat data into tree structure
  const treeData = useMemo(() => {
    try {
      const filteredData = data.filter(
        (item) =>
          item.name &&
          !item.name.endsWith("_Converted.pdf") &&
          getFileType(item.mimeType) !== "Folder"
      );

      if (filteredData.length === 0) return {};

      const tree = {};

      filteredData.forEach((file) => {
        if (!file.path || !file.name) return;

        // Remove the filename from the path to get just the folder path
        // file.path = '/P&IDs/TANK FARM 400/PID-400-006-00.pdf'
        // file.name = 'PID-400-006-00.pdf'
        // folderPath should be '/P&IDs/TANK FARM 400'
        const folderPath = file.path.substring(0, file.path.lastIndexOf('/'));

        // Split the folder path into segments (these are the actual folder names)
        const pathSegments = folderPath.split('/').filter(segment => segment.length > 0);

        // Handle files that might be at the root level (no folder path)
        if (pathSegments.length === 0) {
          console.warn("File has no folder path, skipping:", file.name, file.path);
          return;
        }

        // Build the folder structure
        let currentLevel = tree;

        // Create folders for each path segment
        pathSegments.forEach((segment, index) => {
          if (!currentLevel[segment]) {
            currentLevel[segment] = {
              type: 'folder',
              name: segment,
              children: {},
              files: []
            };
          }

          // If this is the last segment, we're at the target folder - add the file here
          if (index === pathSegments.length - 1) {
            // Add the file to this folder's files array
            currentLevel[segment].files.push({
              ...file,
              type: 'file',
              fileType: getFileType(file.mimeType, file.name)
            });
          } else {
            // Move to the next level (go into this folder's children)
            currentLevel = currentLevel[segment].children;
          }
        });
      });

      console.log("Tree structure built:", tree);
      console.log("Sample tree node:", Object.values(tree)[0]);
      return tree;
    } catch (error) {
      console.error("Error building tree structure:", error);
      return {};
    }
  }, [data]);

  // Auto-filter with selected asset (same as FilesTable)
  useEffect(() => {
    if (selectedAsset) {
      const searchKey = selectedAssetSearchKey === "" ? selectedAsset : selectedAssetSearchKey;
      setSearchQuery(String(searchKey));
    }
  }, [selectedAsset, selectedAssetSearchKey]);

  // Apply file status filter to tree
  const applyFileStatusFilter = (tree, statusFilters) => {
    if (!statusFilters || statusFilters.length === 0) return tree;

    const filterNode = (node) => {
      const filteredNode = { ...node, children: {}, files: [] };

      // Filter files based on status
      if (node.files) {
        filteredNode.files = node.files.filter(file => {
          const fileStatus = getFileStatus(file.Mapped);
          return statusFilters.includes(fileStatus);
        });
      }

      // Recursively filter children
      Object.entries(node.children || {}).forEach(([key, child]) => {
        const filteredChild = filterNode(child);
        // Only include child if it has files or children
        if (filteredChild.files.length > 0 || Object.keys(filteredChild.children).length > 0) {
          filteredNode.children[key] = filteredChild;
        }
      });

      return filteredNode;
    };

    const filtered = {};
    Object.entries(tree).forEach(([key, node]) => {
      const filteredNode = filterNode(node);
      // Only include top-level node if it has content
      if (filteredNode.files.length > 0 || Object.keys(filteredNode.children).length > 0) {
        filtered[key] = filteredNode;
      }
    });

    return filtered;
  };

  // Get file status for filtering (excluding attached status - handled separately)
  const getFileStatus = (mapped) => {
    if (mapped === "DISCARDED" || mapped === "TO_DELETE") return "discarded";
    if (mapped === "TRUE") return "mapped";
    return "unmapped";
  };

  // Smart search: if search matches a folder, show entire path and children
  const getFilteredTree = (tree, query) => {
    if (!query.trim()) return tree;

    const filtered = {};
    const queryLower = query.toLowerCase();

    const searchNode = (node, path = []) => {
      const result = { ...node, children: {}, files: [] };
      let hasMatch = false;

      // Check if current folder name matches
      const folderMatches = node.name && node.name.toLowerCase().includes(queryLower);

      // If folder matches, include everything inside it
      if (folderMatches) {
        return { ...node, expanded: true };
      }

      // Search in children folders
      Object.entries(node.children || {}).forEach(([key, child]) => {
        const childResult = searchNode(child, [...path, key]);
        if (childResult) {
          result.children[key] = childResult;
          hasMatch = true;
        }
      });

      // Search in files array
      if (node.files) {
        node.files.forEach(file => {
          if (file.name.toLowerCase().includes(queryLower) ||
              file.path.toLowerCase().includes(queryLower)) {
            result.files.push(file);
            hasMatch = true;
          }
        });
      }

      return hasMatch ? result : null;
    };

    Object.entries(tree).forEach(([key, node]) => {
      const result = searchNode(node, [key]);
      if (result) {
        filtered[key] = result;
      }
    });

    return filtered;
  };

  // Apply attached files filter (separate from status filter)
  const applyAttachedFilter = (tree) => {
    if (!showAttachedOnly) return tree;

    const filterNode = (node) => {
      const filteredNode = { ...node, children: {}, files: [] };

      // Filter files to show only attached ones - ensure proper path comparison
      if (node.files) {
        filteredNode.files = node.files.filter(file => {
          if (!file.path || !currentAssetFiles) return false;

          // Normalize paths for comparison
          const filePath = file.path.trim();
          return currentAssetFiles.some(assetPath => {
            const normalizedAssetPath = assetPath.trim();
            return filePath === normalizedAssetPath;
          });
        });
      }

      // Recursively filter children
      Object.entries(node.children || {}).forEach(([key, child]) => {
        const filteredChild = filterNode(child);
        // Only include child if it has attached files or children with attached files
        if (filteredChild.files.length > 0 || Object.keys(filteredChild.children).length > 0) {
          filteredNode.children[key] = filteredChild;
        }
      });

      return filteredNode;
    };

    const filtered = {};
    Object.entries(tree).forEach(([key, node]) => {
      const filteredNode = filterNode(node);
      // Only include top-level node if it has content
      if (filteredNode.files.length > 0 || Object.keys(filteredNode.children).length > 0) {
        filtered[key] = filteredNode;
      }
    });

    return filtered;
  };

  // Apply filters in sequence: search -> attached -> status
  const searchFilteredTree = getFilteredTree(treeData, searchQuery);
  const attachedFilteredTree = applyAttachedFilter(searchFilteredTree);
  const filteredTree = applyFileStatusFilter(attachedFilteredTree, fileStatusFilter);

  // Count total files in filtered tree
  const countFilesInTree = (tree) => {
    let count = 0;
    const countNode = (node) => {
      if (node.files) {
        count += node.files.length;
      }
      Object.values(node.children || {}).forEach(countNode);
    };
    Object.values(tree).forEach(countNode);
    return count;
  };

  // Update total files count when filtered tree changes
  useEffect(() => {
    setTotalFiles(countFilesInTree(filteredTree));
  }, [filteredTree]);

  // Auto-expand folders when showing attached files only
  useEffect(() => {
    if (showAttachedOnly) {
      // Simple approach: expand all folders when showing attached files
      const allPaths = getAllFolderPaths(filteredTree);
      setExpandedFolders(new Set(allPaths));
    }
  }, [showAttachedOnly]);

  // Clear collapsed markers when search is cleared
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setExpandedFolders(prev => {
        const newExpanded = new Set();
        // Keep only non-collapsed markers (regular expansion state)
        prev.forEach(item => {
          if (!item.startsWith('collapsed_')) {
            newExpanded.add(item);
          }
        });
        return newExpanded;
      });
    }
  }, [searchQuery]);

  // Preserve scroll position across all re-renders
  useEffect(() => {
    if (treeContainerRef.current && scrollPositionRef.current > 0) {
      const container = treeContainerRef.current;
      // Use multiple methods to ensure scroll position is restored
      const restoreScroll = () => {
        if (container && container.scrollTop !== scrollPositionRef.current) {
          container.scrollTop = scrollPositionRef.current;
        }
      };

      // Immediate restoration
      restoreScroll();

      // Delayed restoration in case of async rendering
      const timeoutId = setTimeout(restoreScroll, 10);

      return () => clearTimeout(timeoutId);
    }
  });

  // Get all folder paths from tree data for expand/collapse functionality
  const getAllFolderPaths = (tree, currentPath = "") => {
    const paths = [];

    Object.entries(tree).forEach(([key, node]) => {
      const fullPath = currentPath ? `${currentPath}/${key}` : key;

      // Add this folder path
      paths.push(fullPath);

      // Recursively get paths from children
      if (node.children && Object.keys(node.children).length > 0) {
        paths.push(...getAllFolderPaths(node.children, fullPath));
      }
    });

    return paths;
  };

  // Count total items (files + subfolders) in a folder recursively
  const countFolderItems = (node) => {
    if (!node) return 0;

    let count = 0;

    // Count files in this folder
    count += (node.files || []).length;

    // Count subfolders and their contents recursively
    Object.values(node.children || {}).forEach(childNode => {
      count += 1; // Count the subfolder itself
      count += countFolderItems(childNode); // Count contents of subfolder
    });

    return count;
  };

  // Expand all folders
  const handleExpandAll = () => {
    const allPaths = getAllFolderPaths(filteredTree);
    setExpandedFolders(new Set(allPaths));
  };

  // Collapse all folders
  const handleCollapseAll = () => {
    setExpandedFolders(new Set());
  };

  // Flatten tree for virtualization
  const flattenedItems = useMemo(() => {
    const items = [];

    const flattenNode = (node, name, path, depth) => {
      if (!node) return;

      // Add folder item
      items.push({
        type: 'folder',
        name,
        path,
        depth,
        node,
        hasChildren: Object.keys(node.children || {}).length > 0 || (node.files && node.files.length > 0)
      });

      // Use same expansion logic as render function
      const hasSearchQuery = searchQuery.trim() !== "";
      const isManuallyExpanded = expandedFolders.has(path);
      const isExpanded = isManuallyExpanded || (hasSearchQuery && !expandedFolders.has(`collapsed_${path}`)) || showAttachedOnly;

      if (isExpanded) {
        // Add child folders
        Object.entries(node.children || {}).forEach(([childName, childNode]) => {
          const childPath = `${path}/${childName}`;
          flattenNode(childNode, childName, childPath, depth + 1);
        });

        // Add files
        (node.files || []).forEach(file => {
          items.push({
            type: 'file',
            name: file.name,
            path: file.path,
            depth: depth + 1,
            file,
            node: null
          });
        });
      }
    };

    Object.entries(filteredTree).forEach(([name, node]) => {
      flattenNode(node, name, name, 0);
    });

    return items;
  }, [filteredTree, expandedFolders, searchQuery, showAttachedOnly]);

  // Toggle folder expansion
  const toggleFolder = (path) => {
    const newExpanded = new Set(expandedFolders);
    const hasSearchQuery = searchQuery.trim() !== "";

    if (hasSearchQuery) {
      // During search: use collapsed markers to track manually collapsed folders
      const collapsedKey = `collapsed_${path}`;
      if (newExpanded.has(collapsedKey)) {
        // Currently collapsed, expand it
        newExpanded.delete(collapsedKey);
      } else {
        // Currently expanded, collapse it
        newExpanded.add(collapsedKey);
      }
    } else {
      // Normal behavior when no search
      if (newExpanded.has(path)) {
        newExpanded.delete(path);
      } else {
        newExpanded.add(path);
      }
    }

    setExpandedFolders(newExpanded);
  };



  // Check if a file is currently open
  const isFileOpen = (fileId) => {
    return openFiles.some(file => file.id === fileId);
  };



  // Get file icon function for compatibility
  const getFileIcon = (mimeType, size = 17) => {
    if (!mimeType) return <IconDefault size={size} />;

    if (mimeType.includes('pdf')) return <IconPDF size={size} />;
    if (mimeType.includes('dwg') || mimeType.includes('autocad')) return <IconDWG size={size} />;
    if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return <IconEXCEL size={size} />;
    if (mimeType.includes('word') || mimeType.includes('document')) return <IconWORD size={size} />;
    if (mimeType.includes('image')) return <IconIMG size={size} />;

    return <IconDefault size={size} />;
  };

  // Get file status color (same logic as FilesTable)
  const getFileStatusColor = (filePath, fileMapped) => {
    const normalizedCurrentGeneralFile = Array.isArray(currentGeneralFile)
      ? currentGeneralFile.join(', ')
      : currentGeneralFile;

    if (fileMapped === "TO_DELETE" || fileMapped === "DISCARDED") {
      return '#FBB360'; // Orange
    } else if (selectedRowData && selectedRowData.Files) {
      const files = JSON.parse(selectedRowData.Files);
      const isFileMatched = files.some(file => file.path === filePath);

      if (isFileMatched && activeTab === "previousInspections") {
        return '#BD6BBD'; // Purple
      } else if (currentInspectionFilesPath.includes(filePath) && activeTab === "previousInspections") {
        return '#BD6BBD'; // Purple
      } else if (filePath === normalizedCurrentGeneralFile && activeTab !== "previousInspections") {
        return '#BD6BBD'; // Purple
      } else if (currentAssetFiles.includes(filePath)) {
        return '#32A467'; // Green
      } else if (fileMapped === "TRUE") {
        return '#2d72d2'; // Blue
      } else {
        return 'grey'; // Default
      }
    } else {
      if (currentInspectionFilesPath.includes(filePath) && activeTab === "previousInspections") {
        return '#BD6BBD'; // Purple
      } else if (filePath === normalizedCurrentGeneralFile && activeTab !== "previousInspections") {
        return '#BD6BBD'; // Purple
      } else if (currentAssetFiles.includes(filePath)) {
        return '#32A467'; // Green
      } else if (fileMapped === "TRUE") {
        return '#2d72d2'; // Blue
      } else {
        return 'grey'; // Default
      }
    }
  };

  // Get linked status color (same logic as FilesTable svgCellRenderer)
  const getLinkedStatusColor = (filePath, fileMapped) => {
    const normalizedCurrentGeneralFile = Array.isArray(currentGeneralFile)
      ? currentGeneralFile.join(', ')
      : currentGeneralFile;

    if (fileMapped === "TO_DELETE" || fileMapped === "DISCARDED") {
      return '#FBB360'; // Orange
    } else if (selectedRowData && selectedRowData.Files) {
      const files = JSON.parse(selectedRowData.Files);
      const isFileMatched = files.some(file => file.path === filePath);

      if (isFileMatched && activeTab === "previousInspections") {
        return '#BD6BBD'; // Purple - linked to inspection
      } else if (currentInspectionFilesPath.includes(filePath) && activeTab === "previousInspections") {
        return '#BD6BBD'; // Purple - linked to inspection
      } else if (filePath === normalizedCurrentGeneralFile && activeTab !== "previousInspections") {
        return '#BD6BBD'; // Purple - linked to general files
      } else if (currentAssetFiles.includes(filePath)) {
        return '#32A467'; // Green
      } else if (fileMapped === "TRUE") {
        return '#2d72d2'; // Blue
      } else {
        return 'grey'; // Default
      }
    } else {
      if (currentInspectionFilesPath.includes(filePath) && activeTab === "previousInspections") {
        return '#BD6BBD'; // Purple - linked to inspection
      } else if (filePath === normalizedCurrentGeneralFile && activeTab !== "previousInspections") {
        return '#BD6BBD'; // Purple - linked to general files
      } else if (currentAssetFiles.includes(filePath)) {
        return '#32A467'; // Green
      } else if (fileMapped === "TRUE") {
        return '#2d72d2'; // Blue
      } else {
        return 'grey'; // Default
      }
    }
  };

  // Get the linked module type for a file
  const getLinkedModuleType = (filePath) => {
    const normalizedCurrentGeneralFile = Array.isArray(currentGeneralFile)
      ? currentGeneralFile.join(', ')
      : currentGeneralFile;

    // Check if linked to general files
    if (filePath === normalizedCurrentGeneralFile && activeTab !== "previousInspections") {
      return 'general';
    }

    // Check if linked to inspections
    if (currentInspectionFilesPath.includes(filePath) && activeTab === "previousInspections") {
      return 'inspection';
    }

    // Check if linked to selected inspection
    if (selectedRowData && selectedRowData.Files) {
      const files = JSON.parse(selectedRowData.Files);
      const isFileMatched = files.some(file => file.path === filePath);
      if (isFileMatched && activeTab === "previousInspections") {
        return 'inspection';
      }
    }

    return null;
  };

  // Render tree node
  const renderTreeNode = (node, name, path = "", depth = 0) => {
    if (!node) {
      console.warn("Attempting to render null/undefined node:", name, path);
      return null;
    }

    const isExpanded = expandedFolders.has(path) || searchQuery.trim() !== "";
    const hasChildren = Object.keys(node.children || {}).length > 0 || (node.files && node.files.length > 0);

    return (
      <div key={path} className="tree-node">
        {/* Folder */}
        {node.type === 'folder' && (
          <div
            className={`tree-item folder ${isDarkTheme ? 'dark' : 'light'}`}
            style={{ paddingLeft: `${depth * 16 + 8}px` }}
            onClick={() => toggleFolder(path)}
          >
            <div className="tree-item-content">
              {hasChildren && (
                <span className="expand-icon">
                  {isExpanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
                </span>
              )}
              {!hasChildren && <span className="expand-icon-placeholder" />}

              <span className="folder-icon">
                {isExpanded ?
                  <FaFolderOpen style={{ color: "#FBB360", fontSize: "14px" }} /> :
                  <FaFolder style={{ color: "#FBB360", fontSize: "14px" }} />
                }
              </span>

              <span className="item-name">{name}</span>
            </div>
          </div>
        )}

        {/* Children */}
        {isExpanded && (
          <div className="tree-children">
            {/* Render child folders */}
            {Object.entries(node.children || {}).map(([childName, childNode]) =>
              renderTreeNode(childNode, childName, `${path}/${childName}`, depth + 1)
            )}

            {/* Render files */}
            {(node.files || []).map((file, index) => {
              if (!file || !file.name) {
                console.warn("Invalid file object:", file);
                return null;
              }

              const fileIsOpen = isFileOpen(file.id);
              const statusColor = getFileStatusColor(file.path, file.Mapped);
              return (
                <div
                  key={`${path}/file-${index}`}
                  className={`tree-item file ${isDarkTheme ? 'dark' : 'light'} ${fileIsOpen ? 'file-open' : ''}`}
                  style={{ paddingLeft: `${(depth + 1) * 16 + 8}px` }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    // Save scroll position before file click
                    if (treeContainerRef.current) {
                      scrollPositionRef.current = treeContainerRef.current.scrollTop;
                      preserveScrollRef.current = true;
                    }
                    onFileClick(file.id, file.name, file.path);
                  }}
                >
                  <div className="tree-item-content">
                    {/* No expand icon for files */}
                    <span className="expand-icon-placeholder" />

                    {/* Status indicator (same as files table) */}
                    <span className="file-status-indicator" style={{ marginRight: '4px' }}>
                      <svg
                        width="12"
                        height="12"
                        style={{
                          display: "block",
                        }}
                      >
                        <rect
                          width="12"
                          height="12"
                          style={{ fill: statusColor }}
                          rx="6"
                          ry="6"
                        />
                      </svg>
                    </span>

                    {/* Use specific file type icon */}
                    <span className="file-icon">
                      <FileIcon fileType={file.fileType} fileName={file.name} />
                    </span>

                    <span className="item-name">
                      {file.name}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    );
  };

  // Enhanced scroll position preservation
  const scrollPositionRef = useRef(0);
  const treeContainerRef = useRef();
  const preserveScrollRef = useRef(false);

  // Virtualized Tree Component
  const VirtualizedTree = ({ items, renderItem }) => {
    const parentRef = useRef();

    const virtualizer = useVirtualizer({
      count: items.length,
      getScrollElement: () => parentRef.current,
      estimateSize: () => 24, // Height of each tree item
      overscan: 10, // Render extra items for smooth scrolling
    });

    // Save scroll position when scrolling
    const handleScroll = (e) => {
      scrollPositionRef.current = e.target.scrollTop;
    };

    // Enhanced scroll position restoration
    useEffect(() => {
      if (parentRef.current && preserveScrollRef.current && scrollPositionRef.current > 0) {
        // Use requestAnimationFrame to ensure DOM is ready
        requestAnimationFrame(() => {
          if (parentRef.current) {
            parentRef.current.scrollTop = scrollPositionRef.current;
            preserveScrollRef.current = false; // Reset flag after restoration
          }
        });
      }
    }, [items]);

    // Also restore on mount
    useEffect(() => {
      if (parentRef.current && scrollPositionRef.current > 0) {
        requestAnimationFrame(() => {
          if (parentRef.current) {
            parentRef.current.scrollTop = scrollPositionRef.current;
          }
        });
      }
    }, []);

    return (
      <div
        ref={(el) => {
          parentRef.current = el;
          treeContainerRef.current = el;
        }}
        className="tree-content"
        style={{
          height: '100%',
          overflow: 'auto',
        }}
        onScroll={handleScroll}
      >
        <div
          style={{
            height: `${virtualizer.getTotalSize()}px`,
            width: '100%',
            position: 'relative',
          }}
        >
          {virtualizer.getVirtualItems().map((virtualItem) => (
            <div
              key={virtualItem.key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
            >
              {renderItem(items[virtualItem.index])}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render virtualized item
  const renderVirtualizedItem = (item) => {
    if (item.type === 'folder') {
      // Allow manual control even during search - only auto-expand if not manually controlled
      const hasSearchQuery = searchQuery.trim() !== "";
      const isManuallyExpanded = expandedFolders.has(item.path);
      const isExpanded = isManuallyExpanded || (hasSearchQuery && !expandedFolders.has(`collapsed_${item.path}`)) || showAttachedOnly;
      const itemCount = countFolderItems(item.node);

      return (
        <div
          className={`tree-item folder ${isExpanded ? 'expanded' : ''}`}
          style={{ paddingLeft: `${Math.min(item.depth * 12, 48) + 8}px` }}
          onClick={() => toggleFolder(item.path)}
        >
          <div
            className="tree-item-content"
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              gap: '4px'
            }}
          >
            {item.hasChildren ? (
              <div className="expand-icon">
                {isExpanded ? (
                  <ChevronDown size={17} color="#FBB360" />
                ) : (
                  <ChevronRight size={17} color="#FBB360" />
                )}
              </div>
            ) : (
              <div className="expand-icon-placeholder" />
            )}

            <div className="folder-icon">
              {isExpanded ? (
                <FaFolderOpen size={17} color="#FBB360" />
              ) : (
                <FaFolder size={17} color="#FBB360" />
              )}
            </div>

            <span className="item-name" style={{ flex: 1 }}>{item.name}</span>

            {/* Normalized count tag */}
            {itemCount > 0 && (
              <span
                className="folder-count-tag"
                style={{
                  backgroundColor: isDarkTheme ? '#383E47' : '#e1e8ed',
                  color: isDarkTheme ? '#FFFFFF' : '#182026',
                  borderRadius: '10px',
                  padding: '1px 6px',
                  fontSize: '10px',
                  fontWeight: '700',
                  minWidth: '16px',
                  height: '16px',
                  lineHeight: '14px',
                  textAlign: 'center',
                  border: `1px solid ${isDarkTheme ? '#2F343C' : '#bfccd6'}`,
                  flexShrink: 0,
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {itemCount}
              </span>
            )}
          </div>
        </div>
      );
    } else if (item.type === 'file') {
      const fileIsOpen = isFileOpen(item.file.id);
      const statusColor = getFileStatusColor(item.file.path, item.file.Mapped);
      const linkedModuleType = getLinkedModuleType(item.file.path);

      // Context menu items for files (same as FilesTable)
      const getContextMenuItems = () => {
        const baseItems = [
          {
            key: 'copyPath',
            icon: <Copy size={17} />,
            label: 'Copy Path',
            onClick: () => navigator.clipboard.writeText(item.file.path),
          },
          {
            key: 'copyName',
            icon: <FileText size={17} />,
            label: 'Copy Name',
            onClick: () => navigator.clipboard.writeText(item.file.name),
          }
        ];

        // Add restore or discard based on current status
        if (item.file.Mapped === "DISCARDED") {
          baseItems.push({
            key: 'restore',
            icon: <RotateCcw size={17} />,
            label: 'Restore',
            onClick: async () => {
              try {
                await axios.post(`${process.env.REACT_APP_DATA_API}/files/restore`, {
                  path: item.file.path
                }, {
                  headers: {
                    "Content-Type": "application/json",
                    "session-id": localStorage.getItem("session_id"),
                  }
                });

                // Update the file status in local state
                setData(prevData =>
                  prevData.map(file =>
                    file.path === item.file.path
                      ? { ...file, Mapped: '' }
                      : file
                  )
                );
              } catch (error) {
                console.error('Error restoring file:', error);
              }
            },
          });
        } else {
          baseItems.push({
            key: 'discard',
            icon: <FolderX size={17} />,
            label: 'Discard',
            onClick: async () => {
              try {
                await axios.post(`${process.env.REACT_APP_DATA_API}/files/discard`, {
                  path: item.file.path,
                  status: "DISCARDED"
                }, {
                  headers: {
                    "Content-Type": "application/json",
                    "session-id": localStorage.getItem("session_id"),
                  }
                });

                // Update the file status in local state
                setData(prevData =>
                  prevData.map(file =>
                    file.path === item.file.path
                      ? { ...file, Mapped: 'DISCARDED' }
                      : file
                  )
                );
              } catch (error) {
                console.error('Error discarding file:', error);
              }
            },
          });
        }

        baseItems.push({
          key: 'viewOnDrive',
          icon: <FaGoogleDrive size={17} />,
          label: 'View on Google Drive',
          onClick: () => window.open(`https://drive.google.com/file/d/${item.file.id}/view`, '_blank'),
        });

        return baseItems;
      };

      return (
        <Dropdown
          menu={{ items: getContextMenuItems() }}
          trigger={['contextMenu']}
          overlayStyle={{ zIndex: 9999 }}
        >
          <div
            className={`tree-item file ${isDarkTheme ? 'dark' : 'light'} ${fileIsOpen ? 'file-open' : ''}`}
            style={{ paddingLeft: `${Math.min(item.depth * 12, 48) + 8}px` }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Enhanced scroll position preservation before file click
              if (treeContainerRef.current) {
                scrollPositionRef.current = treeContainerRef.current.scrollTop;
                preserveScrollRef.current = true;
              }
              onFileClick(item.file.id, item.file.name, item.file.path);
            }}
          >
            <div className="tree-item-content">
              <div className="expand-icon-placeholder" />

              {/* Status Circle */}
              <div className="file-status-indicator" style={{ marginRight: '4px' }}>
                <svg
                  width="12"
                  height="12"
                  style={{ display: "block" }}
                >
                  <rect
                    width="12"
                    height="12"
                    style={{ fill: statusColor }}
                    rx="6"
                    ry="6"
                  />
                </svg>
              </div>

              <div className="file-icon">
                <FileIcon fileType={getFileType(item.file.mimeType)} fileName={item.file.name} size={17} />
              </div>

              <span className="item-name">{item.file.name}</span>
            </div>
          </div>
        </Dropdown>
      );
    }

    return null;
  };

  if (loading) {
    return (
      <div className="vscode-tree-explorer">
        <Card className={`tree-card ${isDarkTheme ? 'dark' : 'light'}`}>
          <div className="loading-state">
            <Spinner size={32} />
            <div>Loading files...</div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="vscode-tree-explorer">
      <Card className={`tree-card ${isDarkTheme ? 'dark' : 'light'}`}>
        {/* Header with Total Files Counter and Expand/Collapse Buttons */}
        <Card
          className="no-padding-card"
          style={{
            marginBottom: "8px",
            padding: "2px 4px",
            height: "29px",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            overflow: "visible",
            flexShrink: 0
          }}
        >
          <Tag style={{ fontWeight: "bold", width: "100px", textAlign: "center", minWidth: "100px" }} intent={"primary"}>
            Total: {totalFiles}
          </Tag>

          {/* Only show expand/collapse buttons when not searching */}
          {!searchQuery.trim() && (
            <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
              <Button
                size="small"
                icon="expand-all"
                onClick={handleExpandAll}
                minimal={true}
                style={{
                  fontSize: "12px",
                  height: "24px",
                  padding: "0 8px",
                  minWidth: "32px",
                  borderRadius: "4px"
                }}
                title="Expand All"
              />
              <Button
                size="small"
                icon="collapse-all"
                onClick={handleCollapseAll}
                minimal={true}
                style={{
                  fontSize: "12px",
                  height: "24px",
                  padding: "0 8px",
                  minWidth: "32px",
                  borderRadius: "4px"
                }}
                title="Collapse All"
              />
              <Button
                size="small"
                icon="refresh"
                onClick={onResetViewedFiles}
                variant="minimal"
                style={{
                  fontSize: "12px",
                  height: "24px",
                  padding: "0 8px",
                  minWidth: "32px",
                  borderRadius: "4px"
                }}
                title="Reset Viewed Files Status"
              />
            </div>
          )}
        </Card>

        {/* Search and Filter Controls */}
        <div className="controls-container" style={{ marginBottom: "8px", display: "flex", gap: "8px" }}>
          <Input
            prefix={<SearchOutlined />}
            placeholder="Search files and folders..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            allowClear
            style={{ fontSize: "14px", flex: 1 }}
          />

          {/* Standalone toggle button for attached files */}
          <Button
            size="small"
            type={showAttachedOnly ? 'primary' : 'default'}
            disabled={isToggling}
            onClick={() => {
              if (isToggling) return;

              setIsToggling(true);
              setShowAttachedOnly(!showAttachedOnly);

              // Reset toggling state after a short delay
              setTimeout(() => {
                setIsToggling(false);
              }, 300);
            }}
            style={{
              display: "flex",
              alignItems: "center",
              gap: "4px",
              fontSize: "12px",
              height: "32px",
              padding: "0 8px",
              borderRadius: "4px"
            }}
            title="Toggle: Show only files attached to assets (green)"
          >
            <svg width="12" height="12" style={{ display: "block", flexShrink: 0 }}>
              <rect width="12" height="12" style={{ fill: "#32A467" }} rx="6" ry="6" />
            </svg>
            Attached
          </Button>
          <Select
            mode="multiple"
            placeholder="File Status"
            value={fileStatusFilter}
            onChange={setFileStatusFilter}
            style={{
              minWidth: "140px",
              fontSize: "14px"
            }}
            dropdownStyle={{
              padding: "4px"
            }}
            options={[
              {
                label: (
                  <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                    padding: "2px 4px",
                    borderRadius: "4px"
                  }}>
                    <svg width="12" height="12" style={{ display: "block", flexShrink: 0 }}>
                      <rect width="12" height="12" style={{ fill: "#2d72d2" }} rx="6" ry="6" />
                    </svg>
                    <span style={{ fontWeight: "600", fontSize: "14px" }}>Mapped</span>
                  </div>
                ),
                value: "mapped"
              },
              {
                label: (
                  <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                    padding: "2px 4px",
                    borderRadius: "4px"
                  }}>
                    <svg width="12" height="12" style={{ display: "block", flexShrink: 0 }}>
                      <rect width="12" height="12" style={{ fill: "grey" }} rx="6" ry="6" />
                    </svg>
                    <span style={{ fontWeight: "600", fontSize: "14px" }}>Unmapped</span>
                  </div>
                ),
                value: "unmapped"
              },
              {
                label: (
                  <div style={{
                    display: "flex",
                    alignItems: "center",
                    gap: "8px",
                    padding: "2px 4px",
                    borderRadius: "4px"
                  }}>
                    <svg width="12" height="12" style={{ display: "block", flexShrink: 0 }}>
                      <rect width="12" height="12" style={{ fill: "#FBB360" }} rx="6" ry="6" />
                    </svg>
                    <span style={{ fontWeight: "600", fontSize: "14px" }}>Discarded</span>
                  </div>
                ),
                value: "discarded"
              }
            ]}
            maxTagCount="responsive"
            allowClear
          />
        </div>



        {/* Tree */}
        <div className="tree-container">
          {flattenedItems.length === 0 ? (
            <NonIdealState
              icon={searchQuery ? "search" : "folder-open"}
              title="No files found"
              description={
                searchQuery
                  ? "Try adjusting your search criteria or clear the search to see all files"
                  : "No files are available in this location"
              }
            />
          ) : (
            <VirtualizedTree items={flattenedItems} renderItem={renderVirtualizedItem} />
          )}
        </div>
      </Card>
    </div>
  );
}

export default React.memo(VSCodeTreeExplorer);
