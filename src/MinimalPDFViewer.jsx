import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Spinner } from '@blueprintjs/core';
import { useDispatch } from 'react-redux';
import { useInView } from 'react-intersection-observer';

// Import required CSS
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

/**
 * MinimalPDFViewer - A simple PDF viewer that works now and can be replaced with Lector later
 */
const MinimalPDFViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  const [pdfData, setPdfData] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  // Fetch PDF data
  useEffect(() => {
    if (!fileId) return;
    
    async function fetchFile() {
      try {
        setLoading(true);
        const apiUrl = `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`;
        console.log('Fetching PDF from:', apiUrl);
        
        const response = await fetch(apiUrl, {
          headers: {
            'session-id': localStorage.getItem('session_id'),
          },
        });
        
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }
        
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        setPdfData(url);
        setLoading(false);
        
        if (dispatch) {
          try {
            dispatch({
              type: 'UPDATE_PDF_VIEWER',
              payload: { fileId, isVisible: inView }
            });
          } catch (e) {
            console.warn('Dispatch error:', e);
          }
        }
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(err.message);
        setLoading(false);
      }
    }
    
    fetchFile();
    
    return () => {
      if (pdfData && typeof pdfData === 'string' && pdfData.startsWith('blob:')) {
        URL.revokeObjectURL(pdfData);
      }
    };
  }, [fileId, dispatch, inView, pdfData]);
  
  function onDocumentLoadSuccess({ numPages }) {
    setNumPages(numPages);
  }
  
  // Render all pages in a scrollable container
  const renderAllPages = () => {
    const pages = [];
    for (let i = 1; i <= numPages; i++) {
      pages.push(
        <div key={`page-${i}`} style={{ margin: '10px 0' }}>
          <Page 
            pageNumber={i} 
            renderTextLayer={true}
            renderAnnotationLayer={true}
          />
        </div>
      );
    }
    return pages;
  };
  
  return (
    <div 
      ref={ref}
      style={{ 
        height: '100%', 
        width: '100%', 
        overflow: 'auto',
        padding: '20px',
        backgroundColor: '#f5f5f5',
        ...style
      }}
    >
      {loading ? (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
          <Spinner size={30} />
          <div style={{ marginLeft: '10px' }}>Loading PDF...</div>
        </div>
      ) : error ? (
        <div style={{ color: 'red', padding: '20px' }}>
          <h3>Error Loading PDF</h3>
          <p>{error}</p>
        </div>
      ) : (
        <Document
          file={pdfData}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={(err) => setError(err.message)}
          options={{
            cMapUrl: 'https://unpkg.com/pdfjs-dist@2.12.313/cmaps/',
            cMapPacked: true,
          }}
        >
          {numPages && renderAllPages()}
        </Document>
      )}
      
      {/* LECTOR IMPLEMENTATION INSTRUCTIONS:
      
      To use Lector instead of react-pdf:
      
      1. Install Lector:
         npm install @anaralabs/lector pdfjs-dist
      
      2. Replace the imports at the top with:
         import { Root, Pages, Page, CanvasLayer, TextLayer } from '@anaralabs/lector';
      
      3. Replace the <Document>...</Document> with:
         <Root
           source={pdfData}
           className="w-full h-full"
           loader={<div style={{padding: '20px'}}><Spinner /></div>}
         >
           <Pages>
             <Page>
               <CanvasLayer />
               <TextLayer />
             </Page>
           </Pages>
         </Root>
      */}
    </div>
  );
};

export default MinimalPDFViewer;
