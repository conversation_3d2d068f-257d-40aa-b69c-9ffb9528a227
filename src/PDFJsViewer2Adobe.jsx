import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Spin<PERSON>, SpinnerSize } from "@blueprintjs/core";
import "@blueprintjs/core/lib/css/blueprint.css";
import { updateCurrentPage, resetCurrentPage } from './redux/currentPdfSlice';

function PDFJsViewer({ fileId, isDarkTheme }) {
    const [pdfFile, setPdfFile] = useState(null);
    const [downloadProgress, setDownloadProgress] = useState(0);
    const dispatch = useDispatch();

    useEffect(() => {
        async function fetchFile() {
            setPdfFile(null);
            setDownloadProgress(0);
            dispatch(resetCurrentPage());

            try {
                const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
                    headers: {
                        'Accept-Encoding': 'gzip, deflate, br',
                        'session-id': localStorage.getItem('session_id'),
                    },
                });
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }

                const contentLength = response.headers.get('Content-Length');
                const reader = response.body.getReader();
                let receivedLength = 0;
                const chunks = [];

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    chunks.push(value);
                    receivedLength += value.length;

                    if (contentLength) {
                        setDownloadProgress(receivedLength / contentLength);
                    }
                }

                const blob = new Blob(chunks);
                const pdfUrl = URL.createObjectURL(blob);
                setPdfFile(pdfUrl);
            } catch (error) {
                console.error("Failed to fetch the PDF:", error);
            }
        }

        fetchFile();

        return () => {
            if (pdfFile) {
                URL.revokeObjectURL(pdfFile); // Memory management
            }
        };
    }, [fileId, dispatch]);

    useEffect(() => {
        const loadAdobeScript = () => {
            const script = document.createElement('script');
            script.src = "https://acrobatservices.adobe.com/view-sdk/viewer.js";
            script.onload = () => {
                if (window.AdobeDC) {
                    const adobeDCView = new window.AdobeDC.View({ clientId: "06c54ba7e2dd499f853ffedd0a8c0a50", divId: "adobe-dc-view" });
                    adobeDCView.previewFile({
                        content: { location: { url: pdfFile } },
                        metaData: { fileName: fileId }
                    }, {
                        showAnnotationTools: false,
                        showDownloadPDF: false
                    });
                }
            };
            document.body.appendChild(script);

            return () => {
                document.body.removeChild(script);
            };
        };

        if (pdfFile) {
            loadAdobeScript();
        }
    }, [pdfFile, fileId]);

    if (!pdfFile) {
        return (
            <div className="spinner-container">
                <Spinner
                    className="spinner-center"
                    intent="primary"
                    size={SpinnerSize.STANDARD}
                    value={downloadProgress}
                />
            </div>
        );
    }

    return (
        <div id="adobe-dc-view" style={{ width: "100%", height: "100vh" }}></div>
    );
}

export default PDFJsViewer;


