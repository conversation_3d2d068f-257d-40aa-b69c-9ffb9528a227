import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Spinner } from "@blueprintjs/core";
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

/**
 * SimplestPDFViewer - A bare-bones PDF viewer with minimal complexity
 * Focused purely on reliable document loading and display
 */
const SimplestPDFViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [scale, setScale] = useState(1.2);

  // Load PDF file
  useEffect(() => {
    async function loadPdfFile() {
      setLoading(true);
      setError(null);
      
      try {
        // Direct path provided
        if (filePath) {
          setPdfFile(filePath);
          setLoading(false);
          return;
        }
        
        // Load from API using fileId
        if (!fileId) {
          throw new Error('No file ID or path provided');
        }
        
        console.log('Fetching PDF with ID:', fileId);
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'session-id': localStorage.getItem('session_id'),
          },
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
        }
        
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        setPdfFile(url);
        console.log('PDF loaded successfully');
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(err.message || 'Failed to load PDF');
      } finally {
        setLoading(false);
      }
    }
    
    loadPdfFile();
    
    // Clean up URL objects
    return () => {
      if (pdfFile && typeof pdfFile === 'string' && pdfFile.startsWith('blob:')) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, filePath, pdfFile]);
  
  // Handle document load success
  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setLoading(false);
    console.log(`PDF loaded with ${numPages} pages`);
  };
  
  // Handle document load failure
  const onDocumentLoadError = (err) => {
    console.error('Error rendering PDF:', err);
    setError(`Error rendering PDF: ${err.message}`);
    setLoading(false);
  };
  
  // Navigate to previous page
  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  };
  
  // Navigate to next page
  const goToNextPage = () => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  };
  
  // Zoom in
  const zoomIn = () => setScale(prev => Math.min(prev + 0.2, 3));
  
  // Zoom out
  const zoomOut = () => setScale(prev => Math.max(prev - 0.2, 0.5));

  return (
    <div style={{ 
      height: '100%', 
      width: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      background: isDarkTheme ? '#1e1e1e' : '#f5f5f5',
      color: isDarkTheme ? '#fff' : '#000',
      overflow: 'hidden',
      ...style 
    }}>
      {/* Simple controls */}
      <div style={{ 
        padding: '10px', 
        display: 'flex', 
        justifyContent: 'space-between',
        borderBottom: `1px solid ${isDarkTheme ? '#444' : '#ddd'}`
      }}>
        <div>
          <button 
            onClick={goToPrevPage} 
            disabled={pageNumber <= 1}
            style={{ 
              padding: '5px 10px', 
              marginRight: '5px',
              background: isDarkTheme ? '#333' : '#fff',
              color: isDarkTheme ? '#fff' : '#000',
              border: `1px solid ${isDarkTheme ? '#555' : '#ccc'}`,
              borderRadius: '3px'
            }}
          >
            Previous
          </button>
          <button 
            onClick={goToNextPage} 
            disabled={pageNumber >= numPages}
            style={{ 
              padding: '5px 10px',
              background: isDarkTheme ? '#333' : '#fff',
              color: isDarkTheme ? '#fff' : '#000',
              border: `1px solid ${isDarkTheme ? '#555' : '#ccc'}`,
              borderRadius: '3px'
            }}
          >
            Next
          </button>
          <span style={{ margin: '0 10px' }}>
            Page {pageNumber} of {numPages || '--'}
          </span>
        </div>
        
        <div>
          <button 
            onClick={zoomOut} 
            style={{ 
              padding: '5px 10px', 
              marginRight: '5px',
              background: isDarkTheme ? '#333' : '#fff',
              color: isDarkTheme ? '#fff' : '#000',
              border: `1px solid ${isDarkTheme ? '#555' : '#ccc'}`,
              borderRadius: '3px'
            }}
          >
            -
          </button>
          <span style={{ margin: '0 5px' }}>
            {Math.round(scale * 100)}%
          </span>
          <button 
            onClick={zoomIn} 
            style={{ 
              padding: '5px 10px',
              background: isDarkTheme ? '#333' : '#fff',
              color: isDarkTheme ? '#fff' : '#000',
              border: `1px solid ${isDarkTheme ? '#555' : '#ccc'}`,
              borderRadius: '3px'
            }}
          >
            +
          </button>
        </div>
      </div>
      
      {/* Document area */}
      <div style={{ 
        flex: 1, 
        overflow: 'auto', 
        display: 'flex', 
        justifyContent: 'center',
        padding: '20px'
      }}>
        {loading ? (
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: '100%'
          }}>
            <Spinner size={50} />
            <div style={{ marginTop: '20px' }}>Loading PDF...</div>
          </div>
        ) : error ? (
          <div style={{ 
            color: 'red', 
            textAlign: 'center', 
            padding: '20px',
            alignSelf: 'center'
          }}>
            <h3>Error Loading PDF</h3>
            <p>{error}</p>
          </div>
        ) : (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div style={{ 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center', 
                justifyContent: 'center' 
              }}>
                <Spinner size={30} />
                <div style={{ marginTop: '10px' }}>Rendering document...</div>
              </div>
            }
          >
            <Page
              key={`page_${pageNumber}`}
              pageNumber={pageNumber}
              scale={scale}
              renderTextLayer={true}
              renderAnnotationLayer={true}
              className={isDarkTheme ? 'dark-page' : ''}
            />
          </Document>
        )}
      </div>
      
      {/* Add dark mode styles */}
      {isDarkTheme && (
        <style dangerouslySetInnerHTML={{
          __html: `
            .dark-page canvas {
              filter: invert(85%) hue-rotate(180deg) brightness(85%) contrast(85%);
            }
            .react-pdf__Page__textContent {
              filter: invert(100%) hue-rotate(180deg);
            }
          `
        }} />
      )}
    </div>
  );
};

export default SimplestPDFViewer;
