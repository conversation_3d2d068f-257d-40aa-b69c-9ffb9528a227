import React, { createContext, useState, useRef, useEffect } from "react";
import { v4 as uuidv4 } from "uuid";
export const AppContext = createContext();

export const AppContextProvider = ({ children }) => {
  // State for selected file rendering on the file viewer
  const [selectedFile, setSelectedFile] = useState({
    id: null,
    name: null,
    path: null,
  });

  const myToaster = React.useRef(null);
  // State for updating files column
  const [updateFilesColumn, setUpdateFilesColumn] = useState({});
  // State for dark theme
  const [isDarkTheme, setIsDarkTheme] = useState(true);

  const [activeModule, setActiveModule] = useState("");
  // State for inspections grid API
  const [inspectionsGridApi, setInspectionsGridApi] = useState(null);

  //User Variables
  //user-email
  const [userEmail, setUserEmail] = useState(null);

  const [userName, setUserName] = useState(null);

  //Previous Inspection Dates

  const [previousInspectionDates, setPreviousInspectionDates] = useState([]);

  //lookupData
  const [lookupValues, setLookupValues] = useState([]);

  // Dropdown Rules

  const [dropdownRules, setDropdownRules] = useState({});

  const [inspectionData, setInspectionData] = useState([]);

  const [currentAssetComponents, setCurrentAssetComponents] = useState([]);

  // user Data
  const [userData, setUserData] = useState([]);

  const [npsODdata,setNpsODdata] = useState([]);
  
  const [pipingSchedule, setPipingSchedule] = useState([]);

  //All Files at current Inspection Level
  const [allCurrentInspectionsFiles, setAllCurrentInspectionsFiles] = useState(
    [],
  );

  //All Files from selected row in Inspection Grid

  const [currentInspectionFiles, setCurrentInspectionFiles] = useState([]);

  //All Files from General Properties

  const [allCurrentGeneralFiles, setAllCurrentGeneralFiles] = useState([]);

  const [currentGeneralFile, setCurrentGeneralFile] = useState([]);

  // All Files General and Inspection
  const [currentAssetFiles, setCurrentAssetFiles] = useState([]);

  //defautl atctive tab
  const [activeTab, setActiveTab] = useState("generalProperties");

  //Notifications

  const [notifications, setNotifications] = useState([]);

  const handleLinkAction = (isLinking, path) => {
    setUpdateFilesColumn({ isLinking, path });
  };

  const [assetData, setAssetData] = useState([]);
  const [nozzlesData, setNozzlesData] = useState([]);

  // selected Asset sheet
  const [selectedSheet, setselectedSheet] = useState(null);

  //Asset Invetory Filter
  const [quickFilterText, setQuickFilterText] = useState("");

  const [isMaterialPickerOpen, setIsMaterialPickerOpen] = useState(false);

  const [assetClassification, setAssetClassification] = useState(null);

  const [selectedTag, setSelectedTag] = useState(null);
  const [userSessions, setUserSessions] = useState({});
  const webSocket = useRef(null);
  const sessionToken = useRef(uuidv4()); // Generate a unique session token for this client

  const [allAssetsData, setAllAssetsData] = useState([]); // All assets data

  useEffect(() => {
    if (!userName) return; // Ensure we have a user name to work with

    const wsURL = `ws://0.0.0.0:8020/ws/${userName}/${sessionToken.current}`; // Adjust the URL to your WebSocket endpoint
    webSocket.current = new WebSocket(wsURL);

    webSocket.current.onopen = () => console.log("WebSocket Connected");

    webSocket.current.onmessage = (event) => {
      console.log("Received message:", event.data);
      const data = JSON.parse(event.data);

      if (data.disconnecting) {
        const userKey = `${data.user_name}_${data.session_token}`;
        setUserSessions((prev) => {
          const updatedSessions = { ...prev };
          delete updatedSessions[userKey];
          return updatedSessions;
        });
      } else {
        const userKey = `${data.user_name}_${data.session_token}`;
        setUserSessions((prev) => ({ ...prev, [userKey]: data.tag }));
      }
    };

    webSocket.current.onclose = () => console.log("WebSocket Disconnected");
    webSocket.current.onerror = (error) =>
      console.log("WebSocket Error:", error);

    return () => {
      if (webSocket.current) {
        webSocket.current.close();
      }
    };
  }, [userName]); // Depend on userName to reconnect if it changes

  const sendMessage = (tag) => {
    if (webSocket.current && webSocket.current.readyState === WebSocket.OPEN) {
      const message = {
        user_name: userName,
        session_token: sessionToken.current,
        tag,
        disconnecting: false,
      };
      console.log("Sending message:", JSON.stringify(message));
      webSocket.current.send(JSON.stringify(message));
    }
  };

  return (
    <AppContext.Provider
      value={{
        myToaster,
        selectedFile,
        setSelectedFile,
        handleLinkAction,
        updateFilesColumn,
        isDarkTheme,
        setIsDarkTheme,
        activeModule,
        setActiveModule,
        inspectionsGridApi,
        setInspectionsGridApi,
        userEmail,
        setUserEmail,
        previousInspectionDates,
        setPreviousInspectionDates,
        lookupValues,
        setLookupValues,
        inspectionData,
        setInspectionData,
        currentAssetComponents,
        setCurrentAssetComponents,
        allCurrentInspectionsFiles,
        setAllCurrentInspectionsFiles,
        currentInspectionFiles,
        setCurrentInspectionFiles,
        allCurrentGeneralFiles,
        setAllCurrentGeneralFiles,
        currentGeneralFile,
        setCurrentGeneralFile,
        currentAssetFiles,
        setCurrentAssetFiles,
        userData,
        setUserData,
        activeTab,
        setActiveTab,
        notifications,
        setNotifications,
        quickFilterText,
        setQuickFilterText,
        isMaterialPickerOpen,
        setIsMaterialPickerOpen,
        assetData,
        setAssetData,
        selectedSheet,
        setselectedSheet,
        userSessions,
        sendMessage,
        selectedTag,
        setSelectedTag,
        userName,
        setUserName,
        dropdownRules,
        setDropdownRules,
        assetClassification,
        setAssetClassification,
        nozzlesData,
        setNozzlesData,
        npsODdata,
        setNpsODdata,
        pipingSchedule,
        setPipingSchedule,
        allAssetsData,
        setAllAssetsData,
        

        // Add any other variables and setters here
      }}
    >
      {children}
    </AppContext.Provider>
  );
};
