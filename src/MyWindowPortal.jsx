import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import store from './redux/store'; // Adjust the import path as necessary

class MyWindowPortal extends React.PureComponent {
  constructor(props) {
    super(props);
    this.containerEl = document.createElement('div');
    this.externalWindow = null;
  }

  componentDidMount() {
    this.externalWindow = window.open('', '', 'width=1500,height=1500,left=500,top=100,popup=yes');
    this.externalWindow.document.body.appendChild(this.containerEl);
    this.copyStylesAndFonts();
    this.externalWindow.addEventListener('beforeunload', this.cleanup);
    this.forceUpdate(); // Trigger a re-render to pass the document reference
  }

  componentWillUnmount() {
    this.cleanup();
  }

  cleanup = () => {
    if (this.externalWindow) {
      this.externalWindow.close();
    }
  }

  copyStylesAndFonts() {
    const styleSheets = Array.from(document.styleSheets);
    const styles = styleSheets.map(styleSheet => {
      try {
        return Array.from(styleSheet.cssRules)
          .map(rule => rule.cssText)
          .join('');
      } catch (err) {
        return '';
      }
    }).join('');
    
    const style = document.createElement('style');
    style.textContent = styles;
    this.externalWindow.document.head.appendChild(style);

    const links = document.querySelectorAll('link[rel="stylesheet"], link[rel="font"]');
    links.forEach((link) => {
      const newLink = this.externalWindow.document.createElement('link');
      newLink.rel = link.rel;
      newLink.href = link.href;
      this.externalWindow.document.head.appendChild(newLink);
    });
  }

  render() {
    return this.externalWindow ? ReactDOM.createPortal(
      <Provider store={store}>
        {React.cloneElement(this.props.children, { ownerDocument: this.externalWindow.document })}
      </Provider>,
      this.containerEl
    ) : null;
  }
}

export default MyWindowPortal;
