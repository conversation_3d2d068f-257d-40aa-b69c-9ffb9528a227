/* eslint-disable no-restricted-globals */
/* eslint-env worker */
import { getDocument } from 'pdfjs-dist';

self.onmessage = async function(event) {
  const { type, data } = event.data;

  switch (type) {
    case 'loadPDF':
      await loadPDF(data.pdfUrl);
      break;
    case 'search':
      await searchDocument(data.pdfUrl, data.searchText, data.numPages);
      break;
    default:
      self.postMessage({ type: 'error', data: { message: 'Unknown message type' } });
  }
};

async function loadPDF(pdfUrl) {
  try {
    const pdf = await getDocument(pdfUrl).promise;
    const numPages = pdf.numPages;
    const orientations = [];

    for (let i = 1; i <= numPages; i++) {
      const page = await pdf.getPage(i);
      const viewport = page.getViewport({ scale: 1 });
      orientations.push(viewport.width > viewport.height ? 'landscape' : 'portrait');
    }

    self.postMessage({ type: 'pdfLoaded', data: { numPages, orientations } });
  } catch (error) {
    self.postMessage({ type: 'error', data: { message: 'Failed to load PDF', error: error.message } });
  }
}

async function searchDocument(pdfUrl, searchText, numPages) {
  try {
    const pdf = await getDocument(pdfUrl).promise;
    const results = [];

    for (let i = 1; i <= numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const textItems = textContent.items.map(item => ({
        str: item.str,
        transform: item.transform,
        width: item.width,
        height: item.height,
        page: i,
      }));

      textItems.forEach(item => {
        let match;
        const regex = new RegExp(searchText, 'gi');
        while ((match = regex.exec(item.str)) !== null) {
          results.push({
            page: item.page,
            str: match[0],
            transform: item.transform,
            width: item.width,
            height: item.height,
          });
        }
      });
    }

    self.postMessage({ type: 'searchResults', data: results });
  } catch (error) {
    self.postMessage({ type: 'error', data: { message: 'Error searching document', error: error.message } });
  }
}