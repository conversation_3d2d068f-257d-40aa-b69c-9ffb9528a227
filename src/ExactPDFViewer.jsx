import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { <PERSON><PERSON>, <PERSON><PERSON> } from '@blueprintjs/core';
import { useDispatch } from 'react-redux';
import { useInView } from 'react-intersection-observer';

// Import required CSS
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

/**
 * ExactPDFViewer - PDF viewer using the exact file loading method specified
 * Uses react-pdf for rendering
 */
const ExactPDFViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  // Core state
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [scale, setScale] = useState(1.2);
  const [error, setError] = useState(null);
  
  // Redux and visibility tracking
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  // Load PDF file using your exact method
  useEffect(() => {
    async function fetchFile() {
      if (hasLoaded) return;
      
      setPdfFile(null);
      setDownloadProgress(0);

      try {
        console.log('Fetching PDF for:', { fileId, filePath });
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });
        
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          if (contentLength) {
            setDownloadProgress(receivedLength / parseInt(contentLength, 10));
          }
        }

        const blob = new Blob(chunks);
        const pdfUrl = URL.createObjectURL(blob);
        setPdfFile(pdfUrl);
        setHasLoaded(true);

        // If you need to dispatch state updates
        if (dispatch && typeof dispatch === 'function') {
          try {
            dispatch({
              type: 'UPDATE_PDF_VIEWER',
              payload: {
                fileId,
                filePath,
                page: 1,
                isVisible: inView
              }
            });
          } catch (e) {
            console.warn('Could not dispatch update:', e);
          }
        }

        console.log('PDF Loaded:', {
          fileId,
          filePath,
          initialPage: 1,
          isVisible: inView
        });
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
        setError(error.message || "Failed to load PDF");
      }
    }

    // Only fetch the file if we're not on the PDF viewer URL directly
    const currentPath = window.location.pathname;
    if (!currentPath.includes('/pdfjs-3.11.174-dist/web/')) {
      fetchFile();
    }

    return () => {
      if (pdfFile && !hasLoaded) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, filePath, dispatch, hasLoaded, inView, pdfFile]);

  // Document load success handler
  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    console.log(`PDF loaded with ${numPages} pages`);
  };
  
  // Navigation handlers
  const goToPrevPage = () => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  };
  
  const goToNextPage = () => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  };
  
  // Zoom handlers
  const zoomIn = () => setScale(prev => Math.min(prev + 0.2, 3));
  const zoomOut = () => setScale(prev => Math.max(prev - 0.2, 0.5));
  
  // Custom loading indicator with progress
  const LoadingIndicator = () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center',
      height: '100%',
      padding: '20px'
    }}>
      <Spinner size={50} />
      {downloadProgress > 0 && downloadProgress < 1 && (
        <div style={{ width: '80%', marginTop: '20px' }}>
          <div 
            style={{ 
              height: '4px', 
              width: `${downloadProgress * 100}%`, 
              backgroundColor: isDarkTheme ? '#48AFF0' : '#2B95D6', 
              borderRadius: '2px' 
            }} 
          />
          <div style={{ marginTop: '8px', textAlign: 'center' }}>
            {Math.round(downloadProgress * 100)}%
          </div>
        </div>
      )}
      <div style={{ marginTop: '20px' }}>Loading PDF...</div>
    </div>
  );

  return (
    <div 
      ref={ref}
      style={{ 
        height: '100%', 
        width: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        background: isDarkTheme ? '#1e1e1e' : '#f5f5f5',
        color: isDarkTheme ? '#fff' : '#000',
        ...style 
      }}
    >
      {pdfFile && (
        <div style={{ 
          padding: '10px', 
          display: 'flex', 
          justifyContent: 'space-between',
          borderBottom: `1px solid ${isDarkTheme ? '#444' : '#ddd'}`
        }}>
          <div>
            <Button
              icon="chevron-left"
              onClick={goToPrevPage}
              disabled={pageNumber <= 1}
              minimal
              small
            />
            <span style={{ margin: '0 10px' }}>
              Page {pageNumber} of {numPages || '--'}
            </span>
            <Button
              icon="chevron-right"
              onClick={goToNextPage}
              disabled={pageNumber >= numPages}
              minimal
              small
            />
          </div>
          
          <div>
            <Button
              icon="zoom-out"
              onClick={zoomOut}
              disabled={scale <= 0.5}
              minimal
              small
            />
            <span style={{ margin: '0 10px' }}>
              {Math.round(scale * 100)}%
            </span>
            <Button
              icon="zoom-in"
              onClick={zoomIn}
              disabled={scale >= 3}
              minimal
              small
            />
          </div>
        </div>
      )}
      
      <div style={{ 
        flex: 1, 
        overflow: 'auto', 
        display: 'flex', 
        justifyContent: 'center',
        padding: '10px'
      }}>
        {!pdfFile ? (
          <LoadingIndicator />
        ) : error ? (
          <div style={{ 
            padding: '20px', 
            textAlign: 'center', 
            color: 'red' 
          }}>
            <h3>Error Loading PDF</h3>
            <p>{error}</p>
          </div>
        ) : (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={(err) => {
              console.error('Error rendering PDF:', err);
              setError(`Error rendering PDF: ${err.message}`);
            }}
            loading={<LoadingIndicator />}
            noData={<div>No PDF file specified</div>}
            options={{
              cMapUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/cmaps/',
              cMapPacked: true,
              standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/standard_fonts/'
            }}
          >
            <Page
              key={`page_${pageNumber}`}
              pageNumber={pageNumber}
              scale={scale}
              renderTextLayer={true}
              renderAnnotationLayer={true}
              className={isDarkTheme ? 'dark-page' : ''}
            />
          </Document>
        )}
      </div>
      
      {/* Dark mode styles */}
      {isDarkTheme && (
        <style dangerouslySetInnerHTML={{
          __html: `
            .dark-page canvas {
              filter: invert(85%) hue-rotate(180deg) brightness(95%) contrast(85%);
            }
            .react-pdf__Page__textContent {
              filter: invert(100%) hue-rotate(180deg);
              color: black !important;
            }
          `
        }} />
      )}
    </div>
  );
};

export default ExactPDFViewer;
