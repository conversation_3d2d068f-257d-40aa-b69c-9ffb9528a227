import React, { useState } from "react";
import FileViewer from "./FileViewer/FileViewer";
import FilesTable from "./FilesTable/FilesTable";
import SplitPane from "react-split-pane";
import PDFReader from "./PDFReader";

function FilesHandler({ selectedWord, setSelectedWord,rightPaneWidth,toggleRightPane }){
  const [selectedFile, setSelectedFile] = useState({ id: null, name: null });

  const handleFileClick = (fileId, filename) => {
    setSelectedFile({ id: fileId, name: filename });
  };

  return (
    <div style={{ position: 'relative', height: '100%', width: '100%' }}>
      <SplitPane
        split="vertical"
        minSize={100}  // Minimum size of the left pane
        maxSize={window.innerWidth - 200}  // Assuming right pane should be at least 200px. Adjust as needed.
        defaultSize={rightPaneWidth}
        size={rightPaneWidth}
        onChange={() => {
          window.dispatchEvent(new Event('resize'));
        }}
      >
        <div className="main-content">
          {selectedFile.id && (
            <PDFReader fileId={selectedFile.id} fileName={selectedFile.name}
            selectedWord={selectedWord}setSelectedWord={setSelectedWord} />
       
          )}
        </div>
        
        <div className="fileviewer-sidebar">
          <FilesTable onFileClick={handleFileClick} />
        </div>
      </SplitPane>
    </div>
  );
}

export default FilesHandler;
