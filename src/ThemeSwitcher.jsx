import React, { useEffect, useContext } from "react";
import { AppContext } from "./AppContextProvider"; // Adjust based on your context setup

const ThemeSwitcher = () => {
  const { isDarkTheme } = useContext(AppContext); // Or however you're determining the theme

  useEffect(() => {
    const themeStyleId = "theme-style";
    const existingLink = document.getElementById(themeStyleId);

    const themeHref = isDarkTheme
      ? "/styles/vaim-flexlayout-dark.css"
      : "/styles/vaim-flexlayout-light.css";

    if (existingLink) {
      existingLink.href = themeHref; // Update the href of the existing link element
    } else {
      const linkElement = document.createElement("link");
      linkElement.id = themeStyleId;
      linkElement.rel = "stylesheet";
      linkElement.href = themeHref;
      document.head.appendChild(linkElement); // Add new link element to the head
    }
  }, [isDarkTheme]); // Dependency array ensures this effect runs when isDarkTheme changes

  return null; // This component does not render anything
};

export default ThemeSwitcher;
