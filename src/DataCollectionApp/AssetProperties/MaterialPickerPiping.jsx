import React, { useState, useEffect, useMemo, useContext, useRef } from "react";
import {
  Dialog,
  FormGroup,
  InputGroup,
  Button,
  MenuItem,
  Intent,
  Tag,
  DialogFooter,
  Icon,
  Spinner,
  DialogBody,
} from "@blueprintjs/core";
import { Select as BPSelect } from "@blueprintjs/select";
import { Select as AntdSelect } from "antd"; // Importing Ant Design Select
import { AgGridReact } from "ag-grid-react";
import { AppContext } from "../../AppContextProvider";
import { Container, Row, Col } from "react-grid-system";
import axios from "axios";
import { TablesMapping } from "./materialsTableMapping";
import { getPropertiesForCode } from "./CodeMapping";
import { PiCubeTransparentBold } from "react-icons/pi";
import { HiCube } from "react-icons/hi2";
import { useSelector } from "react-redux";
import ClearIcon from "./ClearIcon"; // Assuming you have this component for the clear icon

const { Option } = AntdSelect; // Destructure Option from Ant Design Select

// Example structure for materialsProperties
const initialMaterialsProperties = {
  "Construction Code": {
    type: "select",
    required: false,
    placeholder: "Select Construction Code",
    title: "Construction Code",
    disabled: true,
    category: "reference",
  },
  "Material of Construction": {
    type: "select",
    required: false,
    placeholder: "Select Material",
    title: "Material of Construction",
    category: "property",
  },
  "Tensile Strength (TS)": {
    type: "input",
    required: false,
    placeholder: "Tensile Strength (TS)",
    title: "Tensile Strength (TS) - psi",
    category: "property",
  },
  "Yield Strength (YS)": {
    type: "input",
    placeholder: "Yield Strength (YS)",
    required: false,
    title: "Yield Strength (YS) - psi",
    category: "property",
  },
  "Allowable Stress (S)": {
    type: "input",
    required: false,
    placeholder: "Allowable Stress (S)",
    title: "Allowable Stress (S) - psi",
    category: "property",
  },
  "Design Temperature": {
    type: "temp-input",
    required: false,
    placeholder: "Missing Design Temperature",
    title: "Design Temperature - F",
    category: "reference",
  },
  "Coefficient Y (Y)": {
    type: "select",
    required: false,
    placeholder: "Coefficient Y (Y)",
    title: "Coefficient Y (Y) B31.1-3",
    category: "design",
  },
  "Weld Joint Strength Reduction Factor (W)": {
    type: "select",
    required: false,
    placeholder: "Weld Joint Strength Reduction Factor (W)",
    title: "Weld Joint Strength Reduction Factor (W) B31.1-3",
    category: "design",
  },
  "Design Factor": {
    type: "select",
    required: false,
    placeholder: "Design Factor",
    title: "Design Factor B31.4-8",
    category: "design",
  },
  "Quality Factor​​​​​ (E)": {
    type: "select",
    required: false,
    placeholder: "Quality Factor​​​​​ (E)",
    title: "Quality Factor​​​​​ (E) B31.1-3",
    category: "design",
  },
  "Weld Joint Factor": {
    type: "select",
    required: false,
    placeholder: "Weld Joint Factor",
    title: "Weld Joint Factor B31.4",
    category: "design",
  },
  "Longitudinal Joint Factor": {
    type: "select",
    required: false,
    placeholder: "Longitudinal Joint Factor",
    title: "Longitudinal Joint Factor B31.8",
    category: "design",
  },
  // Add more fields as needed
};

function MaterialPickerPiping({
  onSave,
  isMaterialPickerComponentOpen,
  setIsMaterialPickerComponentOpen,
  propertyRowIndex,
  lookupDict,
  myToaster,
  selectedCode,
  currentTemperature,
}) {
  const { isDarkTheme } = useContext(AppContext);
  const [materialData, setMaterialData] = useState({ headers: [], rows: [] });
  const [inputValues, setInputValues] = useState({});
  const { assetData, setAssetData } = useContext(AppContext);
  const [isInputActive, setIsInputActive] = useState(false);
  const { selectedSheet, setSelectedSheet } = useContext(AppContext);
  const materialsGridApiRef = useRef(null);
  const [materialsGridApi, setMaterialsGridApi] = useState(null);
  const [materialsProperties, setMaterialsProperties] = useState(initialMaterialsProperties);
  const [selectedCodeForTable, setSelectedCodeForTable] = useState(selectedCode);
  const [isChangingInputs, setIsChangingInputs] = useState(false);
  const [visibleInputs, setVisibleInputs] = useState(new Set(Object.keys(initialMaterialsProperties)));
  const [isTransparent, setIsTransparent] = useState(false);
  const [tableMaterialId, setTableMaterialId] = useState(null);
  const [currentMaterialId, setCurrentMaterialId] = useState(null);
  const { user } = useSelector((state) => state.auth);

  // Define fields that should always be visible
  const alwaysVisibleFields = new Set([
    "Construction Code",
    "Material of Construction",
    "Tensile Strength (TS)",
    "Yield Strength (YS)",
    "Allowable Stress (S)",
    "Design Temperature",
  ]);

  useEffect(() => {
    const updateVisibleInputs = (selectedCode) => {
      let inputsToShow = new Set(alwaysVisibleFields);

      switch (selectedCode) {
        case "B31.1":
        case "B31.3":
          ["Coefficient Y (Y)", "Quality Factor​​​​​ (E)", "Weld Joint Strength Reduction Factor (W)"].forEach((field) => inputsToShow.add(field));
          break;
        case "B31.4":
          ["Design Factor", "Weld Joint Factor"].forEach((field) => inputsToShow.add(field));
          break;
        case "B31.8":
          ["Design Factor", "Longitudinal Joint Factor"].forEach((field) => inputsToShow.add(field));
          break;
        default:
          break;
      }

      setVisibleInputs(inputsToShow);
    };

    updateVisibleInputs(selectedCodeForTable);
  }, [selectedCodeForTable]);

  const formatDataForApi = () => {
    return Object.entries(inputValues)
      .filter(([header]) => header !== "Design Temperature")
      .map(([header, newValue]) => ({
        header,
        indexrow: `"${propertyRowIndex}"`,
        new_value: newValue,
      }));
  };

  useEffect(() => {
    const updateMaterialsProperties = (code) => {
      const propertiesForCode = getPropertiesForCode(code);

      const codeSpecificProperties = Object.keys(propertiesForCode).reduce(
        (acc, key) => {
          acc[key] = {
            type: initialMaterialsProperties[key]?.type,
            required: false,
            placeholder: key,
            title: initialMaterialsProperties[key]?.title,
            category: initialMaterialsProperties[key]?.category,
          };
          return acc;
        },
        {}
      );

      setMaterialsProperties({
        ...initialMaterialsProperties,
        ...codeSpecificProperties,
      });

      setTimeout(() => {
        setIsChangingInputs(false);
      }, 1000);
    };

    updateMaterialsProperties(selectedCodeForTable);
  }, [selectedCodeForTable]);

  const sendUpdateRequestToApi = async () => {
    setIsChangingInputs(true);

    const rowIndex = assetData.findIndex(
      (row) => Number(row.rowIndex) === Number(propertyRowIndex)
    );

    if (rowIndex !== -1) {
      const updatesJson = formatDataForApi();
      updatesJson.push({
        header: "Material ID",
        indexrow: `"${propertyRowIndex}"`,
        new_value: tableMaterialId,
      });

      const template = selectedSheet;
      const url = `${process.env.REACT_APP_DATA_API}/data/bulk_update_cells/`;
      const userEmail = user.email;
      try {
        const session_id = localStorage.getItem("session_id");
        const response = await axios.put(url, null, {
          params: {
            updatesjson: JSON.stringify(updatesJson),
            template,
            user: userEmail,
          },
          headers: {
            "session-id": session_id,
          },
        });

        myToaster.current?.show({
          message: `Materials Properties updated at ${template}`,
          intent: Intent.SUCCESS,
          timeout: 5000,
          className: "toaster-font",
          icon: "updated",
        });
        setIsChangingInputs(false);

        const materialOfConstruction = inputValues["Material of Construction"];

        onSave(materialOfConstruction, tableMaterialId);

        const updatedAssetData = assetData.map((row) => {
          if (Number(row.rowIndex) === Number(propertyRowIndex)) {
            return {
              ...row,
              ...inputValues,
            };
          }
          return row;
        });

        setAssetData(updatedAssetData);
        setCurrentMaterialId(tableMaterialId);

        const updateMaterialId = assetData.map((row) => {
          if (Number(row.rowIndex) === Number(propertyRowIndex)) {
            return {
              ...row,
              "Material ID": tableMaterialId,
            };
          }
          return row;
        });

        setAssetData(updateMaterialId);

        setIsMaterialPickerComponentOpen(false);

        console.log("Update successful", response.data);
      } catch (error) {
        console.error("Failed to update cells:", error);
        setIsChangingInputs(false);

        myToaster.current?.show({
          message: `Error ${error}`,
          intent: Intent.DANGER,
          timeout: 5000,
          className: "toaster-font",
          icon: "error",
        });
      }
    } else {
      console.error("Row not found in assetData");
    }
  };

  function updateInputValuesWithNewData(newData) {
    setInputValues((prevValues) => ({
      ...prevValues,
      ...newData,
    }));
  }

  function getAdditionalFields(
    construction_codes,
    coefy_designFactor,
    qualityFactorWeldJointLongJoint,
    weldJointStrengthReductionFactor
  ) {
    switch (construction_codes) {
      case "B31.1":
      case "B31.3":
        return {
          "Coefficient Y (Y)": coefy_designFactor?.toString() || "",
          "Quality Factor​​​​​ (E)": qualityFactorWeldJointLongJoint?.toString() || "",
          "Weld Joint Strength Reduction Factor (W)": weldJointStrengthReductionFactor?.toString() || "",
        };
      case "B31.4":
        return {
          "Design Factor": coefy_designFactor?.toString() || "",
          "Weld Joint Factor": qualityFactorWeldJointLongJoint?.toString() || "",
        };
      case "B31.8":
        return {
          "Design Factor": coefy_designFactor?.toString() || "",
          "Longitudinal Joint Factor": qualityFactorWeldJointLongJoint?.toString() || "",
        };
      default:
        console.log("Unhandled construction code:", construction_codes);
        return {};
    }
  }

  const handleRowSelected = async (event) => {
    const selectedNodes = materialsGridApi.getSelectedNodes();
    const rowData = selectedNodes.length > 0 ? selectedNodes[0].data : null;
    setSelectedCodeForTable(rowData?.construction_codes || "");

    setTableMaterialId(rowData?.id || null);

    if (!rowData) return;

    const materialOfConstructionOptions = lookupDict["Material of Construction Pipe"];
    const materialExists = materialOfConstructionOptions?.includes(rowData.map) || false;

    const newData = {
      "Yield Strength (YS)": rowData.minimum_yield_strength_ys?.toString() || "",
      "Tensile Strength (TS)": rowData.minimum_tensile_strength_ts?.toString() || "",
      "Material of Construction": materialExists ? rowData.map.toString() : "",
      ...getAdditionalFields(
        rowData.construction_codes,
        rowData.b31_1_3_coefficient__y_b31_4_8_design_factor_f,
        rowData.b31_1_3_quality_factor__e_b31_4_weld_joint_factor_e_b31_8_longitudinal_joint_factor_e,
        rowData.b31_1_3_weld_joint_strength_reduction_factor__w
      ),
    };

    try {
      const response = await axios.get(
        `${process.env.REACT_APP_MATERIALS_API}/material_properties/allowable_stress`,
        {
          params: {
            id: String(rowData.id),
            temperature: String(inputValues["Design Temperature"]),
            template: selectedSheet,
          },
        }
      );

      const newAllowableStress = response.data.as?.toString() || "";
      updateInputValuesWithNewData({
        "Allowable Stress (S)": newAllowableStress,
      });

      updateInputValuesWithNewData(newData);
    } catch (error) {
      console.error("API call failed:", error);
      updateInputValuesWithNewData(newData);
    }
  };

  const convertDataForAgGrid = (data) => {
    const { headers, rows } = data;

    const transformedRows = rows.map((row) =>
      row.reduce((obj, value, index) => {
        const key = headers[index];
        obj[key] = value;
        return obj;
      }, {})
    );

    return { headers, rows: transformedRows };
  };

  useEffect(() => {
    if (!assetData || propertyRowIndex === undefined) return;

    const currentRowData =
      assetData.find((row) => Number(row.rowIndex) === Number(propertyRowIndex)) || {};

    setCurrentMaterialId(currentRowData["Material ID"]);

    const initialValues = {};
    Object.entries(materialsProperties).forEach(([key, { placeholder }]) => {
      initialValues[key] = currentRowData[key] || "";
    });
    setInputValues(initialValues);
    setInputValues((values) => ({
      ...values,
      "Design Temperature": currentTemperature,
    }));
    setInputValues((values) => ({
      ...values,
      "Construction Code": selectedCodeForTable,
    }));
  }, [assetData, propertyRowIndex, materialsProperties, selectedCodeForTable, currentTemperature]);

  const clearAllFields = () => {
    setInputValues((prevValues) => ({
      ...prevValues,
      "Material of Construction": "",
      "Tensile Strength (TS)": "",
      "Yield Strength (YS)": "",
      "Allowable Stress (S)": "",
      "Material ID": "",
    }));
    setTableMaterialId(null);
    setCurrentMaterialId(null);
  };

  const renderInputField = (key, details) => {
    const shouldDisable = !visibleInputs.has(key) && !alwaysVisibleFields.has(key);
    const isTempEmpty = currentTemperature === "";
    let showWarning = false;

    if ((key === "Allowable Stress (S)" || key === "Design Temperature") && isTempEmpty) {
      showWarning = true;
    }

    if (key === "Material of Construction") {
      const options = lookupDict["Material of Construction Pipe"] || [];

      return (
        <div style={{ display: "flex", alignItems: "center" }}>
          <AntdSelect
            popupMatchSelectWidth={false}
            id={key}
            name={key}
            placeholder={details.placeholder}
            value={String(inputValues[key]) || ""}
            onChange={(value) => handleSelectChange(key, value)}
            style={{ width: "100%" }}
            showSearch
            filterOption={(input, option) =>
              String(option.props.children).toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            allowClear
            onClear={() => handleSelectChange(key, "")}
          >
            {options.map((option) => (
              <Option key={option} value={String(option)}>
                {option}
              </Option>
            ))}
          </AntdSelect>
          <Button
            minimal={true}
            icon={<ClearIcon isDarkTheme={isDarkTheme} />}
            onClick={clearAllFields}
            style={{ marginLeft: "5px" }}
          />
        </div>
      );
    }

    switch (details.type) {
      case "input":
      case "temp-input":
        return (
          <InputGroup
            id={key}
            name={key}
            placeholder={details.placeholder}
            value={shouldDisable ? "" : inputValues[key] || ""}
            onChange={handleInputChange}
            disabled={details.type === "temp-input" || shouldDisable}
            leftIcon={showWarning ? <Icon icon="warning-sign" intent="warning" /> : null}
            intent={showWarning ? Intent.WARNING : Intent.NONE}
          />
        );
      case "select":
        const options = lookupDict[key] || details.options || [];
        return (
          <BPSelect
            fill={true}
            items={options}
            itemPredicate={(query, item) =>
              item && item.toLowerCase().indexOf(query.toLowerCase()) >= 0
            }
            itemRenderer={(item, { handleClick, modifiers }) => (
              <MenuItem
                key={item}
                onClick={handleClick}
                text={item}
                active={modifiers.active}
                disabled={modifiers.disabled}
              />
            )}
            onItemSelect={(item) => handleSelectChange(key, item)}
            disabled={shouldDisable || key === "Construction Code"}
          >
            <Button
              className="ellipsis"
              text={shouldDisable ? details.placeholder : inputValues[key] || details.placeholder}
              rightIcon="double-caret-vertical"
              disabled={shouldDisable || key === "Construction Code"}
            />
          </BPSelect>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_MATERIALS_API}/material_properties`,
          { params: { template: String(selectedSheet) } }
        );

        let { headers, rows } = response.data;

        let transformedRows = rows.map((row) =>
          row.reduce((acc, value, index) => {
            acc[headers[index]] = value === null || value === "None" ? "" : value;
            return acc;
          }, {})
        );

        if (currentMaterialId) {
          const identifiedRowIndex = transformedRows.findIndex(row => row.id === currentMaterialId);
          if (identifiedRowIndex > -1) {
            const [identifiedRow] = transformedRows.splice(identifiedRowIndex, 1);
            transformedRows.unshift(identifiedRow);
          }
        }

        setMaterialData({ headers, rows: transformedRows });
      } catch (error) {
        console.error("Error fetching material data:", error);
      }
    };

    fetchData();
  }, [currentMaterialId, selectedSheet]);

  const gridOptions = {
    floatingFiltersHeight: 35,
  };

  const getColumnWidth = (header) => {
    switch (header) {
      case "Table":
        return 95;
      case "Grade/Temper":
        return 140;
      case "Class According Desing Code":
        return 170;
      case "B31.1-3  Coefficient, Y/ B31.4-8 Design Factor,F":
        return 170;
      case "B31.1-3 Quality factor, E/B31.4 Weld Joint Factor,E/ B31.8 Longitudinal Joint Factor,E":
        return 170;
      case "P Number":
        return 115;
      default:
        return 135;
    }
  };

  const determineHighlightColumn = (currentTemperature, headers) => {
    const temps = headers.filter(h => h.startsWith('as_'))
      .map(h => parseInt(h.replace('as_', '')))
      .sort((a, b) => a - b);

    const closest = temps.find(t => t >= currentTemperature);
    return closest ? `as_${closest}` : null;
  };

  const highlightColumn = determineHighlightColumn(currentTemperature, materialData.headers);

  const columnDefs = useMemo(() => {
    const actualFieldNames = Object.values(TablesMapping);
    const nonEmptyFields = materialData.headers.filter((header) =>
      actualFieldNames.includes(header)
    );

    const normalColumns = [];
    const asColumns = [];

    Object.entries(TablesMapping).forEach(([headerName, actualFieldName]) => {
      if (!nonEmptyFields.includes(actualFieldName)) return;

      const isAsColumn = actualFieldName.startsWith("as_");
      const commonColumnProps = {
        filter: "agTextColumnFilter",
        floatingFilter: true,
        suppressHeaderMenuButton: true,
        floatingFilterComponentParams: { suppressFilterButton: true },
        suppressFloatingFilterButton: true,
        sortable: true,
        resizable: true,
        cellStyle: { fontSize: "13px" },
        pinned:
          actualFieldName === "construction_codes" ||
          actualFieldName === "material_specification",
        hide:
          actualFieldName === "id" ||
          actualFieldName === "material_specification_name" ||
          actualFieldName === "map" ||
          actualFieldName === "table_n",
        width: getColumnWidth(headerName),
        headerName,
        menuTabs: [],
        field: actualFieldName,
      };

      if (isAsColumn) {
        asColumns.push({
          ...commonColumnProps,
          cellStyle: (params) => {
            return params.colDef.field === highlightColumn ? { backgroundColor: 'rgba(180, 93, 195, 0.2)' } : null;
          },
        });
      } else {
        normalColumns.push({ ...commonColumnProps });
      }
    });

    if (asColumns.length > 0) {
      normalColumns.push({
        headerName: "Allowable Stress S, psi, for Metal Temperature, °F",
        children: asColumns.map((column) => ({
          ...column,
          headerName: column.headerName.slice(3),
          width: "80",
        })),
        suppressHeaderMenuButton: true,
      });
    }

    return normalColumns;
  }, [materialData.headers, highlightColumn]);

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setInputValues((prevValues) => ({ ...prevValues, [name]: value }));
  };

  const handleSelectChange = (key, item) => {
    setInputValues((prevValues) => ({ ...prevValues, [key]: item }));
  };

  const onGridReady = (params) => {
    materialsGridApiRef.current = params.api;
    setMaterialsGridApi(params.api);
  };

  const getRowStyle = (params) => {
    if (params.data.id === currentMaterialId) {
      return { backgroundColor: "rgba(180, 93, 195, 0.25)", border: "1.2px solid  #B45DC3" };
    }
  };

  return (
    <>
      {isChangingInputs && (
        <div className="loading-overlay">
          <Spinner></Spinner>
        </div>
      )}

      <Dialog
        usePortal={true}
        minHeight={700}
        className={`${isDarkTheme ? "bp5-dark" : ""} ${
          isTransparent ? "transparent-content" : ""
        }`}
        style={{
          width: "80%",
          height: "70%",
          overflowY: "1",
          position: "relative",
        }}
        isOpen={isMaterialPickerComponentOpen}
        onClose={() => setIsMaterialPickerComponentOpen(false)}
        icon="th"
        title={
          <div
            style={{
              textAlign: "left",
              marginLeft: "5px",
              fontWeight: "bold",
              fontSize: "1.2em",
            }}
          >
            Material Properties
          </div>
        }
        canEscapeKeyClose={true}
        canOutsideClickClose={true}
      >
        <Button
          icon={isTransparent ? <HiCube /> : <PiCubeTransparentBold />}
          minimal={true}
          onClick={() => setIsTransparent(!isTransparent)}
          style={{
            position: "absolute",
            top: "5px",
            right: "50px",
            zIndex: 1000,
          }}
        />

        <DialogBody className={isTransparent ? "transparent-content" : ""}>
          <Container fluid>
            <Row>
              <Col xs={12} md={3}>
                <Tag minimal={false} intent="primary" className="vaim-tag">
                  Material Properties
                </Tag>
                {Object.entries(materialsProperties)
                  .filter(([_, details]) => details.category === "property")
                  .map(([key, details]) => (
                    <FormGroup
                      key={key}
                      label={
                        <>
                          <b className="ellipsis material-select-button">
                            {details.title}
                          </b>
                          {details.required && (
                            <Tag className="compact-tag" intent={Intent.SUCCESS}>
                              Required
                            </Tag>
                          )}
                        </>
                      }
                      labelFor={key}
                    >
                      {renderInputField(key, details)}
                    </FormGroup>
                  ))}
              </Col>

              <Col xs={12} md={6}>
                <Tag minimal={false} intent="primary" className="vaim-empty-tag"></Tag>
                <Row>
                  {Object.entries(materialsProperties)
                    .filter(([_, details]) => details.category === "design")
                    .reduce(
                      (acc, [key, details], index, array) => {
                        const columnIndex = index < array.length / 2 ? 0 : 1;
                        if (!acc[columnIndex]) acc[columnIndex] = [];
                        acc[columnIndex].push(
                          <FormGroup
                            key={key}
                            label={
                              <>
                                <b className="ellipsis material-select-button">
                                  {details.title}
                                </b>
                                {details.required && (
                                  <Tag className="compact-tag" intent={Intent.SUCCESS}>
                                    Required
                                  </Tag>
                                )}
                              </>
                            }
                            labelFor={key}
                          >
                            {renderInputField(key, details)}
                          </FormGroup>
                        );
                        return acc;
                      },
                      [[], []]
                    )
                    .map((columnInputs, index) => (
                      <Col key={index} xs={12} md={6}>
                        {columnInputs}
                      </Col>
                    ))}
                </Row>
              </Col>

              <Col xs={12} md={3}>
                <Tag minimal={false} className="purple-tag">
                  Reference Values
                </Tag>
                {Object.entries(materialsProperties)
                  .filter(([_, details]) => details.category === "reference")
                  .map(([key, details]) => (
                    <FormGroup
                      key={key}
                      label={
                        <>
                          <b className="ellipsis material-select-button">
                            {details.title}
                          </b>
                          {details.required && (
                            <Tag className="compact-tag" intent={Intent.SUCCESS}>
                              Required
                            </Tag>
                          )}
                        </>
                      }
                      labelFor={key}
                    >
                      {renderInputField(key, details)}
                    </FormGroup>
                  ))}
              </Col>
            </Row>
          </Container>
          <div
            className={
              isDarkTheme
                ? "ag-theme-alpine-dark materials"
                : "ag-theme-alpine"
            }
            style={{ width: "100%", height: "35vh", fontSize: "0.8rem" }}
          >
            <AgGridReact
              className="materials"
              style={{ fontSize: "0.8rem" }}
              rowData={materialData.rows}
              columnDefs={columnDefs}
              rowHeight={24}
              rowSelection="single"
              headerHeight={35}
              gridOptions={gridOptions}
              onSelectionChanged={handleRowSelected}
              onGridReady={onGridReady}
              getRowStyle={getRowStyle}
            />
          </div>
        </DialogBody>

        <DialogFooter
          actions={
            <>
              <Button
                intent="danger"
                text="Cancel"
                onClick={() => setIsMaterialPickerComponentOpen(false)}
              />
              <Button
                intent="primary"
                text="Save"
                onClick={sendUpdateRequestToApi}
              />
            </>
          }
        />
      </Dialog>
    </>
  );
}

export default MaterialPickerPiping;
