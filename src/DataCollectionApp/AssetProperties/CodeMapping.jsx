const CodeMapping = {
  "CommonProperties": {
    "B31.1_B31.3": {
      "Coefficient Y (Y)": "b31_1_3_coefficient__y_b31_4_8_design_factor_f",
      "Quality Factor​​​​​ (E)": "b31_1_3_quality_factor__e_b31_4_weld_joint_factor_e_b31_8_longitudinal_joint_factor_e",
      "Weld Joint Strength Reduction Factor (W)": "b31_1_3_weld_joint_strength_reduction_factor__w",
    },
    "B31.4_B31.8": {
      "Design Factor": "b31_1_3_coefficient__y_b31_4_8_design_factor_f",
    }
  },
  "UniqueProperties": {
    "B31.8": {
      "Longitudinal Joint Factor": "unique_property_for_b31_8",
    },
    "B31.4": {
      "Weld Joint Factor": "unique_property_for_b31_4",
    }
  },
  "B31.1": "B31.1_B31.3",
  "B31.3": "B31.1_B31.3",
  "B31.4": ["B31.4_B31.8", "B31.4"],
  "B31.8": ["B31.4_B31.8", "B31.8"] // Now supports both common and unique properties
};

export function getPropertiesForCode(code) {
  if (!code || typeof code !== 'string') {
    console.error("Invalid code provided.");
    return {};
  }

  let properties = {};
  code = code.replace("ASME", "").trim();
  const reference = CodeMapping[code];

  if (!reference) {
    console.error("Code not found.");
    return {};
  }

  // Handle both single and multiple references
  const references = Array.isArray(reference) ? reference : [reference];
  references.forEach(ref => {
    if (CodeMapping.CommonProperties[ref]) {
      Object.assign(properties, CodeMapping.CommonProperties[ref]);
    } else if (CodeMapping.UniqueProperties[ref]) {
      Object.assign(properties, CodeMapping.UniqueProperties[ref]);
    }
  });

  return properties;
}


