import React, { useContext, useState, useEffect, useCallback } from "react";
import {
  FormGroup,
  Tag,
  InputGroup,
  Button,
  Intent,
  MenuItem,
  NonIdealState,
  ResizeSensor,
  Icon,
} from "@blueprintjs/core";
import { Select } from "@blueprintjs/select";
import { Container, Row, Col } from "react-grid-system";
import { propertyDataTypes } from "./dataTypes";
import { propertyDataTypesHE } from "./dataTypesHE";
import { AppContext } from "../../AppContextProvider";
import { DateInput3 } from "@blueprintjs/datetime2";
import { format, parse, isValid } from "date-fns";
import { useSelector } from "react-redux";
import MaterialPickerPiping from "./MaterialPickerPiping";
import AssociatedAssetsDialog from './AssociatedAssetsDialog';
import "@blueprintjs/datetime2/lib/css/blueprint-datetime2.css";

function GeneralProperties({
  selectKeys,
  handlePropertyChange,
  propertyValues,
  inputIntents,
  handlePropertyInput,
  updateCell,
  selectedSheet,
  lastFocusedRef,
  myToaster,
}) {
  const { isDarkTheme } = useContext(AppContext);
  const { lookupValues, setLookupValues } = useContext(AppContext);
  const { isMaterialPickerOpen, setIsMaterialPickerOpen } =
    useContext(AppContext);
  const { assetData, setAssetData } = useContext(AppContext);
  const { dropdownRules, setDropdownRules } = useContext(AppContext);
  const [isAssociatedAssetsDialogOpen, setIsAssociatedAssetsDialogOpen] = useState(false);

  const [selectedNPS, setSelectedNPS] = useState('');
  const [selectedOD, setSelectedOD] = useState('');
  const [selectedSchedule, setSelectedSchedule] = useState('');
  const [selectedThickness, setSelectedThickness] = useState('');

  const { npsODdata, pipingSchedule } = useContext(AppContext);

  const handleNPSChange = (nps) => {
    const od = npsODdata.rows.find(row => String(row[0]) === nps)?.[1] || '';
    setSelectedNPS(nps);
    setSelectedOD(od);
    setSelectedSchedule('<Select>');
    setSelectedThickness('');
    handlePropertyChange('Nominal Pipe Size (NPS)', nps);
    handlePropertyChange('Outside Diameter', od);
    handlePropertyChange('Piping Schedule', '<Select>');
    handlePropertyChange('Nominal Thickness', '');
    updateCell('Nominal Pipe Size (NPS)', nps);
    updateCell('Outside Diameter', od);
  };

  const handleODChange = (od) => {
    const nps = npsODdata.rows.find(row => String(row[1]) === od)?.[0] || '';
    setSelectedOD(od);
    setSelectedNPS(nps);
    setSelectedSchedule('<Select>');
    setSelectedThickness('');
    handlePropertyChange('Outside Diameter', od);
    handlePropertyChange('Nominal Pipe Size (NPS)', nps);
    handlePropertyChange('Piping Schedule', '<Select>');
    handlePropertyChange('Nominal Thickness', '');
    updateCell('Outside Diameter', od);
    updateCell('Nominal Pipe Size (NPS)', nps);
  };

  const handleScheduleChange = (schedule) => {
    setSelectedSchedule(schedule);
    handlePropertyChange('Piping Schedule', schedule);
    updateCell('Piping Schedule', schedule);
  };

  const handleThicknessChange = (thickness) => {
    setSelectedThickness(thickness);
    handlePropertyChange('Nominal Thickness', thickness);
    updateCell('Nominal Thickness', thickness);
  };

  const handleMaterialPickerSave = (materialOfConstruction, materialID) => {
    handlePropertyChange("Material of Construction", materialOfConstruction);
    handlePropertyChange("Material ID", materialID);
    propertyValues["Material of Construction"] = materialOfConstruction;
    propertyValues["Material ID"] = materialID;
  };

  const openMaterialPicker = () => {
    setIsMaterialPickerOpen(true);
  };

  const filterOption = (query, option) => {
    if (option === null || option === undefined || typeof option !== 'string') {
      console.log("debug", query, option);
      return false;
    }
    return option.toLowerCase().indexOf(query.toLowerCase()) >= 0;
  };

  const renderOption = (
    option,
    { handleClick, handleFocus, modifiers, query },
  ) => {
    if (!modifiers.matchesPredicate) {
      return null;
    }
    return (
      <MenuItem
        active={modifiers.active}
        disabled={modifiers.disabled}
        key={option}
        onClick={handleClick}
        onFocus={handleFocus}
        roleStructure="listoption"
        text={option}
      />
    );
  };

  const totalProperties = Object.entries(
    propertyDataTypes[selectedSheet] || {},
  ).length;
  const [columns, setColumns] = useState(4);

  function handleResize(entries) {
    const width = entries[0].contentRect.width;
    let calculatedColumns = 5;
    if (width <= 300) {
      calculatedColumns = 1;
    } else if (width <= 500) {
      calculatedColumns = 2;
    } else if (width <= 700) {
      calculatedColumns = 3;
    } else if (width <= 900) {
      calculatedColumns = 4;
    } else if (width <= 1200) {
      calculatedColumns = 5;
    }
    setColumns(calculatedColumns);
  }

  let placeholdersNeeded =
    totalProperties % 4 !== 0 ? 4 - (totalProperties % 4) : 0;

  const formatDate = useCallback((date) => {
    return format(date, "MM/dd/yyyy");
  }, []);

  const parseDate = useCallback((str) => {
    const parsedDate = parse(str, "MM/dd/yyyy", new Date());
    return isValid(parsedDate) ? parsedDate : undefined;
  }, []);

  function validateInput(value, type) {
    switch (type) {
      case "Numeric":
        return !isNaN(value);
      case "Date":
        if (value === "") return true;
        const datePattern =
          /^(0?[1-9]|1[0-2])\/(0?[1-9]|[12][0-9]|3[01])\/\d{4}$|(0?[1-9]|1[0-2])\/(0?[1-9]|[12][0-9]|3[01])\/\d{4}$/;
        return datePattern.test(value);
      default:
        return true;
    }
  }

  const handleDateChange = (dateStr, isUserChange, property) => {
    if (dateStr === null || dateStr === "") {
      updateCell(property, "");
      return;
    }

    if (isUserChange && dateStr) {
      if (dateStr.startsWith("00")) {
        dateStr = "20" + dateStr.slice(2);
      }

      const parsedDate = parse(dateStr, "yyyy-MM-dd", new Date());

      if (isValid(parsedDate)) {
        const formattedDate = format(parsedDate, "MM/dd/yyyy");
        handlePropertyChange(property, formattedDate);
        const isDateValid = validateInput(formattedDate || "", "Date");

        if (isDateValid) {
          updateCell(property, formattedDate);
        }
      }
    }
  };

  const assetClassification  = useSelector(
    (state) => state.appConfiguration.selectedAssetClass
  );

  const renderAdditionalFields =
    assetClassification &&
    propertyDataTypesHE["Asset Classification"] &&
    propertyDataTypesHE["Asset Classification"][assetClassification];

  const openAssociatedAssetsDialog = () => {
    setIsAssociatedAssetsDialogOpen(true);
  };

  const handleAssociatedAssetsSave = (selectedAssets) => {
    const selectedAssetsString = JSON.stringify(selectedAssets.map((asset) => ({
      Plant: asset.Plant,
      Unit: asset.Unit,
      System: asset.System,
      'Tag Name': asset['Tag Name'],
    })));
    handlePropertyChange('Associated Assets', selectedAssets);
    updateCell("Associated Assets", selectedAssetsString);
    setIsAssociatedAssetsDialogOpen(false);
  };  

  const renderPropertyInput = (property, details) => {
    if (property === 'Associated Assets' && details.type === 'Assets Select') {
      let selectedAssetsData = propertyValues['Associated Assets'] || [];
      const selectedAssetsCount = selectedAssetsData.length;
  
      return (
        <>
          <Button onClick={openAssociatedAssetsDialog}>
            Select Associated Assets
            {selectedAssetsCount > 0 && (
              <span className="selected-count">{selectedAssetsCount}</span>
            )}
          </Button>
          {isAssociatedAssetsDialogOpen && (
            <AssociatedAssetsDialog
              onSave={handleAssociatedAssetsSave}
              onClose={() => setIsAssociatedAssetsDialogOpen(false)}
              initialSelectedAssets={selectedAssetsData}
            />
          )}
        </>
      );
    }
    else if (
      details.type === "Select" &&
      property === "Material of Construction" &&
      selectedSheet === "Piping"
    ) {
      return (
        <>
          <Button
            fill={true}
            className="material-select-button ellipsis"
            onClick={openMaterialPicker}
            rightIcon={<Icon icon="double-caret-vertical" />}
            text={propertyValues[property] || "Select Material"}
            icon={
              assetData[0]["Material ID"] &&
              assetData[0]["Material ID"] !== "" ? (
                <Icon intent="success" icon="tick" />
              ) : null
            }
          />
          {isMaterialPickerOpen && (
            <MaterialPickerPiping
              onSave={handleMaterialPickerSave}
              isMaterialPickerComponentOpen={isMaterialPickerOpen}
              setIsMaterialPickerComponentOpen={setIsMaterialPickerOpen}
              propertyRowIndex={assetData[0].rowIndex}
              lookupDict={lookupValues}
              myToaster={myToaster}
              selectedCode={propertyValues["Construction Code"]}
              currentTemperature={propertyValues["Design Temperature"]}
            />
          )}
        </>
      );
    } else if (
      details.type === "SelectRule" &&
      property === "Asset Classification"
    ) {
      return (
        <Select
          intent={inputIntents[property]}
          fill={true}
          items={dropdownRules["Template"][selectedSheet]}
          itemPredicate={filterOption}
          itemRenderer={renderOption}
          noResults={<MenuItem disabled={true} text="No results." />}
          onItemSelect={(item) => {
            handlePropertyChange(property, item);
            updateCell(property, item);
          }}
        >
          <Button
            className="material-select-button ellipsis"
            text={propertyValues[property] || "<Select>"}
            rightIcon={<Icon icon="double-caret-vertical" />}
          />
        </Select>
      );
    } else if (details.type === "SelectRule") {
      return (
        <Select
          intent={inputIntents[property]}
          fill={true}
          items={
            dropdownRules[property] &&
            assetData[0] &&
            assetData[0]["Asset Classification"]
              ? [
                  "<Select>",
                  ...(Array.isArray(
                    dropdownRules[property][
                      assetData[0]["Asset Classification"]
                    ],
                  )
                    ? dropdownRules[property][
                        assetData[0]["Asset Classification"]
                      ]
                    : [
                        dropdownRules[property][
                          assetData[0]["Asset Classification"]
                        ],
                      ]),
                ]
              : ["<Select>"]
          }
          itemPredicate={filterOption}
          itemRenderer={renderOption}
          noResults={<MenuItem disabled={true} text="No results." />}
          onItemSelect={(item) => {
            handlePropertyChange(property, item);
            updateCell(property, item);
          }}
          value={propertyValues[property] || ""}
        >
          <Button
            className="material-select-button ellipsis"
            text={propertyValues[property] || "<Select>"}
            rightIcon={<Icon icon="double-caret-vertical" />}
          />
        </Select>
      );
    } else if (details.type === "Select") {
      let items;
      if (property === "Nominal Pipe Size (NPS)") {
        items = npsODdata.rows.map(row => String(row[0]));
      } else if (property === "Outside Diameter") {
        items = npsODdata.rows.map(row => String(row[1]));
      } else if (property === "Piping Schedule") {
        items = ["<Select>", ...pipingSchedule.rows.map(row => String(row[0]))];  // Including "<Select>"
      } else {
        items = lookupValues[property] || [];
      }
      return (
        <Select
          intent={inputIntents[property]}
          fill={true}
          items={items}
          itemPredicate={filterOption}
          itemRenderer={renderOption}
          noResults={<MenuItem disabled={true} text="No results." />}
          onItemSelect={(item) => {
            if (property === "Nominal Pipe Size (NPS)") {
              handleNPSChange(item);
            } else if (property === "Outside Diameter") {
              handleODChange(item);
            } else if (property === "Piping Schedule") {
              handleScheduleChange(item);
            } else if (property === "Nominal Thickness") {
              handleThicknessChange(item);
            } else {
              handlePropertyChange(property, item);
            }
            updateCell(property, item);
          }}
        >
          <Button
            className="material-select-button ellipsis"
            text={propertyValues[property] || "<Select>"}
            rightIcon={<Icon icon="double-caret-vertical" />}
          />
        </Select>
      );
    } else if (details.type === "Date") {
      return (
        <DateInput3
          formatDate={formatDate}
          parseDate={parseDate}
          placeholder="mm/dd/yyyy"
          value={propertyValues[property]}
          onChange={(date) => handleDateChange(date, true, property)}
          fill={true}
          className={`compact-input ${isDarkTheme ? "bp5-dark" : ""}`}
          maxDate={new Date(2050, 12, 31)}
          minDate={new Date(1800, 0, 1)}
        />
      );
    } else {
      return (
        <InputGroup
          id={property}
          disabled={details.disabled}
          onFocus={(e) => {
            lastFocusedRef.current = {
              type: "input",
              details: { id: e.target.id },
            };
          }}
          className="compact-input"
          value={propertyValues[property] || ""}
          onChange={(e) => handlePropertyChange(property, e.target.value)}
          autoComplete="off"
          intent={inputIntents[property]}
          onBlur={(e) =>
            handlePropertyInput(e || "", property) &
            handlePropertyChange(property, e.target.value)
          }
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handlePropertyInput(e || "", property);
            }
          }}
        />
      );
    }
  };

  return (
    <div
      className={`property-container ${isDarkTheme ? "bp5-dark" : ""}`}
      style={{ height: "100%", overflowY: "auto", paddingTop: "10px" }}
    >
      {!selectedSheet || !propertyDataTypes[selectedSheet] ? (
        <div className="centered-non-ideal" style={{ height: "100%" }}>
          <NonIdealState
            icon="select"
            title="No Asset Selected"
            description="Please select an asset to view its properties."
          />
        </div>
      ) : (
        <ResizeSensor onResize={handleResize}>
          <Container className={`${isDarkTheme ? "bp5-dark" : ""}`} fluid>
            <Row gutterWidth={10}>
              {Object.entries(propertyDataTypes[selectedSheet]).map(
                ([property, details], index) => (
                  <Col
                    key={index}
                    xs={12 / columns}
                    sm={12 / columns}
                    md={12 / columns}
                    lg={12 / columns}
                  >
                    <div className="property-item">
                      <FormGroup
                        label={
                          <>
                            <b className="ellipsis">
                              {details.title || property}
                            </b>
                            {details.required && (
                              <Tag
                                className="compact-tag"
                                intent={Intent.SUCCESS}
                              >
                                Required
                              </Tag>
                            )}
                          </>
                        }
                      >
                        {renderPropertyInput(property, details)}
                      </FormGroup>
                    </div>
                  </Col>
                ),
              )}

              {renderAdditionalFields &&
                Object.entries(
                  propertyDataTypesHE["Asset Classification"][
                    assetClassification
                  ],
                ).map(([property, details], index) => (
                  <Col
                    key={index}
                    xs={12 / columns}
                    sm={12 / columns}
                    md={12 / columns}
                    lg={12 / columns}
                  >
                    <div className="property-item">
                      <FormGroup
                        label={
                          <>
                            <b className="ellipsis">
                              {details.title || property}
                            </b>
                            {details.required && (
                              <Tag
                                className="compact-tag"
                                intent={Intent.SUCCESS}
                              >
                                Required
                              </Tag>
                            )}
                          </>
                        }
                      >
                        {renderPropertyInput(property, details)}
                      </FormGroup>
                    </div>
                  </Col>
                ))}

              {Array(placeholdersNeeded)
                .fill(null)
                .map((_, index) => (
                  <Col
                    key={index}
                    xs={12 / columns}
                    sm={12 / columns}
                    md={12 / columns}
                    lg={12 / columns}
                  >
                    <div className="property-item placeholder"></div>
                  </Col>
                ))}
            </Row>
          </Container>
        </ResizeSensor>
      )}
    </div>
  );
}

export default GeneralProperties;
