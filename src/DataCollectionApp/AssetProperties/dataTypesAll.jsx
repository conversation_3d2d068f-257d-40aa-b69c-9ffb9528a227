export const propertyDataTypes = {
  Piping: {
    Plant: { type: "Select", required: false },
    Unit: { type: "Select", required: false },
    System: { type: "Select", required: false },
    "Tag Name": { type: "Text", required: true },
    "Asset Classification": { type: "SelectRule", required: true },
    "Equipment Type": { type: "SelectRule", required: true },
    Description: { type: "Text", required: false },
    "Pipe From": { type: "Text", required: false },
    "Pipe To": { type: "Text", required: false },
    "P&ID Drawing Number": { type: "Text", required: false },
    "Date in Service": {
      type: "Date",
      required: true,
      title: "Date in Service(mm/dd/yyyy)",
    },
    "Construction Date": {
      type: "Date",
      required: false,
      title: "Construction Date(mm/dd/yyyy)",
    },
    "Construction Code": {
      type: "SelectRule",
      required: false,
      title: "Construction/Design Code",
    },
    Orientation: { type: "Select", required: false },
    "Design Pressure": {
      type: "Numeric",
      required: false,
      title: "Design Pressure (psi)",
    },
    "Design Temperature": {
      type: "Numeric",
      required: false,
      title: "Design Temperature (F)",
    },
    "Maximum Allowable Working Pressure (MAWP)": {
      type: "Numeric",
      required: false,
      title: "MAWP (psi)",
    },
    "Minimum Design Metal Temperature": {
      type: "Numeric",
      required: false,
      title: "MDMT (F)",
    },
    "Piping Class": { type: "Select", required: false, title: "Class" },
    "Nominal Pipe Size (NPS)": { type: "Select", required: false },
    "Outside Diameter": {
      type: "Select",
      required: false,
      title: "Outside Diameter (in)",
    },
    "Piping Schedule": { type: "Select", required: false },
    "Nominal Thickness": {
      type: "Numeric",
      required: true,
      title: "Nominal Thick (in)",
    },
    "Material of Construction": { type: "Select", required: false },
    "Joint Efficiency": { type: "Select", required: false },
    Insulation: { type: "Select", required: false },
    "Insulation Type": { type: "Select", required: false },
    "Representative Fluid Name": {
      type: "Select",
      required: false,
      title: "Representative Fluid",
    },
    "Fluid Phase": { type: "Select", required: false, title: "Fluid Phase" },
    "Operating Pressure": { type: "Numeric", required: false },
    "Operating Temperature": { type: "Numeric", required: false },
    "Manual Minimum Thickness (tmin)": {
      type: "Numeric",
      required: true,
      title: "Manual Min Thick (in)",
    },
  },
  "Pressure Vessel": {
    Plant: { type: "Select", required: false },
    Unit: { type: "Select", required: false },
    System: { type: "Select", required: false },
    "Tag Name": { type: "Text", required: true },
    "Asset Classification": { type: "SelectRule", required: true },
    "Equipment Type": { type: "SelectRule", required: true },
    Manufacturer: { type: "Text", required: false },
    "National Board/Register Number": {
      type: "Text",
      required: false,
      title: "National Board No.",
    },
    Model: { type: "Text", required: false, title: "Model/Serial" },
    Description: { type: "Text", required: true },
    "P&ID Drawing Number": { type: "Text", required: false },
    "Date in Service": {
      type: "Date",
      required: true,
      title: "Date in Service(mm/dd/yyyy)",
    },
    "Construction Date": {
      type: "Date",
      required: false,
      title: "Construction Date(mm/dd/yyyy)",
    },
    "Construction Code": {
      type: "SelectRule",
      required: false,
      title: "Construction/Design Code",
    },
    Orientation: {
      type: "Select",
      required: false,
      options: ["Vertical", "Horizontal"],
    },
    "Design Pressure General": {
      type: "Numeric",
      required: false,
      title: "Design Pressure (psi)",
    },
    "Design Temperature General": {
      type: "Numeric",
      required: false,
      title: "Design Temperature (F)",
    },
    "Maximum Allowable Working Pressure (MAWP) General": {
      type: "Numeric",
      required: false,
      title: "MAWP (psi)",
    },
    "Minimum Design Metal Temperature General": {
      type: "Numeric",
      required: false,
      title: "MDMT (F)",
    },
    Volume: { type: "Numeric", required: false, title: "Volume (gal)" },
  },
  "Storage Tank": {
    Plant: { type: "Select", required: false },
    Unit: { type: "Select", required: false },
    System: { type: "Select", required: false },
    "Tag Name": { type: "Text", required: true },
    "Asset Classification": { type: "SelectRule", required: true },
    "Equipment Type": { type: "SelectRule", required: true },
    Manufacturer: { type: "Text", required: false },
    Model: { type: "Text", required: false, title: "Model/Serial" },
    Description: { type: "Text", required: true },
    "P&ID Drawing Number": { type: "Text", required: false },
    "Date in Service": {
      type: "Date",
      required: true,
      title: "Date in Service(mm/dd/yyyy)",
    },
    "Construction Date": {
      type: "Date",
      required: false,
      title: "Construction Date(mm/dd/yyyy)",
    },
    "Construction Code": {
      type: "SelectRule",
      required: false,
      title: "Construction/Design Code",
    },
    Orientation: {
      type: "Select",
      required: false,
      options: ["Vertical", "Horizontal"],
    },
    "Design Pressure": { type: "Numeric", required: false },
    "Design Temperature": { type: "Numeric", required: false },
    "Maximum Allowable Working Pressure (MAWP)": {
      type: "Numeric",
      required: false,
      title: "MAWP (psi)",
    },
    "Minimum Design Metal Temperature": {
      type: "Numeric",
      required: false,
      title: "MDMT (F)",
    },
    Volume: { type: "Numeric", required: false, title: "Volume (gal)" },
    "Roof Type": { type: "Select", required: false },
    Height: {
      type: "Numeric",
      required: false,
      title: "Height (ft)",
    },
    "Filling Height": {
      type: "Numeric",
      required: false,
      title: "Filling Height (ft)",
    },
    Diameter: { type: "Numeric", required: false, title: "Diameter (ft)" },
  },

  "Additional Assets": {
    Plant: {
      type: "Select",
      required: false,
    },
    Unit: {
      type: "Select",
      required: false,
    },
    System: {
      type: "Select",
      required: false,
    },
    "Tag Name": {
      type: "Text",
      required: true,
    },
    "Asset Classification": {
      type: "SelectRule",
      required: true,
    },
    "Manufacturer": {
      type: "Text",
      required: false,
    },
    SAP: {
      type: "Text",
      required: false,
    },
    "SAP Functional Location": {
      type: "Text",
      required: false,
    },
    "Drawing Number": {
      type: "Text",
      required: false,
    },
    Mandatory: {
      type: "Text",
      required: false,
    },
    Description: {
      type: "Text",
      required: false,
    },
  },
  PRD: {
    Plant: {
      type: "Select",
      required: false,
      
    },
    Unit: {
      type: "Select",
      required: false,
    },
    System: {
      type: "Select",
      required: false,
    },
    "Tag Name": {
      type: "Text",
      required: true,
    },
    "Asset Classification": {
      type: "SelectRule",
      required: true,
    },
    "Serial Number": {
      type: "Text",
      required: false,
    },
    "Manufacturer Name": {
      type: "Text",
      required: false,
    },
    "National Board Number/Register Number": {
      type: "Text",
      required: false,
    },
    SAP: {
      type: "Text",
      required: false,
    },
    "SAP Functional Location": {
      type: "Text",
      required: false,
    },
    "Drawing Number": {
      type: "Text",
      required: false,
    },
    Model: {
      type: "Text",
      required: false,
    },
    "PSM Critical": {
      type: "Boolean",
      required: false,
    },
    Description: {
      type: "Text",
      required: false,
    },
    "Date In Service": {
      type: "Text",
      required: false,
    },
    "Operating Pressure": {
      type: "Text",
      required: false,
      title: "Operating Pressure (psi)",
    },
    
    "Operating Temperature": {
      type: "Text",
      required: false,
      title: "Operating Temperature (F)",
    },
  
    Fluid: {
      type: "Text",
      required: false,
    },
    "Fluid Phase": {
      type: "Select",
      required: false,
    },
    "Toxic Percent (%)": {
      type: "Text",
      required: false,
    },
    "PRD Discharge Location": {
      type: "Select",
      required: false,
    },
    "PSM Mandatory": {
      type: "Boolean",
      required: false,
    },
    "Construction Date": {
      type: "Text",
      required: false,
    },
    "PRD Design Type": {
      type: "Select",
      required: false,
    },
    "Construction Code": {
      type: "Text",
      required: false,
    },
    "Body Material": {
      type: "Text",
      required: false,
    },
    "Trim Material": {
      type: "Text",
      required: false,
    },
    "Inlet Size": {
      type: "Text",
      required: false,
    },
    "Inlet Size Unit": {
      type: "Select",
      required: false,
    },
    "Outlet Size": {
      type: "Text",
      required: false,
    },
    "Outlet Size Unit": {
      type: "Select",
      required: false,
    },
    Orifice: {
      type: "Text",
      required: false,
    },
    "Orifice Unit": {
      type: "Select",
      required: false,
    },
    CDTP: {
      type: "Text",
      required: false,
    },
    "CDTP Unit": {
      type: "Select",
      required: false,
    },
    "Leak Test Pressure": {
      type: "Text",
      required: false,
    },
    "Leak Test Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Leak Test Pressure Under Pressure": {
      type: "Text",
      required: false,
    },
    "Leak Test Pressure Under Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Closing Pressure": {
      type: "Text",
      required: false,
    },
    "Closing Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Closing Pressure Under Pressure": {
      type: "Text",
      required: false,
    },
    "Closing Pressure Under Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Test Liquid": {
      type: "Text",
      required: false,
    },
    "Inlet Rating": {
      type: "Text",
      required: false,
    },
    "Outlet Rating": {
      type: "Text",
      required: false,
    },
    "Design Pressure": {
      type: "Text",
      required: false,
    },
    "Design Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Maximum Allowable Working Pressure": {
      type: "Text",
      required: false,
    },
    "Maximum Allowable Working Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Maximum Operating Pressure": {
      type: "Text",
      required: false,
    },
    "Maximum Operating Pressure Unit": {
      type: "Select",
      required: false,
    },
    Overpressure: {
      type: "Text",
      required: false,
    },
    "Overpressure Unit": {
      type: "Select",
      required: false,
    },
    "Set Pressure": {
      type: "Text",
      required: false,
    },
    "Set Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Set Pressure Under Pressure": {
      type: "Text",
      required: false,
    },
    "Set Pressure Under Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Rate Relieving Capacity": {
      type: "Text",
      required: false,
    },
    "Rate Relieving Capacity Unit": {
      type: "Select",
      required: false,
    },
    "Rate Relieving Capacity Under Pressure": {
      type: "Text",
      required: false,
    },
    "Rate Relieving Capacity Under Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Stamped Relieving Capacity": {
      type: "Text",
      required: false,
    },
    "Stamped Relieving Capacity Unit": {
      type: "Select",
      required: false,
    },
    "Stamped Relieving Capacity Under Pressure": {
      type: "Text",
      required: false,
    },
    "Stamped Relieving Capacity Under Pressure Unit": {
      type: "Select",
      required: false,
    },
    "Area #1": {
      type: "Text",
      required: false,
    },
    "Area #2": {
      type: "Text",
      required: false,
    },
    "EGS NOTES": {
      type: "Text",
      required: false,
    },
    NOTE: {
      type: "Text",
      required: false,
    },
    "PASSPORT UPDATED": {
      type: "Boolean",
      required: false,
    },
    Picture: {
      type: "Boolean",
      required: false,
    },
    "PSV # READ": {
      type: "Boolean",
      required: false,
    },
    Size: {
      type: "Text",
      required: false,
    },
    "Valve Located": {
      type: "Boolean",
      required: false,
    },
  },
};

