import React, { useState, useContext, useEffect, useMemo } from 'react';
import { Modal, Input, Button, Table } from 'antd';
import { AppContext } from '../../AppContextProvider';
import styled from 'styled-components';

const StyledModal = styled(Modal)`
  .ant-modal-content,
  .ant-modal-header {
    background-color: ${props => props.$isDarkTheme ? '#252A31' : '#fff'};
    border-bottom: 1px solid ${props => props.$isDarkTheme ? '#333' : '#f0f0f0'};
  }
  .ant-modal-title {
    color: ${props => props.$isDarkTheme ? '#fff' : '#000'};
  }
  .ant-modal-close-x {
    color: ${props => props.$isDarkTheme ? '#fff' : '#000'};
  }
  .ant-modal-footer {
    border-top: 1px solid ${props => props.$isDarkTheme ? '#333' : '#f0f0f0'};
    background-color: ${props => props.$isDarkTheme ? '#252A31' : '#fff'};
  }
`;

const StyledInput = styled(Input)`
  background-color: ${props => props.$isDarkTheme ? '#383E47' : '#fff'};
  color: ${props => props.$isDarkTheme ? '#fff' : '#000'};
  border-color: ${props => props.$isDarkTheme ? '#333' : '#d9d9d9'};
  &::placeholder {
    color: ${props => props.$isDarkTheme ? '#888' : '#bfbfbf'};
  }
`;

const StyledTable = styled(Table)`
  .ant-table {
    background-color: ${props => props.$isDarkTheme ? '#252A31' : '#fff'};
    color: ${props => props.$isDarkTheme ? '#fff' : '#000'};
  }
  .ant-table-thead > tr > th {
    background-color: ${props => props.$isDarkTheme ? '#383E47' : '#fafafa'};
    color: ${props => props.$isDarkTheme ? '#fff' : '#000'};
    border-bottom: 1px solid ${props => props.$isDarkTheme ? '#333' : '#f0f0f0'};
    padding: 2px 4px;
  }
  .ant-table-tbody > tr > td {
    background-color: ${props => props.$isDarkTheme ? '#252A31' : '#fff'};
    border-bottom: 1px solid ${props => props.$isDarkTheme ? '#333' : '#f0f0f0'};
    padding: 2px 4px;
  }
  .ant-table-tbody > tr:hover > td {
    background-color: ${props => props.$isDarkTheme ? '#2F353D !important' : '#fafafa !important'};
  }
  .ant-table-row-selected > td {
    background-color: ${props => props.$isDarkTheme ? 'rgba(45, 114, 210, 0.5) !important' : 'rgba(45, 114, 210, 0.5) !important'};
  }
  .ant-checkbox-inner {
    background-color: ${props => props.$isDarkTheme ? '#252A31' : '#fff'};
    border-color: ${props => props.$isDarkTheme ? '#fff' : '#2D72D2'};
  }
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #2D72D2;
    border-color: ${props => props.$isDarkTheme ? '#fff' : '#2D72D2'};
  }
  .ant-table-cell {
    font-size: 12px;
  }
`;

const AssociatedAssetsDialog = ({ onSave, onClose, initialSelectedAssets }) => {
  const [quickFilterText, setQuickFilterText] = useState('');
  const { isDarkTheme, allAssetsData } = useContext(AppContext);

  const columns = [
    { title: 'Plant', dataIndex: 'Plant', key: 'Plant' },
    { title: 'Unit', dataIndex: 'Unit', key: 'Unit' },
    { title: 'System', dataIndex: 'System', key: 'System' },
    { title: 'Tag Name', dataIndex: 'Tag Name', key: 'Tag Name' },
    { title: 'Asset Classification', dataIndex: 'Class', key: 'Class' },
  ];

  const dataWithIds = useMemo(() => {
    return allAssetsData.map((item, index) => ({
      ...item,
      id: `id-${index + 1}`
    }));
  }, [allAssetsData]);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  useEffect(() => {
    let initialSelectedAssetIds = [];
    if (Array.isArray(initialSelectedAssets)) {
      initialSelectedAssetIds = dataWithIds.filter(asset => 
        initialSelectedAssets.some(selectedAsset => 
          selectedAsset.Plant === asset.Plant &&
          selectedAsset.Unit === asset.Unit &&
          selectedAsset.System === asset.System &&
          selectedAsset['Tag Name'] === asset['Tag Name']
        )
      ).map(asset => asset.id);
    }
    setSelectedRowKeys(initialSelectedAssetIds);
  }, [initialSelectedAssets, dataWithIds]);

  const filteredData = useMemo(() => {
    if (!quickFilterText) {
      return dataWithIds.filter(asset => asset.Class !== "Additional Equipment" && asset.Class !== "PRD");
    }

    const searchText = quickFilterText.toLowerCase();
    return dataWithIds.filter(
      (asset) =>
        (asset.Class !== "Additional Equipment" && asset.Class !== "PRD") &&
        Object.values(asset).some((value) =>
          value.toString().toLowerCase().includes(searchText)
        )
    );
  }, [dataWithIds, quickFilterText]);

  const sortedDataWithIds = useMemo(() => {
    const sortedData = [...filteredData];

    if (Array.isArray(initialSelectedAssets)) {
      const existingAssetIds = new Set(initialSelectedAssets.map(asset => `${asset.Plant}-${asset.Unit}-${asset.System}-${asset['Tag Name']}`));
      sortedData.sort((a, b) => {
        const aKey = `${a.Plant}-${a.Unit}-${a.System}-${a['Tag Name']}`;
        const bKey = `${b.Plant}-${b.Unit}-${b.System}-${b['Tag Name']}`;
        if (existingAssetIds.has(aKey) && !existingAssetIds.has(bKey)) {
          return -1;
        } else if (!existingAssetIds.has(aKey) && existingAssetIds.has(bKey)) {
          return 1;
        } else {
          return 0;
        }
      });
    }

    return sortedData;
  }, [filteredData, initialSelectedAssets]);

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys);
    },
    hideSelectAll: true,
  };

  const handleSave = () => {
    const selectedAssets = dataWithIds.filter(asset => selectedRowKeys.includes(asset.id));
    onSave(selectedAssets);
  };

  return (
    <StyledModal
      open={true}
      onCancel={onClose}
      title="Select Associated Assets"
      width={900}
      footer={[
        <Button key="cancel" onClick={onClose} style={{ backgroundColor: isDarkTheme ? '#383E47' : '#f0f0f0', borderColor: isDarkTheme ? '#333' : '#d9d9d9', color: isDarkTheme ? '#fff' : '#000' }}>
          Cancel
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          Save
        </Button>,
      ]}
      $isDarkTheme={isDarkTheme}
    >
      <StyledInput
        placeholder="Search..."
        value={quickFilterText}
        onChange={(e) => setQuickFilterText(e.target.value)}
        style={{ marginBottom: 16 }}
        $isDarkTheme={isDarkTheme}
      />
      <StyledTable
        rowSelection={rowSelection}
        columns={columns}
        dataSource={sortedDataWithIds}
        rowKey="id"
        size="small"
        scroll={{ y: 300, x: 800 }}
        pagination={false}
        $isDarkTheme={isDarkTheme}
      />
    </StyledModal>
  );
};

export default AssociatedAssetsDialog;