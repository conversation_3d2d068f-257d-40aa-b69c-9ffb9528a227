export const tablePropertiesDict = {
  Template: {
    "Pressure Vessel": {
      "Component Tag Name": { type: "text", required: true },
      "Component Type": { type: "SelectRule", required: true },
      Geometry: { type: "Select", required: false },
      "Design Pressure": {
        type: "Numeric",
        required: false,
        title: "Design Pressure (psi)",
      },
      "Design Temperature": {
        type: "Numeric",
        required: false,
        title: "Design Temperature (F)",
      },
      "Material of Construction": { type: "Select", required: false },
      "Nominal Thickness (tnominal)": {
        type: "Numeric",
        required: true,
        title: "Nominal Thick (in)",
      },
      "Manual Minimum Thickness (tmin)": {
        type: "Numeric",
        required: true,
        title: "Manual Min Thick (in)",
      },
      rowIndex: { type: "Numeric", required: true, title: "rowIndex" },

      Length: { type: "Numeric", required: false, title: "Length (ft)" },
      "Outside Diameter": {
        type: "Numeric",
        required: false,
        title: "Outside Diameter (in)",
      },
      "Inside Diameter": {
        type: "Numeric",
        required: false,
        title: "Inside Diameter (in)",
      },
      "Crown Radius": {
        type: "Numeric",
        required: false,
        title: "Crown Radius (in)",
      },
      "Knuckle Radius": {
        type: "Numeric",
        required: false,
        title: "Knuckle Radius (in)",
      },
      "Elliptical Ratio": {
        type: "Select",
        required: false,
        title: "Elliptical Ratio",
      },
      "Conical Apex Angle": {
        type: "Numeric",
        required: false,
        title: "Conical Apex Angle (deg)",
      },
      "Hemispherical Radius": {
        type: "Numeric",
        required: false,
        title: "Hemispherical Radius (in)",
      },

      "Maximum Allowable Working Pressure (MAWP)": {
        type: "Numeric",
        required: false,
        title: "MAWP (psi)",
      },
      "Minimum Design Metal Temperature": {
        type: "Numeric",
        required: false,
        title: "MDMT (F)",
      },
      "Joint Efficiency": {
        type: "Select",
        required: false,
        title: "Joint Efficiency (E)",
      },
      Insulation: { type: "Select", required: false, title: "Insulation" },
      "Insulation Type": {
        type: "Select",
        required: false,
        title: "Insulation Type",
      },
      "Representative Fluid Name": {
        type: "Select",
        required: false,
        title: "Representative Fluid",
      },
      "Fluid Phase": { type: "Select", required: false, title: "Fluid Phase" },
      "Operating Pressure": {
        type: "Numeric",
        required: false,
        title: "Operating Pressure (psi)",
      },
      "Operating Temperature": {
        type: "Numeric",
        required: false,
        title: "Operating Temperature (F)",
      },
      "PWHT": { type: "Select", required: false, title: "PWHT" },
    },
    "Storage Tank": {
      "Component Tag Name": { type: "text", required: true },
      "Component Type": { type: "SelectRule", required: true },
      Geometry: { type: "Select", required: false },
      "Operating Pressure": {
        type: "Numeric",
        required: false,
        title: "Operating Pressure (psi)",
      },
      "Operating Temperature": {
        type: "Numeric",
        required: false,
        title: "Operating Temperature (F)",
      },

      "Material of Construction": { type: "Select", required: false },
      "Nominal Thickness": {
        type: "Numeric", 
        required: true,
        title: "Nominal Thick (in)",
      },
      "Manual Minimum Thickness (tmin)": {
        type: "Numeric",
        required: true,
        title: "Manual Min Thick (in)",
      },
      rowIndex: { type: "Numeric", required: true, title: "rowIndex" },
      "Outside Diameter": {
        type: "Numeric",
        required: false,
        title: "Outside Diameter (in)",
      },
      "Inside Diameter": {
        type: "Numeric",
        required: false,
        title: "Inside Diameter (in)",
      },

      "Joint Efficiency": {
        type: "Select",
        required: false,
        title: "Joint Efficiency (E)",
      },
      "Height/Length": {
        type: "Numeric",
        required: false,
        title: "Height/Length (ft)",
      },
      Insulation: { type: "Select", required: false, title: "Insulation" },
      "Insulation Type": {
        type: "Select",
        required: false,
        title: "Insulation Type",
      },
      "Representative Fluid Name": {
        type: "Select",
        required: false,
        title: "Representative Fluid",
      },
      "Fluid Phase": { type: "Select", required: false, title: "Fluid Phase" },
    },

  },
};
