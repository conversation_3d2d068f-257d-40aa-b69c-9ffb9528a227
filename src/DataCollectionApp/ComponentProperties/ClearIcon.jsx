import * as React from "react";
const ClearIcon = ({ isDarkTheme, ...props }) => {
    const color = isDarkTheme ? 'white' : 'black';

return (
  <svg xmlns="http://www.w3.org/2000/svg" width={28} height={30} viewBox="0 0 50 50" {...props}>
    <path
      style={{
        lineHeight: "normal",
        textIndent: 0,
        textAlign: "start",
        textDecorationLine: "none",
        textDecorationStyle: "solid",
      
        textTransform: "none",
        blockProgression: "tb",
        isolation: "auto",
        mixBlendMode: "normal",
      }}
      d="M 12.541016 10 L -0.31640625 25 L 12.541016 40 L 13 40 L 46 40 C 48.197334 40 50 38.197334 50 36 L 50 14 C 50 11.802666 48.197334 10 46 10 L 12.541016 10 z M 13.458984 12 L 46 12 C 47.116666 12 48 12.883334 48 14 L 48 36 C 48 37.116666 47.116666 38 46 38 L 13.460938 38 L 2.3164062 25 L 13.458984 12 z M 22.707031 18.292969 L 21.292969 19.707031 L 26.585938 25 L 21.292969 30.292969 L 22.707031 31.707031 L 28 26.414062 L 33.292969 31.707031 L 34.707031 30.292969 L 29.414062 25 L 34.707031 19.707031 L 33.292969 18.292969 L 28 23.585938 L 22.707031 18.292969 z"
      fontWeight={400}
      fontFamily="sans-serif"
      white-space="normal"
      overflow="visible"
      stroke={color}
      fill={color}
    />
    <div
      xmlns=""
      id="divScriptsUsed"
      style={{
        display: "none",
      }}
    />
    <script
      xmlns=""
      id="globalVarsDetection"
      src="chrome-extension://cmkdbmfndkfgebldhnkbfhlneefdaaip/js/wrs_env.js"
    />
    <script xmlns="" />
  </svg>
  );
};



export default ClearIcon;
