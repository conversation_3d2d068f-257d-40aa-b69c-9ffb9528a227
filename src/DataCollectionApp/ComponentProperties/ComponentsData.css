/* first column */
.jexcel thead td:nth-child(1),
.jexcel tbody td:nth-child(1) {
    color: #abb3bf !important;
    background-color: #383e47 !important;
    border-top: 0.25px solid var(--border-color) !important;
    border-bottom: 0.25px solid var(--border-color) !important;
    border-left: 0.25px solid var(--border-color) !important;
    border-right: 0.2›5px solid var(--border-color) !important;

    font-size: 12px !important;
    /* aling text to the right */
    text-align: right !important;
}
.jexcel col:nth-child(1) {
    width: 30px !important;
}

/* headers */
.jexcel thead tr:first-child td {
    color: white;
    background-color: #383e47 !important;
    height: 30px !important;
    font-size: 14px !important;

    /* Set the text color for the first column */
    /* all border top bottom left right * set them to background color */

    border-top: 0.25px solid var(--border-color) !important;
    border-bottom: 0.25px solid var(--border-color) !important;
    border-left: 0.25px solid var(--border-color) !important;
    border-right: 0.2›5px solid var(--border-color) !important;
}

.jexcel thead td {
    background-color: rgba(45, 114, 210, 0.1);
}

.jexcel thead th {
    background-color: rgba(45, 114, 210, 0.1);
}

.jexcel .highlight {
    background-color: rgba(45, 114, 209, 0.1);
}

.jexcel {
    color: white !important;
    background-color: #2f343c !important;
    height: auto;
    border-top: 0.25px solid var(--border-color) !important;
    border-bottom: 0.25px solid var(--border-color) !important;
    border-left: 0.25px solid var(--border-color) !important;
    border-right: 0.25px solid var(--border-color) !important;
    font-size: 12px !important;
}

.jexcel td {
    border-top: 0.25px solid var(--border-color) !important;
    border-bottom: 0.25px solid var(--border-color) !important;
    border-left: 0.25px solid var(--border-color) !important;
    border-right: 0.25px solid var(--border-color) !important;
}

.jexcel thead td.resizing {
    border-right-style: dotted !important;
    border-right-color: rgb(229, 203, 10) !important;
    border-right-width: 5px !important;
}

.jexcel tbody tr.resizing > td {
    border-bottom-style: dotted !important;
    border-bottom-color: rgb(229, 203, 10) !important;
    border-bottom-width: 5px !important;
}

.jexcel tbody td.resizing {
    border-right-style: dotted !important;
    border-right-color: rgb(229, 203, 10) !important;
    border-right-width: 2px !important;
}

.jexcel > tbody > tr > td.highlight > a {
    cursor: pointer;
}

.jexcel .selection {
    background-color: rgba(45, 114, 210, 0.1);
}

.jexcel .highlight-top {
    border-top: 2px solid rgba(45, 114, 210, 1) !important;
}

.jexcel .highlight-left {
    border-left: 2px solid rgba(45, 114, 210, 1) !important;
}

.jexcel .highlight-right {
    border-right: 2px solid rgba(45, 114, 210, 1) !important;
}

.jexcel .highlight-bottom {
    border-bottom: 2px solid rgba(45, 114, 210, 1) !important;
}

.jexcel_corner {
    position: absolute;
    background-color: rgba(45, 114, 210, 1);
    height: 2px;
    width: 2px;
    border: 1px solid rgb(255, 255, 255);
    top: -2000px;
    left: -2000px;
    cursor: crosshair;
    box-sizing: initial;
    z-index: 20;
    padding: 2px;
    border-radius: 50%; /* This line makes it round */
}

jexcel_container {
    box-shadow: transparent !important;
    border-top: transparent !important;
    border-left: transparent !important;
    border-right: transparent !important;
    border-bottom: transparent !important;
    scrollbar-color: #666 transparent !important;
}
div.jexcel_content {
    box-shadow: none !important;
    scrollbar-color: #666 transparent !important;
}

.jexcel_content::-webkit-scrollbar {
    width: 5px; /* Adjust width as needed */
}

.jexcel_content::-webkit-scrollbar-track {
    background: var(--border-color) !important;
}

.jexcel_content::-webkit-scrollbar-thumb {
    background-color: #666 !important;
    border-radius: 5px; /* Adjust this value to control the roundness */
}

.jdropdown-content {
    background-color: #2f343c !important;
    color: white !important;
    border-color: #666;
}
.jdropdown-item {
    background-color: #2f343c !important;
    color: white !important;
}
.jdropdown-item:hover {
    background-color: #3f444c !important; /* Adjust this to the desired hover color */
    color: white !important;
}
/* Remove padding and margin */
.bp5-popover-target,
.bp5-popover-target .bp5-button {
    margin: 0;
    padding: 0;
    height: 100%;

    display: flex;
    justify-content: space-between;
    align-items: center;
    border: none;
    box-sizing: border-box;
}

/* Adjust the height of the select to account for potential borders or unexpected styling */
.bp5-popover-target,
.bp5-popover-target .bp5-button {
    height: calc(100% - 0px);
}

/* Remove padding from the cell */
.bp5-table-cell {
    padding: 0;
    margin: 0;
}
/* Adjust the height of the select button to fill its container */
.bp5-table-cell .bp5-popover-target .bp5-button {
    height: calc(100% - 0px); /* Adjust for any potential borders */
    min-height: 0;
    padding: 0;
}
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}
