import React, { useState, useEffect, useMemo, useContext, useRef } from "react";
import {
  Dialog,
  FormGroup,
  InputGroup,
  Button,
  MenuItem,
  Intent,
  Tag,
  DialogFooter,
  Icon,
  DialogBody,
  Callout
} from "@blueprintjs/core";
import { Select as BPSelect } from "@blueprintjs/select";
import { Select as AntdSelect } from "antd"; // Importing Ant Design Select
import { AgGridReact } from "ag-grid-react";
import { AppContext } from "../../AppContextProvider";
import { Container, Row, Col } from "react-grid-system";
import axios from "axios";
import { TablesMapping } from "./materialsTableMapping";
import { PiCubeTransparentBold } from "react-icons/pi";
import { HiCube } from "react-icons/hi2";
import { useSelector } from "react-redux";
import { CloseOutlined } from '@ant-design/icons';
import ClearIcon from "./ClearIcon";
const { Option } = AntdSelect; // Destructure Option from Ant Design Select

const materialsProperties = {
  "Material of Construction": {
    type: "select",
    required: false,
    placeholder: "Select Material",
    title: "Material of Construction",
    category: "properties",
  },
  "Tensile Strength (TS)": {
    type: "input",
    required: false,
    placeholder: "Tensile Strength (TS)",
    title: "Tensile Strength (TS) - psi",
    category: "properties",
  },
  "Yield Strength (YS)": {
    type: "input",
    placeholder: "Yield Strength (YS)",
    required: false,
    title: "Yield Strength (YS) - psi",
    category: "properties",
  },
  "Allowable Stress (S)": {
    type: "input",
    required: false,
    placeholder: "Allowable Stress (S)",
    title: "Allowable Stress (S) - psi",
    category: "properties",
  },
  "Construction Code": {
    type: "temp-input",
    required: false,
    placeholder: "Construction Code",
    title: "Construction Code",
    category: "reference",
  },
  "Design Temperature": {
    type: "temp-input",
    required: false,
    placeholder: "Missing Design Temperature",
    title: "Design Temperature - F",
    category: "reference",
  },
  // Add more fields as needed
};

function MaterialPicker({
  isMaterialPickerComponentOpen,
  setIsMaterialPickerComponentOpen,
  onSave,
  onMaterialSelected,
  propertyRowIndex,
  updateCell,
  lookupDict,
  myToaster,
}) {
  const { isDarkTheme } = useContext(AppContext);
  const [materialData, setMaterialData] = useState({ headers: [], rows: [] });
  const [inputValues, setInputValues] = useState({});
  const { assetData, setAssetData } = useContext(AppContext);
  const [isInputActive, setIsInputActive] = useState(false);
  const [designTemperature, setDesignTemperature] = useState("");
  const { selectedSheet, setSelectedSheet } = useContext(AppContext);
  const { userEmail, setUserEmail } = useContext(AppContext);

  const materialsGridApiRef = useRef(null);
  const [materialsGridApi, setMaterialsGridApi] = useState(null);

  const [materialId, setMaterialId] = useState(null);

  const [currentMaterialId, setCurrentMaterialId] = useState(null);
  const [isTransparent, setIsTransparent] = useState(false);

  const { user } = useSelector((state) => state.auth);
  const [showNamingError, setShowNamingError] = useState(false); // New state to track naming error

  const formatDataForApi = () => {
    return Object.entries(inputValues)
      .filter(([header]) => header !== "Design Temperature")
      .map(([header, newValue]) => ({
        header,
        indexrow: `"${propertyRowIndex}"`,
        new_value: newValue,
      }));
  };

  const sendUpdateRequestToApi = async () => {
    const rowIndex = assetData.findIndex(
      (row) => Number(row.rowIndex) === Number(propertyRowIndex)
    );

    if (rowIndex !== -1) {
      let updatedAssetData = [...assetData];

      updatedAssetData[rowIndex] = {
        ...updatedAssetData[rowIndex],
        "Material of Construction":
          inputValues["Material of Construction"] || "",
        "Allowable Stress (S)": inputValues["Allowable Stress (S)"] || "",
        "Yield Strength (YS)": inputValues["Yield Strength (YS)"] || "",
        "Tensile Strength (TS)": inputValues["Tensile Strength (TS)"] || "",
        "Material ID": materialId || "",
      };

      setAssetData(updatedAssetData);

      const updatesJson = formatDataForApi();
      updatesJson.push({
        header: "Material ID",
        indexrow: `"${propertyRowIndex}"`,
        new_value: materialId,
      });
      const template = selectedSheet;

      const url = `${process.env.REACT_APP_DATA_API}/data/bulk_update_cells/`;
      const userEmail = user.email;
      try {
        const session_id = localStorage.getItem("session_id");
        const response = await axios.put(url, null, {
          params: {
            updatesjson: JSON.stringify(updatesJson),
            template,
            user: userEmail,
          },
          headers: {
            "session-id": session_id,
          },
        });

        const selectedMaterial = inputValues["Material of Construction"];
        myToaster.current?.show({
          message: `Materials Properties updated at ${template}`,
          intent: Intent.SUCCESS,
          timeout: 5000,
          className: "toaster-font",
          icon: "updated",
        });

        onSave(selectedMaterial);

        console.log("Update successful", response.data);
      } catch (error) {
        console.error("Failed to update cells:", error);
        myToaster.current?.show({
          message: `Error ${error}`,
          intent: Intent.DANGER,
          timeout: 5000,
          className: "toaster-font",
          icon: "error",
        });
      }
    } else {
      console.error("Row not found in assetData");
    }
  };

  const handleRowSelected = async () => {
    const selectedNodes = materialsGridApi.getSelectedNodes();
    if (selectedNodes.length > 0) {
      const rowData = selectedNodes[0].data;
      let { id, map, minimum_yield_strength_ys: ys, minimum_tensile_strength_ts: ts, construction_codes: constructionCodes } = rowData;
  
      console.log("Mapped Material:", map);
  
      // Convert map from number to text
      map = String(map);
  
      setMaterialId(id);
  
      setInputValues((values) => ({
        ...values,
        "Yield Strength (YS)": ys.toString(),
        "Tensile Strength (TS)": ts.toString(),
        "Material of Construction": map.toString(),
      }));
  
      // Determine current component type from assetData using propertyRowIndex
      const currentComponent = assetData.find(
        (row) => Number(row.rowIndex) === Number(propertyRowIndex)
      );
  
      const currentComponentType = currentComponent ? currentComponent["Component Type"] : null;
  
      // Count how many components have 'Component Type' as 'SHELL COURSE'
      const shellCourseCount = assetData.filter(
        (row) => row["Component Type"] && row["Component Type"].toUpperCase() === "SHELL COURSE"
      ).length;

      if (constructionCodes === "API 653") {
        console.log("Construction Code is API 653");
  
        if (currentComponentType) {
          if (currentComponentType.toUpperCase() === "SHELL COURSE") {
            // Logic specifically for 'SHELL COURSE'
            if (shellCourseCount <= 2) {
              // Use lower course stress if 2 or fewer SHELL COURSE components
              const allowableProductStress = rowData["allowable_product_stress_lower_two_courses_s"];
              if (allowableProductStress) {
                setInputValues((values) => ({
                  ...values,
                  "Allowable Stress (S)": allowableProductStress.toString(),
                }));
                console.log(`Allowable Stress (S) set to ${allowableProductStress}`);
              } else {
                console.warn("No value found for allowable_product_stress_lower_two_courses_s.");
              }
            } else {
              // More than 2 SHELL COURSE components, determine stress based on Component Tag Name
              const componentTagName = currentComponent["Component Tag Name"];
              const tagParts = componentTagName.split(" ");
              const lastPart = tagParts[tagParts.length - 1];
  
              // Check if the last part is a number
              const lastPartNumber = parseInt(lastPart, 10);
              if (isNaN(lastPartNumber)) {
                setShowNamingError(true);  // Show naming error message
                setInputValues((values) => ({
                  ...values,
                  "Allowable Stress (S)": "",  // Clear the input if naming is incorrect
                }));
              } else if (lastPartNumber === 1 || lastPartNumber === 2) {
                const allowableProductStress = rowData["allowable_product_stress_lower_two_courses_s"];
                setInputValues((values) => ({
                  ...values,
                  "Allowable Stress (S)": allowableProductStress.toString(),
                }));
              } else {
                const allowableProductStress = rowData["allowable_product_stress_upper_courses_s"];
                setInputValues((values) => ({
                  ...values,
                  "Allowable Stress (S)": allowableProductStress.toString(),
                }));
              }
            }
          } else if (currentComponentType.toUpperCase() === "ROOF") {
            // For ROOF, always use upper courses stress
            const allowableProductStress = rowData["allowable_product_stress_upper_courses_s"];
            if (allowableProductStress) {
              setInputValues((values) => ({
                ...values,
                "Allowable Stress (S)": allowableProductStress.toString(),
              }));
              console.log(`Allowable Stress (S) set to ${allowableProductStress} for ROOF`);
            } else {
              console.warn("No value found for allowable_product_stress_upper_courses_s for ROOF.");
            }
          } else if (currentComponentType.toUpperCase() === "BOTTOM") {
            // For BOTTOM, always use lower courses stress
            const allowableProductStress = rowData["allowable_product_stress_lower_two_courses_s"];
            if (allowableProductStress) {
              setInputValues((values) => ({
                ...values,
                "Allowable Stress (S)": allowableProductStress.toString(),
              }));
              console.log(`Allowable Stress (S) set to ${allowableProductStress} for BOTTOM`);
            } else {
              console.warn("No value found for allowable_product_stress_lower_two_courses_s for BOTTOM.");
            }
          }
        }
      } else {
        // Default behavior if not 'API 653'
        try {
          const response = await axios.get(
            `${process.env.REACT_APP_MATERIALS_API}/material_properties/allowable_stress`,
            {
              params: {
                id: String(id),
                temperature: String(inputValues["Design Temperature"]),
                template: selectedSheet,
              },
            }
          );
  
          const newAllowableStress = response.data.as;
          setInputValues((values) => ({
            ...values,
            "Allowable Stress (S)": newAllowableStress.toString(),
          }));
        } catch (error) {
          console.error("API call failed:", error);
        }
      }
    }
  };

  const convertDataForAgGrid = (data) => {
    const { headers, rows } = data;
    const transformedRows = rows.map((row) =>
      row.reduce((obj, value, index) => {
        const key = headers[index];
        obj[key] = value;
        return obj;
      }, {})
    );
    return { headers, rows: transformedRows };
  };

  useEffect(() => {
    if (!assetData || propertyRowIndex === undefined) return;

    const currentRowData =
      assetData.find(
        (row) => Number(row.rowIndex) === Number(propertyRowIndex)
      ) || {};

    const initialValues = {};
    setCurrentMaterialId(currentRowData["Material ID"]);
    Object.entries(materialsProperties).forEach(([key, { placeholder }]) => {
      initialValues[key] = currentRowData[key] || "";
    });
    setInputValues(initialValues);
  }, [assetData, propertyRowIndex]);

  useEffect(() => {
    const temp = inputValues["Design Temperature"];
    if (temp !== undefined) {
      setDesignTemperature(temp);
    }
  }, [inputValues]);

  const clearAllFields = () => {
    setInputValues((prevValues) => ({
      ...prevValues,
      "Material of Construction": "",
      "Tensile Strength (TS)": "",
      "Yield Strength (YS)": "",
      "Allowable Stress (S)": "",
      "Material ID": ""
    }));
    setMaterialId("");
    materialsGridApi.deselectAll();
    setCurrentMaterialId(null);
  };

  const renderInputField = (key, details) => {
    const isTempEmpty = designTemperature === "";
    let showWarning = false;
    
    if ((key === "Allowable Stress (S)" && key === "Design Temperature") && isTempEmpty) {
      showWarning = true;
    }

    // Special case for "Material of Construction"
    if (key === "Material of Construction") {
      const options = selectedSheet === "Pressure Vessel" 
        ? lookupDict["Material of Construction PV"]
        : lookupDict["Material of Construction ST"];

      return (
        <div style={{ display: "flex", alignItems: "center" }}>
          <AntdSelect
            popupMatchSelectWidth={false}
            id={key}
            name={key}
            placeholder={details.placeholder}
            value={String(inputValues[key]) || ""}
            onChange={(value) => handleSelectChange(key, value)}
            style={{ width: '100%' }}
            showSearch // Enable search functionality
            filterOption={(input, option) =>
              String(option.props.children).toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {options.map((option) => (
              <Option key={option} value={String(option)}>
                {option}
              </Option>
            ))}
          </AntdSelect>
          <Button
            minimal={true}
            icon={<ClearIcon isDarkTheme={isDarkTheme} />}
            onClick={clearAllFields}
            style={{ marginLeft: "5px" }}
          />
        </div>
      );
    }

    switch (details.type) {
      case "input":
        return (
          <>
            
            <InputGroup
              id={key}
              name={key}
              placeholder={details.placeholder}
              value={inputValues[key] || ""}
              onChange={handleInputChange}
              leftIcon={showWarning && key !== "Allowable Stress (S)" ? (
                <Icon icon="warning-sign" intent="warning" />
              ) : null}
              intent={showWarning ? Intent.WARNING : Intent.NONE}
            />
            {showNamingError && key === "Allowable Stress (S)" && (
              <Callout
                intent="warning"
                icon="warning-sign"
                style={{ marginBottom: "10px" }}
              >
                Please rename component to assign value automatically.
              </Callout>
            )}
          </>
        );
      case "temp-input":
        return (
          <InputGroup
            id={key}
            name={key}
            placeholder={details.placeholder}
            value={inputValues[key] || ""}
            onValueChange={handleInputChange}
            disabled={true}
            buttonPosition="none"
            intent={showWarning ? Intent.DANGER : Intent.NONE}
            leftIcon={showWarning ? (
              <Icon icon="warning-sign" intent="warning" />
            ) : null}
          />
        );
      case "select":
        const options = lookupDict[key] || details.options;
        return (
          <BPSelect
            fill={true}
            items={options}
            itemPredicate={(query, item) =>
              item !== null && item !== undefined && item.toLowerCase().indexOf(query.toLowerCase()) >= 0
            }
            itemRenderer={(item, { handleClick, modifiers }) => (
              <MenuItem
                key={item}
                onClick={handleClick}
                text={item || "<Select>"}
                active={modifiers.active}
                disabled={modifiers.disabled}
                fill={true}
              />
            )}
            onItemSelect={(item) => handleSelectChange(key, item)}
          >
            <Button
              className="ellipsis"
              text={inputValues[key] || details.placeholder}
              rightIcon="double-caret-vertical"
            />
          </BPSelect>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_MATERIALS_API}/material_properties`,
          { params: { template: String(selectedSheet) } }
        );

        let { headers, rows } = response.data;

        let transformedRows = rows.map((row) =>
          row.reduce((acc, value, index) => {
            acc[headers[index]] = value === null || value === "None" ? "" : value;
            return acc;
          }, {})
        );

        if (currentMaterialId) {
          const identifiedRowIndex = transformedRows.findIndex(row => row.id === currentMaterialId);
          if (identifiedRowIndex > -1) {
            const [identifiedRow] = transformedRows.splice(identifiedRowIndex, 1);
            transformedRows.unshift(identifiedRow);
          }
        }

        setMaterialData({ headers, rows: transformedRows });
      } catch (error) {
        console.error("Error fetching material data:", error);
      }
    };

    fetchData();
  }, [currentMaterialId, selectedSheet]);

  const gridOptions = {
    floatingFiltersHeight: 35,
  };

  const getColumnWidth = (header) => {
    switch (header) {
      case "Table":
        return 95;
      case "Grade/Temper":
        return 140;
      case "Class According Desing Code":
        return 170;
      case "B31.1-3  Coefficient, Y/ B31.4-8 Design Factor,F":
        return 170;
      case "B31.1-3 Quality factor, E/B31.4 Weld Joint Factor,E/ B31.8 Longitudinal Joint Factor,E":
        return 170;
      case "P Number":
        return 115;
      default:
        return 135;
    }
  };

  const determineHighlightColumn = (currentTemperature, headers) => {
    const temps = headers.filter(h => h.startsWith('as_'))
      .map(h => parseInt(h.replace('as_', '')))
      .sort((a, b) => a - b);

    const closest = temps.find(t => t >= currentTemperature);
    return closest ? `as_${closest}` : null;
  };

  const highlightColumn = determineHighlightColumn(designTemperature, materialData.headers);

  const columnDefs = useMemo(() => {
    const actualFieldNames = Object.values(TablesMapping);
    const nonEmptyFields = materialData.headers.filter((header) =>
      actualFieldNames.includes(header)
    );

    const normalColumns = [];
    const asColumns = [];

    Object.entries(TablesMapping).forEach(([headerName, actualFieldName]) => {
      if (!nonEmptyFields.includes(actualFieldName)) return;

      const isAsColumn = actualFieldName.startsWith("as_");
      const commonColumnProps = {
        filter: "agTextColumnFilter",
        floatingFilter: true,
        suppressHeaderMenuButton: true,
        sortable: true,
        resizable: true,
        cellStyle: { fontSize: "13px" },
        pinned:
          actualFieldName === "construction_codes" ||
          actualFieldName === "material_specification",
        hide:
          actualFieldName === "id" ||
          actualFieldName === "material_specification_name" ||
          actualFieldName === "table_n",
        width: getColumnWidth(headerName),
        headerName,
        menuTabs: [],
        field: actualFieldName,
      };

      if (isAsColumn) {
        asColumns.push({
          ...commonColumnProps,
          cellStyle: (params) => {
            return params.colDef.field === highlightColumn ? { backgroundColor: 'rgba(180, 93, 195, 0.2)' } : null;
          },
        });
      } else {
        normalColumns.push({ ...commonColumnProps });
      }
    });

    if (asColumns.length > 0) {
      normalColumns.push({
        headerName: "Allowable Stress (S), psi  , for Metal Temperature, °F",
        children: asColumns.map((column) => ({
          ...column,
          headerName: column.headerName.slice(3),
          width: "80",
        })),
        suppressHeaderMenuButton: true,
      });
    }

    return normalColumns;
  }, [materialData.headers, highlightColumn]);

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setInputValues((prevValues) => ({ ...prevValues, [name]: value }));
  };

  const handleSelectChange = (key, item) => {
    // cast value to be set as string
    item = String(item);
    setInputValues((prevValues) => ({ ...prevValues, [key]: item }));
  };

  const onGridReady = (params) => {
    materialsGridApiRef.current = params.api;
    setMaterialsGridApi(params.api);
  };

  const getRowStyle = (params) => {
    if (params.data.id === currentMaterialId) {
      return { backgroundColor: "rgba(180, 93, 195, 0.25)", border: "1.2px solid  rgba(180, 93, 195, 0.8)" };
    }
  };

  // Stop editing on AgGrid
  const stopGridEditing = () => {
    if (materialsGridApi) {
      materialsGridApi.stopEditing();
    }
  };

  // Handle dialog close
  const handleDialogClose = () => {
    stopGridEditing();
    setIsMaterialPickerComponentOpen(false);
  };

  return (
    <>
      <Dialog
        usePortal={true}
        minHeight={700}
        className={`${isDarkTheme ? "bp5-dark" : ""} ${
          isTransparent ? "transparent-content" : ""
        }`}
        style={{
          width: "80%",
          height: "70%",
          overflowY: "1",
          position: "relative",
        }}
        isOpen={isMaterialPickerComponentOpen}
        onClose={handleDialogClose} // Updated to handle close
        icon="th"
        title={
          <div
            style={{
              textAlign: "left",
              marginLeft: "5px",
              fontWeight: "bold",
              fontSize: "1.2em",
            }}
          >
            Material Properties
          </div>
        }
        canEscapeKeyClose={true}
        canOutsideClickClose={true}
      >
        <Button
          className="cube-button"
          icon={isTransparent ? <HiCube /> : <PiCubeTransparentBold />}
          minimal={true}
          onClick={() => setIsTransparent(!isTransparent)}
          style={{
            position: "absolute",
            top: "5px",
            right: "50px",
            zIndex: 1000,
          }}
        />
        <DialogBody className={isTransparent ? "transparent-content" : ""}>
          <Container fluid>
            <Row>
              {Object.entries(materialsProperties).map(([key, details]) => (
                <Col key={key} xs={12} sm={6} md={4} lg={2}>
                  <div className="property-item property-container">
                    <FormGroup
                      label={
                        <>
                          <b className="ellipsis material-select-button">
                            {details.title}
                          </b>
                          {details.required && (
                            <Tag className="compact-tag" intent={Intent.SUCCESS}>
                              Required
                            </Tag>
                          )}
                        </>
                      }
                      labelFor={key}
                    >
                      {renderInputField(key, details)}
                    </FormGroup>
                  </div>
                </Col>
              ))}
            </Row>
          </Container>
          <div
            className={
              isDarkTheme ? "ag-theme-alpine-dark materials" : "ag-theme-alpine"
            }
            style={{ width: "100%", height: "60vh", fontSize: "0.8rem" }}
          >
            <AgGridReact
              getRowStyle={getRowStyle}
              className="materials"
              style={{ fontSize: "0.8rem" }}
              rowData={materialData.rows}
              columnDefs={columnDefs}
              rowHeight={24}
              rowSelection="single"
              headerHeight={35}
              gridOptions={gridOptions}
              onSelectionChanged={handleRowSelected}
              onGridReady={onGridReady}
              suppressDragLeaveHidesColumns={true}
            />
          </div>
        </DialogBody>
        <DialogFooter
          actions={
            <>
              <Button
                intent="danger"
                text="Cancel"
                onClick={handleDialogClose} // Updated to handle close
              />
              <Button
                intent="primary"
                text="Save"
                onClick={sendUpdateRequestToApi}
              />
            </>
          }
        />
      </Dialog>
    </>
  );
}

export default MaterialPicker;