import React from 'react';
import { ConfigProvider, Select as AntdSelect } from 'antd'; // Importing ConfigProvider and Select from Ant Design
import { useContext } from 'react';
import { AppContext } from '../../AppContextProvider';

const { Option } = AntdSelect;

const CustomSelect = ({ options, value, onChange, placeholder }) => {
  const { isDarkTheme } = useContext(AppContext); // Accessing the theme context

  const customToken = {
    components: {
      Select: {
        itemSelectedBg: isDarkTheme ? '#333' : '#f0f0f0',
        itemHoverBg: isDarkTheme ? '#444' : '#e6f7ff',
        borderColor: isDarkTheme ? '#555' : '#d9d9d9',
        borderRadius: '4px',
      },
    },
  };

  return (
    <ConfigProvider theme={customToken}>
      <AntdSelect
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        style={{ width: '100%' }}
        showSearch
        filterOption={(input, option) =>
          String(option.props.children).toLowerCase().includes(input.toLowerCase())
        }
      >
        {options.map((option) => (
          <Option key={option} value={String(option)}>
            {option}
          </Option>
        ))}
      </AntdSelect>
    </ConfigProvider>
  );
};

export default CustomSelect;
