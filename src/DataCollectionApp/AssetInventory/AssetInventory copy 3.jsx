import React, { useState, useEffect, useMemo, useRef, useContext } from "react";
import { AgGridReact } from "ag-grid-react";
import {
  Card,
  Intent,
  Icon,
  Tag as BPTag,
  Button,
  Tooltip,
  Position,
} from "@blueprintjs/core";
import axios from "axios";
import { Input, Select as AntSelect, Carousel, Tag as AntTag } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { HiMiniChevronUpDown as DropdownIcon } from "react-icons/hi2";
import { useSelector, useDispatch } from "react-redux";
import {
  setLookupValuesDict,
  setAppUsers,
} from "../../redux/appConfigurationSlice";
import { RiFilterOffLine } from "react-icons/ri";
import makeApiCallWithRetry from "../Helpers/apiHelper";
import ReadyFilter from "./ReadyFilter";
import { AppContext } from "../../AppContextProvider";
import { Container, Row, Col } from "react-grid-system";
import { PiTrendUp } from "react-icons/pi";
import AntLightThemeWrapper from "../../Styles/AntLightThemeWrapper";
import {
  ChevronDown,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  FileText,
  Download,
  FilterX,
  Filter,
} from "lucide-react";
import "antd/dist/reset.css";

const { Option } = AntSelect;

const contentStyle = {
  margin: 2,
  height: "65px",
  lineHeight: "25px",
  textAlign: "center",
};

function AssetInventory({
  setFocusedCell,
  setIsSearchFocused,
  handleFocusAssetClick,
  focusedCell,
  selectedSheet,
  myToaster,
  validPropertiesChange,
  setValidPropertiesChange,
  gridContainerRef,
}) {
  const [appUsersReady, setAppUsersReady] = useState(false);
  const [data, setData] = useState([]);
  const {
    isDarkTheme,
    quickFilterText,
    setQuickFilterText,
    setLookupValues,
    setUserData,
    setNotifications,
    setDropdownRules,
    sendMessage,

  } = useContext(AppContext);
  const [isLoading, setIsLoading] = useState(true);
  const [gridColumnApi, setGridColumnApi] = useState(null);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [areStatsReady, setAreStatsReady] = useState(false);
  const assetsGridApiRef = useRef(null);
  const [assetsGridApi, setAssetsGridApi] = useState(null);
  const dispatch = useDispatch();
  const appUsers = useSelector((state) => state.appConfiguration.appUsers);
  const { user } = useSelector((state) => state.auth);
  const [headers, setHeaders] = useState([]);
  const { setNpsODdata, setPipingSchedule } = useContext(AppContext);
  const [readyStats, setReadyStats] = useState({
    trueCount: 0,
    total: 0,
    truePercentage: "0.00",
    qcvApproved: 0,
    qcApproved: 0,
  });
  const { allAssetsData, setAllAssetsData } = useContext(AppContext);
  let QCV_Owner = useSelector(
    (state) => state.selectedAssetProperties.QCV_Owner
  );

  useEffect(() => {
    console.log("Fetching data...");
    axios
      .get(`${process.env.REACT_APP_MATERIALS_DATA_API}/pipe_properties/nps_od`)
      .then((response) => {
        console.log("nps", response.data);
        setNpsODdata(response.data);
      })
      .catch((error) => {
        console.error("Error fetching data: ", error);
      });

    axios
      .get(
        `${process.env.REACT_APP_MATERIALS_DATA_API}/pipe_properties/schedule`
      )
      .then((response) => {
        console.log("pipingS", response.data);
        setPipingSchedule(response.data);
      })
      .catch((error) => {
        console.error("Error fetching data: ", error);
      });
  }, [setNpsODdata, setPipingSchedule]);

  useEffect(() => {
    console.log("Fetching data...");
    console.log("User Email: AI", user.email);

    const fetchData = () => {
      return makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data`, {
        method: "get",
        params: {
          user: user.email,
        },
      });
    };

    fetchData()
      .then((response) => {
        setData(response.data.data);

        let reduceAssetData = response.data.data.reduce((acc, item) => {
          let asset = {
            Plant: item[10],
            Unit: item[11],
            System: item[12],
            "Tag Name": item[7],
            Class: item[8],
          };
          acc.push(asset);
          return acc;
        }, []);

        setAllAssetsData(reduceAssetData);

        let errorInAssets = response.data.validation;
        setNotifications(response.data.validation);

        let currentAppUsers = response.data.users["User Email"];
        console.log("appuser", currentAppUsers);
        dispatch(setAppUsers(currentAppUsers));
        setAppUsersReady(true);

        console.log("Error in Assets:", errorInAssets);

        const lookupDict = cleanLookupDict(response.data.lookup);
        setLookupValues(lookupDict);
        dispatch(setLookupValuesDict(lookupDict));
        console.log(data);
        console.log("headers imported", response.data.headers);
        const updatedHeaders = ["_index", ...response.data.headers];
        setHeaders(updatedHeaders);

        const dropdownRules = response.data.dropdown_rules;
        setDropdownRules(dropdownRules);

        console.log("dropdown", dropdownRules);

        console.log("");
        setUserData(response.data.users);

        setIsLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        setIsLoading(false);
      });
  }, []);

  const cleanLookupDict = (dict) => {
    for (const key in dict) {
      dict[key] = dict[key].filter((value) => value !== "");
    }
    return dict;
  };

  const calculateReadyStats = () => {
    console.log("Recalculating stats...");

    if (assetsGridApiRef.current) {
      let trueCount = 0;
      let total = 0;
      let qcvApproved = 0;
      let qcApproved = 0;

      assetsGridApiRef.current.forEachNodeAfterFilter((node) => {
        total++;
        if (
          node.data.Ready === "Completed" ||
          node.data.Ready === "Resubmitted"
        ) {
          trueCount++;
        }
        if (node.data.QCV === "Approved") {
          qcvApproved++;
        }
        if (node.data.QC === "Approved") {
          qcApproved++;
        }
      });

      const truePercentage = ((trueCount / total) * 100).toFixed(2);
      console.log({
        trueCount,
        total,
        truePercentage,
        qcvApproved,
        qcApproved,
      });
      setReadyStats({
        trueCount,
        total,
        truePercentage,
        qcvApproved,
        qcApproved,
      });
      setAreStatsReady(true);
    } else {
      console.warn("assetsGridApi is not initialized yet");
    }
  };

  useEffect(() => {
    if (assetsGridApi) {
      calculateReadyStats();
    }
  }, [assetsGridApi, validPropertiesChange]);

  useEffect(() => {
    if (assetsGridApi) {
      assetsGridApi.redrawRows();
      console.log("USER SESSIONS:", userSessions);
    }
  }, [userSessions, assetsGridApi]);

  useEffect(() => {
    if (assetsGridApi) {
      assetsGridApi.refreshCells({ force: true });
      console.log("userSessions changed, grid styles updated.");
    }
  }, [userSessions, assetsGridApi]);

  const onGridReady = (params) => {
    console.log("Grid is ready");
    assetsGridApiRef.current = params.api;
    setAssetsGridApi(params.api);
    setGridColumnApi(params.columnApi);
    params.api.addEventListener("filterChanged", calculateReadyStats);
  };

  const getColumnWidth = (header) => {
    switch (header) {
      case "Tag Name":
        return 180;
      case "Owner":
      case "Owner QC":
      case "Owner QCV":
        return 100;
      case "Ready":
        return 95;
      case "QC":
      case "QCV":
        return 82;
      case "Asset Classification":
        return 180;
      case "Equipment Type":
        return 150;
      default:
        return 95;
    }
  };

  const IndexCellRenderer = ({ value }) => {
    return (
      <div
        style={{
          fontWeight: "bold",
          backgroundColor: "#f0f0f0",
          color: "#333",
          padding: "4px",
        }}
      >
        {value}
      </div>
    );
  };

  const CustomTooltip = (props) => {
    const { userSessions, rowColor } = props;

    const data = useMemo(
      () => props.api.getDisplayedRowAtIndex(props.rowIndex).data,
      [props.api, props.rowIndex]
    );

    const email = useMemo(() => {
      if (!userSessions) return null;

      const sessionKeyWithEmail = Object.keys(userSessions).find(
        (key) => userSessions[key] === data["Tag Name"]
      );
      return sessionKeyWithEmail ? sessionKeyWithEmail.split("_")[0] : null;
    }, [userSessions, data]);

    if (!email) return null;

    return (
      <BPTag
        style={{
          backgroundColor: "#1C2127",
          color: "white",
          fontWeight: "bold",
        }}
      >
        {email}
      </BPTag>
    );
  };

  function SelectCellRenderer(params) {
    let checkboxProps,
      additionalIcon = null;

    switch (params.value) {
      case "Completed":
      case "Approved":
        checkboxProps = {
          icon: "tick-circle",
          intent: "success",
        };
        break;
      case "Resubmitted":
        checkboxProps = {
          icon: "tick-circle",
          intent: "primary",
        };
        additionalIcon = {
          icon: "tick-circle",
          intent: "primary",
        };
        break;
      case "Rejected":
        checkboxProps = {
          icon: "cross-circle",
          intent: "danger",
        };
        break;
      default:
        checkboxProps = {};
    }

    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: params.value ? "space-between" : "flex-end",
          width: "100%",
          minHeight: "20px",
          lineHeight: "20px",
        }}
      >
        {params.value && <Icon {...checkboxProps} />}
        {additionalIcon && <Icon {...additionalIcon} />}
        <DropdownIcon />
      </div>
    );
  }

  function OwnerCellRenderer(params) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
          minHeight: "20px",
          lineHeight: "20px",
        }}
      >
        <span>{params.value}</span>
        <DropdownIcon />
      </div>
    );
  }

  const getColorByUserIndex = (index) => {
    const colors = [
      "rgba(35,133,81,0.2)",
      "rgba(255,165,0,0.2)",
      "rgba(172,47,51,0.2)",
      "rgba(86,66,166,0.2)",
      "rgba(0,191,255,0.2)",
      "rgba(92,37,92,0.2)",
    ];
    return colors[index];
  };

  class SelectCellEditor extends React.Component {
    constructor(props) {
      super(props);
      this.state = { value: props.value };
    }

    componentDidMount() {
      document.addEventListener("keydown", this.handleKeyDown);
    }

    componentWillUnmount() {
      document.removeEventListener("keydown", this.handleKeyDown);
    }

    getValue() {
      return this.state.value;
    }

    isPopup() {
      return true;
    }

    handleKeyDown = (event) => {
      if (event.key === "Enter") {
        event.preventDefault();
      }
    };

    handleChange = async (selectedValue) => {
      const { node, column, api } = this.props;

      api.stopEditing();

      const rowData = node.data;

      const newData = {
        ...rowData,
        [column.colId]: selectedValue,
      };

      node.setData(newData);

      handleCellValueChange(column.colId, selectedValue, newData);

      api.refreshCells({
        rowNodes: [node],
        columns: [column.colId],
        force: true,
      });

      setTimeout(() => {
        api.setFocusedCell(node.rowIndex, column.colId);
      }, 0);
    };

    render() {
      const options =
        this.props.colDef.field === "Owner" ||
        this.props.colDef.field === "Owner QC" ||
        this.props.colDef.field === "Owner QCV"
          ? this.props.appUsers || []
          : this.props.colDef.field === "QC" ||
            this.props.colDef.field === "QCV"
          ? ["Approved", "Rejected", "No Action"]
          : ["Completed", "Resubmitted", "No Action"];

      return (
        <AntSelect
          showSearch
          placeholder="Select a status"
          optionFilterProp="children"
          onChange={this.handleChange}
          value={this.state.value}
          defaultOpen={true}
          style={{ width: "100%" }}
          autoFocus={true}
          popupMatchSelectWidth={false}
        >
          {options.map((option) => (
            <Option key={option} value={option}>
              {option}
            </Option>
          ))}
        </AntSelect>
      );
    }
  }

  const getRowStyle = (params) => {
    const userSessionEntries = Object.entries(userSessions);

    const isSelectedByAnyUser = userSessionEntries.some(
      ([sessionKey, tag]) => tag === params.data["Tag Name"]
    );

    if (isSelectedByAnyUser) {
      const selectedUserIndex = userSessionEntries.findIndex(
        ([sessionKey, tag]) => tag === params.data["Tag Name"]
      );

      const backgroundColor = getColorByUserIndex(selectedUserIndex);

      return {
        backgroundColor,
        border: `1.2px solid ${backgroundColor.replace(/[^,]+(?=\))/, "1")}`,
      };
    }

    return null;
  };

  const TagNameCellRenderer = (params) => {
    if (params.data["Issue Status"] === "Open") {
      return (
        <div>
          <Icon icon="issue" intent="warning" style={{ marginRight: "4px" }} />
          {params.value}
        </div>
      );
    }

    if (params.data["Issue Status"] === "Pending") {
      return (
        <div>
          <Icon icon="time" intent="primary" style={{ marginRight: "4px" }} />
          {params.value}
        </div>
      );
    }

    return <span>{params.value}</span>;
  };

  const FilesSuppliedRenderer = (params) => {
    if (params.value) {
      return (
        <Icon icon="document" intent="success" style={{ marginRight: "4px" }} />
      );
    }
    return null;
  };

  const columnDefs = useMemo(
    () =>
      headers.map((header, index) => {
        const commonProps = {
          headerName: header,
          field: header,
          sortable: true,
          filter: "agTextColumnFilter",
          resizable: true,
          width: getColumnWidth(header),
          tooltipField: header,
          getRowStyle: getRowStyle,
          tooltipComponent: (props) => {
            const rowStyle = props.node.rowStyle || {};
            const rowColor = rowStyle.backgroundColor || "transparent";
            return (
              <CustomTooltip
                {...props}
                rowColor={rowColor}
                userSessions={userSessions}
              />
            );
          },
          editable:
            header === "Ready" ||
            header === "QC" ||
            header === "QCV" ||
            header === "Owner" ||
            header === "Owner QC" ||
            header === "Owner QCV",
          hide: header === "Template" || header === "Email",
        };

        if (header === "_index") {
          return {
            ...commonProps,
            headerName: "",
            cellRenderer: "rowIndexRenderer",
            width: 35,
            suppressSizeToFit: true,
            suppressMovable: true,
            lockPosition: "left",
            valueGetter: (params) => {
              if (params.node.rowIndex != null) {
                return params.node.rowIndex + 1;
              }
              return "";
            },
            cellStyle: {
              textAlign: "right",
              fontSize: "10px",
              background: isDarkTheme ? "#383E47" : "#F5f7f7",
              color: "#abb3bf",
              paddingLeft: "0px",
              paddingRight: "4px",
            },
            sortable: false,
            filter: false,
            resizable: false,
            pinned: "left",
          };
        }

        if (header === "Tag Name") {
          return {
            ...commonProps,
            cellRenderer: "TagNameCellRenderer",
          };
        }

        if (
          header === "Owner" ||
          header === "Owner QC" ||
          header === "Owner QCV"
        ) {
          return {
            ...commonProps,
            cellEditor: "SelectCellEditor",
            cellRenderer: "OwnerCellRenderer",
            cellEditorParams: {
              appUsers: appUsers,
            },
          };
        }

        if (header === "QC" || header === "Ready" || header === "QCV") {
          return {
            ...commonProps,
            cellEditor: "SelectCellEditor",
            cellRenderer: "SelectCellRenderer",
            filterFramework: ReadyFilter,
            filterParams: {
              values: ["Completed", "Resubmitted", "Rejected", "No Action"],
            },
          };
        }
        if (header === "Files Supplied") {
          return {
            ...commonProps,
            filter: "agTextColumnFilter",
            cellRenderer: "FilesSuppliedRenderer",
            width: 130,
          };
        }

        return commonProps;
      }),
    [headers, userSessions, appUsers, getRowStyle, isDarkTheme, appUsersReady]
  );

  const rowData = useMemo(
    () =>
      data.map((row) => {
        const rowDataObject = {};
        headers.slice(1).forEach((header, index) => {
          rowDataObject[header] = row[index];
        });
        return rowDataObject;
      }),
    [data, headers]
  );

  const checkAndUpdateAllAssetsData = (selectedAsset) => {
    const currentQCV_Owner = QCV_Owner; // Redux variable
    const {
      "Tag Name": selectedTagName,
      "Asset Classification": selectedAssetClass,
      "Owner QCV": selectedQCVOwner, // Corrected column name
    } = selectedAsset;

    // Find the matching asset in allAssetsData based on 'Tag Name' and 'Asset Classification'
    const matchingAsset = allAssetsData.find(
      (asset) =>
        asset["Tag Name"] === selectedTagName &&
        asset["Asset Classification"] === selectedAssetClass
    );

    // Check if the Redux QCV_Owner and selected asset's "Owner QCV" match with allAssetsData
    if (
      matchingAsset &&
      (matchingAsset["Owner QCV"] !== currentQCV_Owner ||
        matchingAsset["Owner QCV"] !== selectedQCVOwner)
    ) {
      // Update the allAssetsData if there's a mismatch
      const updatedAllAssetsData = allAssetsData.map((asset) =>
        asset["Tag Name"] === selectedTagName &&
        asset["Asset Classification"] === selectedAssetClass
          ? { ...asset, "Owner QCV": selectedQCVOwner || currentQCV_Owner } // Use selected or Redux value
          : asset
      );

      // Set the updated allAssetsData (you may need to update it in state/context)
      setAllAssetsData(updatedAllAssetsData);

      console.log(
        "All assets data updated with new Owner QCV for selected asset"
      );
    } else {
      console.log("No discrepancies found in Owner QCV.");
    }
  };

  const handleRowSelection = () => {
    const selectedNodes = assetsGridApi.getSelectedNodes();

    if (selectedNodes && selectedNodes.length > 0) {
      const selectedData = selectedNodes[0].data;
      sendMessage(selectedData["Tag Name"]);

      console.log("selectedData:", selectedData);

      setFocusedCell(selectedData);
      setSelectedRowData(selectedData);
      handleFocusAssetClick(selectedData);

      // Add the check and update logic after handleFocusAssetClick
      checkAndUpdateAllAssetsData(selectedData);
    }
  };

  const handleCellValueChange = async (columnField, newValue, rowData) => {
    console.log("Column changing:", columnField);
    console.log("New value:", newValue);
    console.log("Row data:", rowData);

    if (assetsGridApi) {
      assetsGridApi.showLoadingOverlay();
    }

    try {
      await makeApiCallWithRetry(
        `${process.env.REACT_APP_DATA_API}/update_column/`,
        {
          method: "get",
          params: {
            plant: rowData.Plant,
            tag_name: rowData["Tag Name"],
            unit: rowData.Unit,
            system: rowData.System,
            asset_classification: rowData["Asset Classification"],
            equipment_type: rowData["Equipment Type"],
            template: rowData.Template,
            column_name: columnField,
            column_value: newValue,
            user: user.email,
          },
          headers: {
            "session-id": localStorage.getItem("session-id"),
          },
        }
      );

      if (
        ["Ready", "QC", "QCV", "Owner", "Owner QC", "Owner QCV"].includes(
          columnField
        )
      ) {
        await createTrackerRecord(columnField, newValue, rowData);
      }

      setIsLoading(false);
      console.log("Column updated successfully.");

      if (myToaster.current) {
        myToaster.current.show({
          message: `${columnField} has been updated with value: ${newValue}`,
          intent: Intent.SUCCESS,
          timeout: 4000,
          className: "font-size-11",
          icon: "tick-circle",
        });
      }
      setValidPropertiesChange((prev) => !prev);
      if (assetsGridApi) {
        assetsGridApi.hideOverlay();
      }
    } catch (error) {
      console.error("Error updating column:", error);
      if (myToaster.current) {
        myToaster.current.show({
          message: `Error updating ${columnField}: ${error.message}`,
          intent: Intent.WARNING,
          timeout: 4000,
          className: "font-size-11",
        });
      }
    }
  };

  useEffect(() => {
    if (assetsGridApi && data) {
      calculateReadyStats();
    }
  }, [assetsGridApi, data]);

  useEffect(() => {
    if (gridContainerRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        if (assetsGridApi) {
        }
      });

      resizeObserver.observe(gridContainerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [assetsGridApi, gridContainerRef]);

  const parentClassName = isDarkTheme ? "dark-theme" : "";

  const onCellClicked = (params) => {
    if (params.colDef.cellEditor === "SelectCellEditor") {
      params.api.startEditingCell({
        rowIndex: params.rowIndex,
        colKey: params.column.colId,
      });
    }
  };

  const filterByOpenIssue = () => {
    assetsGridApi.setFilterModel({
      "Issue Status": {
        type: "equals",
        filter: "Open",
      },
    });
  };

  const filterByOpenFilesSupplied = () => {
    assetsGridApi.setFilterModel({
      "Files Supplied": {
        type: "set",
        filter: true,
      },
    });
  };

  const clearFilters = () => {
    setQuickFilterText("");
    assetsGridApi.setFilterModel(null);
  };

  const DownloadCSV = () => {
    assetsGridApi.exportDataAsCsv({ fileName: "AssetInventory.csv" });
  };

  const StatusLegend = () => {
    return (
      <Card className="no-padding-card">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-around",
            padding: "1px",
            margin: "1px 0",
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="success"
              className="bp5-dark"
            />{" "}
            <span style={{ marginLeft: "5px" }}>Completed/Approved</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              className="bp5-dark"
            />
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              style={{ marginLeft: "1px" }}
            />{" "}
            <span style={{ marginLeft: "5px" }}>Resubmitted</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="cross-circle" size="12" intent="danger" />{" "}
            <span style={{ marginLeft: "5px" }}>Rejected</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="issue" size="12" intent="warning" />{" "}
            <span style={{ marginLeft: "5px" }}>Open Issue</span>
          </div>
        </div>
      </Card>
    );
  };

  const createTrackerRecord = async (columnField, newValue, rowData) => {
    try {
      const payload = {
        owner: columnField === "Owner" ? newValue : rowData.Owner,
        owner_qc: columnField === "Owner QC" ? newValue : rowData["Owner QC"],
        owner_qcv:
          columnField === "Owner QCV" ? newValue : rowData["Owner QCV"],
        submitter: user.email,
        action: newValue,
        step: columnField,
        tag_name: rowData["Tag Name"],
        plant: rowData.Plant,
        unit: rowData.Unit,
        system: rowData.System,
        asset_classification: rowData["Asset Classification"],
        equipment_type: rowData["Equipment Type"],
      };

      console.log("Creating tracker record:", payload);

      const response = await axios.post(
        `${process.env.REACT_APP_DATA_API}/inventory_action_tracker/`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            "session-id": localStorage.getItem("session-id"),
          },
        }
      );

      console.log("Tracker record created successfully:", response.data);
    } catch (error) {
      console.error("Error creating tracker record:", error);
    }
  };

  return (
    <Card style={{ height: "100%", marginBottom: "0px" }}>
      <Container className={parentClassName} fluid style={{ padding: "2px" }}>
        <Row>
          <Col>
            <Row>
              <Col sm={12}>
                <h3
                  style={{
                    margin: "0px",
                    fontWeight: "bold",
                    paddingLeft: "0px",
                    width: "100",
                  }}
                >
                  Asset Inventory
                </h3>
                <Button
                  icon={<RiFilterOffLine size={18} />}
                  minimal={true}
                  onClick={clearFilters}
                  style={{ marginLeft: "0px", paddingBottom: "12px" }}
                ></Button>
                <Tooltip
                  content="Filter by Open Issue"
                  position={Position.BOTTOM}
                  intent="primary"
                >
                  <Button
                    icon={
                      <Icon
                        icon="issue"
                        intent="warning"
                        size={18}
                        style={{ paddingLeft: "11px" }}
                      />
                    }
                    minimal={true}
                    onClick={filterByOpenIssue}
                    style={{ marginLeft: "0px", paddingBottom: "5px" }}
                  ></Button>
                </Tooltip>
                <Tooltip
                  content="Filter by Files Supplied"
                  position={Position.BOTTOM}
                  intent="primary"
                >
                  <Button
                    icon={
                      <Icon
                        icon="document"
                        size={18}
                        style={{ paddingLeft: "11px" }}
                      />
                    }
                    minimal={true}
                    onClick={filterByOpenFilesSupplied}
                    style={{ marginLeft: "0px", paddingBottom: "5px" }}
                  ></Button>
                </Tooltip>
                <Tooltip
                  content="Download Asset Inventory"
                  position={Position.BOTTOM}
                  intent="primary"
                >
                  <Button
                    icon={
                      <Icon
                        icon="download"
                        size={18}
                        style={{ paddingLeft: "11px" }}
                      />
                    }
                    minimal={true}
                    onClick={DownloadCSV}
                    style={{ marginLeft: "0px", paddingBottom: "5px" }}
                  ></Button>
                </Tooltip>
              </Col>
            </Row>
            <Row>
              <Col sm={12}>
                <Input
                  variant="outlined"
                  autoFocus
                  style={{ width: "100%" }}
                  prefix={<SearchOutlined />}
                  placeholder="Search..."
                  onChange={(e) => setQuickFilterText(e.target.value)}
                  value={quickFilterText}
                />
              </Col>
            </Row>
          </Col>
          <Col sm={6}>
            <div>
              {isLoading || !data.length ? (
                <p>Loading...</p>
              ) : (
                <Carousel arrows={true} arrowSize={100}>
                  <div>
                    <div style={contentStyle}>
                      <div style={{ fontWeight: "bold" }}>
                        <BPTag
                          intent="primary"
                          style={{ fontWeigth: "bold" }}
                          color="blue"
                        >
                          Ready
                        </BPTag>
                      </div>
                      <h3>
                        {readyStats.trueCount} / {readyStats.total} (
                        {readyStats.truePercentage}%)
                      </h3>
                    </div>
                  </div>
                  <div>
                    <div style={contentStyle}>
                      <div style={{ fontWeight: "bold" }}>
                        <BPTag
                          intent="primary"
                          style={{ fontWeigth: "bold" }}
                          color="purple"
                        >
                          QC
                        </BPTag>
                      </div>
                      <h3>
                        {readyStats.qcApproved} / {readyStats.total} (
                        {(
                          (readyStats.qcApproved / readyStats.total) *
                          100
                        ).toFixed(2)}
                        %)
                      </h3>
                    </div>
                  </div>
                  <div>
                    <div style={contentStyle}>
                      <div style={{ fontWeight: "bold" }}>
                        <BPTag intent="success" style={{ fontWeigth: "bold" }}>
                          QCV
                        </BPTag>
                      </div>
                      <h3>
                        {readyStats.qcvApproved} / {readyStats.total} (
                        {(
                          (readyStats.qcvApproved / readyStats.total) *
                          100
                        ).toFixed(2)}
                        %)
                      </h3>
                    </div>
                  </div>
                </Carousel>
              )}
            </div>
          </Col>
        </Row>
      </Container>
      <div
        ref={gridContainerRef}
        className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"}
        style={{ height: "calc(100% - 110px)", width: "100%" }}
      >
        <StatusLegend />
        <AgGridReact
          id="assetGrid"
          gridOptions={{
            undoRedoCellEditing: true,
            undoRedoCellEditingLimit: 30,
            rowDragManaged: true,
            components: {
              SelectCellEditor: SelectCellEditor,
              SelectCellRenderer: SelectCellRenderer,
              IndexCellRenderer: IndexCellRenderer,
              TagNameCellRenderer: TagNameCellRenderer,
              FilesSuppliedRenderer: FilesSuppliedRenderer,
              OwnerCellRenderer: OwnerCellRenderer,
            },
            context: {
              appUsers: appUsers,
            },
          }}
          tooltipShowDelay={0}
          getRowStyle={getRowStyle}
          headerHeight={40}
          onGridReady={onGridReady}
          columnDefs={columnDefs}
          rowData={rowData}
          onSelectionChanged={handleRowSelection}
          rowSelection="single"
          getRowHeight={() => 20}
          quickFilterText={quickFilterText}
          onCellValueChanged={(params) => {
            console.log("onCellValueChanged:", params);
            handleCellValueChange(
              params.colDef.field,
              params.newValue,
              params.data
            );
          }}
          enableFillHandle={true}
          enableRangeSelection={true}
          enableCellTextSelection={true}
          suppressDragLeaveHidesColumns={true}
          stopEditingWhenCellsLoseFocus={true}
          frameworkComponents={{
            SelectCellEditor,
            SelectCellRenderer,
            IndexCellRenderer,
            TagNameCellRenderer,
            ReadyFilter,
            OwnerCellRenderer,
          }}
        />
      </div>
    </Card>
  );
}

export default React.memo(AssetInventory);
