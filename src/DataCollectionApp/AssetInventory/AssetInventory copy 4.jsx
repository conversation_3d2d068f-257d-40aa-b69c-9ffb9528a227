import React, { useState, useEffect, useMemo, useRef, useContext } from "react";
import { AgGridReact } from "ag-grid-react";
import {
  Card,
  Intent,
  Icon,
  Tag as BPTag,
  Button,
  Tooltip,
  Position,
} from "@blueprintjs/core";
import axios from "axios";
import { Input, Select as AntSelect, Carousel, Tag as AntTag } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { HiMiniChevronUpDown as DropdownIcon } from "react-icons/hi2";
import { useSelector, useDispatch } from "react-redux";
import {
  setLookupValuesDict,
  setAppUsers,
} from "../../redux/appConfigurationSlice";
import { RiFilterOffLine } from "react-icons/ri";
import makeApiCallWithRetry from "../Helpers/apiHelper";
import ReadyFilter from "./ReadyFilter";
import { AppContext } from "../../AppContextProvider";
import { Container, Row, Col } from "react-grid-system";
import { PiTrendUp } from "react-icons/pi";
import AntLightThemeWrapper from "../../Styles/AntLightThemeWrapper";
import {
  ChevronDown,
  CheckCircle,
  XCircle,
  AlertCircle,
  Clock,
  FileText,
  Download,
  FilterX,
  Filter,
} from "lucide-react";
import "antd/dist/reset.css";
import { setSelectedAssetSearchKey  } from "../../redux/appConfigurationSlice";

const { Option } = AntSelect;

const contentStyle = {
  margin: 2,
  height: "65px",
  lineHeight: "25px",
  textAlign: "center",
};

function AssetInventory({
  setFocusedCell,
  setIsSearchFocused,
  handleFocusAssetClick,
  focusedCell,
  selectedSheet,
  myToaster,
  validPropertiesChange,
  setValidPropertiesChange,
  gridContainerRef,
}) {
  const [appUsersReady, setAppUsersReady] = useState(false);
  const [data, setData] = useState([]);
  const {
    isDarkTheme,
    quickFilterText,
    setQuickFilterText,
    setLookupValues,
    setUserData,
    setNotifications,
    setDropdownRules,
    sendMessage,
  } = useContext(AppContext);
  const [isLoading, setIsLoading] = useState(true);
  const [gridColumnApi, setGridColumnApi] = useState(null);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [areStatsReady, setAreStatsReady] = useState(false);
  const assetsGridApiRef = useRef(null);
  const [assetsGridApi, setAssetsGridApi] = useState(null);
  const dispatch = useDispatch();
  const appUsers = useSelector((state) => state.appConfiguration.appUsers);
  const { user } = useSelector((state) => state.auth);
  const [headers, setHeaders] = useState([]);
  const { setNpsODdata, setPipingSchedule } = useContext(AppContext);
  const [readyStats, setReadyStats] = useState({
    trueCount: 0,
    total: 0,
    truePercentage: "0.00",
    qcvApproved: 0,
    qcApproved: 0,
  });
  const { allAssetsData, setAllAssetsData } = useContext(AppContext);
  let QCV_Owner = useSelector(
    (state) => state.selectedAssetProperties.QCV_Owner
  );

  useEffect(() => {
    console.log("Fetching data...");
    axios
      .get(`${process.env.REACT_APP_MATERIALS_DATA_API}/pipe_properties/nps_od`)
      .then((response) => {
        console.log("nps", response.data);
        setNpsODdata(response.data);
      })
      .catch((error) => {
        console.error("Error fetching data: ", error);
      });

    axios
      .get(
        `${process.env.REACT_APP_MATERIALS_DATA_API}/pipe_properties/schedule`
      )
      .then((response) => {
        console.log("pipingS", response.data);
        setPipingSchedule(response.data);
      })
      .catch((error) => {
        console.error("Error fetching data: ", error);
      });
  }, [setNpsODdata, setPipingSchedule]);

  useEffect(() => {
    console.log("Fetching data...");
    console.log("User Email: AI", user.email);

    const fetchData = () => {
      return makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data`, {
        method: "get",
        params: {
          user: user.email,
        },
      });
    };

    fetchData()
      .then((response) => {
        setData(response.data.data);

        let reduceAssetData = response.data.data.reduce((acc, item) => {
          let asset = {
            Plant: item[10],
            Unit: item[11],
            System: item[12],
            "Tag Name": item[7],
            Class: item[8],
            "Owner QCV": item[13],
          };
          acc.push(asset);
          return acc;
        }, []);

        setAllAssetsData(reduceAssetData);

        let errorInAssets = response.data.validation;
        setNotifications(response.data.validation);

        let currentAppUsers = response.data.users["User Email"];
        console.log("appuser", currentAppUsers);
        dispatch(setAppUsers(currentAppUsers));
        setAppUsersReady(true);

        console.log("Error in Assets:", errorInAssets);

        const lookupDict = cleanLookupDict(response.data.lookup);
        setLookupValues(lookupDict);
        dispatch(setLookupValuesDict(lookupDict));
        console.log(data);
        console.log("headers imported", response.data.headers);
        const updatedHeaders = ["_index", ...response.data.headers];
        setHeaders(updatedHeaders);

        const dropdownRules = response.data.dropdown_rules;
        setDropdownRules(dropdownRules);

        console.log("dropdown", dropdownRules);

        console.log("");
        setUserData(response.data.users);

        setIsLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        setIsLoading(false);
      });
  }, []);

  const cleanLookupDict = (dict) => {
    for (const key in dict) {
      dict[key] = dict[key].filter((value) => value !== "");
    }
    return dict;
  };

  const calculateReadyStats = () => {
    console.log("Recalculating stats...");

    if (assetsGridApiRef.current) {
      let trueCount = 0;
      let total = 0;
      let qcvApproved = 0;
      let qcApproved = 0;

      assetsGridApiRef.current.forEachNodeAfterFilter((node) => {
        total++;
        if (
          node.data.Ready === "Completed" ||
          node.data.Ready === "Resubmitted"
        ) {
          trueCount++;
        }
        if (node.data.QCV === "Approved") {
          qcvApproved++;
        }
        if (node.data.QC === "Approved") {
          qcApproved++;
        }
      });

      const truePercentage = ((trueCount / total) * 100).toFixed(2);
      console.log({
        trueCount,
        total,
        truePercentage,
        qcvApproved,
        qcApproved,
      });
      setReadyStats({
        trueCount,
        total,
        truePercentage,
        qcvApproved,
        qcApproved,
      });
      setAreStatsReady(true);
    } else {
      console.warn("assetsGridApi is not initialized yet");
    }
  };

  useEffect(() => {
    if (assetsGridApi) {
      calculateReadyStats();
    }
  }, [assetsGridApi, validPropertiesChange]);

  const onGridReady = (params) => {
    console.log("Grid is ready");
    assetsGridApiRef.current = params.api;
    setAssetsGridApi(params.api);
    setGridColumnApi(params.columnApi);
    params.api.addEventListener("filterChanged", calculateReadyStats);
  };

  const getColumnWidth = (header) => {
    switch (header) {
      case "Tag Name":
        return 180;
      case "Owner":
      case "Owner QC":
      case "Owner QCV":
        return 100;
      case "Ready":
        return 95;
      case "QC":
      case "QCV":
        return 82;
      case "Asset Classification":
        return 180;
      case "Equipment Type":
        return 150;
      default:
        return 95;
    }
  };

  const IndexCellRenderer = ({ value }) => {
    return (
      <div
        style={{
          fontWeight: "bold",
          backgroundColor: "#f0f0f0",
          color: "#333",
          padding: "4px",
        }}
      >
        {value}
      </div>
    );
  };

  function SelectCellRenderer(params) {
    let checkboxProps,
      additionalIcon = null;

    switch (params.value) {
      case "Completed":
      case "Approved":
        checkboxProps = {
          icon: "tick-circle",
          intent: "success",
        };
        break;
      case "Resubmitted":
        checkboxProps = {
          icon: "tick-circle",
          intent: "primary",
        };
        additionalIcon = {
          icon: "tick-circle",
          intent: "primary",
        };
        break;
      case "Rejected":
        checkboxProps = {
          icon: "cross-circle",
          intent: "danger",
        };
        break;
      default:
        checkboxProps = {};
    }

    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: params.value ? "space-between" : "flex-end",
          width: "100%",
          minHeight: "20px",
          lineHeight: "20px",
        }}
      >
        {params.value && <Icon {...checkboxProps} />}
        {additionalIcon && <Icon {...additionalIcon} />}
        <DropdownIcon />
      </div>
    );
  }

  function OwnerCellRenderer(params) {
    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
          minHeight: "20px",
          lineHeight: "20px",
        }}
      >
        <span>{params.value}</span>
        <DropdownIcon />
      </div>
    );
  }

  const getColorByUserIndex = (index) => {
    const colors = [
      "rgba(35,133,81,0.2)",
      "rgba(255,165,0,0.2)",
      "rgba(172,47,51,0.2)",
      "rgba(86,66,166,0.2)",
      "rgba(0,191,255,0.2)",
      "rgba(92,37,92,0.2)",
    ];
    return colors[index];
  };

  class SelectCellEditor extends React.Component {
    constructor(props) {
      super(props);
      this.state = { value: props.value };
    }

    componentDidMount() {
      document.addEventListener("keydown", this.handleKeyDown);
    }

    componentWillUnmount() {
      document.removeEventListener("keydown", this.handleKeyDown);
    }

    getValue() {
      return this.state.value;
    }

    isPopup() {
      return true;
    }

    handleKeyDown = (event) => {
      if (event.key === "Enter") {
        event.preventDefault();
      }
    };

    handleChange = async (selectedValue) => {
      const { node, column, api } = this.props;

      api.stopEditing();

      const rowData = node.data;

      const newData = {
        ...rowData,
        [column.colId]: selectedValue,
      };

      node.setData(newData);

      handleCellValueChange(column.colId, selectedValue, newData);

      api.refreshCells({
        rowNodes: [node],
        columns: [column.colId],
        force: true,
      });

      setTimeout(() => {
        api.setFocusedCell(node.rowIndex, column.colId);
      }, 0);
    };

    render() {
      const options =
        this.props.colDef.field === "Owner" ||
        this.props.colDef.field === "Owner QC" ||
        this.props.colDef.field === "Owner QCV"
          ? this.props.appUsers || []
          : this.props.colDef.field === "QC" ||
            this.props.colDef.field === "QCV"
          ? ["Approved", "Rejected", "No Action"]
          : ["Completed", "Resubmitted", "No Action"];

      return (
        <AntSelect
          showSearch
          placeholder="Select a status"
          optionFilterProp="children"
          onChange={this.handleChange}
          value={this.state.value}
          defaultOpen={true}
          style={{ width: "100%" }}
          autoFocus={true}
          popupMatchSelectWidth={false}
        >
          {options.map((option) => (
            <Option key={option} value={option}>
              {option}
            </Option>
          ))}
        </AntSelect>
      );
    }
  }

  const TagNameCellRenderer = (params) => {
    if (params.data["Issue Status"] === "Open") {
      return (
        <div>
          <Icon icon="issue" intent="warning" style={{ marginRight: "4px" }} />
          {params.value}
        </div>
      );
    }

    if (params.data["Issue Status"] === "Pending") {
      return (
        <div>
          <Icon icon="time" intent="primary" style={{ marginRight: "4px" }} />
          {params.value}
        </div>
      );
    }

    return <span>{params.value}</span>;
  };

  const FilesSuppliedRenderer = (params) => {
    if (params.value) {
      return (
        <Icon icon="document" intent="success" style={{ marginRight: "4px" }} />
      );
    }
    return null;
  };

  const columnDefs = useMemo(
    () =>
      headers.map((header, index) => {
        const commonProps = {
          headerName: header,
          field: header,
          sortable: true,
          filter: "agTextColumnFilter",
          resizable: true,
          width: getColumnWidth(header),
          tooltipField: header,
          editable:
            header === "Ready" ||
            header === "QC" ||
            header === "QCV" ||
            header === "Owner" ||
            header === "Owner QC" ||
            header === "Owner QCV",
          hide: header === "Template" || header === "Email"  || header === "Search Key"
          //|| header === "Search Key"
        };

        if (header === "_index") {
          return {
            ...commonProps,
            headerName: "",
            cellRenderer: "rowIndexRenderer",
            width: 35,
            suppressSizeToFit: true,
            suppressMovable: true,
            lockPosition: "left",
            valueGetter: (params) => {
              if (params.node.rowIndex != null) {
                return params.node.rowIndex + 1;
              }
              return "";
            },
            cellStyle: {
              textAlign: "right",
              fontSize: "10px",
              background: isDarkTheme ? "#383E47" : "#F5f7f7",
              color: "#abb3bf",
              paddingLeft: "0px",
              paddingRight: "4px",
            },
            sortable: false,
            filter: false,
            resizable: false,
            pinned: "left",
          };
        }

        if (header === "Tag Name") {
          return {
            ...commonProps,
            cellRenderer: "TagNameCellRenderer",
          };
        }

        if (
          header === "Owner" ||
          header === "Owner QC" ||
          header === "Owner QCV"
        ) {
          return {
            ...commonProps,
            cellEditor: "SelectCellEditor",
            cellRenderer: "OwnerCellRenderer",
            cellEditorParams: {
              appUsers: appUsers,
            },
          };
        }

        if (header === "QC" || header === "Ready" || header === "QCV") {
          return {
            ...commonProps,
            cellEditor: "SelectCellEditor",
            cellRenderer: "SelectCellRenderer",
            filterFramework: ReadyFilter,
            filterParams: {
              values: ["Completed", "Resubmitted", "Rejected", "No Action"],
            },
          };
        }
        if (header === "Files Supplied") {
          return {
            ...commonProps,
            filter: "agTextColumnFilter",
            cellRenderer: "FilesSuppliedRenderer",
            width: 130,
          };
        }

        return commonProps;
      }),
    [headers, allAssetsData, appUsers, isDarkTheme, appUsersReady]
  );

  const rowData = useMemo(
    () =>
      data.map((row) => {
        const rowDataObject = {};
        headers.slice(1).forEach((header, index) => {
          rowDataObject[header] = row[index];
        });
        return rowDataObject;
      }),
    [data, headers]
  );

  const checkAndUpdateAllAssetsData = (selectedAsset) => {
    setTimeout(() => {
      const currentQCV_Owner = QCV_Owner; // Redux variable for the QCV Owner
      const {
        "Tag Name": selectedTagName,
        "Asset Classification": selectedAssetClass,
        "Owner QCV": selectedQCVOwner,
      } = selectedAsset;

      // Log the relevant fields for debugging
      console.log("Selected Asset:", selectedAsset);
      console.log("Redux QCV_Owner:", currentQCV_Owner);
      console.log("Selected Row Owner QCV:", selectedQCVOwner);
      console.log("All Assets Data:", allAssetsData);

      // Find the matching asset in allAssetsData
      const matchingAsset = allAssetsData.find(
        (asset) =>
          asset["Tag Name"]?.trim() === selectedTagName?.trim() &&
          asset["Class"]?.trim() === selectedAssetClass?.trim()
      );

      console.log("Matching Asset in allAssetsData:", matchingAsset);

      if (!matchingAsset) {
        console.log("No matching asset found in allAssetsData.");
        return;
      }

      // Update QCV Owner and currentAsset if there are discrepancies
      if (
        matchingAsset["Owner QCV"]?.trim() !== currentQCV_Owner?.trim() ||
        matchingAsset["Owner QCV"]?.trim() !== selectedQCVOwner?.trim()
      ) {
        console.log("Discrepancy found in Owner QCV!");

        // Update allAssetsData with correct Owner QCV
        const updatedAllAssetsData = allAssetsData.map((asset) =>
          asset["Tag Name"]?.trim() === selectedTagName?.trim() &&
          asset["Class"]?.trim() === selectedAssetClass?.trim()
            ? { ...asset, "Owner QCV": selectedQCVOwner || currentQCV_Owner }
            : asset
        );

        setAllAssetsData(updatedAllAssetsData);
        console.log("All assets data updated with new Owner QCV for selected asset");

        // Now also update the grid data and highlight the updated row
        assetsGridApi.forEachNode((rowNode) => {
          if (
            rowNode.data["Tag Name"]?.trim() === selectedTagName?.trim() &&
            rowNode.data["Class"]?.trim() === selectedAssetClass?.trim()
          ) {
            rowNode.setData({
              ...rowNode.data,
              "Owner QCV": selectedQCVOwner || currentQCV_Owner,
            });
            // Highlight the updated row in yellow
            rowNode.setRowStyle({ backgroundColor: "yellow" });
          }
        });

        console.log("AgGrid row updated and highlighted with new Owner QCV.");
      } else {
        console.log("No discrepancies found in Owner QCV.");
      }
    }, 200); // Timeout to ensure Redux state has settled
  };

  const handleRowSelection = () => {
    const selectedNodes = assetsGridApi.getSelectedNodes();


    if (selectedNodes && selectedNodes.length > 0) {
      const selectedData = selectedNodes[0].data;
      sendMessage(selectedData["Tag Name"]);

      console.log("Selected Data:", selectedData); // Log the selected data

      setFocusedCell(selectedData);
      setSelectedRowData(selectedData);
      handleFocusAssetClick(selectedData);
      dispatch(setSelectedAssetSearchKey(selectedData["Search Key"]));

    }
  };

  const handleCellValueChange = async (columnField, newValue, rowData) => {
    console.log("Column changing:", columnField);
    console.log("New value:", newValue);
    console.log("Row data:", rowData);

    if (assetsGridApi) {
      assetsGridApi.showLoadingOverlay();
    }

    try {
      await makeApiCallWithRetry(
        `${process.env.REACT_APP_DATA_API}/update_column/`,
        {
          method: "get",
          params: {
            plant: rowData.Plant,
            tag_name: rowData["Tag Name"],
            unit: rowData.Unit,
            system: rowData.System,
            asset_classification: rowData["Asset Classification"],
            equipment_type: rowData["Equipment Type"],
            template: rowData.Template,
            column_name: columnField,
            column_value: newValue,
            user: user.email,
          },
          headers: {
            "session-id": localStorage.getItem("session-id"),
          },
        }
      );

      if (
        ["Ready", "QC", "QCV", "Owner", "Owner QC", "Owner QCV"].includes(
          columnField
        )
      ) {
        await createTrackerRecord(columnField, newValue, rowData);
      }

      setIsLoading(false);
      console.log("Column updated successfully.");

      if (myToaster.current) {
        myToaster.current.show({
          message: `${columnField} has been updated with value: ${newValue}`,
          intent: Intent.SUCCESS,
          timeout: 4000,
          className: "font-size-11",
          icon: "tick-circle",
        });
      }
      setValidPropertiesChange((prev) => !prev);
      if (assetsGridApi) {
        assetsGridApi.hideOverlay();
      }
    } catch (error) {
      console.error("Error updating column:", error);
      if (myToaster.current) {
        myToaster.current.show({
          message: `Error updating ${columnField}: ${error.message}`,
          intent: Intent.WARNING,
          timeout: 4000,
          className: "font-size-11",
        });
      }
    }
  };

  useEffect(() => {
    if (assetsGridApi && data) {
      calculateReadyStats();
    }
  }, [assetsGridApi, data]);

  useEffect(() => {
    if (gridContainerRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        if (assetsGridApi) {
        }
      });

      resizeObserver.observe(gridContainerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [assetsGridApi, gridContainerRef]);

  const parentClassName = isDarkTheme ? "dark-theme" : "";

  const onCellClicked = (params) => {
    if (params.colDef.cellEditor === "SelectCellEditor") {
      params.api.startEditingCell({
        rowIndex: params.rowIndex,
        colKey: params.column.colId,
      });
    }
  };

  const filterByOpenIssue = () => {
    assetsGridApi.setFilterModel({
      "Issue Status": {
        type: "equals",
        filter: "Open",
      },
    });
  };

  const filterByOpenFilesSupplied = () => {
    assetsGridApi.setFilterModel({
      "Files Supplied": {
        type: "set",
        filter: true,
      },
    });
  };

  const clearFilters = () => {
    setQuickFilterText("");
    assetsGridApi.setFilterModel(null);
  };

  const DownloadCSV = () => {
    assetsGridApi.exportDataAsCsv({ fileName: "AssetInventory.csv" });
  };

  const StatusLegend = () => {
    return (
      <Card className="no-padding-card">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-around",
            padding: "1px",
            margin: "1px 0",
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="success"
              className="bp5-dark"
            />{" "}
            <span style={{ marginLeft: "5px" }}>Completed/Approved</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              className="bp5-dark"
            />
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              style={{ marginLeft: "1px" }}
            />{" "}
            <span style={{ marginLeft: "5px" }}>Resubmitted</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="cross-circle" size="12" intent="danger" />{" "}
            <span style={{ marginLeft: "5px" }}>Rejected</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="issue" size="12" intent="warning" />{" "}
            <span style={{ marginLeft: "5px" }}>Open Issue</span>
          </div>
        </div>
      </Card>
    );
  };

  const createTrackerRecord = async (columnField, newValue, rowData) => {
    try {
      const payload = {
        owner: columnField === "Owner" ? newValue : rowData.Owner,
        owner_qc: columnField === "Owner QC" ? newValue : rowData["Owner QC"],
        owner_qcv:
          columnField === "Owner QCV" ? newValue : rowData["Owner QCV"],
        submitter: user.email,
        action: newValue,
        step: columnField,
        tag_name: rowData["Tag Name"],
        plant: rowData.Plant,
        unit: rowData.Unit,
        system: rowData.System,
        asset_classification: rowData["Asset Classification"],
        equipment_type: rowData["Equipment Type"],
      };

      console.log("Creating tracker record:", payload);

      const response = await axios.post(
        `${process.env.REACT_APP_DATA_API}/inventory_action_tracker/`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            "session-id": localStorage.getItem("session-id"),
          },
        }
      );

      console.log("Tracker record created successfully:", response.data);
    } catch (error) {
      console.error("Error creating tracker record:", error);
    }
  };

  return (
    <Card style={{ height: "100%", marginBottom: "0px" }}>
      <Container className={parentClassName} fluid style={{ padding: "2px" }}>
        <Row>
          <Col>
            <Row>
              <Col sm={12}>
                <h3
                  style={{
                    margin: "0px",
                    fontWeight: "bold",
                    paddingLeft: "0px",
                    width: "100",
                  }}
                >
                  Asset Inventory
                </h3>
                <Button
                  icon={<RiFilterOffLine size={18} />}
                  minimal={true}
                  onClick={clearFilters}
                  style={{ marginLeft: "0px", paddingBottom: "12px" }}
                ></Button>
                <Tooltip
                  content="Filter by Open Issue"
                  position={Position.BOTTOM}
                  intent="primary"
                >
                  <Button
                    icon={
                      <Icon
                        icon="issue"
                        intent="warning"
                        size={18}
                        style={{ paddingLeft: "11px" }}
                      />
                    }
                    minimal={true}
                    onClick={filterByOpenIssue}
                    style={{ marginLeft: "0px", paddingBottom: "5px" }}
                  ></Button>
                </Tooltip>
                <Tooltip
                  content="Filter by Files Supplied"
                  position={Position.BOTTOM}
                  intent="primary"
                >
                  <Button
                    icon={
                      <Icon
                        icon="document"
                        size={18}
                        style={{ paddingLeft: "11px" }}
                      />
                    }
                    minimal={true}
                    onClick={filterByOpenFilesSupplied}
                    style={{ marginLeft: "0px", paddingBottom: "5px" }}
                  ></Button>
                </Tooltip>
                <Tooltip
                  content="Download Asset Inventory"
                  position={Position.BOTTOM}
                  intent="primary"
                >
                  <Button
                    icon={
                      <Icon
                        icon="download"
                        size={18}
                        style={{ paddingLeft: "11px" }}
                      />
                    }
                    minimal={true}
                    onClick={DownloadCSV}
                    style={{ marginLeft: "0px", paddingBottom: "5px" }}
                  ></Button>
                </Tooltip>
              </Col>
            </Row>
            <Row>
              <Col sm={12}>
                <Input
                  variant="outlined"
                  autoFocus
                  style={{ width: "100%" }}
                  prefix={<SearchOutlined />}
                  placeholder="Search..."
                  onChange={(e) => setQuickFilterText(e.target.value)}
                  value={quickFilterText}
                />
              </Col>
            </Row>
          </Col>
          <Col sm={6}>
            <div>
              {isLoading || !data.length ? (
                <p>Loading...</p>
              ) : (
                <Carousel arrows={true} arrowSize={100}>
                  <div>
                    <div style={contentStyle}>
                      <div style={{ fontWeight: "bold" }}>
                        <BPTag
                          intent="primary"
                          style={{ fontWeigth: "bold" }}
                          color="blue"
                        >
                          Ready
                        </BPTag>
                      </div>
                      <h3>
                        {readyStats.trueCount} / {readyStats.total} (
                        {readyStats.truePercentage}%)
                      </h3>
                    </div>
                  </div>
                  <div>
                    <div style={contentStyle}>
                      <div style={{ fontWeight: "bold" }}>
                        <BPTag
                          intent="primary"
                          style={{ fontWeigth: "bold" }}
                          color="purple"
                        >
                          QC
                        </BPTag>
                      </div>
                      <h3>
                        {readyStats.qcApproved} / {readyStats.total} (
                        {(
                          (readyStats.qcApproved / readyStats.total) *
                          100
                        ).toFixed(2)}
                        %)
                      </h3>
                    </div>
                  </div>
                  <div>
                    <div style={contentStyle}>
                      <div style={{ fontWeight: "bold" }}>
                        <BPTag intent="success" style={{ fontWeigth: "bold" }}>
                          QCV
                        </BPTag>
                      </div>
                      <h3>
                        {readyStats.qcvApproved} / {readyStats.total} (
                        {(
                          (readyStats.qcvApproved / readyStats.total) *
                          100
                        ).toFixed(2)}
                        %)
                      </h3>
                    </div>
                  </div>
                </Carousel>
              )}
            </div>
          </Col>
        </Row>
      </Container>
      <div
        ref={gridContainerRef}
        className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"}
        style={{ height: "calc(100% - 110px)", width: "100%" }}
      >
        <StatusLegend />
        <AgGridReact
          id="assetGrid"
          gridOptions={{
            undoRedoCellEditing: true,
            undoRedoCellEditingLimit: 30,
            rowDragManaged: true,
            components: {
              SelectCellEditor: SelectCellEditor,
              SelectCellRenderer: SelectCellRenderer,
              IndexCellRenderer: IndexCellRenderer,
              TagNameCellRenderer: TagNameCellRenderer,
              FilesSuppliedRenderer: FilesSuppliedRenderer,
              OwnerCellRenderer: OwnerCellRenderer,
            },
            context: {
              appUsers: appUsers,
            },
          }}
          tooltipShowDelay={0}
          headerHeight={40}
          onGridReady={onGridReady}
          columnDefs={columnDefs}
          rowData={rowData}
          onSelectionChanged={handleRowSelection}
          rowSelection="single"
          getRowHeight={() => 20}
          quickFilterText={quickFilterText}
          onCellValueChanged={(params) => {
            console.log("onCellValueChanged:", params);
            handleCellValueChange(
              params.colDef.field,
              params.newValue,
              params.data
            );
          }}
          enableFillHandle={true}
          enableRangeSelection={true}
          enableCellTextSelection={true}
          suppressDragLeaveHidesColumns={true}
          stopEditingWhenCellsLoseFocus={true}
          frameworkComponents={{
            SelectCellEditor,
            SelectCellRenderer,
            IndexCellRenderer,
            TagNameCellRenderer,
            ReadyFilter,
            OwnerCellRenderer,
          }}
        />
      </div>
    </Card>
  );
}

export default React.memo(AssetInventory);
