import React, { useState, useEffect, useMemo, useRef, useContext } from "react";
import { AgGridReact } from "ag-grid-react";
import { Card, Intent, Icon, Tag } from "@blueprintjs/core";
import axios from "axios";
import { useCopyPaste } from "../../hooks/useCopyPaste";
import { AppContext } from "../../AppContextProvider";
import { Statistic } from "antd";
import { Input } from "antd"; // Importing Input from Ant Design
import "primereact/resources/themes/nova/theme.css"; //theme
import "primereact/resources/primereact.min.css"; //core css
import "primeicons/primeicons.css"; //icons
import { SearchOutlined } from "@ant-design/icons";
import { HiMiniChevronUpDown as DropdownIcon } from "react-icons/hi2";
import { Select as AntSelect } from "antd";
import { lineSpinner } from "ldrs";
import { quantum } from "ldrs";
import { useSelector, useDispatch } from "react-redux";
import { setLookupValuesDict, setAppUsers } from "../../appConfigurationSlice";
lineSpinner.register();
quantum.register();

const { Option } = AntSelect;

function AssetInventory({
  setFocusedCell,
  setIsSearchFocused,
  handleFocusAssetClick,
  focusedCell,
  selectedSheet,
  myToaster,
  validPropertiesChange,
  setValidPropertiesChange,
  gridContainerRef,
}) {
  const [data, setData] = useState([]);
  const { isDarkTheme, setIsDarkTheme } = useContext(AppContext);
  const [isLoading, setIsLoading] = useState(true);

  const [gridColumnApi, setGridColumnApi] = useState(null);

  const { quickFilterText, setQuickFilterText } = useContext(AppContext);

  const [selectedRowData, setSelectedRowData] = useState(null);

  const [areStatsReady, setAreStatsReady] = useState(false);

  const assetsGridApiRef = useRef(null);
  const [assetsGridApi, setassetsGridApi] = useState(null);

  const { lookupValues, setLookupValues } = useContext(AppContext);

  const { userEmail, setUserEmail } = useContext(AppContext);

  const { userData, setUserData } = useContext(AppContext);

  const [headers, setHeaders] = useState([]);

  const [validationErrors, setValidationErrors] = useState([]);
  const [readyStats, setReadyStats] = useState({
    trueCount: 0,
    total: 0,
    truePercentage: "0.00",
  });
  const { notifiactions, setNotifications } = useContext(AppContext);

  const [selectedRowIndex, setSelectedRowIndex] = useState(null);

  const { dropdownRules, setDropdownRules } = useContext(AppContext);

  const {
    sendMessage,
    userSessions, // Assuming you might want to use it for some conditional checks
  } = useContext(AppContext);

  const dispatch = useDispatch();

  const appUsers = useSelector((state) => state.appConfiguration.appUsers);

  const makeApiCallWithRetry = async (url, options, maxTotalTime = 180000) => {
    let totalWaitTime = 0;

    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
    const fixedIncrement = 5000; // 5 seconds in milliseconds

    while (true) {
      try {
        console.log(`Calling ${url}`);
        return await axios(url, options);
      } catch (error) {
        const isRetryableError =
          error.response &&
          (error.response.status === 503 ||
            error.response.status === 429 ||
            // error.response.status === 403||
            error.response.status === 500);
        let waitTime = fixedIncrement + Math.random() * 1000; // 5 seconds + a random amount up to 1 second

        if (!isRetryableError || totalWaitTime + waitTime > maxTotalTime) {
          console.log(`Error: `, error);
          throw error;
        }

        console.log(`Waiting ${waitTime}ms before next attempt`);
        await delay(waitTime);
        totalWaitTime += waitTime;
        console.log(`Total wait time: ${totalWaitTime}s`);
      }
    }
  };

  useEffect(() => {
    console.log("Fetching data...");
    console.log("User Email: AI", userEmail);

    const fetchData = () => {
      return makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data`, {
        method: "get",
        params: {
          user: userEmail,
        },
      });
    };

    fetchData()
      .then((response) => {
        setData(response.data.data);

        let errorInAssets = response.data.validation;
        setNotifications(response.data.validation);

        let currentAppUsers = response.data.users["User Email"];
        console.log("appuser", currentAppUsers);
        dispatch(setAppUsers(currentAppUsers));

        console.log("Error in Assets:", errorInAssets);

        const lookupDict = cleanLookupDict(response.data.lookup);
        setLookupValues(lookupDict);
        dispatch(setLookupValuesDict(lookupDict));
        console.log(data);
        console.log("headers imported", response.data.headers);
        const updatedHeaders = ["_index", ...response.data.headers];
        setHeaders(updatedHeaders);

        const dropdownRules = response.data.dropdown_rules;
        setDropdownRules(dropdownRules);

        console.log("dropdown", dropdownRules);

        console.log("");
        //set usersData
        setUserData(response.data.users);

        setIsLoading(false); // Set loading to false after data is fetched
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        setIsLoading(false); // Also set loading to false in case of error
      });
  }, []);

  const cleanLookupDict = (dict) => {
    for (const key in dict) {
      dict[key] = dict[key].filter((value) => value !== "");
    }
    return dict;
  };

  const calculateReadyStats = () => {
    console.log("Recalculating stats..."); // Log to check if function is triggered

    if (assetsGridApiRef.current) {
      let trueCount = 0;
      let total = 0;

      // Use forEachNodeAfterFilter to iterate over the nodes that passed the filter
      assetsGridApiRef.current.forEachNodeAfterFilter((node) => {
        total++;
        if (node.data.Ready === "Completed") {
          trueCount++;
        }
      });

      const truePercentage = ((trueCount / total) * 100).toFixed(2);
      console.log({ trueCount, total, truePercentage });
      setReadyStats({ trueCount, total, truePercentage });
      setAreStatsReady(true);
    } else {
      console.warn("assetsGridApi is not initialized yet");
    }
  };

  useEffect(() => {
    if (assetsGridApi) {
      calculateReadyStats();
    }
  }, [assetsGridApi, validPropertiesChange]);

  useEffect(() => {
    if (assetsGridApi) {
      assetsGridApi.redrawRows(); // or assetsGridApi.refreshCells({ force: true });
      console.log("USER SESSIONS:", userSessions);
    }
  }, [userSessions, assetsGridApi]);

  useEffect(() => {
    if (assetsGridApi) {
      // Trigger the grid to recompute styles. This method name might vary depending on the grid library you're using.
      assetsGridApi.refreshCells({ force: true }); // You may need to adjust based on your grid API
      console.log("userSessions changed, grid styles updated.");
    }
  }, [userSessions, assetsGridApi]); // Listen to userSessions changes

  const onGridReady = (params) => {
    console.log("Grid is ready");
    assetsGridApiRef.current = params.api;
    setassetsGridApi(params.api);
    setGridColumnApi(params.columnApi);
    params.api.addEventListener("filterChanged", calculateReadyStats);
  };

  const getColumnWidth = (header) => {
    switch (header) {
      case "Tag Name":
        return 180;
      case "Owner":
        return 100;
      case "Ready":
        return 95;
      case "QC":
        return 82;
      case "Asset Classification":
        return 180;
      case "Equipment Type":
        return 150;
      default:
        return 95;
    }
  };

  const IndexCellRenderer = ({ value }) => {
    return (
      <div
        style={{
          // Customize these styles as needed
          fontWeight: "bold",
          backgroundColor: "#f0f0f0", // Light grey background
          color: "#333", // Dark text color
          padding: "4px", // Add some padding inside the cell
        }}
      >
        {value}
      </div>
    );
  };

  // Example custom tooltip renderer
  const CustomTooltip = (props) => {
    const { userSessions, rowColor } = props;

    const data = useMemo(
      () => props.api.getDisplayedRowAtIndex(props.rowIndex).data,
      [props.api, props.rowIndex],
    );

    const email = useMemo(() => {
      if (!userSessions) return null;

      const sessionKeyWithEmail = Object.keys(userSessions).find(
        (key) => userSessions[key] === data["Tag Name"],
      );
      return sessionKeyWithEmail ? sessionKeyWithEmail.split("_")[0] : null;
    }, [userSessions, data]);

    if (!email) return null;

    return (
      <Tag
        style={{
          backgroundColor: "#1C2127",
          color: "white",
          fontWeight: "bold",
        }}
      >
        {email}
      </Tag>
    );
  };

  function SelectCellRenderer(params) {
    let checkboxProps,
      additionalIcon = null;

    // Determine the props for the Checkbox based on params.value
    switch (params.value) {
      case "Completed":
      case "Approved":
        checkboxProps = {
          icon: "tick-circle",
          intent: "success",
          // Add any other props you need to pass to the Checkbox component
        };
        break;
      case "Resubmitted":
        checkboxProps = {
          icon: "tick-circle",
          intent: "primary",
          // Add any other props you need to pass to the Checkbox component
        };
        // Define additional icon for "Resubmitted" status
        // Replace with your actual icon and props
        additionalIcon = {
          icon: "tick-circle", // This should be replaced with your actual icon for resubmitted status
          intent: "primary", // Adjust as necessary
          // Add any other props you need for the second icon
        };
        break;
      case "Rejected":
        checkboxProps = {
          icon: "cross-circle",
          intent: "danger",
          // Add any other props you need to pass to the Checkbox component
        };
        break;
      default:
        checkboxProps = {
          // Add any other props you need to pass to the Checkbox component
        };
    }

    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: params.value ? "space-between" : "flex-end",
          width: "100%",
          minHeight: "20px",
          lineHeight: "20px",
        }}
      >
        {/* Render your Checkbox component, spreading the checkboxProps */}
        <div style={{ flexGrow: 1 }}>
          {params.value && <Icon {...checkboxProps} />}
        </div>
        {/* Conditionally render the additional icon for the "Resubmitted" status */}
        {additionalIcon && <Icon {...additionalIcon} />}
        <DropdownIcon />{" "}
        {/* Placeholder for your DropdownIcon, adjust as necessary */}
      </div>
    );
  }

  class SelectCellEditor extends React.Component {
    constructor(props) {
      super(props);
      this.state = { value: props.value };
    }

    componentDidMount() {
      document.addEventListener("keydown", this.handleKeyDown);
    }

    componentWillUnmount() {
      document.removeEventListener("keydown", this.handleKeyDown);
    }

    getValue() {
      return this.state.value;
    }

    isPopup() {
      return true;
    }

    handleKeyDown = (event) => {
      if (event.key === "Enter") {
        event.preventDefault();
      }
    };

    handleChange = async (selectedValue) => {
      this.setState({ value: selectedValue }, () => {
        this.props.api.stopEditing();
      });

      const rowData = this.props.node.data;
      const fieldName = this.props.colDef.field; // This will be either "QC" or "Ready"
      await this.updateValueApi(rowData, selectedValue, fieldName);

      const { rowIndex, column } = this.props;
      setTimeout(() => {
        this.props.api.setFocusedCell(rowIndex, column.colId);
        this.props.api.ensureColumnVisible(column.colId);
      }, 0);
    };

    updateValueApi = async (rowData, newValue, fieldName) => {
      // Determine the correct endpoint based on the field name
      const endpointSuffix = fieldName === "QC" ? "update_qc" : "update_ready";
      const updateEndpoint = `${process.env.REACT_APP_DATA_API}/${endpointSuffix}`;

      try {
        const params = {
          plant: rowData["Plant"],
          tag_name: rowData["Tag Name"],
          unit: rowData["Unit"],
          system: rowData["System"],
          asset_classification: rowData["Asset Classification"],
          equipment_type: rowData["Equipment Type"],
          template: rowData["Template"],
          user: userEmail, // Ensure this variable is accessible in this context
        };

        // Dynamically set the appropriate parameter based on the column being edited
        params[fieldName.toLowerCase()] = newValue;

        const response = await axios.get(updateEndpoint, { params });
        console.log("Value updated successfully", response.data);
        // Optionally, display a success message
        if (myToaster.current) {
          myToaster.current.show({
            message: `${fieldName} status has been updated with value: ${newValue}`,
            intent: Intent.SUCCESS,
            timeout: 4000,
            className: "font-size-11",
          });
        }
      } catch (error) {
        console.error("Error updating value", error);
        // Optionally, display an error message
        if (myToaster.current) {
          myToaster.current.show({
            message: `Error updating ${fieldName}: ${error.message}`,
            intent: Intent.WARNING,
            timeout: 4000,
            className: "font-size-11",
          });
        }
      } finally {
        // This is where we make sure to hide the loading overlay
        this.props.api.hideOverlay();
        // Additionally, force the grid to refresh the cells to reflect the updated data
        this.props.api.refreshCells({ force: true });
      }
    };

    render() {
      const options =
        this.props.colDef.field === "Owner"
          ? appUsers || []
          : this.props.colDef.field === "QC" ||
              this.props.colDef.field === "QCV"
            ? ["Approved", "Rejected", "No Action"]
            : ["Completed", "Resubmitted", "No Action"];
      return (
        <AntSelect
          showSearch
          placeholder="Select a status"
          optionFilterProp="children"
          onChange={this.handleChange}
          value={this.state.value}
          defaultOpen={true}
          style={{ width: "100%" }}
          autoFocus={true}
          popupMatchSelectWidth={false}
        >
          {options.map((option) => (
            <Option key={option} value={option}>
              {option}
            </Option>
          ))}
        </AntSelect>
      );
    }
  }

  const getRowStyle = (params) => {
    const userSessionEntries = Object.entries(userSessions);

    const isSelectedByAnyUser = userSessionEntries.some(
      ([sessionKey, tag]) => tag === params.data["Tag Name"],
    );

    if (isSelectedByAnyUser) {
      const selectedUserIndex = userSessionEntries.findIndex(
        ([sessionKey, tag]) => tag === params.data["Tag Name"],
      );

      const colorIndex = selectedUserIndex % rowColors.length;
      const backgroundColor = rowColors[colorIndex];

      return {
        backgroundColor,
        border: `1.2px solid ${backgroundColor.replace(/[^,]+(?=\))/, "1")}`,
      };
    }

    return null;
  };

  const columnDefs = useMemo(
    () =>
      headers.map((header, index) => {
        const commonProps = {
          headerName: header,
          field: header,
          sortable: true,
          filter: true,
          resizable: true,
          width: getColumnWidth(header),
          tooltipField: header,
          getRowStyle: getRowStyle,
          tooltipComponent: (props) => {
            const rowStyle = props.node.rowStyle || {};
            const rowColor = rowStyle.backgroundColor || "transparent";
            return (
              <CustomTooltip
                {...props}
                rowColor={rowColor}
                userSessions={userSessions}
              />
            );
          },
          editable:
            header === "Ready" ||
            header === "QC" ||
            header === "QCV" ||
            header === "Owner",
          hide: header === "Template" || header === "Email",
        };

        if (header === "_index") {
          return {
            ...commonProps,
            headerName: "",
            cellRenderer: "rowIndexRenderer",
            width: 100,
            filter: false,
            suppressSizeToFit: true,
            suppressMovable: true,
            valueGetter: (params) => {
              if (params.node.rowIndex != null) {
                return params.node.rowIndex + 1;
              }
              return "";
            },
            cellStyle: {
              textAlign: "center",
              fontSize: "9px",
              background: isDarkTheme ? "#383E47" : "#F5f7f7",
              color: "#abb3bf",
              // Changes the cursor to a pointer on hover
            }, // Center text
            // This column should not be filterable or sortable
            sortable: false,
            filter: false,
            resizable: false,
            pinned: "left",
          };
        }

        if (header === "Owner") {
          return {
            ...commonProps,
            cellEditor: "SelectCellEditor",
            cellEditorParams: {
              values: appUsers,
            },
          };
        }

        if (header === "QC" || header === "Ready" || header === "QCV") {
          return {
            ...commonProps,
            // Specify the custom editor for these fields
            cellEditor: "SelectCellEditor",
            cellRenderer: "SelectCellRenderer",
            // Optional: define how to get the value for the editor, e.g., from the data
            cellEditorParams: {
              values:
                header === "QC"
                  ? ["Accepted", "Rejected"]
                  : ["Submitted", "Resubmitted"],
            },
          };
        }

        return commonProps;
      }),
    [headers, userSessions, appUsers, getRowStyle],
  );

  const rowData = useMemo(
    () =>
      data.map((row) => {
        const rowDataObject = {};
        // Start iterating from 1 to skip the _index field in headers
        headers.slice(1).forEach((header, index) => {
          // Since we're skipping the first header (_index), align data with headers starting from the second header
          rowDataObject[header] = row[index];
        });
        return rowDataObject;
      }),
    [data, headers],
  );

  const handleRowSelection = () => {
    const selectedNodes = assetsGridApi.getSelectedNodes();

    if (selectedNodes && selectedNodes.length > 0) {
      const selectedData = selectedNodes[0].data;
      // Now that we're sure selectedData is valid, send it through WebSocket
      sendMessage(selectedData["Tag Name"]);

      console.log("selectedData:", selectedData);

      setFocusedCell(selectedData); // Update focused cell state
      setSelectedRowData(selectedData);
      handleFocusAssetClick(selectedData); // Trigger any additional actions for selected data
    }
  };

  const handleCellValueChange = async (event) => {
    assetsGridApi.showLoadingOverlay();
    // Check if the edited column is "Owner"
    if (event.colDef.field === "Owner") {
      // Extract the new value from the event object
      const newValue = event.newValue;

      // Make an Axios call with the new value
      await makeApiCallWithRetry(
        `${process.env.REACT_APP_DATA_API}/update_owner/`,
        {
          method: "get",
          plant: event.data["Plant"],
          tag_name: event.data["Tag Name"],
          unit: event.data["Unit"],
          system: event.data["System"],
          asset_classification: event.data["Asset Classification"],
          equipment_type: event.data["Equipment Type"],
          template: event.data["Template"],
          owner: newValue, // Use the new value here
        },
      )
        .then((response) => {
          console.log(response.data);
          if (myToaster.current) {
            myToaster.current.show({
              message: `Owner has been updated with value: ${newValue}`,
              intent: Intent.SUCCESS,
              timeout: 4000,
              className: "font-size-11",
            });
          }
          setValidPropertiesChange((prev) => !prev);
        })
        .catch((error) => {
          if (error.response) {
            console.error("Error response:", error.response);
          } else if (error.request) {
            console.error("No response received:", error.request);
          } else {
            console.error("Error:", error.message);
          }
          if (myToaster.current) {
            myToaster.current.show({
              message: `Error updating Owner: ${error.message}`,
              intent: Intent.WARNING,
              timeout: 4000,
              className: "font-size-11",
            });
          }
        });
    }
  };

  useCopyPaste(assetsGridApi, gridContainerRef);
  useEffect(() => {
    if (assetsGridApi && data) {
      // Ensure assetsGridApi and data are both available
      calculateReadyStats();
    }
  }, [assetsGridApi, data]); // Dependencies: re-run this useEffect whenever assetsGridApi or data changes

  useEffect(() => {
    if (gridContainerRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        if (assetsGridApi) {
        }
      });

      resizeObserver.observe(gridContainerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [assetsGridApi]);

  const parentClassName = isDarkTheme ? "dark-theme" : "";

  const rowColors = [
    "rgba(35,133,81,0.2)", // Green
    "rgba(255,165,0,0.2)", // Orange
    "rgba(172,47,51,0.2)", // Red
    "rgba(86,66,166,0.2)", // Purple
    "rgba(0,191,255,0.2)", // Deep Sky Blue
    "rgba(92,37,92,0.2)", // Goldenrod
    // Add more colors if needed
  ];

  const onCellClicked = (params) => {
    // Check if the clicked column is a 'SelectCellEditor' column
    /*  if (params.colDef.cellEditor === "SelectCellEditor") {
      params.api.startEditingCell({
        rowIndex: params.rowIndex,
        colKey: params.column.colId,
      });
      } */
  };

  const gridOptions = {
    //singleClickEdit: true,
    onCellClicked: onCellClicked,
    undoRedoCellEditing: true,
    undoRedoCellEditingLimit: 30,
    rowDragManaged: true,
    components: {
      SelectCellEditor: SelectCellEditor,
      SelectCellRenderer: SelectCellRenderer,
      IndexCellRenderer: IndexCellRenderer,
    },
    overlayLoadingTemplate:
      // 3 dots bouncing
      '<span class=""><l-line-spinner size="25" speed="1.0" color="white"></l-bouncy></span>',
    //  '<span class=""><l-quantum size="30" speed="1.75" color="white"></l-quantum><span>',/

    onCellFocused: (event) => {},
  };

  const StatusLegend = () => {
    return (
      <Card className="no-padding-card">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-around",
            padding: "1px",
            margin: "1px 0",
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="success"
              className="bp5-dark"
            />{" "}
            <span style={{ marginLeft: "5px" }}>Completed/Approved</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              className="bp5-dark"
            />
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              style={{ marginLeft: "1px" }}
            />{" "}
            <span style={{ marginLeft: "5px" }}>Resubmitted</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="cross-circle" size="12" intent="danger" />{" "}
            <span style={{ marginLeft: "5px" }}>Rejected</span>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <Card
      className={parentClassName}
      style={{ height: "100%", padding: "15px 20px", marginBottom: "5px" }}
    >
      <h3 style={{ margin: "0px", fontWeight: "bold" }}>Asset Inventory</h3>

      {/* Search and Stats Section */}
      <div className="asset-search-group">
        <Input
          style={{ width: "50%" }}
          prefix={<SearchOutlined />}
          placeholder="Search..."
          onChange={(e) => setQuickFilterText(e.target.value)}
          value={quickFilterText}
        />
        <div className="ready-stats" style={{ paddingLeft: "10px" }}>
          {isLoading || !data.length ? (
            <p>Loading...</p>
          ) : (
            <div
              className="statistic-container"
              style={{ transform: "translate(10px, -10px)" }}
            >
              <Statistic
                className={`custom-statistic ${
                  isDarkTheme ? "dark-theme-statistic" : "custom-statistic"
                }`}
                title={
                  <Tag
                    minimal={true}
                    style={{ fontWeight: "bold" }}
                    intent={"primary"}
                    children="Assets Ready"
                  ></Tag>
                }
                value={readyStats.trueCount}
                suffix={`/ ${readyStats.total} (${readyStats.truePercentage}%)`}
              />
            </div>
          )}
        </div>
      </div>

      {/* Grid Display Section */}
      <div
        ref={gridContainerRef}
        className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"}
        style={{ height: "calc(100% - 70px)", width: "100%" }} // Adjust this height as needed
      >
        <StatusLegend />
        <AgGridReact
          id="assetGrid"
          gridOptions={gridOptions}
          tooltipShowDelay={0}
          getRowStyle={getRowStyle}
          headerHeight={40}
          onGridReady={onGridReady}
          columnDefs={columnDefs}
          rowData={rowData}
          onSelectionChanged={handleRowSelection}
          rowSelection="single"
          getRowHeight={() => 20}
          quickFilterText={quickFilterText}
          onCellValueChanged={handleCellValueChange}
          enableFillHandle={true}
          enableRangeSelection={true}
          enableCellTextSelection={true}
        />
      </div>
    </Card>
  );
}

export default React.memo(AssetInventory);
