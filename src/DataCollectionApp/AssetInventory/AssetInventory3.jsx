import React, { useState, useEffect, useMemo, useRef, useContext } from "react";
import { AgGridReact } from "ag-grid-react";
import { InputGroup, Card, Intent, Button, Icon,Tag } from "@blueprintjs/core";
import axios from "axios";
import { Select } from "@blueprintjs/select";
import { MenuItem } from "@blueprintjs/core";
import { Checkbox } from "primereact/checkbox";
import { useCopyPaste } from "../../hooks/useCopyPaste";
import { AppContext } from "../../AppContextProvider";
import { Statistic } from "antd";
import { Input } from "antd"; // Importing Input from Ant Design
import "primereact/resources/themes/nova/theme.css"; //theme
import "primereact/resources/primereact.min.css"; //core css
import "primeicons/primeicons.css"; //icons
import { SearchOutlined } from "@ant-design/icons";
import { MdOutlineDownloadForOffline } from "react-icons/md";

function AssetInventory({
  setFocusedCell,
  setIsSearchFocused,
  handleFocusAssetClick,
  focusedCell,
  selectedSheet,
  myToaster,
  validPropertiesChange,
  setValidPropertiesChange,
  gridContainerRef,
  data,
  setData,
}) {
  const { isDarkTheme, setIsDarkTheme } = useContext(AppContext);
  const [isLoading, setIsLoading] = useState(true);

  const [gridColumnApi, setGridColumnApi] = useState(null);

  const { quickFilterText, setQuickFilterText } = useContext(AppContext);

  const [selectedRowData, setSelectedRowData] = useState(null);

  const [areStatsReady, setAreStatsReady] = useState(false);

  const assetsGridApiRef = useRef(null);
  const [assetsGridApi, setassetsGridApi] = useState(null);

  const { lookupValues, setLookupValues } = useContext(AppContext);

  const { userEmail, setUserEmail } = useContext(AppContext);

  const { userData, setUserData } = useContext(AppContext);

  const [headers, setHeaders] = useState([]);

  const [validationErrors, setValidationErrors] = useState([]);
  const [readyStats, setReadyStats] = useState({
    trueCount: 0,
    total: 0,
    truePercentage: "0.00",
  });
  const { notifiactions, setNotifications } = useContext(AppContext);

  const [selectedRowIndex, setSelectedRowIndex] = useState(null);

  


  const makeApiCallWithRetry = async (url, options, maxTotalTime = 180000) => {
    let totalWaitTime = 0;

    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
    const fixedIncrement = 5000; // 5 seconds in milliseconds

    while (true) {
      try {
        console.log(`Calling ${url}`);
        return await axios(url, options);
      } catch (error) {
        const isRetryableError =
          error.response &&
          (error.response.status === 503 ||
            error.response.status === 429 ||
            // error.response.status === 403||
            error.response.status === 500);
        let waitTime = fixedIncrement + Math.random() * 1000; // 5 seconds + a random amount up to 1 second

        if (!isRetryableError || totalWaitTime + waitTime > maxTotalTime) {
          console.log(`Error: `, error);
          throw error;
        }

        console.log(`Waiting ${waitTime}ms before next attempt`);
        await delay(waitTime);
        totalWaitTime += waitTime;
        console.log(`Total wait time: ${totalWaitTime}s`);
      }
    }
  };

  useEffect(() => {
    console.log("Fetching data...");
    console.log("User Email: AI", userEmail);

    const fetchData = () => {
      return makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data`, {
        method: "get",
        params: {
          user: userEmail,
        },
      });
    };

    fetchData()
      .then((response) => {
        setData(response.data.data);

        let errorInAssets = response.data.validation;
        setNotifications(response.data.validation);

        console.log("Error in Assets:", errorInAssets);

        const lookupDict = cleanLookupDict(response.data.lookup);
        setLookupValues(lookupDict);
        console.log(data);
        setHeaders(response.data.headers);

        console.log("");
        //set usersData
        setUserData(response.data.users);

        setIsLoading(false); // Set loading to false after data is fetched
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        setIsLoading(false); // Also set loading to false in case of error
      });
  }, []);

  const cleanLookupDict = (dict) => {
    for (const key in dict) {
      dict[key] = dict[key].filter((value) => value !== "");
    }
    return dict;
  };

  const calculateReadyStats = () => {
    console.log("Recalculating stats..."); // Log to check if function is triggered

    if (assetsGridApiRef.current) {
      let trueCount = 0;
      let total = 0;

      // Use forEachNodeAfterFilter to iterate over the nodes that passed the filter
      assetsGridApiRef.current.forEachNodeAfterFilter((node) => {
        total++;
        if (node.data.Ready === "TRUE") {
          trueCount++;
        }
      });

      const truePercentage = ((trueCount / total) * 100).toFixed(2);
      console.log({ trueCount, total, truePercentage });
      setReadyStats({ trueCount, total, truePercentage });
      setAreStatsReady(true);
    } else {
      console.warn("assetsGridApi is not initialized yet");
    }
  };

  useEffect(() => {
    if (assetsGridApi) {
      calculateReadyStats();
    }
  }, [assetsGridApi, validPropertiesChange]);

  
  

  const onGridReady = (params) => {
    console.log("Grid is ready");
    assetsGridApiRef.current = params.api;
    setassetsGridApi(params.api);
    setGridColumnApi(params.columnApi);
    params.api.addEventListener("filterChanged", calculateReadyStats);
  };

  const getColumnWidth = (header) => {
    switch (header) {
      case "Tag Name":
        return 180;
      case "Owner":
        return 110;
      case "Ready":
        return 93;
      case "QC":
        return 73;
      case "Asset Classification":
        return 180;
      case "Equipment Type":
        return 150;
      default:
        return 95;
    }
  };

    
    
    
  
   

  const columnDefs = useMemo(
    () =>
      headers.map((header, index) => {
        const commonProps = {
          headerName: header,
          field: header,
          sortable: true,
          filter: true,
          resizable: true,
          width: getColumnWidth(header),
          
          editable: header === "Ready" || header === "QC",
          hide: header === "Template" || header === "Email",
        };

        if (header === "QC") {
          return {
            ...commonProps,
            filter: true,

            cellRenderer: (props) => {
              const cellValue = props.valueFormatted
                ? props.valueFormatted
                : props.value;
              const checked = cellValue === "TRUE";

              const handleChange = async (e) => {
                assetsGridApi.showLoadingOverlay();
                const newValue = e.checked ? "TRUE" : "FALSE";
                props.data[props.colDef.field] = newValue;
                console.log(`Ready status changed to: ${newValue}`);
                assetsGridApi.refreshCells(); // Force the grid to re-render

                try {
                  const response = await axios.get(
                    `${process.env.REACT_APP_DATA_API}/update_qc/`,
                    {
                      params: {
                        plant: props.data["Plant"],
                        tag_name: props.data["Tag Name"],
                        unit: props.data["Unit"],
                        system: props.data["System"],
                        asset_classification:
                          props.data["Asset Classification"],
                        equipment_type: props.data["Equipment Type"],
                        template: props.data["Template"],
                        user: userEmail,
                        qc: newValue,
                      },
                    }
                  );
                  console.log(response.data);
                  myToaster.current?.show({
                    message: `QC has been updated with value: ${newValue}`,
                    intent: Intent.SUCCESS,
                    timeout: 4000,
                    className: "font-size-11",
                  });
                  setValidPropertiesChange((prev) => !prev);
                } catch (error) {
                  console.error("Error while updating Ready status:", error);
                  myToaster.current?.show({
                    message: `Error updating Ready: ${error.message}`,
                    intent: Intent.WARNING,
                    timeout: 4000,
                    className: "font-size-11",
                  });
                } finally {
                  assetsGridApi.hideOverlay(); // Hide loading overlay after the operation
                }
              };

              return (
                <span>
                  <Checkbox
                    checked={checked}
                    onChange={handleChange}
                    // Add a black theme class; you will need to define this in your CSS
                    className="p-checkbox p-component p-checkbox-green"
                    // If you need to handle changes, you can add an onChange handler here
                  ></Checkbox>
                </span>
              );
            },
          };
        }

        if (header === "Ready") {
          return {
            ...commonProps,
            filter: "agSetColumnFilter",

            cellRenderer: (props) => {
              const cellValue = props.valueFormatted
                ? props.valueFormatted
                : props.value;
              const checked = cellValue === "TRUE";

              const handleChange = async (e) => {
                assetsGridApi.showLoadingOverlay();
                const newValue = e.checked ? "TRUE" : "FALSE";
                props.data[props.colDef.field] = newValue;
                console.log(`Ready status changed to: ${newValue}`);
                assetsGridApi.refreshCells(); // Force the grid to re-render

                try {
                  const response = await axios.get(
                    `${process.env.REACT_APP_DATA_API}/update_ready/`,
                    {
                      params: {
                        plant: props.data["Plant"],
                        tag_name: props.data["Tag Name"],
                        unit: props.data["Unit"],
                        system: props.data["System"],
                        asset_classification:
                          props.data["Asset Classification"],
                        equipment_type: props.data["Equipment Type"],
                        template: props.data["Template"],
                        ready: newValue,
                        user: userEmail,
                      },
                    }
                  );
                  console.log(response.data);
                  myToaster.current?.show({
                    message: `Ready has been updated with value: ${newValue}`,
                    intent: Intent.SUCCESS,
                    timeout: 4000,
                    className: "font-size-11",
                  });
                  setValidPropertiesChange((prev) => !prev);
                } catch (error) {
                  console.error("Error while updating Ready status:", error);
                  myToaster.current?.show({
                    message: `Error updating Ready: ${error.message}`,
                    intent: Intent.WARNING,
                    timeout: 4000,
                    className: "font-size-11",
                  });
                } finally {
                  assetsGridApi.hideOverlay(); // Hide loading overlay after the operation
                }
              };

              return (
                <span>
                  <Checkbox
                    checked={checked}
                    onChange={handleChange}
                    // Add a black theme class; you will need to define this in your CSS
                    className="p-checkbox p-component p-checkbox-black"
                    // If you need to handle changes, you can add an onChange handler here
                  ></Checkbox>
                </span>
              );
            },
          };
        }

        return commonProps;
      }),
    [headers]
  );

  const rowData = useMemo(
    () =>
      data.map((row) => {
        const rowDataObject = {};
        headers.forEach((header, index) => {
          rowDataObject[header] = row[index];
        });
        return rowDataObject;
      }),
    [data, headers]
  );

  const handleRowSelection = () => {
    const selectedNodes = assetsGridApi.getSelectedNodes();

    if (selectedNodes && selectedNodes.length > 0) {
      const selectedData = selectedNodes[0].data;
      // Now that we're sure selectedData is valid, send it through WebSocket


      setFocusedCell(selectedData); // Update focused cell state
      setSelectedRowData(selectedData);
      handleFocusAssetClick(selectedData); // Trigger any additional actions for selected data
    }
  };

  const handleCellValueChange = async (event) => {
    assetsGridApi.showLoadingOverlay();
    // Check if the edited column is "Owner"
    if (event.colDef.field === "Owner") {
      // Extract the new value from the event object
      const newValue = event.newValue;

      // Make an Axios call with the new value
      await makeApiCallWithRetry(
        `${process.env.REACT_APP_DATA_API}/update_owner/`,
        {
          method: "get",
          plant: event.data["Plant"],
          tag_name: event.data["Tag Name"],
          unit: event.data["Unit"],
          system: event.data["System"],
          asset_classification: event.data["Asset Classification"],
          equipment_type: event.data["Equipment Type"],
          template: event.data["Template"],
          owner: newValue, // Use the new value here
        }
      )
        .then((response) => {
          console.log(response.data);
          if (myToaster.current) {
            myToaster.current.show({
              message: `Owner has been updated with value: ${newValue}`,
              intent: Intent.SUCCESS,
              timeout: 4000,
              className: "font-size-11",
            });
          }
          setValidPropertiesChange((prev) => !prev);
        })
        .catch((error) => {
          if (error.response) {
            console.error("Error response:", error.response);
          } else if (error.request) {
            console.error("No response received:", error.request);
          } else {
            console.error("Error:", error.message);
          }
          if (myToaster.current) {
            myToaster.current.show({
              message: `Error updating Owner: ${error.message}`,
              intent: Intent.WARNING,
              timeout: 4000,
              className: "font-size-11",
            });
          }
        });
    }
  };

  useCopyPaste(assetsGridApi, gridContainerRef);
  useEffect(() => {
    if (assetsGridApi && data) {
      // Ensure assetsGridApi and data are both available
      calculateReadyStats();
    }
  }, [assetsGridApi, data]); // Dependencies: re-run this useEffect whenever assetsGridApi or data changes

  useEffect(() => {
    if (gridContainerRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        if (assetsGridApi) {
        }
      });

      resizeObserver.observe(gridContainerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [assetsGridApi]);

  const parentClassName = isDarkTheme ? "dark-theme" : "";

 


  return (
    <Card
      className={parentClassName}
      style={{ height: "100%", padding: "15px 20px", marginBottom: "5px" }}
    >
      <h3 style={{ margin: "0px", fontWeight: "bold" }}>Asset Inventory</h3>

      {/* Search and Stats Section */}
      <div className="asset-search-group">
        <Input
          style={{ width: "50%" }}
          prefix={<SearchOutlined />}
          placeholder="Search..."
          onChange={(e) => setQuickFilterText(e.target.value)}
          value={quickFilterText}
        />
        <div className="ready-stats" style={{ paddingLeft: "10px" }}>
          {isLoading || !data.length ? (
            <p>Loading...</p>
          ) : (
            <div
              className="statistic-container"
              style={{ transform: "translate(10px, -10px)" }}
            >
              <Statistic
                className={`custom-statistic ${
                  isDarkTheme ? "dark-theme-statistic" : "custom-statistic"
                }`}
                title="Assets Ready"
                value={readyStats.trueCount}
                suffix={`/ ${readyStats.total} (${readyStats.truePercentage}%)`}
              />
            </div>
          )}
        </div>
      </div>

      {/* Grid Display Section */}
      <div
        ref={gridContainerRef}
        className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"}
        style={{ height: "calc(100% - 70px)", width: "100%" }} // Adjust this height as needed
      >
        <AgGridReact
          id="assetGrid"
          tooltipShowDelay={0}
          headerHeight={40}
          onGridReady={onGridReady}
          columnDefs={columnDefs}
          rowData={rowData}
          onSelectionChanged={handleRowSelection}
          rowSelection="single"
          getRowHeight={() => 20}
          quickFilterText={quickFilterText}
          onCellValueChanged={handleCellValueChange}
          enableFillHandle={true}
          enableRangeSelection={true}
          enableCellTextSelection={true}
        />
        
       
      </div>
    </Card>
  );
}

export default React.memo(AssetInventory);
