import * as React from "react";
const CsvIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    width={25}
    height={25}
    viewBox="0 0 256 256"
    xmlSpace="preserve"
    {...props}
  >
    <defs />
    <g
      style={{
        stroke: "none",
        strokeWidth: 0,
        strokeDasharray: "none",
        strokeLinecap: "butt",
        strokeLinejoin: "miter",
        strokeMiterlimit: 10,
        fill: "none",
        fillRule: "nonzero",
        opacity: 1,
      }}
      transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)"
    >
      <path
        d="M 19.309 0 C 15.04 0 11.58 3.46 11.58 7.729 v 47.153 v 27.389 c 0 4.269 3.46 7.729 7.729 7.729 h 51.382 c 4.269 0 7.729 -3.46 7.729 -7.729 V 54.882 V 25.82 L 52.601 0 H 19.309 z"
        style={{
          stroke: "none",
          strokeWidth: 1,
          strokeDasharray: "none",
          strokeLinecap: "butt",
          strokeLinejoin: "miter",
          strokeMiterlimit: 10,
          fill: "rgb(0,115,59)",
          fillRule: "nonzero",
          opacity: 1,
        }}
        transform=" matrix(1 0 0 1 0 0) "
        strokeLinecap="round"
      />
      <path
        d="M 78.42 25.82 H 60.159 c -4.175 0 -7.559 -3.384 -7.559 -7.559 V 0 L 78.42 25.82 z"
        style={{
          stroke: "none",
          strokeWidth: 1,
          strokeDasharray: "none",
          strokeLinecap: "butt",
          strokeLinejoin: "miter",
          strokeMiterlimit: 10,
          fill: "rgb(76,156,117)",
          fillRule: "nonzero",
          opacity: 1,
        }}
        transform=" matrix(1 0 0 1 0 0) "
        strokeLinecap="round"
      />
      <path
        d="M 60.155 65.236 c -0.729 0 -1.409 -0.459 -1.657 -1.188 l -5.539 -16.308 c -0.311 -0.915 0.18 -1.909 1.095 -2.22 c 0.916 -0.311 1.908 0.179 2.22 1.095 l 5.539 16.308 c 0.311 0.915 -0.18 1.908 -1.095 2.22 C 60.531 65.207 60.342 65.236 60.155 65.236 z"
        style={{
          stroke: "none",
          strokeWidth: 1,
          strokeDasharray: "none",
          strokeLinecap: "butt",
          strokeLinejoin: "miter",
          strokeMiterlimit: 10,
          fill: "rgb(255,255,255)",
          fillRule: "nonzero",
          opacity: 1,
        }}
        transform=" matrix(1 0 0 1 0 0) "
        strokeLinecap="round"
      />
      <path
        d="M 32.9 65.205 h -3.504 c -3.772 0 -6.841 -3.068 -6.841 -6.841 V 52.3 c 0 -3.772 3.069 -6.841 6.841 -6.841 H 32.9 c 0.966 0 1.75 0.783 1.75 1.75 s -0.784 1.75 -1.75 1.75 h -3.504 c -1.842 0 -3.341 1.499 -3.341 3.341 v 6.064 c 0 1.842 1.499 3.341 3.341 3.341 H 32.9 c 0.966 0 1.75 0.783 1.75 1.75 S 33.866 65.205 32.9 65.205 z"
        style={{
          stroke: "none",
          strokeWidth: 1,
          strokeDasharray: "none",
          strokeLinecap: "butt",
          strokeLinejoin: "miter",
          strokeMiterlimit: 10,
          fill: "rgb(255,255,255)",
          fillRule: "nonzero",
          opacity: 1,
        }}
        transform=" matrix(1 0 0 1 0 0) "
        strokeLinecap="round"
      />
      <path
        d="M 46.495 65.205 h -5.776 c -0.966 0 -1.75 -0.783 -1.75 -1.75 s 0.784 -1.75 1.75 -1.75 h 5.776 c 0.589 0 1.068 -0.479 1.068 -1.067 V 58.15 c 0 -0.589 -0.479 -1.068 -1.068 -1.068 h -2.958 c -2.519 0 -4.568 -2.049 -4.568 -4.567 v -2.487 c 0 -2.519 2.049 -4.568 4.568 -4.568 h 3.798 c 0.967 0 1.75 0.783 1.75 1.75 s -0.783 1.75 -1.75 1.75 h -3.798 c -0.589 0 -1.068 0.479 -1.068 1.068 v 2.487 c 0 0.589 0.479 1.067 1.068 1.067 h 2.958 c 2.519 0 4.568 2.05 4.568 4.568 v 2.487 C 51.063 63.156 49.014 65.205 46.495 65.205 z"
        style={{
          stroke: "none",
          strokeWidth: 1,
          strokeDasharray: "none",
          strokeLinecap: "butt",
          strokeLinejoin: "miter",
          strokeMiterlimit: 10,
          fill: "rgb(255,255,255)",
          fillRule: "nonzero",
          opacity: 1,
        }}
        transform=" matrix(1 0 0 1 0 0) "
        strokeLinecap="round"
      />
      <path
        d="M 60.155 65.236 c -0.187 0 -0.376 -0.029 -0.562 -0.093 c -0.916 -0.312 -1.405 -1.305 -1.095 -2.22 l 5.54 -16.308 c 0.312 -0.916 1.304 -1.405 2.22 -1.095 c 0.916 0.311 1.405 1.305 1.095 2.22 l -5.54 16.308 C 61.564 64.777 60.885 65.236 60.155 65.236 z"
        style={{
          stroke: "none",
          strokeWidth: 1,
          strokeDasharray: "none",
          strokeLinecap: "butt",
          strokeLinejoin: "miter",
          strokeMiterlimit: 10,
          fill: "rgb(255,255,255)",
          fillRule: "nonzero",
          opacity: 1,
        }}
        transform=" matrix(1 0 0 1 0 0) "
        strokeLinecap="round"
      />
    </g>
  </svg>
);
export default CsvIcon;
