import React, { useState, useEffect, useMemo, useRef, useContext, useCallback } from "react";
import { AgGridReact } from "ag-grid-react";
import { Card, Intent, Icon, Tag, Button, Tooltip } from "@blueprintjs/core";
import axios from "axios";
import Select from 'react-select';
import { Input, Statistic } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { HiMiniChevronUpDown as DropdownIcon } from "react-icons/hi2";
import { quantum } from "ldrs";
import { useSelector, useDispatch } from "react-redux";
import { setLookupValuesDict, setAppUsers } from "../../redux/appConfigurationSlice";
import { RiFilterOffLine } from "react-icons/ri";
import makeApiCallWithRetry from "../Helpers/apiHelper";
import ReadyFilter from "./ReadyFilter";
import { AppContext } from "../../AppContextProvider";

quantum.register();

function AssetInventory({
  setFocusedCell,
  setIsSearchFocused,
  handleFocusAssetClick,
  focusedCell,
  selectedSheet,
  myToaster,
  validPropertiesChange,
  setValidPropertiesChange,
  gridContainerRef,
}) {
  const [appUsersReady, setAppUsersReady] = useState(false);
  const [data, setData] = useState([]);
  const { isDarkTheme, quickFilterText, setQuickFilterText, setLookupValues, setUserData, notifications, setNotifications, dropdownRules, setDropdownRules, sendMessage, userSessions } = useContext(AppContext);
  const [isLoading, setIsLoading] = useState(true);
  const [gridColumnApi, setGridColumnApi] = useState(null);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [areStatsReady, setAreStatsReady] = useState(false);
  const assetsGridApiRef = useRef(null);
  const [assetsGridApi, setAssetsGridApi] = useState(null);
  const dispatch = useDispatch();
  const appUsers = useSelector((state) => state.appConfiguration.appUsers);
  const { user } = useSelector((state) => state.auth);
  const [headers, setHeaders] = useState([]);
  const { setNpsODdata, setPipingSchedule } = useContext(AppContext);
  const [readyStats, setReadyStats] = useState({
    trueCount: 0,
    total: 0,
    truePercentage: "0.00",
  });
  const { allAssetsData, setAllAssetsData } = useContext(AppContext);

  useEffect(() => {
    console.log("Fetching data...");
    axios.get(`${process.env.REACT_APP_MATERIALS_DATA_API}/pipe_properties/nps_od`)
      .then(response => {
        console.log("nps", response.data);
        setNpsODdata(response.data);
      })
      .catch(error => {
        console.error('Error fetching data: ', error);
      });

    axios.get(`${process.env.REACT_APP_MATERIALS_DATA_API}/pipe_properties/schedule`)
      .then(response => {
        console.log("pipingS", response.data);
        setPipingSchedule(response.data);
      })
      .catch(error => {
        console.error('Error fetching data: ', error);
      });
  }, [setNpsODdata, setPipingSchedule]);

  useEffect(() => {
    console.log("Fetching data...");
    console.log("User Email: AI", user.email);

    const fetchData = () => {
      return makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data`, {
        method: "get",
        params: {
          user: user.email,
        },
      });
    };

    fetchData()
      .then((response) => {
        setData(response.data.data);

        let reduceAssetData = response.data.data.reduce((acc, item) => {
          let asset = {
            "Plant": item[8],
            "Unit": item[9],
            "System": item[10],
            "Tag Name": item[5],
            "Class": item[11],
          };
          acc.push(asset);
          return acc;
        }, []);

        setAllAssetsData(reduceAssetData);

        let errorInAssets = response.data.validation;
        setNotifications(response.data.validation);

        let currentAppUsers = response.data.users["User Email"];
        console.log("appuser", currentAppUsers);
        dispatch(setAppUsers(currentAppUsers));
        setAppUsersReady(true);

        console.log("Error in Assets:", errorInAssets);

        const lookupDict = cleanLookupDict(response.data.lookup);
        setLookupValues(lookupDict);
        dispatch(setLookupValuesDict(lookupDict));
        console.log(data);
        console.log("headers imported", response.data.headers);
        const updatedHeaders = ["_index", ...response.data.headers];
        setHeaders(updatedHeaders);

        const dropdownRules = response.data.dropdown_rules;
        setDropdownRules(dropdownRules);

        console.log("dropdown", dropdownRules);

        console.log("");
        setUserData(response.data.users);

        setIsLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        setIsLoading(false);
      });
  }, []);

  const cleanLookupDict = (dict) => {
    for (const key in dict) {
      dict[key] = dict[key].filter((value) => value !== "");
    }
    return dict;
  };

  const calculateReadyStats = () => {
    console.log("Recalculating stats...");

    if (assetsGridApiRef.current) {
      let trueCount = 0;
      let total = 0;

      assetsGridApiRef.current.forEachNodeAfterFilter((node) => {
        total++;
        if (node.data.Ready === "Completed") {
          trueCount++;
        }
      });

      const truePercentage = ((trueCount / total) * 100).toFixed(2);
      console.log({ trueCount, total, truePercentage });
      setReadyStats({ trueCount, total, truePercentage });
      setAreStatsReady(true);
    } else {
      console.warn("assetsGridApi is not initialized yet");
    }
  };

  useEffect(() => {
    if (assetsGridApi) {
      calculateReadyStats();
    }
  }, [assetsGridApi, validPropertiesChange]);

  useEffect(() => {
    if (assetsGridApi) {
      assetsGridApi.redrawRows();
      console.log("USER SESSIONS:", userSessions);
    }
  }, [userSessions, assetsGridApi]);

  useEffect(() => {
    if (assetsGridApi) {
      assetsGridApi.refreshCells({ force: true });
      console.log("userSessions changed, grid styles updated.");
    }
  }, [userSessions, assetsGridApi]);

  const onGridReady = (params) => {
    console.log("Grid is ready");
    assetsGridApiRef.current = params.api;
    setAssetsGridApi(params.api);
    setGridColumnApi(params.columnApi);
    params.api.addEventListener("filterChanged", calculateReadyStats);
  };

  const getColumnWidth = (header) => {
    switch (header) {
      case "Tag Name":
        return 180;
      case "Owner":
        return 100;
      case "Ready":
        return 95;
      case "QC":
        return 82;
      case "Asset Classification":
        return 180;
      case "Equipment Type":
        return 150;
      default:
        return 95;
    }
  };

  const IndexCellRenderer = ({ value }) => {
    return (
      <div
        style={{
          fontWeight: "bold",
          backgroundColor: "#f0f0f0",
          color: "#333",
          padding: "4px",
        }}
      >
        {value}
      </div>
    );
  };

  const CustomTooltip = (props) => {
    const { userSessions, rowColor } = props;

    const data = useMemo(
      () => props.api.getDisplayedRowAtIndex(props.rowIndex).data,
      [props.api, props.rowIndex]
    );

    const email = useMemo(() => {
      if (!userSessions) return null;

      const sessionKeyWithEmail = Object.keys(userSessions).find(
        (key) => userSessions[key] === data["Tag Name"]
      );
      return sessionKeyWithEmail ? sessionKeyWithEmail.split("_")[0] : null;
    }, [userSessions, data]);

    if (!email) return null;

    return (
      <Tag
        style={{
          backgroundColor: "#1C2127",
          color: "white",
          fontWeight: "bold",
        }}
      >
        {email}
      </Tag>
    );
  };

  const getColorByUserIndex = (index) => {
    const colors = [
      "rgba(35,133,81,0.2)",
      "rgba(255,165,0,0.2)",
      "rgba(172,47,51,0.2)",
      "rgba(86,66,166,0.2)",
      "rgba(0,191,255,0.2)",
      "rgba(92,37,92,0.2)",
    ];
    return colors[index];
  };

  function SelectCellRenderer({ value, data, node, column }) {
    const options = [
      { value: "Completed", label: "Completed" },
      { value: "Resubmitted", label: "Resubmitted" },
      { value: "Rejected", label: "Rejected" },
      { value: "No Action", label: "No Action" }
    ];

    const handleChange = (selectedOption) => {
      const newValue = selectedOption ? selectedOption.value : null;
      const rowData = node.data;
      const newData = {
        ...rowData,
        [column.colId]: newValue,
      };
      node.setData(newData);
      handleCellValueChange(column.colId, newValue, newData);
    };

    return (
      <div style={{ height: '100%', width: '100%' }}>
        <Select
          value={options.find(option => option.value === value)}
          onChange={handleChange}
          options={options}
          styles={{ container: base => ({ ...base, height: '100%', width: '100%' }) }}
        />
      </div>
    );
  }

  const getRowStyle = (params) => {
    const userSessionEntries = Object.entries(userSessions);

    const isSelectedByAnyUser = userSessionEntries.some(
      ([sessionKey, tag]) => tag === params.data["Tag Name"]
    );

    if (isSelectedByAnyUser) {
      const selectedUserIndex = userSessionEntries.findIndex(
        ([sessionKey, tag]) => tag === params.data["Tag Name"]
      );

      const backgroundColor = getColorByUserIndex(selectedUserIndex);

      return {
        backgroundColor,
        border: `1.2px solid ${backgroundColor.replace(/[^,]+(?=\))/, "1")}`,
      };
    }

    return null;
  };

  const TagNameCellRenderer = (params) => {
    if (params.data["Issue Status"] === "Open") {
      return (
        <div>
          <Icon icon="issue" intent="warning" style={{ marginRight: "4px" }} />
          {params.value}
        </div>
      );
    }

    if (params.data["Issue Status"] === "Pending") {
      return (
        <div>
          <Icon icon="time" intent="primary" style={{ marginRight: "4px" }} />
          {params.value}
        </div>
      );
    }
    
    return <span>{params.value}</span>;
  };

  const columnDefs = useMemo(
    () =>
      headers.map((header, index) => {
        const commonProps = {
          headerName: header,
          field: header,
          sortable: true,
          filter: true,
          resizable: true,
          width: getColumnWidth(header),
          tooltipField: header,
          getRowStyle: getRowStyle,
          tooltipComponent: (props) => {
            const rowStyle = props.node.rowStyle || {};
            const rowColor = rowStyle.backgroundColor || "transparent";
            return (
              <CustomTooltip
                {...props}
                rowColor={rowColor}
                userSessions={userSessions}
              />
            );
          },
          editable: false,
          hide: header === "Template" || header === "Email",
        };

        if (header === "_index") {
          return {
            ...commonProps,
            headerName: "",
            cellRenderer: "rowIndexRenderer",
            width: 35,
            suppressSizeToFit: true,
            suppressMovable: true,
            lockPosition: "left",
            valueGetter: (params) => {
              if (params.node.rowIndex != null) {
                return params.node.rowIndex + 1;
              }
              return "";
            },
            cellStyle: {
              textAlign: "right",
              fontSize: "10px",
              background: isDarkTheme ? "#383E47" : "#F5f7f7",
              color: "#abb3bf",
              paddingLeft: "0px",
              paddingRight: "4px",
            },
            sortable: false,
            filter: false,
            resizable: false,
            pinned: "left",
          };
        }

        if (header === "Tag Name") {
          return {
            ...commonProps,
            cellRenderer: "TagNameCellRenderer",
          };
        }

        if (header === "Owner") {
          return {
            ...commonProps,
            cellRenderer: "SelectCellRenderer",
            cellRendererParams: {
              appUsers: appUsers,
            },
          };
        }

        if (header === "QC" || header === "Ready" || header === "QCV") {
          return {
            ...commonProps,
            cellRenderer: "SelectCellRenderer",
            filterFramework: ReadyFilter,
            filterParams: {
              values: ["Completed", "Resubmitted", "Rejected", "No Action"],
            },
          };
        }

        return commonProps;
      }),
    [headers, userSessions, appUsers, getRowStyle, isDarkTheme, appUsersReady]
  );

  const rowData = useMemo(
    () =>
      data.map((row) => {
        const rowDataObject = {};
        headers.slice(1).forEach((header, index) => {
          rowDataObject[header] = row[index];
        });
        return rowDataObject;
      }),
    [data, headers]
  );

  const handleRowSelection = () => {
    const selectedNodes = assetsGridApi.getSelectedNodes();

    if (selectedNodes && selectedNodes.length > 0) {
      const selectedData = selectedNodes[0].data;
      sendMessage(selectedData["Tag Name"]);

      console.log("selectedData:", selectedData);

      setFocusedCell(selectedData);
      setSelectedRowData(selectedData);
      handleFocusAssetClick(selectedData);
    }
  };

  const handleCellValueChange = async (columnField, newValue, rowData) => {
    console.log("Column changing:", columnField);
    console.log("New value:", newValue);
    console.log("Row data:", rowData);

    if (assetsGridApi) {
      assetsGridApi.showLoadingOverlay();
    }

    await makeApiCallWithRetry(
      `${process.env.REACT_APP_DATA_API}/update_column/`,
      {
        method: 'get',
        params: {
          plant: rowData.Plant,
          tag_name: rowData["Tag Name"],
          unit: rowData.Unit,
          system: rowData.System,
          asset_classification: rowData["Asset Classification"],
          equipment_type: rowData["Equipment Type"],
          template: rowData.Template,
          column_name: columnField,
          column_value: newValue,
          user: user.email,
        },
        headers: {
          'session-id': localStorage.getItem('session-id'),
        }
      }
    )
      .then((response) => {
        setIsLoading(false);
        console.log(response.data);
        if (myToaster.current) {
          myToaster.current.show({
            message: `${columnField} has been updated with value: ${newValue}`,
            intent: Intent.SUCCESS,
            timeout: 4000,
            className: 'font-size-11',
            icon: 'tick-circle',
          });
        }
        setValidPropertiesChange((prev) => !prev);
        if (assetsGridApi) {
          assetsGridApi.hideOverlay();
        }
      })
      .catch((error) => {
        if (error.response) {
          console.error('Error response:', error.response);
        } else if (error.request) {
          console.error('No response received:', error.request);
        } else {
          console.error('Error:', error.message);
        }
        if (myToaster.current) {
          myToaster.current.show({
            message: `Error updating ${columnField}: with ${newValue} ${error.message}`,
            intent: Intent.WARNING,
            timeout: 4000,
            className: 'font-size-11',
          });
        }
      });
  };

  useEffect(() => {
    if (assetsGridApi && data) {
      calculateReadyStats();
    }
  }, [assetsGridApi, data]);

  useEffect(() => {
    if (gridContainerRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        if (assetsGridApi) {
        }
      });

      resizeObserver.observe(gridContainerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [assetsGridApi, gridContainerRef]);

  const parentClassName = isDarkTheme ? "dark-theme" : "";

  const onCellClicked = (params) => {
    if (params.colDef.cellRenderer === "SelectCellRenderer") {
      params.api.startEditingCell({
        rowIndex: params.rowIndex,
        colKey: params.column.colId,
      });
    }
  };

  const filterByOpenIssue = () => {
    assetsGridApi.setFilterModel({
      "Issue Status": {
        type: "equals",
        filter: "Open",
      },
    });
  };

  const clearFilters = () => {
    setQuickFilterText("");
    assetsGridApi.setFilterModel(null);
  };

  const StatusLegend = () => {
    return (
      <Card className="no-padding-card">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-around",
            padding: "1px",
            margin: "1px 0",
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="success"
              className="bp5-dark"
            />{" "}
            <span style={{ marginLeft: "5px" }}>Completed/Approved</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              className="bp5-dark"
            />
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              style={{ marginLeft: "1px" }}
            />{" "}
            <span style={{ marginLeft: "5px" }}>Resubmitted</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="cross-circle" size="12" intent="danger" />{" "}
            <span style={{ marginLeft: "5px" }}>Rejected</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="issue" size="12" intent="warning" />{" "}
            <span style={{ marginLeft: "5px" }}>Open Issue</span>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <Card
      className={parentClassName}
      style={{ height: "100%", padding: "15px 20px", marginBottom: "5px" }}
    >
      <h3 style={{ margin: "0px", fontWeight: "bold" }}>Asset Inventory</h3>

      <div className="asset-search-group">
        <Input
          autoFocus
          style={{ width: "38%" }}
          prefix={<SearchOutlined />}
          placeholder="Search..."
          onChange={(e) => setQuickFilterText(e.target.value)}
          value={quickFilterText}
        />

        <Button
          icon={<RiFilterOffLine size={18} />}
          minimal={true}
          onClick={clearFilters}
          style={{ marginLeft: "0px" }}
        >
        </Button>

        <Button
          icon={<Icon icon="issue" intent="warning" size={18} />}
          minimal={true}
          onClick={filterByOpenIssue}
          style={{ marginLeft: "0px" }}
        >
        </Button>

        <div className="ready-stats" style={{ paddingLeft: "0px" }}>
          {isLoading || !data.length ? (
            <p>Loading...</p>
          ) : (
            <div
              className="statistic-container"
              style={{ transform: "translate(10px, -10px)" }}
            >
              <Statistic
                className={`custom-statistic ${isDarkTheme ? "dark-theme-statistic" : "custom-statistic"
                  }`}
                title={
                  <Tag
                    minimal={true}
                    style={{ fontWeight: "bold" }}
                    intent={"primary"}
                    children="Assets Ready"
                  ></Tag>
                }
                value={readyStats.trueCount}
                suffix={`/ ${readyStats.total} (${readyStats.truePercentage}%)`}
              />
            </div>
          )}
        </div>
      </div>

      <div
        ref={gridContainerRef}
        className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"}
        style={{ height: "calc(100% - 70px)", width: "100%" }}
      >
        <StatusLegend />
        <AgGridReact
          id="assetGrid"
          gridOptions={{
            undoRedoCellEditing: true,
            undoRedoCellEditingLimit: 30,
            rowDragManaged: true,
            components: {
              SelectCellRenderer: SelectCellRenderer,
              IndexCellRenderer: IndexCellRenderer,
              TagNameCellRenderer: TagNameCellRenderer,
            },
            context: {
              appUsers: appUsers,
            },
          }}
          tooltipShowDelay={0}
          getRowStyle={getRowStyle}
          headerHeight={40}
          onGridReady={onGridReady}
          columnDefs={columnDefs}
          rowData={rowData}
          onSelectionChanged={handleRowSelection}
          rowSelection="single"
          getRowHeight={() => 20}
          quickFilterText={quickFilterText}
          onCellValueChanged={(params) => {
            console.log('onCellValueChanged:', params);
            handleCellValueChange(params);
          }}
          enableFillHandle={true}
          enableRangeSelection={true}
          enableCellTextSelection={true}
          suppressDragLeaveHidesColumns={true}
          frameworkComponents={{
            SelectCellRenderer,
            IndexCellRenderer,
            TagNameCellRenderer,
            ReadyFilter
          }}
        />
      </div>
    </Card>
  );
}

export default React.memo(AssetInventory);
