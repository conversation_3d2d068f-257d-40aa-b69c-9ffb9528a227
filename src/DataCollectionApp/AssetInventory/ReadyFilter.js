import React, { useState, useImperativeHandle, forwardRef, useCallback, useEffect } from 'react';
import { Checkbox, Button, Menu, MenuItem } from '@blueprintjs/core';

const ReadyFilter = forwardRef((props, ref) => {
  const [selectedValues, setSelectedValues] = useState([]);
  const [closeFilter, setCloseFilter] = useState();

  const doesFilterPass = useCallback((params) => {
    const value = props.valueGetter(params).toLowerCase();
    return selectedValues.length === 0 || selectedValues.includes(value);
  }, [selectedValues, props]);

  useImperativeHandle(ref, () => ({
    isFilterActive() {
      return selectedValues.length > 0;
    },
    doesFilterPass,
    getModel() {
      return { values: selectedValues };
    },
    setModel(model) {
      setSelectedValues(model ? model.values : []);
    },
    afterGuiAttached(params) {
      setCloseFilter(() => params.hidePopup);
    }
  }));

  useEffect(() => {
    setSelectedValues(props.model?.values || []);
  }, [props.model]);

  const handleSelect = (value) => {
    setSelectedValues((prevSelected) => {
      if (prevSelected.includes(value)) {
        return prevSelected.filter((item) => item !== value);
      } else {
        return [...prevSelected, value];
      }
    });
  };

  const applyFilter = () => {
    props.onModelChange({ values: selectedValues });
    if (closeFilter) {
      closeFilter();
    }
  };

  const filterOptions = ["Completed", "Resubmitted", "Rejected", "No Action"];

  return (
    <Menu>
      {filterOptions.map((value) => (
        <MenuItem
          key={value}
          text={value}
          shouldDismissPopover={false}
          labelElement={<Checkbox checked={selectedValues.includes(value)} />}
          onClick={() => handleSelect(value)}
        />
      ))}
      <Button intent="primary" text="Apply" onClick={applyFilter} />
    </Menu>
  );
});

export default ReadyFilter;
