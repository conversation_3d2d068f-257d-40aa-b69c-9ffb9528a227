import React, { useState, useEffect, useMemo, useRef, useContext } from "react";
import { AgGridReact } from "ag-grid-react";
import { Card, Intent, Icon, Tag, Button, Tooltip, Menu, MenuItem, Popover } from "@blueprintjs/core";
import axios from "axios";
import { useCopyPaste } from "../../hooks/useCopyPaste";
import { AppContext } from "../../AppContextProvider";
import { lineSpinner } from "ldrs";
import { Statistic } from "antd";
import { Input } from "antd"; // Importing Input from Ant Design
import { SearchOutlined } from "@ant-design/icons";
import { HiMiniChevronUpDown as DropdownIcon } from "react-icons/hi2";
import { Select as AntSelect } from "antd";
import { quantum } from "ldrs";
import { useSelector, useDispatch } from "react-redux";
import { setLookupValuesDict, setAppUsers } from "../../redux/appConfigurationSlice";
import { RiFilterOffLine, RiFilterFill } from "react-icons/ri"; // Added RiFilterFill
import makeApiCallWithRetry from "../Helpers/apiHelper";

lineSpinner.register();
quantum.register();

const { Option } = AntSelect;

function AssetInventory({
  setFocusedCell,
  setIsSearchFocused,
  handleFocusAssetClick,
  focusedCell,
  selectedSheet,
  myToaster,
  validPropertiesChange,
  setValidPropertiesChange,
  gridContainerRef,
}) {
  const [appUsersReady, setAppUsersReady] = useState(false);
  const [data, setData] = useState([]);
  const { isDarkTheme } = useContext(AppContext);
  const [isLoading, setIsLoading] = useState(true);
  const [gridColumnApi, setGridColumnApi] = useState(null);
  const { quickFilterText, setQuickFilterText } = useContext(AppContext);
  const [selectedRowData, setSelectedRowData] = useState(null);
  const [areStatsReady, setAreStatsReady] = useState(false);
  const assetsGridApiRef = useRef(null);
  const [assetsGridApi, setassetsGridApi] = useState(null);
  const { setLookupValues } = useContext(AppContext);
  const { user } = useSelector((state) => state.auth);
  const { setUserData } = useContext(AppContext);
  const [headers, setHeaders] = useState([]);
  const { setNpsODdata, setPipingSchedule } = useContext(AppContext);
  const [readyStats, setReadyStats] = useState({
    trueCount: 0,
    total: 0,
    truePercentage: "0.00",
  });
  const { notifications, setNotifications } = useContext(AppContext);
  const { dropdownRules, setDropdownRules } = useContext(AppContext);
  const { sendMessage, userSessions } = useContext(AppContext); // Assuming you might want to use it for some conditional checks
  const dispatch = useDispatch();
  const appUsers = useSelector((state) => state.appConfiguration.appUsers);
  const { allAssetsData, setAllAssetsData } = useContext(AppContext);

  useEffect(() => {
    console.log("Fetching data...");
    axios.get(`${process.env.REACT_APP_MATERIALS_DATA_API}/pipe_properties/nps_od`)
      .then(response => {
        console.log("nps", response.data);
        setNpsODdata(response.data);
      })
      .catch(error => {
        console.error('Error fetching data: ', error);
      });

    axios.get(`${process.env.REACT_APP_MATERIALS_DATA_API}/pipe_properties/schedule`)
      .then(response => {
        console.log("pipingS", response.data);
        setPipingSchedule(response.data);
      })
      .catch(error => {
        console.error('Error fetching data: ', error);
      });
  }, [setNpsODdata, setPipingSchedule]);

  useEffect(() => {
    console.log("Fetching data...");
    console.log("User Email: AI", user.email);

    const fetchData = () => {
      return makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data`, {
        method: "get",
        params: {
          user: user.email,
        },
      });
    };

    fetchData()
      .then((response) => {
        setData(response.data.data);

        // from response.data.data get for each list of items , item 8,9 ,5 and 11 to be set and allAssetsData
        let reduceAssetData = response.data.data.reduce((acc, item) => {
          let asset = {
            "Plant": item[8],
            "Unit": item[9],
            "System": item[10],
            "Tag Name": item[5],
            "Class": item[11],
          };
          acc.push(asset);
          return acc;  // This line is necessary to ensure proper accumulation
        }, []);

        setAllAssetsData(reduceAssetData);

        let errorInAssets = response.data.validation;
        setNotifications(response.data.validation);

        let currentAppUsers = response.data.users["User Email"];
        console.log("appuser", currentAppUsers);
        dispatch(setAppUsers(currentAppUsers));
        setAppUsersReady(true);

        console.log("Error in Assets:", errorInAssets);

        const lookupDict = cleanLookupDict(response.data.lookup);
        setLookupValues(lookupDict);
        dispatch(setLookupValuesDict(lookupDict));
        console.log(data);
        console.log("headers imported", response.data.headers);
        const updatedHeaders = ["_index", ...response.data.headers];
        setHeaders(updatedHeaders);

        const dropdownRules = response.data.dropdown_rules;
        setDropdownRules(dropdownRules);

        console.log("dropdown", dropdownRules);

        console.log("");
        //set usersData
        setUserData(response.data.users);

        setIsLoading(false); // Set loading to false after data is fetched
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        setIsLoading(false); // Also set loading to false in case of error
      });
  }, []);

  const cleanLookupDict = (dict) => {
    for (const key in dict) {
      dict[key] = dict[key].filter((value) => value !== "");
    }
    return dict;
  };

  const calculateReadyStats = () => {
    console.log("Recalculating stats..."); // Log to check if function is triggered

    if (assetsGridApiRef.current) {
      let trueCount = 0;
      let total = 0;

      // Use forEachNodeAfterFilter to iterate over the nodes that passed the filter
      assetsGridApiRef.current.forEachNodeAfterFilter((node) => {
        total++;
        if (node.data.Ready === "Completed") {
          trueCount++;
        }
      });

      const truePercentage = ((trueCount / total) * 100).toFixed(2);
      console.log({ trueCount, total, truePercentage });
      setReadyStats({ trueCount, total, truePercentage });
      setAreStatsReady(true);
    } else {
      console.warn("assetsGridApi is not initialized yet");
    }
  };

  useEffect(() => {
    if (assetsGridApi) {
      calculateReadyStats();
    }
  }, [assetsGridApi, validPropertiesChange]);

  useEffect(() => {
    if (assetsGridApi) {
      assetsGridApi.redrawRows(); // or assetsGridApi.refreshCells({ force: true });
      console.log("USER SESSIONS:", userSessions);
    }
  }, [userSessions, assetsGridApi]);

  useEffect(() => {
    if (assetsGridApi) {
      // Trigger the grid to recompute styles. This method name might vary depending on the grid library you're using.
      assetsGridApi.refreshCells({ force: true }); // You may need to adjust based on your grid API
      console.log("userSessions changed, grid styles updated.");
    }
  }, [userSessions, assetsGridApi]); // Listen to userSessions changes

  const onGridReady = (params) => {
    console.log("Grid is ready");
    assetsGridApiRef.current = params.api;
    setassetsGridApi(params.api);
    setGridColumnApi(params.columnApi);
    params.api.addEventListener("filterChanged", calculateReadyStats);
  };

  const getColumnWidth = (header) => {
    switch (header) {
      case "Tag Name":
        return 180;
      case "Owner":
        return 100;
      case "Ready":
        return 95;
      case "QC":
        return 82;
      case "Asset Classification":
        return 180;
      case "Equipment Type":
        return 150;
      default:
        return 95;
    }
  };

  const IndexCellRenderer = ({ value }) => {
    return (
      <div
        style={{
          // Customize these styles as needed
          fontWeight: "bold",
          backgroundColor: "#f0f0f0", // Light grey background
          color: "#333", // Dark text color
          padding: "4px", // Add some padding inside the cell
        }}
      >
        {value}
      </div>
    );
  };

  // Example custom tooltip renderer
  const CustomTooltip = (props) => {
    const { userSessions, rowColor } = props;

    const data = useMemo(
      () => props.api.getDisplayedRowAtIndex(props.rowIndex).data,
      [props.api, props.rowIndex]
    );

    const email = useMemo(() => {
      if (!userSessions) return null;

      const sessionKeyWithEmail = Object.keys(userSessions).find(
        (key) => userSessions[key] === data["Tag Name"]
      );
      return sessionKeyWithEmail ? sessionKeyWithEmail.split("_")[0] : null;
    }, [userSessions, data]);

    if (!email) return null;

    return (
      <Tag
        style={{
          backgroundColor: "#1C2127",
          color: "white",
          fontWeight: "bold",
        }}
      >
        {email}
      </Tag>
    );
  };

  function SelectCellRenderer(params) {
    let checkboxProps,
      additionalIcon = null;

    // Determine the props for the Checkbox based on params.value
    switch (params.value) {
      case "Completed":
      case "Approved":
        checkboxProps = {
          icon: "tick-circle",
          intent: "success",
          // Add any other props you need to pass to the Checkbox component
        };
        break;
      case "Resubmitted":
        checkboxProps = {
          icon: "tick-circle",
          intent: "primary",
          // Add any other props you need to pass to the Checkbox component
        };
        // Define additional icon for "Resubmitted" status
        // Replace with your actual icon and props
        additionalIcon = {
          icon: "tick-circle", // This should be replaced with your actual icon for resubmitted status
          intent: "primary", // Adjust as necessary
          // Add any other props you need for the second icon
        };
        break;
      case "Rejected":
        checkboxProps = {
          icon: "cross-circle",
          intent: "danger",
          // Add any other props you need to pass to the Checkbox component
        };
        break;
      default:
        checkboxProps = {
          // Add any other props you need to pass to the Checkbox component
        };
    }

    return (
      <div
        style={{
          display: "flex",
          alignItems: "center",
          justifyContent: params.value ? "space-between" : "flex-end",
          width: "100%",
          minHeight: "20px",
          lineHeight: "20px",
        }}
      >
        {/* Render your Checkbox component only if params.value is truthy */}
        {params.value && <Icon {...checkboxProps} />}
        {/* Conditionally render the additional icon for the "Resubmitted" status */}
        {additionalIcon && <Icon {...additionalIcon} />}
        <DropdownIcon />{" "}
        {/* Placeholder for your DropdownIcon, adjust as necessary */}
      </div>
    );
  }

  const getColorByUserIndex = (index) => {
    const colors = [
      "rgba(35,133,81,0.2)", // Green
      "rgba(255,165,0,0.2)", // Orange
      "rgba(172,47,51,0.2)", // Red
      "rgba(86,66,166,0.2)", // Purple
      "rgba(0,191,255,0.2)", // Deep Sky Blue
      "rgba(92,37,92,0.2)", // Goldenrod
      // Add more colors if needed
    ];
    return colors[index];
  };

  class SelectCellEditor extends React.Component {
    constructor(props) {
      super(props);
      this.state = { value: props.value };
    }

    componentDidMount() {
      document.addEventListener("keydown", this.handleKeyDown);
    }

    componentWillUnmount() {
      document.removeEventListener("keydown", this.handleKeyDown);
    }

    getValue() {
      return this.state.value;
    }

    isPopup() {
      return true;
    }

    handleKeyDown = (event) => {
      if (event.key === "Enter") {
        event.preventDefault();
      }
    };

    handleChange = async (selectedValue) => {
      const { node, column, api } = this.props;

      // Stop editing the cell
      api.stopEditing();

      // Get the current row data
      const rowData = node.data;

      // Create a new object with the updated value
      const newData = {
        ...rowData,
        [column.colId]: selectedValue,
      };

      // Update the row data using the grid's API
      node.setData(newData);

      // Call handleCellValueChange with the necessary parameters
      handleCellValueChange(column.colId, selectedValue, newData);

      // Refresh the cell to reflect the updated value
      api.refreshCells({ rowNodes: [node], columns: [column.colId], force: true });

      // Set the focus back to the cell after updating the value
      setTimeout(() => {
        api.setFocusedCell(node.rowIndex, column.colId);
      }, 0);
    };

    render() {
      const options =
        this.props.colDef.field === "Owner"
          ? this.props.appUsers || []
          : this.props.colDef.field === "QC" ||
            this.props.colDef.field === "QCV"
          ? ["Approved", "Rejected", "No Action"]
          : ["Completed", "Resubmitted", "No Action"];
      return (
        <AntSelect
          showSearch
          placeholder="Select a status"
          optionFilterProp="children"
          onChange={this.handleChange}
          value={this.state.value}
          defaultOpen={true}
          style={{ width: "100%" }}
          autoFocus={true}
          popupMatchSelectWidth={false}
        >
          {options.map((option) => (
            <Option key={option} value={option}>
              {option}
            </Option>
          ))}
        </AntSelect>
      );
    }
  }

  const getRowStyle = (params) => {
    const userSessionEntries = Object.entries(userSessions);

    const isSelectedByAnyUser = userSessionEntries.some(
      ([sessionKey, tag]) => tag === params.data["Tag Name"]
    );

    if (isSelectedByAnyUser) {
      const selectedUserIndex = userSessionEntries.findIndex(
        ([sessionKey, tag]) => tag === params.data["Tag Name"]
      );

      const backgroundColor = getColorByUserIndex(selectedUserIndex);

      return {
        backgroundColor,
        border: `1.2px solid ${backgroundColor.replace(/[^,]+(?=\))/, "1")}`,
      };
    }

    return null;
  };

  const TagNameCellRenderer = (params) => {
    if (params.data["Open Issue"] === true) {
      return (
        <div>
          <Icon icon="issue" intent="warning" style={{ marginRight: "4px" }} />
          {params.value}
        </div>
      );
    }
    return <span>{params.value}</span>;
  };

  const columnDefs = useMemo(
    () =>
      headers.map((header, index) => {
        const commonProps = {
          headerName: header,
          field: header,
          sortable: true,
          filter: true,
          resizable: true,
          width: getColumnWidth(header),
          tooltipField: header,
          getRowStyle: getRowStyle,
          tooltipComponent: (props) => {
            const rowStyle = props.node.rowStyle || {};
            const rowColor = rowStyle.backgroundColor || "transparent";
            return (
              <CustomTooltip
                {...props}
                rowColor={rowColor}
                userSessions={userSessions}
              />
            );
          },
          editable:
            header === "Ready" ||
            header === "QC" ||
            header === "QCV" ||
            header === "Owner",
          hide: header === "Template" || header === "Email",
        };

        if (header === "_index") {
          return {
            ...commonProps,
            headerName: "",
            cellRenderer: "rowIndexRenderer",
            width: 35,
            suppressSizeToFit: true,
            suppressMovable: true,
            lockPosition: "left",
            valueGetter: (params) => {
              if (params.node.rowIndex != null) {
                return params.node.rowIndex + 1;
              }
              return "";
            },
            cellStyle: {
              textAlign: "right",
              fontSize: "10px",
              background: isDarkTheme ? "#383E47" : "#F5f7f7",
              color: "#abb3bf",
              paddingLeft: "0px",
              paddingRight: "4px",
              // Changes the cursor to a pointer on hover
            }, // Center text
            // This column should not be filterable or sortable
            sortable: false,
            filter: false,
            resizable: false,
            pinned: "left",
          };
        }

        if (header === "Tag Name") {
          return {
            ...commonProps,
            cellRenderer: "TagNameCellRenderer",
          };
        }

        if (header === "Owner") {
          return {
            ...commonProps,
            cellEditor: "SelectCellEditor",
            cellEditorParams: {
              appUsers: appUsers,
            },
          };
        }

        if (header === "QC" || header === "Ready" || header === "QCV") {
          return {
            ...commonProps,
            // Specify the custom editor for these fields
            cellEditor: "SelectCellEditor",
            cellRenderer: "SelectCellRenderer",
            // Optional: define how to get the value for the editor, e.g., from the data
          };
        }

        return commonProps;
      }),
    [headers, userSessions, appUsers, getRowStyle, isDarkTheme, appUsersReady]
  );

  const rowData = useMemo(
    () =>
      data.map((row) => {
        const rowDataObject = {};
        // Start iterating from 1 to skip the _index field in headers
        headers.slice(1).forEach((header, index) => {
          // Since we're skipping the first header (_index), align data with headers starting from the second header
          rowDataObject[header] = row[index];
        });
        return rowDataObject;
      }),
    [data, headers]
  );

  const handleRowSelection = () => {
    const selectedNodes = assetsGridApi.getSelectedNodes();

    if (selectedNodes && selectedNodes.length > 0) {
      const selectedData = selectedNodes[0].data;
      // Now that we're sure selectedData is valid, send it through WebSocket
      sendMessage(selectedData["Tag Name"]);

      console.log("selectedData:", selectedData);

      setFocusedCell(selectedData); // Update focused cell state
      setSelectedRowData(selectedData);
      handleFocusAssetClick(selectedData); // Trigger any additional actions for selected data
    }
  };

  const handleCellValueChange = async (columnField, newValue, rowData) => {
    console.log("Column changing:", columnField);
    console.log("New value:", newValue);
    console.log("Row data:", rowData);

    if (assetsGridApi) {
      assetsGridApi.showLoadingOverlay();
    }

    await makeApiCallWithRetry(
      `${process.env.REACT_APP_DATA_API}/update_column/`,
      {
        method: 'get',
        params: {
          plant: rowData.Plant,
          tag_name: rowData["Tag Name"],
          unit: rowData.Unit,
          system: rowData.System,
          asset_classification: rowData["Asset Classification"],
          equipment_type: rowData["Equipment Type"],
          template: rowData.Template,
          column_name: columnField,
          column_value: newValue,
          user: user.email,
        },
        headers: {
          'session-id': localStorage.getItem('session-id'),
        }
      }
    )
      .then((response) => {
        setIsLoading(false);
        console.log(response.data);
        if (myToaster.current) {
          myToaster.current.show({
            message: `${columnField} has been updated with value: ${newValue}`,
            intent: Intent.SUCCESS,
            timeout: 4000,
            className: 'font-size-11',
            icon: 'tick-circle',
          });
        }
        setValidPropertiesChange((prev) => !prev);
        if (assetsGridApi) {
          assetsGridApi.hideOverlay();
        }
      })
      .catch((error) => {
        if (error.response) {
          console.error('Error response:', error.response);
        } else if (error.request) {
          console.error('No response received:', error.request);
        } else {
          console.error('Error:', error.message);
        }
        if (myToaster.current) {
          myToaster.current.show({
            message: `Error updating ${columnField}: with ${newValue} ${error.message}`,
            intent: Intent.WARNING,
            timeout: 4000,
            className: 'font-size-11',
          });
        }
      });
  };

  useEffect(() => {
    if (assetsGridApi && data) {
      // Ensure assetsGridApi and data are both available
      calculateReadyStats();
    }
  }, [assetsGridApi, data]); // Dependencies: re-run this useEffect whenever assetsGridApi or data changes

  useEffect(() => {
    if (gridContainerRef.current) {
      const resizeObserver = new ResizeObserver(() => {
        if (assetsGridApi) {
        }
      });

      resizeObserver.observe(gridContainerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, [assetsGridApi, gridContainerRef]);

  const parentClassName = isDarkTheme ? "dark-theme" : "";

  const rowColors = [
    "rgba(35,133,81,0.2)", // Green
    "rgba(255,165,0,0.2)", // Orange
    "rgba(172,47,51,0.2)", // Red
    "rgba(86,66,166,0.2)", // Purple
    "rgba(0,191,255,0.2)", // Deep Sky Blue
    "rgba(92,37,92,0.2)", // Goldenrod
    // Add more colors if needed
  ];

  const onCellClicked = (params) => {
    // Check if the clicked column is a 'SelectCellEditor' column
    if (params.colDef.cellEditor === "SelectCellEditor") {
      params.api.startEditingCell({
        rowIndex: params.rowIndex,
        colKey: params.column.colId,
      });
    }
  };

  const filterByOpenIssue = () => {
    assetsGridApi.setFilterModel({
      "Open Issue": {
        type: "equals",
        filter: true,
      },
    });
  };

  const clearFilters = () => {
    setQuickFilterText("");
    assetsGridApi.setFilterModel(null);
  };

  const handleContextMenu = (column) => {
    const filterItems = ["Completed", "Resubmitted", "Rejected", "No Action"];
    return (
      <Menu>
        {filterItems.map((item) => (
          <MenuItem
            key={item}
            text={item}
            onClick={() => applyColumnFilter(column, item)}
          />
        ))}
      </Menu>
    );
  };

  const applyColumnFilter = (column, filterValue) => {
    const filterModel = assetsGridApi.getFilterModel();
    filterModel[column] = { type: "set", values: [filterValue] };
    assetsGridApi.setFilterModel(filterModel);
  };

  const gridOptions = {
    undoRedoCellEditing: true,
    undoRedoCellEditingLimit: 30,
    rowDragManaged: true,
    components: {
      SelectCellEditor: SelectCellEditor,
      SelectCellRenderer: SelectCellRenderer,
      IndexCellRenderer: IndexCellRenderer,
      TagNameCellRenderer: TagNameCellRenderer,
    },
    context: {
      appUsers: appUsers,
    },
    overlayLoadingTemplate:
      '<span class=""><l-line-spinner size="25" speed="1.0" color="white"></l-bouncy></span>',
  };

  const StatusLegend = () => {
    return (
      <Card className="no-padding-card">
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-around",
            padding: "1px",
            margin: "1px 0",
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="success"
              className="bp5-dark"
            />{" "}
            <span style={{ marginLeft: "5px" }}>Completed/Approved</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              className="bp5-dark"
            />
            <Icon
              icon="tick-circle"
              size="12"
              intent="primary"
              style={{ marginLeft: "1px" }}
            />{" "}
            <span style={{ marginLeft: "5px" }}>Resubmitted</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="cross-circle" size="12" intent="danger" />{" "}
            <span style={{ marginLeft: "5px" }}>Rejected</span>
          </div>
          <div style={{ display: "flex", alignItems: "center" }}>
            <Icon icon="issue" size="12" intent="warning" />{" "}
            <span style={{ marginLeft: "5px" }}>Open Issue</span>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <Card
      className={parentClassName}
      style={{ height: "100%", padding: "15px 20px", marginBottom: "5px" }}
    >
      <h3 style={{ margin: "0px", fontWeight: "bold" }}>Asset Inventory</h3>

      {/* Search and Stats Section */}
      <div className="asset-search-group">
        <Input
          autoFocus
          style={{ width: "40%" }}
          prefix={<SearchOutlined />}
          placeholder="Search..."
          onChange={(e) => setQuickFilterText(e.target.value)}
          value={quickFilterText}
        />

        <Button
          icon={<RiFilterOffLine size={18} />}
          minimal={true}
          onClick={clearFilters}
          style={{ marginLeft: "10px" }}
        >
        </Button>

        <Button
          icon={<Icon icon="warning-sign" size={18} />} // Changed icon to warning triangle
          minimal={true}
          onClick={filterByOpenIssue}
          style={{ marginLeft: "10px" }}
        >
        </Button>

        <div className="ready-stats" style={{ paddingLeft: "10px" }}>
          {isLoading || !data.length ? (
            <p>Loading...</p>
          ) : (
            <div
              className="statistic-container"
              style={{ transform: "translate(10px, -10px)" }}
            >
              <Statistic
                className={`custom-statistic ${isDarkTheme ? "dark-theme-statistic" : "custom-statistic"
                  }`}
                title={
                  <Tag
                    minimal={true}
                    style={{ fontWeight: "bold" }}
                    intent={"primary"}
                    children="Assets Ready"
                  ></Tag>
                }
                value={readyStats.trueCount}
                suffix={`/ ${readyStats.total} (${readyStats.truePercentage}%)`}
              />
            </div>
          )}
        </div>
      </div>

      {/* Grid Display Section */}
      <div
        ref={gridContainerRef}
        className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"}
        style={{ height: "calc(100% - 70px)", width: "100%" }} // Adjust this height as needed
      >
        <StatusLegend />
        <AgGridReact
          id="assetGrid"
          gridOptions={gridOptions}
          tooltipShowDelay={0}
          getRowStyle={getRowStyle}
          headerHeight={40}
          onGridReady={onGridReady}
          columnDefs={columnDefs}
          rowData={rowData}
          onSelectionChanged={handleRowSelection}
          rowSelection="single"
          getRowHeight={() => 20}
          quickFilterText={quickFilterText}
          onCellValueChanged={(params) => {
            console.log('onCellValueChanged:', params);
            handleCellValueChange(params);
          }}
          enableFillHandle={true}
          enableRangeSelection={true}
          enableCellTextSelection={true}
          suppressDragLeaveHidesColumns={true}
          frameworkComponents={{
            SelectCellEditor,
            SelectCellRenderer,
            IndexCellRenderer,
            TagNameCellRenderer,
            agColumnHeader: (props) => {
              const { contextMenu, ...restProps } = props;
              return (
                <div>
                  <div {...restProps} />
                  <Popover content={handleContextMenu(props.column.colId)}>
                    <Button
                      minimal
                      icon={<DropdownIcon size={18} />}
                      style={{ float: "right" }}
                    />
                  </Popover>
                </div>
              );
            },
          }}
        />
      </div>
    </Card>
  );
}

export default React.memo(AssetInventory);
