import React, { useState, useEffect, useMemo, useRef, useContext } from "react";

export function MethodsDateCellRenderer(params) {
  const rawValues = params.value;

  let values = {};
  try {
    values = JSON.parse(params.value);
  } catch (error) {
    console.error("Error parsing cell value:", error);
    return <div>Error in data format: {rawValues} </div>;
  }

  return (
    <div>
      {Object.entries(values).map(([method, date]) => (
        <span>
          {method} - {date} |{" "}
        </span>
      ))}
    </div>
  );
}
