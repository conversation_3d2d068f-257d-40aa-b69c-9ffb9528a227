export const validateInput = (value, type) => {
  switch (type) {
    case "Text":
      return typeof value === "string";
    case "Numeric":
      return !isNaN(value) && value !== "";
    case "Date":
      if (value === "" || value == null) return true;
      const datePattern =
        /^(0?[1-9]|1[0-2])\/(0?[1-9]|[12][0-9]|3[01])\/(18[0-9]{2}|19[0-9]{2}|20[0-9]{2}|2100)$/;
      return datePattern.test(value);
    default:
      return true;
  }
};
