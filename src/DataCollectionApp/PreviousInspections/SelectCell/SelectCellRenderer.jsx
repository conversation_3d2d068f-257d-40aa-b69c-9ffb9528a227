// Import React (needed for using JSX)
import React from "react";

// Import the specific icon you need from react-icons
import { HiMiniChevronUpDown as DropdownIcon } from "react-icons/hi2";

// Define your functional component
export function SelectCellRenderer(params) {
  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        width: "100%",
      }}
    >
      <span>{params.value}</span>
      <DropdownIcon />
    </div>
  );
}
