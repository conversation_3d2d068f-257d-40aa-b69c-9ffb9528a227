import React from "react";
import { Select as AntSelect } from "antd";

const { Option } = AntSelect;

export class SelectCellEditor extends React.Component {
  constructor(props) {
    super(props);
    this.state = { value: props.value };
  }

  componentDidMount() {
    document.addEventListener("keydown", this.handleKeyDown);
  }

  componentWillUnmount() {
    document.removeEventListener("keydown", this.handleKeyDown);
  }

  getValue() {
    return this.state.value;
  }

  isPopup() {
    return true;
  }

  handleKeyDown = (event) => {
    if (event.key === "Enter") {
      event.preventDefault(); // Prevent default behavior on Enter key
    }
  };

  handleChange = (selectedValue) => {
    this.setState({ value: selectedValue }, () => {
      this.props.api.stopEditing();
    });

    const { rowIndex, column } = this.props;
    setTimeout(() => {
      this.props.api.setFocusedCell(rowIndex, column.colId);
      this.props.api.ensureColumnVisible(column.colId);
    }, 0);
  };

  render() {
    return (
      <AntSelect
        showSearch
        placeholder="Select"
        optionFilterProp="children"
        onChange={this.handleChange}
        value={this.state.value}
        defaultOpen={true}
        style={{ width: "100%" }}
        autoFocus={true}
        bordered={false}
        popupMatchSelectWidth={true}
      >
        {this.props.values.map((option) => (
          <Option key={option} value={option}>
            {option}
          </Option>
        ))}
      </AntSelect>
    );
  }
}
