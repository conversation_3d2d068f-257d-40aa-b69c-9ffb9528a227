import React from "react";
import { Select as AntSelect } from "antd";

const { Option } = AntSelect;

export class MultiSelectCellEditor extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedValues: props.value
        ? props.value.split(",").map((v) => v.trim())
        : [],
    };
    this.allOptionValue = "ALL"; // Define the value for the 'ALL' option
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.currentAssetComponents !== this.props.currentAssetComponents
    ) {
      this.setState({
        /* updated state based on new props */
      });
    }
  }

  handleChange = (selectedValues) => {
    const { allOptionValue } = this;
    const currentAssetComponents = this.props.currentAssetComponents || [];
    const allIndex = selectedValues.indexOf(allOptionValue);
    const isAllSelected = allIndex > -1;
    const otherOptions = currentAssetComponents.length;

    if (isAllSelected && selectedValues.length === 1) {
      // Only "ALL" is selected, do nothing
      this.setState({ selectedValues });
    } else if (isAllSelected) {
      // If "ALL" is selected along with other options, deselect "ALL"
      this.setState({
        selectedValues: selectedValues.filter(
          (value) => value !== allOptionValue,
        ),
      });
    } else if (selectedValues.length === otherOptions) {
      // If all options except "ALL" are selected, select only "ALL"
      this.setState({ selectedValues: [allOptionValue] });
    } else {
      // Normal selection
      this.setState({ selectedValues });
    }
  };

  getValue = () => {
    // When closing the editor, return the selected values as a comma-separated string
    return this.state.selectedValues.join(", ");
  };

  isPopup = () => {
    return true;
  };

  render() {
    const { selectedValues } = this.state;
    const { currentAssetComponents } = this.props;

    return (
      <AntSelect
        size="small"
        mode="multiple"
        showSearch
        placeholder="Select components"
        optionFilterProp="children"
        onChange={this.handleChange}
        value={selectedValues}
        style={{ width: "100vw" }}
        autoFocus={true}
        defaultOpen={true}
        bordered={false}
        popupMatchSelectWidth={false}
        maxTagCount={1}
      >
        {/* Add "ALL" option */}
        <Option key={this.allOptionValue} value={this.allOptionValue}>
          ALL
        </Option>
        {/* Add other options dynamically */}
        {currentAssetComponents.map((component) => (
          <Option key={component} value={component}>
            {component}
          </Option>
        ))}
      </AntSelect>
    );
  }
}
