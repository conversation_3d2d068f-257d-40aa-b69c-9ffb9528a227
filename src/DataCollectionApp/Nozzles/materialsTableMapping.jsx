export const TablesMapping = {
  id: "id",
  MAP: "map",
  "Material Specification Name": "material_specification_name",
  "Construction Codes": "construction_codes",
  Table: "table_n",
  "Material Specification": "material_specification",
  "Grade/Temper": "grade_temper",
  Size: "size",
  "Year (yyyy)": "year_yyyy",
  "Lower Valid Year (yyyy)": "lower_valid_year_yyyy",
  "UNS Number": "uns_number",
  "Product Form": "product_form",
  "Nominal Composition": "nominal_composition",
  "Material Type": "material_type",
  "Class According Desing Code": "class_according_desing_code",
  "P Number": "p_number",
  Temper: "temper",
  "Minimum Design Temperature": "minimum_design_temperature",
  "Maximum Temperature": "maximum_temperature",
  "Minimum Tensile Strength (TS)": "minimum_tensile_strength_ts",
  "Minimum Yield Strength (YS)": "minimum_yield_strength_ys",
  "AS_-20": "as_20",
  AS_100: "as_100",
  AS_150: "as_150",
  AS_175: "as_175",
  AS_200: "as_200",
  AS_250: "as_250",
  AS_300: "as_300",
  AS_350: "as_350",
  AS_400: "as_400",
  AS_425: "as_425",
  AS_450: "as_450",
  AS_500: "as_500",
  AS_550: "as_550",
  AS_600: "as_600",
  AS_650: "as_650",
  AS_700: "as_700",
  AS_750: "as_750",
  AS_800: "as_800",
  AS_850: "as_850",
  AS_900: "as_900",
  AS_950: "as_950",
  AS_1000: "as_1000",
  AS_1050: "as_1050",
  AS_1100: "as_1100",
  AS_1150: "as_1150",
  AS_1200: "as_1200",
  AS_1250: "as_1250",
  AS_1300: "as_1300",
  AS_1350: "as_1350",
  AS_1400: "as_1400",
  AS_1450: "as_1450",
  AS_1500: "as_1500",
  AS_1550: "as_1550",
  AS_1600: "as_1600",
  AS_1650: "as_1650",
  AS_1700: "as_1700",
  AS_1750: "as_1750",
  AS_1800: "as_1800",
  AS_1850: "as_1850",
  "Allowable Stress, Tensile and Yield Strength Unit":
    "allowable_stress__tensile_and_yield_strength_unit",
  "Temperature Unit": "temperature_unit",
  Notes: "notes",
  "Group Number": "group_number",
  "External Pressure Chart Number": "external_pressure_chart_number",
  "Code Specification": "code_specification",
};
