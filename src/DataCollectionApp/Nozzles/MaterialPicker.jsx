import React, { useState, useEffect, useMemo, useContext, useRef } from "react";
import {
  Dialog,
  FormGroup,
  InputGroup,
  Button,
  MenuItem,
  Intent,
  Tag,
  DialogFooter,
  Icon,
  Popover,
  DialogBody,
} from "@blueprintjs/core";
import { Select } from "@blueprintjs/select";
import { AgGridReact } from "ag-grid-react";
import { AppContext } from "../../AppContextProvider";
import { Container, Row, Col } from "react-grid-system";
import axios from "axios";
import { TablesMapping } from "./materialsTableMapping";
import { PiCubeTransparentBold } from "react-icons/pi";
import { HiCube } from "react-icons/hi2";
import { PiCubeDuotone } from "react-icons/pi";

import makeApiCallWithRetry from "../Helpers/apiHelper";
import { useSelector, useDispatch } from "react-redux";
// Example structure for materialsProperties
const materialsProperties = {
  "Material of Construction": {
    type: "select",
    required: false,
    placeholder: "Select Material",
    title: "Material of Construction",
    category:"properties",
    
  },

  "Tensile Strength (TS)": {
    type: "input",
    required: false,
    placeholder: "Tensile Strength (TS)",
    title: "Tensile Strength (TS) - psi",
    category:"properties",
  },
  "Yield Strength (YS)": {
    type: "input",
    placeholder: "Yield Strength (YS)",
    required: false,
    title: "Yield Strength (YS) - psi",
    category:"properties",
  },

  "Allowable Stress (S)": {
    type: "input",
    required: false,
    placeholder: "Allowable Stress (S)",
    title: "Allowable Stress (S) - psi",
    category:"properties",
  },

  "Construction Code": {
    type: "temp-input",
    required: false,
    placeholder: "Construction Code",
    title: "Construction Code",
    category:"reference",
  },



  "Design Temperature": {
    type: "temp-input",
    required: false,
    placeholder: "Missing Design Temperature",
    title: "Design Temperature - F",
    category:"reference",
  },

  // Add more fields as needed
};

function MaterialPicker({
  isMaterialPickerComponentOpen,
  setIsMaterialPickerComponentOpen,
  onSave,
  onMaterialSelected,
  propertyRowIndex,
  updateCell,
  lookupDict,
  myToaster
  
}) {
  const { isDarkTheme } = useContext(AppContext);
  const [materialData, setMaterialData] = useState({ headers: [], rows: [] });
  const [inputValues, setInputValues] = useState({});
  const { nozzlesData, setNozzlesData } = useContext(AppContext);
  const [isInputActive, setIsInputActive] = useState(false);
  const [designTemperature, setDesignTemperature] = useState("");
  const { selectedSheet, setSelectedSheet } = useContext(AppContext);
  
  const { user } = useSelector((state) => state.auth);

  const materialsGridApiRef = useRef(null);
  const [materialsGridApi, setMaterialsGridApi] = useState(null);

  const [materialId, setMaterialId] = useState(null);

  const [currentMaterialId, setCurrentMaterialId] = useState(null);
  const [isTransparent, setIsTransparent] = useState(false);



  const formatDataForApi = () => {
    return Object.entries(inputValues)
      .filter(([header]) => header !== "Design Temperature") // Exclude "Design Temperature"
      .map(([header, newValue]) => ({
        header,
        indexrow: `"${propertyRowIndex}"`, // Assuming propertyRowIndex is the row index you're referring to
        new_value: newValue,
      }));
  };

  const sendUpdateRequestToApi = async () => {
    // First, find the index of the row in assetData that matches propertyRowIndex
    const rowIndex = nozzlesData.findIndex(
      (row) => Number(row.rowIndex) === Number(propertyRowIndex)
    );

    if (rowIndex !== -1) {
      // Clone the assetData to avoid direct state mutation
      let updatedAssetData = [...nozzlesData];

      // Update the specific row with new values from inputValues
      updatedAssetData[rowIndex] = {
        ...updatedAssetData[rowIndex],
        "Material of Construction":
          inputValues["Material of Construction"] || "",
        "Allowable Stress (S)": inputValues["Allowable Stress (S)"] || "",
        "Yield Strength (YS)": inputValues["Yield Strength (YS)"] || "",
        "Tensile Strength (TS)": inputValues["Tensile Strength (TS)"] || "",
        "Material ID": materialId || "",
        // Include any other fields that need updating
      };

      // Update the assetData state in your context
      setNozzlesData(updatedAssetData);
      

      // Now proceed to format your update data for the API request
      const updatesJson = formatDataForApi(); // Ensure this reflects the changes appropriately
      // add material id to the updatesJson
      updatesJson.push({ header: "Material ID", indexrow: `"${propertyRowIndex}"`, new_value: materialId });
      const template = "Nozzles";
      

      const url = `${process.env.REACT_APP_DATA_API}/data/bulk_update_cells/`;
      const userEmail = user.email;
      try {

        const session_id = localStorage.getItem("session_id");
        const response = await axios.put(url, null, {
          params: {
            updatesjson: JSON.stringify(updatesJson), // Convert your data to a JSON string
           template,
            user: userEmail
          },
          headers: {
            "session-id":session_id,
          }
        });

    
        const selectedMaterial = inputValues["Material of Construction"];
      // Pass this value to the onSave method provided by the parent component
        // Close dialog on success


        myToaster.current?.show({
          message: `Materials Properties updated at ${template}`,
          intent: Intent.SUCCESS,
          timeout: 5000,
          className: "toaster-font",
          icon: "updated",
        });

        
        setIsMaterialPickerComponentOpen(false);
        onSave(selectedMaterial); 

        console.log("Update successful", response.data);


      } catch (error) {
        console.error("Failed to update cells:", error);


        myToaster.current?.show({
          message: `Error ${error}`,
          intent: Intent.DANGER,
          timeout: 5000,
          className: "toaster-font",
          icon: "error",
        });
        // Handle error
      }
    } else {
      console.error("Row not found in assetData");
      // Handle the case where the row isn't found
    }
  };
  const handleRowSelected = async (event) => {
    const selectedNodes = materialsGridApi.getSelectedNodes();

    const rowData = selectedNodes[0].data;

    const {
      id,
      map,
      minimum_yield_strength_ys: ys,
      minimum_tensile_strength_ts: ts,
    } = rowData;
    console.log("values from table", id, ys, ts);

    setMaterialId(id);

    // Directly obtain the Design Temperature from the input field

    // Update the input fields for YS and TS
    setInputValues((values) => ({
      ...values,
      "Yield Strength (YS)": ys.toString(),
      "Tensile Strength (TS)": ts.toString(),

    }));

    // check if map exist on looupDict under "Material of Construction" compare to each value on the list
    console.log(map);
    if (lookupDict["Material of Construction Pipe"].includes(map)) {

      setInputValues((values) => ({
        ...values,
        "Material of Construction": map.toString(),
      }));
    }

    // Call the API with the row's id and the current Design Temperature
    try {
      const response = await axios.get(
        `${process.env.REACT_APP_MATERIALS_API}/material_properties/allowable_stress`,
        {
          params: {
            id: String(id),
            temperature: String(inputValues["Design Temperature"]),
            template: "Piping",
          },
        }
      );

      console.log("response allowable", response);

      // Assume the API response directly gives the new Allowable Stress value
      const newAllowableStress = response.data.as;
      console.log("AS1", newAllowableStress);

      // Update the Allowable Stress input value
      setInputValues((values) => ({
        ...values,
        "Allowable Stress (S)": newAllowableStress.toString(),
        
      }));
    } catch (error) {
      console.error("API call failed:", error);
    }
  };

  const convertDataForAgGrid = (data) => {
    const { headers, rows } = data;

    // Transform rows from arrays to objects using headers for keys
    const transformedRows = rows.map((row) =>
      row.reduce((obj, value, index) => {
        const key = headers[index]; // Use header value as key
        obj[key] = value;
        return obj;
      }, {})
    );

    return { headers, rows: transformedRows };
  };

  const CustomFloatingFilter = React.memo(({ column }) => {
    const [inputValue, setInputValue] = useState("");
    const inputRef = useRef(null);

    // Function to call API
    const callApi = async (value) => {
      const filterValue = JSON.stringify({ [column.colId]: value });
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_MATERIALS_API}/material_properties/filter`,
          {
            params: { filters: filterValue, template: selectedSheet },
          }
        );
        console.log("API Response", response.data);
        const convertedData = convertDataForAgGrid(response.data);
        // Update the material data which should trigger a grid update
        setMaterialData({
          headers: convertedData.headers,
          rows: convertedData.rows,
        });
      } catch (error) {
        console.error("Error fetching filtered data:", error);
      }
    };

    // Handle Enter key press to trigger the search
    const handleKeyDown = (e) => {
      if (e.key === "Enter") {
        callApi(inputValue);
      }
    };

    return (
      <InputGroup
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown} // Use onKeyDown event to listen for Enter key press
      />
    );
  });

  useEffect(() => {
    if (!nozzlesData || propertyRowIndex === undefined) return;
    console.log("assetData in MaterialPicker", nozzlesData);
    console.log("propertyRowIndex", propertyRowIndex);

    // Find the specific object in assetData where its rowIndex matches propertyRowIndex
    const currentRowData =
      nozzlesData.find(
        (row) => Number(row.rowIndex) === Number(propertyRowIndex)
      ) || {};

    console.log("currentRowData", currentRowData);

    const initialValues = {};
    setCurrentMaterialId(currentRowData["Material ID"]);
    Object.entries(materialsProperties).forEach(([key, { placeholder }]) => {
      // Use the currentRowData to set initial values for inputs
      initialValues[key] = currentRowData[key] || "";
    });
    setInputValues(initialValues);
  }, [nozzlesData, propertyRowIndex]);




  useEffect(() => {
    // Assuming you want to track a specific field's value for design temperature
    const temp = inputValues["Design Temperature"];
    if (temp !== undefined) {
      setDesignTemperature(temp);
    }
  }, [inputValues]);


  

  const renderInputField = (key, details) => {
    const isTempEmpty = designTemperature === "";
    let  showWarning = false;
    if ((key === "Allowable Stress (S)" || key === "Design Temperature") && isTempEmpty) {
      showWarning = true;
      console.log("showWarning", showWarning);
       
  }
    switch (details.type) {
      case "input":
        return (
  
          <InputGroup
            id={key}
            name={key}
            placeholder={details.placeholder}
            value={inputValues[key] || ""}
            onChange={handleInputChange}
            leftIcon={showWarning ? (
              
              <Icon
                icon="warning-sign"
                intent="warning"
              />
         
            ) : null}
            intent={showWarning ? Intent.WARNING : Intent.NONE}
          />
         
        );
      case "temp-input":
        return (
          <InputGroup
            id={key}
            name={key}
            placeholder={details.placeholder}
            value={inputValues[key] || ""}
            onValueChange={handleInputChange}
            disabled={true}
            
            buttonPosition="none"
            intent={showWarning ? Intent.DANGER : Intent.NONE}
            leftIcon={showWarning ? (
              <Icon
                icon="warning-sign"
                intent="warning"
              />
            ) : null}
           
            
          />
        );

      // Update the renderInputField function for 'select' case
      case "select":
        const options = lookupDict[key] || details.options; // Use lookupDict if available
        return (
          <Select
            fill={true}
            items={options}
            itemPredicate={(query, item) =>
              item !== null && item !== undefined && item.toLowerCase().indexOf(query.toLowerCase()) >= 0
            }
            itemRenderer={(item, { handleClick, modifiers }) => (
              <MenuItem
                key={item}
                onClick={handleClick}
                text={item || "<Select>"}
                active={modifiers.active}
                disabled={modifiers.disabled}
                fill={true}
              />
            )}
            onItemSelect={(item) => handleSelectChange(key, item)}
          >
            <Button
              className="ellipsis"
              text={inputValues[key] || details.placeholder}
              rightIcon="double-caret-vertical"
            />
          </Select>
        );

      default:
        return null;
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_MATERIALS_API}/material_properties`,
          { params: { template: "Piping" } }
        );
  
        let { headers, rows } = response.data;
  
        // Transform each row into an object where each value is mapped to its corresponding header
        let transformedRows = rows.map((row) => row.reduce((acc, value, index) => {
          acc[headers[index]] = value === null || value === "None" ? "" : value;
          return acc;
        }, {}));
  
        // Move the identified row to the beginning of the array if exists
        if (currentMaterialId) {
          const identifiedRowIndex = transformedRows.findIndex(row => row.id === currentMaterialId);
          if (identifiedRowIndex > -1) {
            const [identifiedRow] = transformedRows.splice(identifiedRowIndex, 1);
            transformedRows.unshift(identifiedRow); // Place the identified row at the beginning
          }
        }
  
        setMaterialData({ headers, rows: transformedRows });
      } catch (error) {
        console.error("Error fetching material data:", error);
      }
    };
  
    fetchData();
  }, [currentMaterialId, selectedSheet]); // Ensure useEffect runs when currentMaterialId or selectedSheet changes
  

  const gridOptions = {
    floatingFiltersHeight: 35,
  };
  const getColumnWidth = (header) => {
    switch (header) {
      case "Table":
        return 95;
      case "Grade/Temper":
        return 140;
      case "Class According Desing Code":
        return 170;
      case "B31.1-3  Coefficient, Y/ B31.4-8 Design Factor,F":
        return 170;
      case "B31.1-3 Quality factor, E/B31.4 Weld Joint Factor,E/ B31.8 Longitudinal Joint Factor,E":
        return 170;
      case "P Number":
        return 115;
      default:
        return 135;
    }
  };

    // Function to determine the closest higher column based on the current temperature
const determineHighlightColumn = (currentTemperature, headers) => {
  // Extract temperature values from headers and sort them
  const temps = headers.filter(h => h.startsWith('as_'))
                       .map(h => parseInt(h.replace('as_', '')))
                       .sort((a, b) => a - b);

  // Find the closest higher temperature or exact match
  const closest = temps.find(t => t >= currentTemperature);
  return closest ? `as_${closest}` : null;
};

// Calculate the column to highlight
const highlightColumn = determineHighlightColumn(designTemperature, materialData.headers);


  const columnDefs = useMemo(() => {
    const actualFieldNames = Object.values(TablesMapping);
    const nonEmptyFields = materialData.headers.filter((header) =>
      actualFieldNames.includes(header)
    );

    const normalColumns = []; // Columns without "as_" prefix
    const asColumns = []; // Columns with "as_" prefix, to be grouped

    Object.entries(TablesMapping).forEach(([headerName, actualFieldName]) => {
      if (!nonEmptyFields.includes(actualFieldName)) return; // Skip empty columns

      const isAsColumn = actualFieldName.startsWith("as_");
      const commonColumnProps = {
        filter: "agTextColumnFilter",
        floatingFilter: true,
        suppressHeaderMenuButton: true,
       // floatingFilterComponent: CustomFloatingFilter,
        floatingFilterComponentParams: { suppressFilterButton: true },
        suppressFloatingFilterButton: true,
        sortable: true,
        resizable: true,
        cellStyle: { fontSize: "13px" },
        pinned:
          actualFieldName === "construction_codes" ||
          actualFieldName === "material_specification",
        hide:
          actualFieldName === "id" ||
          actualFieldName === "material_specification_name" ||
          actualFieldName === "table_n" ||
          actualFieldName === "map",
        
        width: getColumnWidth(headerName),
        headerName,
        menuTabs: [],
        field: actualFieldName,
      };

      if (isAsColumn) {
        // Adjust headerName or any other property for "as_" columns as needed
        asColumns.push({
          ...commonColumnProps,
          cellStyle: (params) => {
            // Apply highlight style directly inline if this column is the one to be highlighted
            return params.colDef.field === highlightColumn ? { backgroundColor: 'rgba(180, 93, 195, 0.2)' } : null;
          },
        });
      } else {
        normalColumns.push({ ...commonColumnProps });
      }
    });

    // If there are "as_" columns to be grouped
    if (asColumns.length > 0) {
      normalColumns.push({
        headerName:
          "Allowable Stress (S), psi  , for Metal Temperature, °F",
        children: asColumns.map((column) => ({
          ...column,
          headerName: column.headerName.slice(3),
          width: "80",
        })),
        suppressHeaderMenuButton: true,
      });
    }

    return normalColumns;
  }, [CustomFloatingFilter, materialData.headers]); // Reacting to changes in materialData.headers

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setInputValues((prevValues) => ({ ...prevValues, [name]: value }));
  };

  const handleSelectChange = (key, item) => {
    setInputValues((prevValues) => ({ ...prevValues, [key]: item }));
  };

  const onGridReady = (params) => {
    console.log("Grid is ready");
    materialsGridApiRef.current = params.api;
    setMaterialsGridApi(params.api);
  };
  const getRowStyle = (params) => {
    if (params.data.id === currentMaterialId) {
      return { backgroundColor: "rgba(180, 93, 195, 0.25)", border: "1.2px solid  #B45DC3" }; // Apply your desired styling here
    }
    // Apply other styles or return nothing for rows without special styling
  };

  return (
  <>

    <Dialog
      usePortal={true}
      minHeight={700}
      className={`${isDarkTheme ? "bp5-dark" : ""} ${
        isTransparent ? "transparent-content" : ""
      }`}
      style={{
        width: "80%",
        height: "70%",
        overflowY: "1",
        position: "relative",
      }} // Add position: 'relative'
      isOpen={isMaterialPickerComponentOpen}
      onClose={() => setIsMaterialPickerComponentOpen(false)}
      icon="th"
      title={
        <div
          style={{
            textAlign: "left",

            marginLeft: "5px",
            fontWeight: "bold",
            fontSize: "1.2em",
          }}
        >
          Material Properties
        </div>
      }
      canEscapeKeyClose={true}
      canOutsideClickClose={true}
    >
       <Button
            className="cube-button"
            icon={isTransparent ? <HiCube /> : <PiCubeTransparentBold />}
            minimal={true}
            onClick={() => setIsTransparent(!isTransparent)}
            style={{
              position: "absolute",
              top: "5px",
              right: "50px",
              zIndex: 1000, // Ensure it's above other elements
            }}
          />
      <DialogBody className={isTransparent ? "transparent-content" : ""}>
        <Container fluid>
          <Row>
            {Object.entries(materialsProperties).map(([key, details]) => (
              <Col key={key} xs={12} sm={6} md={4} lg={2}>
                <div className="property-item property-container">
                  <FormGroup
                    label={
                      <>
                        <b className="ellipsis material-select-button">
                          {details.title}
                        </b>
                        {details.required && (
                          <Tag className="compact-tag" intent={Intent.SUCCESS}>
                            Required
                          </Tag>
                        )}
                      </>
                    }
                    labelFor={key}
                  >
                    {renderInputField(key, details)}
                  </FormGroup>
                </div>
              </Col>
            ))}
          </Row>
        </Container>
        <div
          className={
            isDarkTheme ? "ag-theme-alpine-dark materials" : "ag-theme-alpine"
          }
          style={{ width: "100%", height: "60vh", fontSize: "0.8rem" }}
        >
          <AgGridReact
            getRowStyle={getRowStyle}
            className="materials"
            style={{ fontSize: "0.8rem" }}
            rowData={materialData.rows}
            columnDefs={columnDefs}
            rowHeight={24}
            rowSelection="single"
            headerHeight={35}
            gridOptions={gridOptions}
            onSelectionChanged={handleRowSelected}
            onGridReady={onGridReady}
          />
        </div>
      </DialogBody>
      <DialogFooter
        actions={
          <>
            <Button
              intent="danger"
              text="Cancel"
              onClick={() => setIsMaterialPickerComponentOpen(false)}
            />
            <Button
              intent="primary"
              text="Save"
              onClick={sendUpdateRequestToApi}
            />
          </>
        }
      />
    </Dialog>
    </>
  );
}

export default MaterialPicker;
