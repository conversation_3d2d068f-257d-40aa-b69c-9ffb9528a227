import React from "react";
import { Tag, Intent } from "@blueprintjs/core"; // or your tag component

const ComponentsGridHeader = ({ displayName, isRequired }) => {
  return (
    <div className="custom-grid-header">
      <span>{displayName}</span>
      {isRequired && (
        <div className="header-tag">
          <Tag className="compact-tag" intent={Intent.SUCCESS}>
            Required
          </Tag>
        </div>
      )}
    </div>
  );
};

export default ComponentsGridHeader;
