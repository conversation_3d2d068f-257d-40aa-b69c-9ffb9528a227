import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setPostsLoading, setPostsSuccess, setPostsError } from "../../../redux/postsSlice";
import makeApiCallWithRetry from "../../Helpers/apiHelper";

const useFetchPosts = (postIds, userEmail) => {
  const { posts, shouldRefetch } = useSelector((state) => state.posts);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const dispatch = useDispatch();

  useEffect(() => {
    const fetchData = async () => {
      const sessionId = localStorage.getItem("session_id");
      if (!sessionId) {
        console.error("No session ID available");
        setError("No session ID available");
        return;
      }

      if (!postIds || postIds.length === 0) return;

      if (posts.length === 0 || shouldRefetch) {
        setIsLoading(true);
        dispatch(setPostsLoading());

        try {
          const response = await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/retrieve_posts`, {
            method: "get",
            params: {
              user: userEmail,
              post_ids: postIds.join(","),
            },
            headers: {
              "session-id": sessionId,
            },
          });

          console.log("post response", response);

          if (response.data.posts.length === 0) {
            setIsLoading(false);
          } else {
            const postsWithReplies = await Promise.all(
              response.data.posts.map(async (post) => {
                const repliesResponse = await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/retrieve_replies`, {
                  method: "get",
                  params: {
                    post_id: post.id,
                    user: userEmail,
                  },
                  headers: {
                    "session-id": sessionId,
                  },
                });
                return { ...post, replies: repliesResponse.data.replies };
              })
            );

            dispatch(setPostsSuccess(postsWithReplies));
            setIsLoading(false);
          }
        } catch (error) {
          console.error("Error fetching posts:", error);
          setError(error.message || "Error fetching posts");
          dispatch(setPostsError(error.message || "Error fetching posts"));
          setIsLoading(false);
        }
      }
    };

    fetchData();
  }, [postIds, shouldRefetch, dispatch, userEmail]);

  const refetchPosts = () => {
    setIsLoading(true);
    dispatch(setPostsLoading());

    makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/retrieve_posts`, {
      method: "get",
      params: {
        post_ids: postIds.join(","),
        user: userEmail,
      },
      headers: {
        "session-id": localStorage.getItem("session_id"),
      },
    })
      .then((response) => {
        console.log("post response", response);

        if (response.data.posts.length === 0) {
          setIsLoading(false);
        } else {
          Promise.all(
            response.data.posts.map(async (post) => {
              const repliesResponse = await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/retrieve_replies`, {
                method: "get",
                params: {
                  post_id: post.id,
                  user: userEmail,
                },
                headers: {
                  "session-id": localStorage.getItem("session_id"),
                },
              });
              return { ...post, replies: repliesResponse.data.replies };
            })
          ).then((postsWithReplies) => {
            dispatch(setPostsSuccess(postsWithReplies));
            setIsLoading(false);
          });
        }
      })
      .catch((error) => {
        setError(error.message || "Error fetching posts");
        dispatch(setPostsError(error.message || "Error fetching posts"));
        setIsLoading(false);
      });
  };

  return { posts, isLoading, error, refetchPosts };
};

export default useFetchPosts;