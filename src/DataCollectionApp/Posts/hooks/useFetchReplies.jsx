import { useDispatch } from "react-redux";
import axios from "axios";
import { setRepliesLoading, setRepliesSuccess, setRepliesError } from "../../../redux/postsSlice";

const useFetchReplies = () => {
  const dispatch = useDispatch();

  const getReplies = async (postId) => {
    dispatch(setRepliesLoading({ postId }));

    try {
      const response = await axios.get(`${process.env.REACT_APP_DATA_API}/retrieve_replies`, {
        params: { post_id: postId },
      });
      dispatch(setRepliesSuccess({ postId, replies: response.data.replies }));
    } catch (error) {
      dispatch(setRepliesError({ postId, error: error.message || "Error fetching replies" }));
    }
  };

  return { getReplies };
};

export default useFetchReplies;