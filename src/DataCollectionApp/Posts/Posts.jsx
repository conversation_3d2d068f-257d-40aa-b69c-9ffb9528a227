import React, { useState, useContext, useEffect } from "react";
import {
  Card,
  Elevation,
  Button,
  Intent,
  H4,
  H6,
  Dialog,
  TextArea,
  InputGroup,
  MenuItem,
  Divider,
  Icon,
  Alert,
} from "@blueprintjs/core";
import { Select } from "@blueprintjs/select";
import { useDispatch, useSelector } from "react-redux";
import { v4 as uuidv4 } from "uuid";
import useFetchPosts from "./hooks/useFetchPosts";
import {
  addPost,
  updatePostStatus,
  removePost,
  clearPosts,
  setPostIds,
  setPostsIndices,
  addReply,
  removeReply,
} from "../../redux/postsSlice";
import makeApiCallWithRetry from "./apiHelper";
import { AppContext } from "../../AppContextProvider";

const statusOptions = [
  { label: "Open", value: "Open", icon: "issue", intent: Intent.WARNING },
  { label: "Resolved", value: "Resolved", icon: "tick-circle", intent: Intent.SUCCESS },
  { label: "Pending", value: "Pending", icon: "time", intent: Intent.PRIMARY },
];

function Posts({ latestAssetClickParams }) {
  const { postIds, postsIndices } = useSelector((state) => state.posts);
  const [isAddPostDialogOpen, setIsAddPostDialogOpen] = useState(false);
  const [formTitle, setFormTitle] = useState("");
  const [formBody, setFormBody] = useState("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletePostId, setDeletePostId] = useState(null);
  const [isReplyFormOpen, setIsReplyFormOpen] = useState(false);
  const [replyFormBody, setReplyFormBody] = useState("");
  const [expandedPostId, setExpandedPostId] = useState(null);
  const [isDeleteReplyDialogOpen, setIsDeleteReplyDialogOpen] = useState(false);
  const [deleteReplyId, setDeleteReplyId] = useState(null);
  const [sortCriteria, setSortCriteria] = useState("date");
  const { isDarkTheme } = useContext(AppContext);
  const { user } = useSelector((state) => state.auth);
  const { posts, isLoading, error, refetchPosts } = useFetchPosts(postIds, user.email);
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(clearPosts());
  }, [latestAssetClickParams, dispatch]);

  const handleRefetch = () => {
    refetchPosts();
  };

  const handleAddPost = () => {
    setIsAddPostDialogOpen(true);
  };

  const openDeleteDialog = (postId) => {
    setDeletePostId(postId);
    setIsDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setDeletePostId(null);
    setIsDeleteDialogOpen(false);
  };

  const softDeletePost = async () => {
    try {
      const response = await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/delete_post/${deletePostId}`, {
        method: "delete",
        params: {
          user: user.email,
        },
      });

      if (response.data.message === "Post deleted successfully") {
        dispatch(removePost(deletePostId));

        const postIndex = postIds.indexOf(deletePostId);
        const postMetadataItem = postsIndices[postIndex];

        await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data/delete_row`, {
          method: "delete",
          params: {
            rowIndex: postMetadataItem + 3,
            template: "Posts",
            user: user.email,
          },
        });

        refetchPosts();
        closeDeleteDialog();
      }
    } catch (error) {
      console.error("Error soft deleting the post:", error);
    }
  };

  const fetchPostData = async () => {
    try {
      const response = await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data/posts`, {
        method: "get",
        params: {
          plant: String(latestAssetClickParams["Plant"]),
          unit: String(latestAssetClickParams["Unit"]),
          system: String(latestAssetClickParams["System"]),
          tag_name: String(latestAssetClickParams["Tag Name"]),
          asset_classification: String(latestAssetClickParams["Asset Classification"]),
          equipment_type: String(latestAssetClickParams["Equipment Type"]),
          user: String(user.email),
        },
      });

      const posts = response.data.posts.rows.map((row) => row.post_id);
      const indices = response.data.posts.rowIndices;

      dispatch(setPostIds(posts));
      dispatch(setPostsIndices(indices));
    } catch (error) {
      console.error("Error fetching post data:", error);
    }
  };

  const createPost = async (postId, title, body) => {
    if (!title || !body) {
      console.error("Title and body cannot be empty");
      return;
    }

    try {
      const currentTimestamp = new Date().toISOString();
      const postData = {
        id: String(postId),
        user_id: "",
        user_name: user.email,
        title: title,
        body: body,
        status: "Open",
        created_at: currentTimestamp,
      };

      const response = await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/create_post`, {
        method: "post",
        data: postData,
        headers: {
          "Content-Type": "application/json",
          "session-id": "testing",
        },
        params: {
          user: user.email,
        },
      });

      const newPost = response.data;
      newPost.id = postId;
      newPost.title = title;
      newPost.user_name = user.email;
      newPost.body = body;
      newPost.status = "Open";
      newPost.created_at = currentTimestamp;

      const postIndex = latestAssetClickParams["rowIndex"];
      dispatch(addPost({ ...newPost, index: postIndex }));

      setIsAddPostDialogOpen(false);
      setFormTitle("");
      setFormBody("");

      try {
        await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data/new_post/`, {
          method: "get",
          params: {
            plant: String(latestAssetClickParams["Plant"]),
            unit: String(latestAssetClickParams["Unit"]),
            system: String(latestAssetClickParams["System"]),
            tag_name: String(latestAssetClickParams["Tag Name"]),
            asset_classification: String(latestAssetClickParams["Asset Classification"]),
            equipment_type: String(latestAssetClickParams["Equipment Type"]),
            id: String(postId),
            user: String(user.email),
          },
        });

        setTimeout(fetchPostData, 2000);
      } catch (error) {
        console.log("Error creating post in backend:", error);
        dispatch(removePost(postId));
      }
    } catch (error) {
      console.error("Error creating post:", error);
    }
  };

  const updateStatus = async (postId, status) => {
    try {
      const post = posts.find((post) => post.id === postId);
      const postIndex = postIds.indexOf(postId);
      const postMetadataItem = postsIndices[postIndex];

      const editPostData = {
        post_id: postId,
        title: post.title,
        body: post.body,
        status: status,
      };

      const response = await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/edit_post/${postId}`, {
        method: "patch",
        data: editPostData,
        headers: {
          "Content-Type": "application/json",
          "session-id": localStorage.getItem("session_id"),
        },
        params: {
          user: user.email,
        },
      });

      if (response.data.message === "Post updated successfully") {
        dispatch(updatePostStatus({ postId, status }));

        await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/data/update_cell`, {
          method: "PUT",
          params: {
            indexrows: String(postMetadataItem + 3),
            template: "Posts",
            header: "status",
            new_value: status,
            user: user.email,
          },
        });

        refetchPosts();
      }
    } catch (error) {
      console.error("Error updating post status:", error);
    }
  };

  const createReply = async (postId, body) => {
    try {
      const currentTimestamp = new Date().toISOString();
      const replyData = {
        post_id: postId,
        user_id: user.email,
        body: body,
      };

      const response = await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/create_reply`, {
        method: "post",
        data: replyData,
        headers: {
          "Content-Type": "application/json",
          "session-id": "testing",
        },
        params: {
          user: user.email,
        },
      });

      const newReply = {
        id: response.data.id,
        user_id: user.email,
        body: body,
        created_at: currentTimestamp,
      };

      dispatch(addReply({ postId, reply: newReply }));

      setIsReplyFormOpen(false);
      setReplyFormBody("");
    } catch (error) {
      console.error("Error creating reply:", error);
    }
  };

  const openDeleteReplyDialog = (replyId) => {
    setDeleteReplyId(replyId);
    setIsDeleteReplyDialogOpen(true);
  };

  const closeDeleteReplyDialog = () => {
    setDeleteReplyId(null);
    setIsDeleteReplyDialogOpen(false);
  };

  const deleteReply = async (postId, replyId) => {
    try {
      const post = posts.find((post) => post.id === postId);
      const reply = post.replies.find((reply) => reply.id === replyId);

      if (reply.user_id !== user.email) {
        Alert.show({
          intent: "warning",
          title: "Delete Reply",
          message: "You can only delete your own replies.",
          confirmButtonText: "OK",
        });
        return;
      }

      await makeApiCallWithRetry(`${process.env.REACT_APP_DATA_API}/delete_reply/${replyId}`, {
        method: "patch",
        params: {
          user: user.email,
        },
      });

      dispatch(removeReply({ postId, replyId }));
      closeDeleteReplyDialog();
    } catch (error) {
      console.error("Error deleting reply:", error);
    }
  };

  const sortedPosts = posts.slice().sort((a, b) => {
    return new Date(b.created_at) - new Date(a.created_at);
  });

  const filterStatus = (query, option) => {
    return option.label.toLowerCase().indexOf(query.toLowerCase()) >= 0;
  };

  const renderStatusOption = (status, { handleClick, modifiers }) => {
    if (!modifiers.matchesPredicate) {
      return null;
    }
    return (
      <MenuItem
        active={modifiers.active}
        key={status.value}
        onClick={handleClick}
        text={status.label}
        icon={<Icon icon={status.icon} intent={status.intent} />}
        shouldDismissPopover={false}
      />
    );
  };

  const getStatusButtonText = (status) => {
    const statusOption = statusOptions.find((option) => option.value === status);
    return statusOption ? (
      <>
        {statusOption.label}
      </>
    ) : (
      status
    );
  };

  return (
    <div style={{ maxWidth: "800px", margin: "0 auto", padding: "20px" }}>
      <H4>Issue Tracker</H4>
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          marginBottom: "20px",
        }}
      >
        <Button
          icon="plus"
          onClick={handleAddPost}
          intent={Intent.PRIMARY}
          style={{ marginRight: "10px" }}
        >
          New Post
        </Button>
        <Button icon="refresh" onClick={handleRefetch} />
      </div>
      {isLoading ? (
        <div>Loading...</div>
      ) : error ? (
        <div>Error: {error}</div>
      ) : (
        sortedPosts?.map((post) => (
          <Card
            key={post.id}
            elevation={Elevation.TWO}
            style={{ marginBottom: "20px", padding: "10px" }}
          >
            <div style={{ marginBottom: "10px" }}>
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <div style={{ display: "flex", alignItems: "center" }}>
                  <H6>{post.user_name}</H6>
                  <Divider style={{ height: "20px", margin: "0 10px" }} />
                  <span>Status</span>
                  <Icon icon={statusOptions.find((option) => option.value === post.status)?.icon} style={{ marginLeft: "10px" }} intent={statusOptions.find((option) => option.value === post.status)?.intent} />
                  <Select
                    items={statusOptions}
                    itemPredicate={filterStatus}
                    itemRenderer={renderStatusOption}
                    noResults={<MenuItem disabled={true} text="No results." />}
                    onItemSelect={(item) => updateStatus(post.id, item.value)}
                    popoverProps={{ minimal: true }}
                  >
                    <Button
                      text={getStatusButtonText(post.status)}
                      rightIcon="caret-down"
                      style={{ marginLeft: "10px" }}
                    />
                  </Select>
                </div>
                <H6>{formatDateTime(post.created_at)}</H6>
              </div>
              <H6>{post.title}</H6>
            </div>
            <Divider />
            <div>
              <p>{post.body}</p>
              <div style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  icon="trash"
                  minimal={true}
                  intent={Intent.DANGER}
                  onClick={() => openDeleteDialog(post.id)}
                />
              </div>
            </div>

            <div style={{ marginTop: "10px", display: "flex", justifyContent: "flex-start" }}>
              <Button
                minimal={true}
                icon={expandedPostId === post.id ? "chevron-up" : "chevron-down"}
                onClick={() =>
                  setExpandedPostId(expandedPostId === post.id ? null : post.id)
                }
              >
                {expandedPostId === post.id ? "Hide Replies" : `Show Replies (${post.replies.length})`}
              </Button>
              <Button
                minimal={true}
                icon="comment"
                onClick={() => {
                  setExpandedPostId(post.id);
                  setIsReplyFormOpen(true);
                }}
                style={{ marginLeft: "10px" }}
              >
                Add Reply
              </Button>
            </div>

            {expandedPostId === post.id && (
              <>
                {post.replies
                  .slice()
                  .sort((a, b) => a.created_at - b.created_at)
                  .map((reply) => (
                    <Card
                      key={reply.id}
                      elevation={Elevation.ONE}
                      style={{ marginTop: "10px", padding: "10px" }}
                    >
                      <div style={{ marginBottom: "10px" }}>
                        <div
                          style={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <H6>{reply.user_id}</H6>
                          <H6>{formatDateTime(reply.created_at)}</H6>
                        </div>
                      </div>
                      <div>
                        <p>{reply.body}</p>
                        <div style={{ display: "flex", justifyContent: "flex-end" }}>
                          {reply.user_id === user.email && (
                            <Button
                              minimal={true}
                              icon="trash"
                              intent={Intent.DANGER}
                              onClick={() => openDeleteReplyDialog(reply.id)}
                            />
                          )}
                        </div>
                      </div>
                    </Card>
                  ))}
                {isReplyFormOpen && expandedPostId === post.id && (
                  <div style={{ marginTop: "10px" }}>
                    <TextArea
                      fill={true}
                      placeholder="Reply"
                      value={replyFormBody}
                      onChange={(e) => setReplyFormBody(e.target.value)}
                    />
                    <div style={{ marginTop: "5px" }}>
                      <Button
                        small={true}
                        intent={Intent.PRIMARY}
                        onClick={() => createReply(post.id, replyFormBody)}
                      >
                        Submit
                      </Button>
                      <Button
                        small={true}
                        style={{ marginLeft: "5px" }}
                        onClick={() => setIsReplyFormOpen(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </>
            )}
          </Card>
        ))
      )}

      <Dialog
        className={isDarkTheme ? "bp5-dark" : ""}
        isOpen={isAddPostDialogOpen}
        onClose={() => setIsAddPostDialogOpen(false)}
        title="Add Post"
      >
        <div className="bp5-dialog-body">
          <InputGroup
            placeholder="Subject"
            value={formTitle}
            onChange={(e) => setFormTitle(e.target.value)}
            style={{ marginBottom: "10px" }}
          />
          <TextArea
            fill={true}
            placeholder="Message"
            value={formBody}
            onChange={(e) => setFormBody(e.target.value)}
          />
        </div>
        <div className="bp5-dialog-footer">
          <div className="bp5-dialog-footer-actions">
            <Button
              intent="danger"
              onClick={() => setIsAddPostDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              intent={Intent.PRIMARY}
              onClick={() => createPost(uuidv4(), formTitle, formBody)}
            >
              Save
            </Button>
          </div>
        </div>
      </Dialog>

      <Dialog
        className={isDarkTheme ? "bp5-dark" : ""}
        isOpen={isDeleteDialogOpen}
        onClose={closeDeleteDialog}
        title="Confirm Delete"
      >
        <div className="bp5-dialog-body">
          <p>Are you sure you want to delete this post?</p>
        </div>
        <div className="bp5-dialog-footer">
          <div className="bp5-dialog-footer-actions">
            <Button onClick={closeDeleteDialog}>Cancel</Button>
            <Button intent={Intent.DANGER} onClick={softDeletePost}>
              Delete
            </Button>
          </div>
        </div>
      </Dialog>

      <Dialog
        className={isDarkTheme ? "bp5-dark" : ""}
        isOpen={isDeleteReplyDialogOpen}
        onClose={closeDeleteReplyDialog}
        title="Confirm Delete"
      >
        <div className="bp5-dialog-body">
          <p>Are you sure you want to delete this reply?</p>
        </div>
        <div className="bp5-dialog-footer-actions">
          <Button onClick={closeDeleteReplyDialog}>Cancel</Button>
          <Button intent={Intent.DANGER} onClick={() => deleteReply(expandedPostId, deleteReplyId)}>
            Delete
          </Button>
        </div>
      </Dialog>
    </div>
  );
}

function formatDateTime(timestamp) {
  if (typeof timestamp === "string") {
    return new Date(timestamp).toLocaleString();
  }
  return new Date(timestamp * 1000).toLocaleString();
}

export default Posts;
