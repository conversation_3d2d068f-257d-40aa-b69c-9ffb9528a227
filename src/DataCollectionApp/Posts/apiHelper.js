// File: apiHelper.js
import axios from "axios";

const makeApiCallWithRetry = async (url, options, maxTotalTime = 180000) => {
  let totalWaitTime = 0;
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
  const fixedIncrement = 5000; // 5 seconds in milliseconds

  // Get the session ID from local storage
  const sessionId = localStorage.getItem('session_id');

  // Add the session ID to the request headers
  const axiosOptions = {
    ...options,
    headers: {
      ...options.headers,
      'session-id': sessionId,
    },
  };

  while (true) {
    try {
      console.log(`Calling ${url}`);
      return await axios(url, axiosOptions);
    } catch (error) {
      const isRetryableError =
        error.response &&
        (error.response.status === 503 ||
          error.response.status === 429 ||
          error.response.status === 500);

      let waitTime = fixedIncrement + Math.random() * 1000; // 5 seconds + a random amount up to 1 second

      if (!isRetryableError || totalWaitTime + waitTime > maxTotalTime) {
        console.log(`Error: `, error);
        throw error;
      }

      console.log(`Waiting ${waitTime}ms before next attempt`);
      await delay(waitTime);
      totalWaitTime += waitTime;
      console.log(`Total wait time: ${totalWaitTime}s`);
    }
  }
};

export default  makeApiCallWithRetry ;
