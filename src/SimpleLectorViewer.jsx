import React, { useState, useEffect } from 'react';
import {
  <PERSON>vas<PERSON><PERSON><PERSON>,
  Page,
  Pages,
  Root,
  TextLayer,
  HighlightLayer,
  CurrentPage,
  TotalPages,
  ZoomIn,
  ZoomOut,
  CurrentZoom,
  Thumbnails,
  Thumbnail
} from '@anaralabs/lector';
import { GlobalWorkerOptions } from 'pdfjs-dist';
import "pdfjs-dist/web/pdf_viewer.css";
import { Spinner } from '@blueprintjs/core';
import { useDispatch } from 'react-redux';
import { useInView } from 'react-intersection-observer';

// Set up the worker according to the documentation
GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.js",
  import.meta.url
).toString();

console.log("Worker source set to:", GlobalWorkerOptions.workerSrc);

/**
 * SimpleLectorViewer - Basic PDF viewer using Lector with your specific file loading method
 */
const SimpleLectorViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [error, setError] = useState(null);
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });

  // Load PDF file using the exact method you specified
  useEffect(() => {
    async function fetchFile() {
      if (hasLoaded) return;

      setPdfFile(null);
      setDownloadProgress(0);

      try {
        console.log('Fetching PDF for:', { fileId, filePath });
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });

        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          if (contentLength) {
            setDownloadProgress(receivedLength / parseInt(contentLength, 10));
          }
        }

        // Create a blob with the correct MIME type
        const blob = new Blob(chunks, { type: 'application/pdf' });

        // Validate PDF header
        const headerBuffer = await blob.slice(0, 5).arrayBuffer();
        const headerBytes = new Uint8Array(headerBuffer);
        const header = String.fromCharCode.apply(null, headerBytes);

        if (header.indexOf('%PDF-') !== 0) {
          console.error("Invalid PDF header:", header);
          throw new Error("The file does not appear to be a valid PDF. Missing PDF header signature.");
        }

        const pdfUrl = URL.createObjectURL(blob);
        console.log("PDF blob created successfully:", {
          size: blob.size,
          type: blob.type,
          url: pdfUrl
        });
        setPdfFile(pdfUrl);
        setHasLoaded(true);

        // If you need to dispatch state updates
        if (dispatch && typeof dispatch === 'function') {
          dispatch({
            type: 'UPDATE_PDF_VIEWER',
            payload: {
              fileId,
              filePath,
              page: 1,
              isVisible: inView
            }
          });
        }

        console.log('PDF Loaded:', {
          fileId,
          filePath,
          initialPage: 1,
          isVisible: inView
        });
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
        setError(error.message || "Failed to load PDF");
      }
    }

    // Only fetch the file if we're not on the PDF viewer URL directly
    const currentPath = window.location.pathname;
    if (!currentPath.includes('/pdfjs-3.11.174-dist/web/')) {
      fetchFile();
    }

    return () => {
      if (pdfFile && !hasLoaded) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, filePath, dispatch, hasLoaded, inView, pdfFile]);

  // Custom loading indicator with progress
  const LoadingIndicator = () => (
    <div className="p-4 flex flex-col items-center justify-center h-full">
      <Spinner size={50} />
      {downloadProgress > 0 && downloadProgress < 1 && (
        <div style={{ width: '80%', marginTop: '20px' }}>
          <div
            style={{
              height: '4px',
              width: `${downloadProgress * 100}%`,
              backgroundColor: isDarkTheme ? '#48AFF0' : '#2B95D6',
              borderRadius: '2px'
            }}
          />
          <div style={{ marginTop: '8px', textAlign: 'center' }}>
            {Math.round(downloadProgress * 100)}%
          </div>
        </div>
      )}
      <div style={{ marginTop: '20px' }}>Loading PDF...</div>
    </div>
  );

  // Error display
  if (error) {
    return (
      <div
        style={{
          padding: '20px',
          textAlign: 'center',
          color: 'red',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <h3>Error Loading PDF</h3>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div
      ref={ref}
      style={{
        height: '100%',
        width: '100%',
        overflow: 'hidden',
        ...style
      }}
    >
      {pdfFile ? (
        <Root
          source={pdfFile}
          className={`w-full h-full border overflow-auto ${isDarkTheme ? 'bg-gray-800' : 'bg-white'}`}
          loader={<LoadingIndicator />}
          options={{
            cMapUrl: 'https://unpkg.com/pdfjs-dist@2.14.305/cmaps/',
            cMapPacked: true,
            standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@2.14.305/standard_fonts/',
            disableStream: false,
            disableAutoFetch: false
          }}
        >
          <div style={{ display: 'flex', padding: '8px', borderBottom: '1px solid #ccc' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>Page</span>
              <CurrentPage style={{
                padding: '2px 8px',
                border: '1px solid #ccc',
                borderRadius: '3px',
                minWidth: '30px',
                textAlign: 'center'
              }} />
              <span>of</span>
              <TotalPages />
            </div>

            <div style={{ marginLeft: '24px', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span>Zoom</span>
              <ZoomOut>
                <button style={{ background: 'none', border: 'none', cursor: 'pointer' }}>-</button>
              </ZoomOut>
              <CurrentZoom style={{
                padding: '2px 8px',
                border: '1px solid #ccc',
                borderRadius: '3px',
                minWidth: '50px',
                textAlign: 'center'
              }} />
              <ZoomIn>
                <button style={{ background: 'none', border: 'none', cursor: 'pointer' }}>+</button>
              </ZoomIn>
            </div>
          </div>

          <div style={{ display: 'flex', flexGrow: 1, overflow: 'hidden' }}>
            <div style={{ width: '180px', borderRight: '1px solid #ccc', overflowY: 'auto' }}>
              <Thumbnails>
                <Thumbnail style={{
                  width: '120px',
                  margin: '10px auto',
                  display: 'block',
                  border: '1px solid #ccc'
                }} />
              </Thumbnails>
            </div>

            <Pages
              className={isDarkTheme ?
                'invert-[94%] hue-rotate-180 brightness-[80%] contrast-[228%] flex-1' :
                'flex-1'
              }
            >
              <Page>
                <CanvasLayer />
                <TextLayer />
                <HighlightLayer className="bg-yellow-200/70" />
              </Page>
            </Pages>
          </div>
        </Root>
      ) : (
        <LoadingIndicator />
      )}
    </div>
  );
};

export default SimpleLectorViewer;
