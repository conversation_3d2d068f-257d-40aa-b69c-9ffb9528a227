/**
 * This file patches the react-pdf module to fix the issue with pdfjs-dist imports
 * and ensures consistent versioning between the PDF.js API and worker
 */
import * as pdfjsLib from 'pdfjs-dist';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';

// Log the current versions to help with debugging
console.log('PDF.js API version:', pdfjsLib.version);

// Create an object with all the exports from pdfjs-dist
const pdfjsModule = {
  ...pdfjsLib,
  default: pdfjsLib, // Add a default export that points to the entire module
  version: pdfjsLib.version // Use the actual version from the library
};

// Set the worker using unpkg CDN with the exact same version as the API
// This ensures perfect version matching between API and worker
pdfjsLib.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.js`;

console.log(`Using PDF.js worker from: ${pdfjsLib.GlobalWorkerOptions.workerSrc}`);

export default pdfjsModule;
