import React, { useState, useCallback, useMemo } from "react";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";
import { Icon } from "@blueprintjs/core";
import "@blueprintjs/core/lib/css/blueprint.css";
import "@blueprintjs/icons/lib/css/blueprint-icons.css";

let idCount = 0;

const addIdInDataRecursive = (arrayData) => {
  arrayData.forEach((mainRow) => {
    mainRow.customId = idCount;
    idCount++;
    if (mainRow.childrens) {
      addIdInDataRecursive(mainRow.childrens);
    }
  });
};

const addAssetNameRecursive = (arrayData, assetName) => {
  arrayData.forEach((mainRow) => {
    if (mainRow.isLeaf) {
      mainRow.asset = assetName;
    }
    if (mainRow.childrens) {
      addAssetNameRecursive(mainRow.childrens, mainRow.type);
    }
  });
};

const MedalCellRenderer = (props) => {
  const { data, value } = props;
  const icon = data.isLeaf ? (
    <Icon icon="document" />
  ) : data.expanded ? (
    <Icon icon="chevron-down" />
  ) : (
    <Icon icon="chevron-right" />
  );

  return (
    <div className="ag-cell" style={{ paddingLeft: `${data.level * 20}px` }}>
      {icon}
      <span>{value}</span>
    </div>
  );
};

const App = () => {
  const [gridApi, setGridApi] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [rowData, setRowData] = useState([]);

  const columnDefs = useMemo(
    () => [
      {
        headerName: "Equipment Files",
        field: "type",
        cellRenderer: MedalCellRenderer,
        filter: true,
        sortable: false,
      },
      {
        headerName: "Asset",
        field: "asset",
        hide: true,
      },
      {
        headerName: "File ID",
        field: "fileid",
        hide: true,
      },
    ],
    [],
  );

  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      resizable: true,
    }),
    [],
  );

  const onGridReady = useCallback((params) => {
    setGridApi(params.api);
    const data = [
      {
        type: "Piping",
        childrens: [
          {
            type: "Piping-1",
            childrens: [
              { type: "file1.pdf", isLeaf: true },
              { type: "file2.pdf", isLeaf: true },
            ],
          },
          {
            type: "Piping-2",
            childrens: [
              { type: "drawing1.pdf", isLeaf: true },
              { type: "specification1.pdf", isLeaf: true },
            ],
          },
        ],
      },
      {
        type: "Fix Equipment",
        childrens: [
          {
            type: "D-1",
            childrens: [
              { type: "UT-Report.pdf", isLeaf: true },
              { type: "EXTVI-Report.pdf", isLeaf: true },
            ],
          },
          {
            type: "D-82",
            childrens: [
              { type: "UT-Report.pdf", isLeaf: true },
              { type: "EXTVI-Report.pdf", isLeaf: true },
            ],
          },
          {
            type: "E-4",
            childrens: [
              { type: "UT-Report.pdf", isLeaf: true },
              { type: "EXTVI-Report.pdf", isLeaf: true },
            ],
          },
          {
            type: "E-12",
            childrens: [
              { type: "UT-Report.pdf", isLeaf: true },
              { type: "EXTVI-Report.pdf", isLeaf: true },
            ],
          },
          {
            type: "E-85",
            childrens: [
              { type: "UT-Report.pdf", isLeaf: true },
              { type: "EXTVI-Report.pdf", isLeaf: true },
            ],
          },
          {
            type: "T-3",
            childrens: [
              { type: "UT-Report.pdf", isLeaf: true },
              { type: "EXTVI_Report.pdf", isLeaf: true },
            ],
          },
        ],
      },
    ];

    addIdInDataRecursive(data);
    addAssetNameRecursive(data, "");
    setRowData(data);
  }, []);

  const updateDataRecursive = useCallback((data, customId, expanded) => {
    data.forEach((row) => {
      if (row.customId === customId) {
        row.expanded = expanded;
      }
      if (row.childrens) {
        updateDataRecursive(row.childrens, customId, expanded);
      }
    });
  }, []);

  const onRowClicked = useCallback(
    (event) => {
      if (!event.data.isLeaf) {
        const expanded = !event.data.expanded;
        updateDataRecursive(rowData, event.data.customId, expanded);
        setRowData([...rowData]);
      }
    },
    [rowData, updateDataRecursive],
  );

  const onRowSelected = useCallback((event) => {
    if (event.data.isLeaf) {
      setSelectedFile(event.data);
      console.log("Selected File:", event.data);
    }
  }, []);

  const isRowSelectable = useCallback((node) => {
    return node.data.isLeaf;
  }, []);

  const getRowClass = useCallback((params) => {
    if (params.data.isLeaf) {
      return params.node.selected
        ? "ag-row-selected ag-row-leaf"
        : "ag-row-leaf";
    }
    return "";
  }, []);

  return (
    <div className="ag-theme-alpine" style={{ height: "500px", width: "100%" }}>
      <AgGridReact
        columnDefs={columnDefs}
        defaultColDef={defaultColDef}
        rowData={rowData}
        treeData={true}
        getDataPath={(data) => data.customId.toString()}
        autoGroupColumnDef={{
          headerName: "Equipment Files",
          field: "type",
          cellRenderer: "agGroupCellRenderer",
          cellRendererParams: {
            innerRenderer: MedalCellRenderer,
          },
        }}
        groupDefaultExpanded={-1}
        rowSelection="single"
        onRowClicked={onRowClicked}
        onRowSelected={onRowSelected}
        isRowSelectable={isRowSelectable}
        getRowClass={getRowClass}
        onGridReady={onGridReady}
      />
    </div>
  );
};

export default App;
