import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON><PERSON>, SpinnerSize, Button, ButtonGroup } from "@blueprintjs/core";
import "@blueprintjs/core/lib/css/blueprint.css";
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Use a working CDN for the PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const MultiPagePDFViewer = ({ fileId, isDarkTheme }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [numPages, setNumPages] = useState(null);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const containerRef = useRef();

  // Fetch PDF file with progress tracking
  useEffect(() => {
    async function fetchFile() {
      if (!fileId) return;
      
      setLoading(true);
      setPdfFile(null);
      setDownloadProgress(0);

      try {
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });
        
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          if (contentLength) {
            setDownloadProgress(receivedLength / contentLength);
          }
        }

        const blob = new Blob(chunks);
        const pdfUrl = URL.createObjectURL(blob);
        setPdfFile(pdfUrl);
        setLoading(false);
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
        setError("Failed to load PDF. Please try again.");
        setLoading(false);
      }
    }

    fetchFile();

    return () => {
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId]);

  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    setNumPages(numPages);
    console.log(`PDF loaded with ${numPages} pages`);
  }, []);

  const onDocumentLoadError = useCallback((error) => {
    console.error("Error loading PDF:", error);
    setError("Failed to load the PDF. Please check if the file is corrupted or try again.");
  }, []);

  // Zoom functions
  const handleZoomIn = useCallback(() => {
    setScale(prevScale => Math.min(prevScale + 0.2, 3.0));
  }, []);

  const handleZoomOut = useCallback(() => {
    setScale(prevScale => Math.max(prevScale - 0.2, 0.5));
  }, []);

  const handleRotate = useCallback(() => {
    setRotation(prevRotation => (prevRotation + 90) % 360);
  }, []);

  const resetView = useCallback(() => {
    setScale(1.0);
    setRotation(0);
  }, []);

  // Loading state
  if (loading) {
    return (
      <div style={{
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa'
      }}>
        <Spinner size={SpinnerSize.STANDARD} />
        <div style={{ marginTop: "10px", color: isDarkTheme ? '#f5f8fa' : '#182026' }}>
          Loading PDF...
        </div>
        {downloadProgress > 0 && downloadProgress < 1 && (
          <div style={{ marginTop: "10px", color: isDarkTheme ? '#f5f8fa' : '#182026' }}>
            {Math.round(downloadProgress * 100)}%
          </div>
        )}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div style={{
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa',
        color: isDarkTheme ? '#f5f8fa' : '#182026'
      }}>
        <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>
        <Button intent="primary" onClick={() => window.location.reload()}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div
      style={{
        height: "100vh",
        width: "100%",
        display: "flex",
        flexDirection: "column",
        backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa'
      }}
    >
      {/* Toolbar */}
      <div style={{
        padding: "10px",
        borderBottom: "1px solid #ccc",
        backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        flexShrink: 0
      }}>
        <div style={{ color: isDarkTheme ? '#f5f8fa' : '#182026' }}>
          {numPages ? `${numPages} pages` : 'Loading...'}
        </div>
        <ButtonGroup>
          <Button icon="zoom-out" onClick={handleZoomOut} disabled={scale <= 0.5} />
          <Button text={`${Math.round(scale * 100)}%`} disabled />
          <Button icon="zoom-in" onClick={handleZoomIn} disabled={scale >= 3.0} />
          <Button icon="refresh" onClick={handleRotate} />
          <Button text="Reset" onClick={resetView} />
        </ButtonGroup>
      </div>

      {/* PDF Content - Scrollable container showing all pages */}
      <div
        ref={containerRef}
        style={{
          flex: 1,
          overflow: "auto",
          padding: "20px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          gap: "20px"
        }}
      >
        {pdfFile && (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div style={{ padding: "20px", textAlign: "center" }}>
                <Spinner size={SpinnerSize.STANDARD} />
                <div style={{ marginTop: "10px", color: isDarkTheme ? '#f5f8fa' : '#182026' }}>
                  Loading PDF...
                </div>
              </div>
            }
            error={
              <div style={{ padding: "20px", color: "red", textAlign: "center" }}>
                Failed to load PDF. Please try again.
              </div>
            }
          >
            {/* Render all pages */}
            {numPages && Array.from(new Array(numPages), (el, index) => (
              <div
                key={`page_${index + 1}`}
                style={{
                  marginBottom: "20px",
                  border: "1px solid #ccc",
                  borderRadius: "4px",
                  overflow: "hidden",
                  boxShadow: "0 2px 8px rgba(0,0,0,0.1)"
                }}
              >
                <div
                  style={{
                    padding: "8px",
                    backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
                    borderBottom: "1px solid #ccc",
                    fontSize: "12px",
                    color: isDarkTheme ? '#f5f8fa' : '#182026'
                  }}
                >
                  Page {index + 1}
                </div>
                <Page
                  pageNumber={index + 1}
                  scale={scale}
                  rotate={rotation}
                  renderTextLayer={true}
                  renderAnnotationLayer={true}
                  loading={
                    <div style={{ 
                      padding: "40px", 
                      textAlign: "center",
                      color: isDarkTheme ? '#f5f8fa' : '#182026'
                    }}>
                      <Spinner size={SpinnerSize.SMALL} />
                      <div style={{ marginTop: "10px" }}>
                        Rendering page {index + 1}...
                      </div>
                    </div>
                  }
                  error={
                    <div style={{ 
                      padding: "40px", 
                      color: "red", 
                      textAlign: "center" 
                    }}>
                      Failed to render page {index + 1}
                    </div>
                  }
                />
              </div>
            ))}
          </Document>
        )}
      </div>
    </div>
  );
};

export default MultiPagePDFViewer;
