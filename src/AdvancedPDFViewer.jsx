import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { Card, Spinner, Button, ButtonGroup, HTMLSelect, Dialog, FormGroup, NumericInput } from "@blueprintjs/core";
import { Input, InputGroup } from '@blueprintjs/core';
import { useDispatch, useSelector } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import { FixedSizeList as List } from 'react-window';
import { TransformWrapper, TransformComponent } from "react-zoom-pan-pinch";
import Draggable from 'react-draggable';
import { throttle } from 'lodash';

// Import Redux actions
import {
  updatePdfViewer,
  setViewerVisibility,
  setFocusedViewer,
} from './redux/currentPdfSlice';

// Import CSS
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import "./pdf-viewer.css";

// Set up the worker for PDF.js
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

// Constants
const ZOOM_LEVELS = [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3];
const PAGE_PADDING = 20;

// SearchWindow component
const SearchWindow = ({ isOpen, onClose, searchQuery, onSearchChange, onSearch, searchResults, currentSearchIndex, onNavigateSearch, isDarkTheme }) => {
  if (!isOpen) return null;
  
  return (
    <Draggable handle=".search-window-header">
      <div className={`search-window ${isDarkTheme ? 'bp5-dark' : ''}`} style={{
        position: 'absolute',
        top: '50px',
        right: '20px',
        width: '350px',
        backgroundColor: isDarkTheme ? '#30404D' : '#FFFFFF',
        border: `1px solid ${isDarkTheme ? '#394B59' : '#E1E8ED'}`,
        borderRadius: '3px',
        boxShadow: '0 0 10px rgba(0, 0, 0, 0.2)',
        zIndex: 1000,
      }}>
        <div className="search-window-header" style={{
          padding: '8px 10px',
          backgroundColor: isDarkTheme ? '#394B59' : '#F5F8FA',
          borderBottom: `1px solid ${isDarkTheme ? '#30404D' : '#E1E8ED'}`,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          cursor: 'move',
        }}>
          <span style={{ fontWeight: 'bold' }}>Search PDF</span>
          <Button icon="cross" minimal={true} small={true} onClick={onClose} />
        </div>
        <div style={{ padding: '10px' }}>
          <FormGroup>
            <InputGroup
              placeholder="Enter search term"
              value={searchQuery}
              onChange={onSearchChange}
              onKeyPress={(e) => e.key === 'Enter' && onSearch()}
              rightElement={
                <Button icon="search" minimal={true} onClick={onSearch} />
              }
              style={{ marginBottom: "16px" }}
            />
          </FormGroup>
          
          {searchResults.length > 0 ? (
            <div>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '10px'
              }}>
                <span>
                  Result {currentSearchIndex + 1} of {searchResults.length}
                </span>
                <ButtonGroup>
                  <Button 
                    icon="arrow-left"
                    onClick={() => onNavigateSearch('prev')} 
                    small={true} 
                    disabled={searchResults.length <= 1}
                  />
                  <Button 
                    icon="arrow-right"
                    onClick={() => onNavigateSearch('next')} 
                    small={true} 
                    disabled={searchResults.length <= 1}
                  />
                </ButtonGroup>
              </div>
              
              <div style={{ 
                padding: '10px', 
                backgroundColor: isDarkTheme ? '#394B59' : '#F5F8FA', 
                borderRadius: '3px',
                marginBottom: '10px',
                fontSize: '13px',
                maxHeight: '150px',
                overflow: 'auto',
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word'
              }}>
                <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>
                  Page {searchResults[currentSearchIndex].page}
                </div>
                <div>{searchResults[currentSearchIndex].text}</div>
              </div>
            </div>
          ) : (
            searchQuery && <div style={{ textAlign: 'center', padding: '10px' }}>No results found</div>
          )}
        </div>
      </div>
    </Draggable>
  );
};

// GoToPage dialog component
const GoToPageDialog = ({ isOpen, onClose, onGoTo, currentPage, totalPages }) => {
  const [pageInput, setPageInput] = useState(currentPage);
  
  const handleGoTo = () => {
    if (pageInput >= 1 && pageInput <= totalPages) {
      onGoTo(pageInput);
      onClose();
    }
  };
  
  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      title="Go to Page"
      style={{ width: '300px' }}
    >
      <div className="bp5-dialog-body">
        <FormGroup label={`Page (1-${totalPages})`} labelFor="page-input">
          <NumericInput
            id="page-input"
            min={1}
            max={totalPages}
            value={pageInput}
            onValueChange={setPageInput}
            fill={true}
            allowNumericCharactersOnly={true}
          />
        </FormGroup>
      </div>
      <div className="bp5-dialog-footer">
        <div className="bp5-dialog-footer-actions">
          <Button onClick={onClose}>Cancel</Button>
          <Button intent="primary" onClick={handleGoTo}>Go</Button>
        </div>
      </div>
    </Dialog>
  );
};

// Main PDF Viewer component
const AdvancedPDFViewer = ({ fileId, filePath, isDarkTheme }) => {
  // State for PDF
  const [pdfUrl, setPdfUrl] = useState(null);
  const [pdfDocument, setPdfDocument] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  
  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isGoToPageOpen, setIsGoToPageOpen] = useState(false);
  
  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  
  // Refs
  const containerRef = useRef(null);
  const pagesRef = useRef({});
  const transformComponentRef = useRef(null);
  
  // Redux and visibility
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  // Fetch PDF file
  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();
    let objectUrl = null;
    
    // Reset states
    setPdfUrl(null);
    setPdfDocument(null);
    setLoading(true);
    setError(null);
    setSearchResults([]);
    setCurrentSearchIndex(-1);
    
    const fetchFile = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: { "session-id": localStorage.getItem("session_id") },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );
        
        if (isMounted) {
          objectUrl = URL.createObjectURL(response.data);
          setPdfUrl(objectUrl);
          
          // Load the PDF document for searching
          const loadingTask = pdfjs.getDocument(objectUrl);
          const pdf = await loadingTask.promise;
          setPdfDocument(pdf);
          
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(err)) {
            console.log("Request canceled:", err.message);
          } else {
            console.error("Failed to fetch the PDF:", err);
            setError(`Failed to load the PDF. Please try again.`);
          }
        }
      }
    };
    
    if (fileId) {
      fetchFile();
    } else {
      setLoading(false);
      setError('No file ID provided. Cannot load the PDF.');
    }
    
    // Cleanup function
    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [fileId]);
  
  // Redux/Visibility effects
  useEffect(() => {
    dispatch(setViewerVisibility({ fileId, isVisible: inView }));
    if (inView) {
      dispatch(setFocusedViewer(fileId));
    }
  }, [inView, fileId, dispatch, filePath]);
  
  // Update Redux store when page changes
  useEffect(() => {
    if (numPages > 0) {
      dispatch(updatePdfViewer({
        fileId,
        filePath,
        page: pageNumber,
        isVisible: inView
      }));
    }
  }, [pageNumber, fileId, filePath, inView, dispatch, numPages]);
  
  // Document load handlers
  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    console.log(`Document loaded with ${numPages} pages`);
    setNumPages(numPages);
    setLoading(false);
    setPageNumber(1);
  }, []);
  
  const onDocumentLoadError = useCallback((error) => {
    console.error('Error loading PDF:', error);
    setError(`Failed to load PDF: ${error.message}`);
    setLoading(false);
  }, []);
  
  // Navigation functions
  const goToPrevPage = useCallback(() => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  }, []);
  
  const goToNextPage = useCallback(() => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  }, [numPages]);
  
  const goToPage = useCallback((page) => {
    if (page >= 1 && page <= numPages) {
      setPageNumber(page);
      scrollToPage(page);
    }
  }, [numPages]);
  
  // Scroll to page function
  const scrollToPage = useCallback((pageNum) => {
    if (containerRef.current && pagesRef.current[pageNum]) {
      pagesRef.current[pageNum].scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, []);
  
  // Zoom functions
  const zoomIn = useCallback(() => {
    if (transformComponentRef.current) {
      transformComponentRef.current.zoomIn();
    }
  }, []);
  
  const zoomOut = useCallback(() => {
    if (transformComponentRef.current) {
      transformComponentRef.current.zoomOut();
    }
  }, []);
  
  const resetZoom = useCallback(() => {
    if (transformComponentRef.current) {
      transformComponentRef.current.resetTransform();
    }
  }, []);
  
  // Search functions
  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);
  
  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim() || !pdfDocument) {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      return;
    }
    
    try {
      setIsSearchOpen(true);
      
      const results = [];
      
      // Search through each page
      for (let i = 1; i <= pdfDocument.numPages; i++) {
        const page = await pdfDocument.getPage(i);
        const textContent = await page.getTextContent();
        
        // Group text items by their y-position to maintain paragraph structure
        const lineMap = new Map();
        textContent.items.forEach(item => {
          // Round y position to account for slight variations in same line
          const yPos = Math.round(item.transform[5] * 100) / 100;
          if (!lineMap.has(yPos)) {
            lineMap.set(yPos, []);
          }
          lineMap.get(yPos).push(item);
        });
        
        // Sort lines by y-position (top to bottom)
        const sortedLines = Array.from(lineMap.entries())
          .sort((a, b) => b[0] - a[0]) // Reverse order (top to bottom)
          .map(([_, items]) => {
            // Sort items in each line by x-position (left to right)
            return items.sort((a, b) => a.transform[4] - b.transform[4]);
          });
        
        // Create a structured text representation with proper line breaks
        let structuredText = '';
        sortedLines.forEach(line => {
          const lineText = line.map(item => item.str).join('');
          structuredText += lineText + '\n';
        });
        
        // Search for matches in the structured text
        const lowerQuery = searchQuery.toLowerCase();
        const lowerText = structuredText.toLowerCase();
        let searchIndex = lowerText.indexOf(lowerQuery);
        
        while (searchIndex !== -1) {
          // Get some context around the match (up to 100 chars before and after)
          const contextStart = Math.max(0, searchIndex - 100);
          const contextEnd = Math.min(structuredText.length, searchIndex + lowerQuery.length + 100);
          
          // Extract the context text
          let contextText = structuredText.substring(contextStart, contextEnd);
          
          // Add ellipsis if we're not at the beginning or end
          if (contextStart > 0) contextText = '...' + contextText;
          if (contextEnd < structuredText.length) contextText = contextText + '...';
          
          // Add the result
          results.push({
            page: i,
            text: contextText,
            index: searchIndex,
            query: searchQuery
          });
          
          // Find the next occurrence
          searchIndex = lowerText.indexOf(lowerQuery, searchIndex + 1);
        }
      }
      
      setSearchResults(results);
      
      if (results.length > 0) {
        setCurrentSearchIndex(0);
        setPageNumber(results[0].page);
        scrollToPage(results[0].page);
        
        // Highlight the text on the page
        setTimeout(() => {
          highlightTextOnPage(results[0].page, searchQuery);
        }, 500);
      } else {
        setCurrentSearchIndex(-1);
      }
    } catch (error) {
      console.error('Error during search:', error);
    }
  }, [searchQuery, pdfDocument, scrollToPage]);
  
  const navigateSearch = useCallback((direction) => {
    if (searchResults.length === 0) return;
    
    const newIndex = direction === 'next'
      ? (currentSearchIndex + 1) % searchResults.length
      : (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
    
    setCurrentSearchIndex(newIndex);
    
    // Go to the page with the search result
    const page = searchResults[newIndex].page;
    setPageNumber(page);
    scrollToPage(page);
    
    // Highlight the text on the page
    setTimeout(() => {
      highlightTextOnPage(page, searchQuery);
    }, 500);
  }, [searchResults, currentSearchIndex, scrollToPage, searchQuery]);
  
  // Function to highlight text on a page
  const highlightTextOnPage = useCallback((page, text) => {
    if (!text || !pagesRef.current[page]) return;
    
    // Find the text layer in the page
    const textLayer = pagesRef.current[page].querySelector('.react-pdf__Page__textContent');
    if (!textLayer) return;
    
    // Clear previous highlights
    const existingHighlights = document.querySelectorAll('.pdf-text-highlight');
    existingHighlights.forEach(el => el.classList.remove('pdf-text-highlight'));
    
    // Find and highlight text nodes containing the search text
    const textNodes = Array.from(textLayer.querySelectorAll('span'));
    const lowerText = text.toLowerCase();
    
    textNodes.forEach(node => {
      const nodeText = node.textContent.toLowerCase();
      if (nodeText.includes(lowerText)) {
        node.classList.add('pdf-text-highlight');
      }
    });
  }, []);
  
  // Render pages with virtualization
  const renderPages = useCallback(() => {
    if (!numPages) return null;
    
    const pages = [];
    
    for (let i = 1; i <= numPages; i++) {
      pages.push(
        <div 
          key={`page-container-${i}`}
          ref={el => pagesRef.current[i] = el}
          style={{
            margin: `${PAGE_PADDING}px 0`,
            display: 'flex',
            justifyContent: 'center'
          }}
          className={i === pageNumber ? 'current-page-container' : ''}
        >
          <Page
            key={`page-${i}-${rotation}`}
            pageNumber={i}
            rotate={rotation}
            renderTextLayer={true}
            renderAnnotationLayer={true}
            className={`pdf-page ${i === pageNumber ? 'current-page' : ''}`}
            loading={
              <div style={{ width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <Spinner size={30} />
              </div>
            }
            error={
              <div style={{ padding: '20px', color: 'red', textAlign: 'center' }}>
                Error loading page {i}
              </div>
            }
            onRenderSuccess={() => {
              // If this is the current page and we have search results, highlight the text
              if (i === pageNumber && searchResults.length > 0 && currentSearchIndex >= 0) {
                highlightTextOnPage(i, searchQuery);
              }
            }}
          />
        </div>
      );
    }
    
    return pages;
  }, [numPages, rotation, pageNumber, searchResults, currentSearchIndex, searchQuery, highlightTextOnPage]);
  
  return (
    <div ref={ref} className={isDarkTheme ? 'bp5-dark' : ''} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      {/* Toolbar */}
      <Card style={{ padding: '5px', marginBottom: '5px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Navigation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <ButtonGroup>
              <Button icon="chevron-left" onClick={goToPrevPage} disabled={pageNumber <= 1} />
              <Button 
                text={`Page ${pageNumber} of ${numPages || '?'}`} 
                onClick={() => setIsGoToPageOpen(true)} 
              />
              <Button icon="chevron-right" onClick={goToNextPage} disabled={pageNumber >= numPages} />
            </ButtonGroup>
          </div>
          
          {/* Zoom and rotation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <ButtonGroup>
              <Button icon="rotate-left" onClick={() => setRotation(prev => (prev - 90 + 360) % 360)} />
              <Button icon="rotate-right" onClick={() => setRotation(prev => (prev + 90) % 360)} />
            </ButtonGroup>
            
            <ButtonGroup>
              <Button icon="zoom-out" onClick={zoomOut} />
              <Button icon="refresh" onClick={resetZoom} />
              <Button icon="zoom-in" onClick={zoomIn} />
            </ButtonGroup>
          </div>
          
          {/* Search controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <InputGroup
              placeholder="Search..."
              value={searchQuery}
              onChange={handleSearchChange}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              rightElement={
                <Button icon="search" minimal={true} onClick={handleSearch} />
              }
              style={{ width: '200px' }}
            />
          </div>
        </div>
      </Card>
      
      {/* PDF Viewer */}
      <div ref={containerRef} style={{ flex: 1, overflow: 'auto', position: 'relative' }}>
        {error ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
            <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>
            <Button intent="primary" onClick={() => window.location.reload()}>Retry</Button>
          </div>
        ) : loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Spinner size={50} />
          </div>
        ) : (
          <TransformWrapper
            ref={transformComponentRef}
            initialScale={1}
            minScale={0.5}
            maxScale={3}
            centerOnInit={true}
            wheel={{ step: 0.1 }}
            doubleClick={{ disabled: false }}
            panning={{ disabled: false }}
          >
            {({ zoomIn, zoomOut, resetTransform }) => (
              <TransformComponent
                wrapperStyle={{ width: '100%', height: '100%' }}
                contentStyle={{ padding: '20px' }}
              >
                <Document
                  file={pdfUrl}
                  onLoadSuccess={onDocumentLoadSuccess}
                  onLoadError={onDocumentLoadError}
                  loading={
                    <div style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                      <Spinner size={50} />
                    </div>
                  }
                >
                  {renderPages()}
                </Document>
              </TransformComponent>
            )}
          </TransformWrapper>
        )}
        
        {/* Search Window */}
        <SearchWindow
          isOpen={isSearchOpen}
          onClose={() => setIsSearchOpen(false)}
          searchQuery={searchQuery}
          onSearchChange={handleSearchChange}
          onSearch={handleSearch}
          searchResults={searchResults}
          currentSearchIndex={currentSearchIndex}
          onNavigateSearch={navigateSearch}
          isDarkTheme={isDarkTheme}
        />
        
        {/* Go To Page Dialog */}
        <GoToPageDialog
          isOpen={isGoToPageOpen}
          onClose={() => setIsGoToPageOpen(false)}
          onGoTo={goToPage}
          currentPage={pageNumber}
          totalPages={numPages}
        />
      </div>
    </div>
  );
};

export default AdvancedPDFViewer;
