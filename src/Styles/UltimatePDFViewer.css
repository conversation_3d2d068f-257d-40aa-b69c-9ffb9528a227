/* Ultimate PDF Viewer Styles */

.ultimate-pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  overflow: hidden;
}

.ultimate-pdf-viewer.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: var(--pdf-viewer-bg);
}

/* Theme Variables */
.ultimate-pdf-viewer.dark {
  --pdf-viewer-bg: #1C2127;
  --pdf-viewer-surface: #252A31;
  --pdf-viewer-surface-hover: #2F343C;
  --pdf-viewer-border: #383E47;
  --pdf-viewer-text: #F5F8FA;
  --pdf-viewer-text-muted: #A7B6C2;
  --pdf-viewer-accent: #2D72D2;
  --pdf-viewer-accent-hover: #215DB0;
  --pdf-viewer-success: #238551;
  --pdf-viewer-warning: #D9822B;
  --pdf-viewer-danger: #DB3737;
  --pdf-viewer-shadow: rgba(0, 0, 0, 0.3);
}

.ultimate-pdf-viewer.light {
  --pdf-viewer-bg: #FFFFFF;
  --pdf-viewer-surface: #F5F8FA;
  --pdf-viewer-surface-hover: #EBF1F5;
  --pdf-viewer-border: #D8E1E8;
  --pdf-viewer-text: #182026;
  --pdf-viewer-text-muted: #5C7080;
  --pdf-viewer-accent: #2D72D2;
  --pdf-viewer-accent-hover: #215DB0;
  --pdf-viewer-success: #0F9960;
  --pdf-viewer-warning: #D9822B;
  --pdf-viewer-danger: #DB3737;
  --pdf-viewer-shadow: rgba(0, 0, 0, 0.1);
}

/* Toolbar Styles */
.pdf-toolbar {
  background: var(--pdf-viewer-surface);
  border-bottom: 1px solid var(--pdf-viewer-border);
  display: flex;
  align-items: center;
  padding: 0 16px;
  flex-shrink: 0;
  z-index: 100;
}

/* Content Area */
.pdf-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  background: var(--pdf-viewer-bg);
}

/* Main PDF Viewer */
.main-pdf-viewer {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: var(--pdf-viewer-bg);
}

/* Panel Containers */
.annotation-explorer-container,
.thumbnail-panel-container {
  background: var(--pdf-viewer-surface);
  border-right: 1px solid var(--pdf-viewer-border);
  overflow: hidden;
  position: relative;
}

.thumbnail-panel-container {
  border-right: none;
  border-left: 1px solid var(--pdf-viewer-border);
}

/* Resizable Handle Styles */
.react-resizable-handle {
  background: var(--pdf-viewer-border);
  transition: background-color 0.2s ease;
}

.react-resizable-handle:hover {
  background: var(--pdf-viewer-accent);
}

.react-resizable-handle-e {
  width: 4px;
  right: -2px;
  cursor: col-resize;
}

.react-resizable-handle-w {
  width: 4px;
  left: -2px;
  cursor: col-resize;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: var(--pdf-viewer-bg);
  color: var(--pdf-viewer-text);
}

.loading-container p {
  margin-top: 16px;
  color: var(--pdf-viewer-text-muted);
}

/* Scrollbar Styles */
.ultimate-pdf-viewer ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.ultimate-pdf-viewer ::-webkit-scrollbar-track {
  background: var(--pdf-viewer-surface);
}

.ultimate-pdf-viewer ::-webkit-scrollbar-thumb {
  background: var(--pdf-viewer-border);
  border-radius: 4px;
}

.ultimate-pdf-viewer ::-webkit-scrollbar-thumb:hover {
  background: var(--pdf-viewer-text-muted);
}

/* Focus Styles */
.ultimate-pdf-viewer:focus {
  outline: none;
}

.ultimate-pdf-viewer:focus-visible {
  box-shadow: inset 0 0 0 2px var(--pdf-viewer-accent);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Utility Classes */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.p-2 {
  padding: 8px;
}

.p-4 {
  padding: 16px;
}

.m-2 {
  margin: 8px;
}

.m-4 {
  margin: 16px;
}

.text-center {
  text-align: center;
}

.text-muted {
  color: var(--pdf-viewer-text-muted);
}

.bg-surface {
  background: var(--pdf-viewer-surface);
}

.border {
  border: 1px solid var(--pdf-viewer-border);
}

.border-radius {
  border-radius: 4px;
}

.shadow {
  box-shadow: 0 2px 8px var(--pdf-viewer-shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
  .ultimate-pdf-viewer {
    font-size: 13px;
  }
  
  .pdf-toolbar {
    padding: 0 8px;
  }
  
  .annotation-explorer-container,
  .thumbnail-panel-container {
    min-width: 150px;
  }
}

@media (max-width: 480px) {
  .ultimate-pdf-viewer {
    font-size: 12px;
  }
  
  .pdf-content {
    flex-direction: column;
  }
  
  .annotation-explorer-container,
  .thumbnail-panel-container {
    height: 200px;
    width: 100% !important;
    border-right: none;
    border-bottom: 1px solid var(--pdf-viewer-border);
  }
  
  .main-pdf-viewer {
    width: 100% !important;
  }
}

/* PDF Rendering Engine Styles */
.pdf-rendering-engine {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.pdf-rendering-engine.loading,
.pdf-rendering-engine.error {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--pdf-viewer-bg);
  color: var(--pdf-viewer-text);
}

.loading-spinner,
.error-message {
  text-align: center;
}

.error-message h3 {
  margin-bottom: 8px;
  color: var(--pdf-viewer-danger);
}

.error-message button {
  margin-top: 16px;
}

/* PDF Toolbar Styles */
.pdf-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: var(--pdf-viewer-surface);
  border-bottom: 1px solid var(--pdf-viewer-border);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-name {
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-info {
  margin: 0 8px;
  color: var(--pdf-viewer-text-muted);
  font-size: 13px;
}

.size-slider {
  width: 100px;
  margin: 0 8px;
}

/* Annotation Explorer Styles */
.annotation-explorer {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--pdf-viewer-surface);
}

.explorer-header {
  padding: 12px;
  border-bottom: 1px solid var(--pdf-viewer-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-container {
  padding: 8px 12px;
  border-bottom: 1px solid var(--pdf-viewer-border);
}

.annotations-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.annotation-group {
  margin-bottom: 8px;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.group-header:hover {
  background: var(--pdf-viewer-surface-hover);
}

.group-name {
  flex: 1;
  font-weight: 500;
}

.group-count {
  font-size: 11px;
}

.group-content {
  padding-left: 24px;
}

.annotation-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.annotation-item:hover {
  background: var(--pdf-viewer-surface-hover);
}

.annotation-item.selected {
  background: var(--pdf-viewer-accent);
  color: white;
  border-color: var(--pdf-viewer-accent);
}

.annotation-item.hidden {
  opacity: 0.5;
}

.annotation-content {
  flex: 1;
  min-width: 0;
}

.annotation-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.annotation-icon {
  display: flex;
  align-items: center;
}

.annotation-type-label {
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.annotation-color-tag {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  border: 1px solid rgba(0,0,0,0.2);
}

.annotation-text {
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.annotation-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: var(--pdf-viewer-text-muted);
}

.annotation-actions {
  display: flex;
  align-items: center;
}

.filter-menu {
  padding: 12px;
  min-width: 200px;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 500;
}

.filter-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 13px;
  cursor: pointer;
}

.color-filters {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.color-filter {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.color-filter.active {
  border-color: var(--pdf-viewer-text);
  transform: scale(1.1);
}

/* Thumbnail Panel Styles */
.thumbnail-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--pdf-viewer-surface);
}

.thumbnail-header {
  padding: 12px;
  border-bottom: 1px solid var(--pdf-viewer-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.page-count {
  color: var(--pdf-viewer-text-muted);
  font-size: 12px;
  margin-left: 4px;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.size-control {
  padding: 8px 12px;
  border-bottom: 1px solid var(--pdf-viewer-border);
  display: flex;
  align-items: center;
  gap: 8px;
}

.thumbnails-container {
  flex: 1;
  position: relative;
}

.thumbnails-list {
  height: 100% !important;
  width: 100%;
}

.thumbnails-list.grid .thumbnail-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
}

.thumbnails-list.list .thumbnail-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  gap: 8px;
}

.thumbnail-item {
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.thumbnail-item:hover {
  background: var(--pdf-viewer-surface-hover);
}

.thumbnail-item.current {
  border-color: var(--pdf-viewer-accent);
  background: var(--pdf-viewer-accent)20;
}

.thumbnail-image-container {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px var(--pdf-viewer-shadow);
}

.thumbnail-image {
  display: block;
  border-radius: 4px;
}

.thumbnail-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 140px;
  background: var(--pdf-viewer-bg);
  border: 1px solid var(--pdf-viewer-border);
  border-radius: 4px;
}

.thumbnail-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 140px;
  background: var(--pdf-viewer-bg);
  border: 1px solid var(--pdf-viewer-border);
  border-radius: 4px;
  font-size: 12px;
  color: var(--pdf-viewer-text-muted);
}

.annotation-indicators {
  position: absolute;
  top: 4px;
  right: 4px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.annotation-count {
  background: var(--pdf-viewer-accent);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.annotation-dots {
  display: flex;
  gap: 2px;
}

.annotation-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  border: 1px solid rgba(255,255,255,0.8);
}

.annotation-dot.more {
  background: var(--pdf-viewer-text);
  color: white;
  font-size: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12px;
  height: 12px;
}

.thumbnail-label {
  margin-top: 4px;
  text-align: center;
  font-size: 12px;
}

.page-number {
  font-weight: 500;
}

.annotation-summary {
  color: var(--pdf-viewer-text-muted);
  font-size: 11px;
  margin-top: 2px;
}

.loading-more {
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 12px;
  color: var(--pdf-viewer-text-muted);
  border-top: 1px solid var(--pdf-viewer-border);
}

/* High DPI Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .ultimate-pdf-viewer {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Print Styles */
@media print {
  .ultimate-pdf-viewer {
    background: white !important;
    color: black !important;
  }

  .pdf-toolbar,
  .annotation-explorer-container,
  .thumbnail-panel-container {
    display: none !important;
  }

  .main-pdf-viewer {
    width: 100% !important;
    height: auto !important;
  }
}

/* Accessibility */
.ultimate-pdf-viewer[aria-hidden="true"] {
  display: none;
}

.ultimate-pdf-viewer .sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Selection Styles */
.ultimate-pdf-viewer ::selection {
  background: var(--pdf-viewer-accent);
  color: white;
}

.ultimate-pdf-viewer ::-moz-selection {
  background: var(--pdf-viewer-accent);
  color: white;
}

/* Search Panel Styles */
.search-panel {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 300px;
  max-height: 500px;
  z-index: 1000;
  background: var(--pdf-viewer-surface);
  border: 1px solid var(--pdf-viewer-border);
  border-radius: 6px;
  box-shadow: 0 4px 12px var(--pdf-viewer-shadow);
}

.search-header {
  padding: 12px;
  border-bottom: 1px solid var(--pdf-viewer-border);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.search-form {
  padding: 12px;
}

.search-input-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.search-options {
  padding: 0 12px 12px;
  border-bottom: 1px solid var(--pdf-viewer-border);
}

.advanced-options {
  padding: 8px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-summary {
  padding: 8px 12px;
  border-bottom: 1px solid var(--pdf-viewer-border);
  font-size: 13px;
}

.searching-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--pdf-viewer-text-muted);
}

.results-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navigation-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.current-result {
  font-size: 12px;
  color: var(--pdf-viewer-text-muted);
}

.no-results {
  color: var(--pdf-viewer-text-muted);
}

.search-results {
  max-height: 300px;
  overflow-y: auto;
}

.results-list {
  padding: 8px;
}

.search-result-item {
  padding: 8px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.search-result-item:hover {
  background: var(--pdf-viewer-surface-hover);
}

.search-result-item.active {
  background: var(--pdf-viewer-accent);
  color: white;
  border-color: var(--pdf-viewer-accent);
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
  font-weight: 500;
}

.result-page {
  color: var(--pdf-viewer-accent);
}

.search-result-item.active .result-page {
  color: white;
}

.result-position {
  color: var(--pdf-viewer-text-muted);
}

.search-result-item.active .result-position {
  color: rgba(255,255,255,0.8);
}

.result-context {
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.search-stats {
  padding: 8px 12px;
  border-top: 1px solid var(--pdf-viewer-border);
  font-size: 12px;
}

.stats-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stats-label {
  color: var(--pdf-viewer-text-muted);
}

.stats-value {
  font-weight: 500;
}

/* Selection Tool Styles */
.selection-tool-overlay {
  pointer-events: none;
}

.selection-rectangle {
  border-style: dashed;
  animation: marching-ants 1s linear infinite;
}

@keyframes marching-ants {
  0% { border-offset: 0; }
  100% { border-offset: 8px; }
}

.selection-tool-controls {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
}

.tool-panel {
  background: var(--pdf-viewer-surface);
  border: 1px solid var(--pdf-viewer-border);
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 4px 12px var(--pdf-viewer-shadow);
  display: flex;
  align-items: center;
  gap: 16px;
}

.color-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.color-options {
  display: flex;
  gap: 4px;
}

.color-option {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.color-option.active {
  border-color: var(--pdf-viewer-text);
  transform: scale(1.1);
}

.comment-input {
  min-width: 200px;
}

.comment-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.comment-dialog-content {
  background: var(--pdf-viewer-surface);
  border-radius: 6px;
  padding: 20px;
  min-width: 300px;
  box-shadow: 0 8px 24px var(--pdf-viewer-shadow);
}

.comment-dialog-content h4 {
  margin: 0 0 16px 0;
}

.comment-dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.tool-instructions {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
}

.instruction {
  background: var(--pdf-viewer-surface);
  border: 1px solid var(--pdf-viewer-border);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 13px;
  box-shadow: 0 2px 8px var(--pdf-viewer-shadow);
}

/* Properties Panel Styles */
.properties-panel-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 350px;
  z-index: 1500;
  pointer-events: none;
}

.properties-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--pdf-viewer-surface);
  border-left: 1px solid var(--pdf-viewer-border);
  box-shadow: -4px 0 12px var(--pdf-viewer-shadow);
  pointer-events: auto;
}

.panel-header {
  padding: 12px;
  border-bottom: 1px solid var(--pdf-viewer-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-nav {
  padding: 8px;
  border-bottom: 1px solid var(--pdf-viewer-border);
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.properties-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
}

.properties-section h5 {
  margin: 12px 0 8px 0;
  font-size: 13px;
  font-weight: 500;
  color: var(--pdf-viewer-text-muted);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--pdf-viewer-text-muted);
}

.type-stats,
.author-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.type-stat,
.author-stat {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px;
  background: var(--pdf-viewer-bg);
  border-radius: 4px;
  font-size: 13px;
}

.date-range {
  font-size: 13px;
  color: var(--pdf-viewer-text-muted);
}

.date-range div {
  margin-bottom: 4px;
}
