/* VSCode-inspired Login Page Styles */
.login-container {
  min-height: 100vh;
  background: #1C2127;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.login-card {
  width: 440px;
  background: linear-gradient(145deg, #2F343C 0%, #252A31 100%);
  border: 1px solid #383E47;
  border-radius: 0;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(45, 114, 210, 0.08);
  position: relative;
  z-index: 1;
  animation: fadeInUp 0.6s ease-out;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #2D72D2, transparent);
  opacity: 0.5;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  padding: 45px 40px 25px;
  text-align: center;
  position: relative;
  background: linear-gradient(180deg, rgba(45, 114, 210, 0.02) 0%, transparent 100%);
}

.login-logo {
  width: 80px;
  height: auto;
  margin-bottom: 20px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease, filter 0.3s ease;
  cursor: pointer;
  animation: logoFloat 4s ease-in-out infinite;
}

.login-logo:hover {
  transform: scale(1.15) rotate(5deg);
  filter: drop-shadow(0 6px 12px rgba(45, 114, 210, 0.4));
  animation-play-state: paused;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-3px) rotate(1deg);
  }
  50% {
    transform: translateY(-6px) rotate(0deg);
  }
  75% {
    transform: translateY(-3px) rotate(-1deg);
  }
}

.login-title {
  color: #F5F8FA !important;
  font-size: 28px !important;
  font-weight: 700 !important;
  margin: 0 0 8px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}

.login-subtitle {
  color: #A7B6C2 !important;
  font-size: 17px !important;
  margin: 0 0 8px 0 !important;
  opacity: 0.85;
  font-weight: 400;
  letter-spacing: 0.2px;
}

.login-form-container {
  padding: 25px 40px 45px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.login-form-item {
  margin-bottom: 24px !important;
}

.login-form-item:last-child {
  margin-bottom: 0 !important;
}

/* Plant Selector Styling */
.plant-selector-container {
  position: relative;
}

.plant-selector-container .ant-select {
  width: 100% !important;
  height: 44px !important;
}

.plant-selector-container .ant-select .ant-select-selector {
  background: linear-gradient(135deg, #1C2127 0%, #252A31 100%) !important;
  border: 1px solid #383E47 !important;
  border-radius: 0 !important;
  height: 44px !important;
  padding: 0 16px !important;
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

.plant-selector-container .ant-select .ant-select-selector::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(45, 114, 210, 0.1), transparent);
  transition: left 0.6s ease;
}

.plant-selector-container .ant-select .ant-select-selector:hover {
  border-color: #2D72D2 !important;
  background: linear-gradient(135deg, #252A31 0%, #2F343C 100%) !important;
  box-shadow: 0 2px 8px rgba(45, 114, 210, 0.2) !important;
  transform: translateY(-1px);
}

.plant-selector-container .ant-select .ant-select-selector:hover::before {
  left: 100%;
}

.plant-selector-container .ant-select-focused .ant-select-selector {
  border-color: #2D72D2 !important;
  box-shadow: 0 0 0 2px rgba(45, 114, 210, 0.3) !important;
  background: linear-gradient(135deg, #1C2127 0%, #252A31 100%) !important;
  transform: translateY(-1px);
}

.plant-selector-container .ant-select-selection-placeholder {
  color: #A7B6C2 !important;
  font-size: 14px !important;
  line-height: 42px !important;
}

.plant-selector-container .ant-select-selection-item {
  color: #F5F8FA !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 42px !important;
}

/* Google Sign-In Button Container - Clean */
.google-signin-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Alert Styling */
.login-alert {
  background: rgba(251, 179, 96, 0.1) !important;
  border: 1px solid rgba(251, 179, 96, 0.3) !important;
  border-radius: 0 !important;
  margin-bottom: 20px !important;
}

.login-alert .ant-alert-message {
  color: #FBB360 !important;
  font-weight: 500 !important;
}

/* Loading States */
.login-loading {
  opacity: 0.7;
  pointer-events: none;
  position: relative;
}

.login-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid transparent;
  border-top: 2px solid #2D72D2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-card {
    width: 100%;
    max-width: 380px;
    margin: 0 20px;
  }
  
  .login-header {
    padding: 30px 30px 15px;
  }
  
  .login-form-container {
    padding: 15px 30px 30px;
  }
  
  .login-title {
    font-size: 24px !important;
  }
  
  .login-subtitle {
    font-size: 14px !important;
  }
}



/* Footer Styling */
.login-footer {
  border-top: 1px solid rgba(56, 62, 71, 0.6);
  padding-top: 24px;
  margin-top: 24px;
  text-align: center;
  background: linear-gradient(180deg, transparent 0%, rgba(28, 33, 39, 0.3) 100%);
}

.login-footer .support-link {
  color: #2D72D2 !important;
  text-decoration: none !important;
  font-weight: 500;
  transition: color 0.2s ease;
}

.login-footer .support-link:hover {
  color: #4C90F0 !important;
  text-decoration: underline !important;
}

.login-footer .version-text {
  color: #A7B6C2 !important;
  font-size: 11px !important;
  opacity: 0.6;
  font-weight: 400;
}

/* Focus states for accessibility */
.plant-selector-container .ant-select:focus-within {
  outline: 1px solid #2D72D2;
  outline-offset: 1px;
}

.google-signin-container:focus-within {
  outline: 1px solid #2D72D2;
  outline-offset: 1px;
}
