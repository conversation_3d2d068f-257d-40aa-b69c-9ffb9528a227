import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { Spinner, SpinnerSize } from "@blueprintjs/core";
import "@blueprintjs/core/lib/css/blueprint.css";

function PDFJsViewer({ fileId, isDarkTheme }) {
    const [pdfFile, setPdfFile] = useState(null);
    const [downloadProgress, setDownloadProgress] = useState(0);

    useEffect(() => {
        async function fetchFile() {
            setPdfFile(null);
            setDownloadProgress(0);

            try {
                const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
                    headers: {
                        'Accept-Encoding': 'gzip, deflate, br',
                        'session-id': localStorage.getItem('session_id'),
                    },
                });
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }

                const contentLength = response.headers.get('Content-Length');
                const reader = response.body.getReader();
                let receivedLength = 0;
                const chunks = [];

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    chunks.push(value);
                    receivedLength += value.length;

                    if (contentLength) {
                        setDownloadProgress(receivedLength / contentLength);
                    }
                }

                const blob = new Blob(chunks);
                const pdfUrl = URL.createObjectURL(blob);
                setPdfFile(pdfUrl);
            } catch (error) {
                console.error("Failed to fetch the PDF:", error);
            }
        }

        fetchFile();

        return () => {
            if (pdfFile) {
                URL.revokeObjectURL(pdfFile); // Memory management
            }
        };
    }, [fileId]);

    const simulateClickOnHandTool = (iframeDocument) => {
        const script = iframeDocument.createElement('script');
        script.textContent = `
            function simulateClick() {
                const handToolButton = document.getElementById('cursorHandTool');
                if (handToolButton) {
                    handToolButton.click();
                }
            }
            document.addEventListener('DOMContentLoaded', simulateClick);
            if (document.readyState === 'complete') {
                simulateClick();
            }
        `;
        iframeDocument.head.appendChild(script);
    };

    if (!pdfFile) {
        return (
            <div className="spinner-container">
                <Spinner
                    className="spinner-center"
                    intent="primary"
                    size={SpinnerSize.STANDARD}
                    value={downloadProgress}
                />
            </div>
        );
    }

    const setThemeInIframe = (iframeDocument, theme) => {
        const setThemeFunctionString = `
            function setTheme(theme) {
                if (theme == 'Dark') {
                    PDFViewerApplicationOptions.set('viewerCssTheme', 1);
                }
                else {
                    PDFViewerApplicationOptions.set('viewerCssTheme', 2);
                }
                PDFViewerApplication._forceCssTheme();
            }
        `;

        const script = iframeDocument.createElement('script');
        script.textContent = setThemeFunctionString;
        iframeDocument.head.appendChild(script);

        iframeDocument.defaultView.setTheme(isDarkTheme ? 'Dark' : 'Light');
    };

    return (
      <div style={{height:"calc(100vh)"}}>
        <iframe
            title="PDF Viewer"
            width="100%"
            height="100%"
            src={`${process.env.PUBLIC_URL}/pdfjs-3.11.174-dist/web/viewer.html?file=${encodeURIComponent(pdfFile)}`}
            className={isDarkTheme ? "dark-theme" : "light-theme"}
            onLoad={(event) => {
                const iframeDocument = event.target.contentDocument || event.target.contentWindow.document;
                const customStyle = iframeDocument.createElement('style');
                customStyle.innerHTML = `
                    body { 
                        font-family: 'NeueHaasDisplayRoman', sans-serif !important; 
                        font-size: 0.7em !important; 
                    }
                `;
                iframeDocument.head.appendChild(customStyle);
                setThemeInIframe(iframeDocument, isDarkTheme ? 'Dark' : 'Light');
                simulateClickOnHandTool(iframeDocument);
            }}
        />
        </div>
    );
}

export default PDFJsViewer;