/**
 * This script fixes the PDF.js version mismatch error by patching the version at runtime
 * It specifically targets the error: "The API version X does not match the Viewer version Y"
 */

// Function to patch PDF.js versions to match
export function patchPdfJsVersions() {
  console.log('Attempting to patch PDF.js versions...');
  
  // This runs after a slight delay to ensure PDF.js has loaded
  setTimeout(() => {
    try {
      // Find the react-pdf-highlighter's PDF.js instance
      const pdfJsModules = Object.entries(window).filter(([key]) => 
        key.startsWith('__reactPdfHighlighterPdfJs')
      );
      
      if (pdfJsModules.length > 0) {
        console.log('Found react-pdf-highlighter PDF.js modules:', pdfJsModules.length);
        
        // For each module, try to patch the version
        pdfJsModules.forEach(([key, module]) => {
          if (module && typeof module === 'object') {
            // Force the API version to match the viewer version
            if (module.version) {
              const originalVersion = module.version;
              module.version = '4.4.168'; // Set to the viewer version
              console.log(`Patched PDF.js module ${key} version from ${originalVersion} to 4.4.168`);
            }
          }
        });
      } else {
        console.log('No react-pdf-highlighter PDF.js modules found to patch');
      }
      
      // Also try to patch the global pdfjsLib if it exists
      if (window.pdfjsLib) {
        const originalVersion = window.pdfjsLib.version;
        window.pdfjsLib.version = '4.4.168';
        console.log(`Patched global pdfjsLib version from ${originalVersion} to 4.4.168`);
      }
      
      // Try to patch any PDF.js instances in the react-pdf-highlighter's iframe
      const iframes = document.querySelectorAll('iframe');
      iframes.forEach(iframe => {
        try {
          if (iframe.contentWindow && iframe.contentWindow.pdfjsLib) {
            const originalVersion = iframe.contentWindow.pdfjsLib.version;
            iframe.contentWindow.pdfjsLib.version = '4.4.168';
            console.log(`Patched iframe pdfjsLib version from ${originalVersion} to 4.4.168`);
          }
        } catch (e) {
          // Ignore cross-origin errors
          console.log('Could not access iframe content:', e.message);
        }
      });
      
    } catch (error) {
      console.error('Error while patching PDF.js versions:', error);
    }
  }, 1000);
}

// Export a function to monkey patch the PDFViewer constructor
export function monkeyPatchPdfViewer() {
  console.log('Setting up PDF.js monkey patch...');
  
  // Set up a MutationObserver to detect when new script elements are added
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes) {
        mutation.addedNodes.forEach((node) => {
          if (node.tagName === 'SCRIPT' && node.src && node.src.includes('pdf_viewer.js')) {
            console.log('Detected PDF.js viewer script loading:', node.src);
            
            // After the script loads, attempt to patch the PDFViewer constructor
            node.addEventListener('load', () => {
              setTimeout(() => {
                try {
                  // Try to find the PDFViewer constructor in the global scope
                  if (window.pdfjsViewer && window.pdfjsViewer.PDFViewer) {
                    const originalPDFViewer = window.pdfjsViewer.PDFViewer;
                    
                    // Replace with our patched version
                    window.pdfjsViewer.PDFViewer = function(...args) {
                      console.log('Intercepted PDFViewer constructor call');
                      
                      // Skip version check by temporarily removing the version property
                      const originalVersion = window.pdfjsLib.version;
                      window.pdfjsLib.version = '4.4.168';
                      
                      // Call the original constructor
                      const result = new originalPDFViewer(...args);
                      
                      // Restore the version
                      window.pdfjsLib.version = originalVersion;
                      
                      return result;
                    };
                    
                    // Copy prototype and properties
                    window.pdfjsViewer.PDFViewer.prototype = originalPDFViewer.prototype;
                    Object.setPrototypeOf(window.pdfjsViewer.PDFViewer, originalPDFViewer);
                    
                    console.log('Successfully monkey patched PDFViewer constructor');
                  }
                } catch (error) {
                  console.error('Error while monkey patching PDFViewer:', error);
                }
              }, 500);
            });
          }
        });
      }
    });
  });
  
  // Start observing
  observer.observe(document.documentElement, {
    childList: true,
    subtree: true
  });
  
  return () => observer.disconnect(); // Return cleanup function
}
