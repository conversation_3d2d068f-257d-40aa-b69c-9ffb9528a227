# PDF Annotation Viewer Implementation Guide

This guide explains how to implement the advanced PDF annotation capabilities using the `AnnotatablePDFViewer` component and the pdfjs-annotation-extension.

## Setup Instructions

### 1. Directory Structure

First, set up your directory structure as follows:

```
public/
  └── pdfjs-dist/
      └── web/
          ├── viewer.html (modified)
          ├── viewer.css
          ├── viewer.mjs
          └── pdfjs-annotation-extension/ (add this directory)
```

### 2. Download the PDF.js Annotation Extension

Download the latest version of pdfjs-annotation-extension from the GitHub repository:
https://github.com/Laomai-codefee/pdfjs-annotation-extension

- Download the release files or clone the repository
- Build the extension (follow the instructions in their README)
- Copy the built files to your `public/pdfjs-dist/web/pdfjs-annotation-extension/` directory

### 3. Modify PDF.js Viewer HTML

Edit the `public/pdfjs-dist/web/viewer.html` file to include the annotation extension script:

Add this line before the closing `</head>` tag:

```html
<script src="./pdfjs-annotation-extension/pdfjs-annotation-extension.js" type="module"></script>
```

### 4. Configure PDF.js Viewer

Modify the PDF.js viewer configuration to support annotation editing. In your PDF.js viewer file (usually `viewer.mjs`), find the `annotationMode` option and change it to 0:

```javascript
annotationMode: {
  value: 0,  // Change to 0 (was 1 by default)
  kind: OptionKind.VIEWER + OptionKind.PREFERENCE
}
```

### 5. Using the AnnotatablePDFViewer Component

Now you can use the `AnnotatablePDFViewer` component in your application:

```jsx
import AnnotatablePDFViewer from './AnnotatablePDFViewer';

function MyApp() {
  return (
    <div style={{ height: '100vh' }}>
      <AnnotatablePDFViewer
        fileId="your-file-id"
        filePath="path/to/your/file.pdf"
        isDarkTheme={false}
        username="JohnDoe"
        getAnnotationsUrl="https://your-api.com/get-annotations?id=doc123"
        saveAnnotationsUrl="https://your-api.com/save-annotations"
        enableEditing={true}
      />
    </div>
  );
}
```

## Component Props

The `AnnotatablePDFViewer` component accepts the following props:

| Prop | Type | Description |
|------|------|-------------|
| `fileId` | string | Unique identifier for the PDF file |
| `filePath` | string | Path to the PDF file |
| `isDarkTheme` | boolean | Whether to use dark theme for the viewer |
| `username` | string | Name of the user making annotations (default: "user") |
| `getAnnotationsUrl` | string | URL to fetch saved annotations (leave empty to disable) |
| `saveAnnotationsUrl` | string | URL to save annotations (leave empty to disable) |
| `enableEditing` | boolean | Whether to enable annotation editing (default: true) |

## Available Annotation Tools

This implementation provides the following annotation tools:

1. Rectangle
2. Circle
3. Free Hand (grouped if drawn within a short time)
4. Free Highlight (with auto-correction)
5. FreeText
6. Signature
7. Stamp (upload custom images)
8. Text Highlight
9. Text Strikeout
10. Text Underline
11. Annotation Selection (double-click to delete)

## Editing Existing PDF Annotations

The component supports editing the following types of existing annotations in PDF files:

1. Square
2. Circle
3. Ink
4. FreeText
5. Line
6. Polygon
7. PolyLine
8. Text
9. Highlight
10. Underline
11. StrikeOut

## Saving and Loading Annotations

The component can save and load annotations using the provided URLs:

- `getAnnotationsUrl`: URL to fetch previously saved annotations
- `saveAnnotationsUrl`: URL to save the current annotations

The annotation data is sent as JSON and can be processed on your server.

## Mobile Support

All annotation tools are fully supported on mobile devices, making this component perfect for both desktop and mobile applications.

## Troubleshooting

If you encounter issues:

1. Check that the PDF.js version matches the annotation extension version
2. Ensure all required files are properly placed in the public directory
3. Verify that the viewer.html file has been modified to include the annotation extension
4. Check that annotationMode is set to 0 in the viewer configuration

## Reference

For more information, refer to the original repository:
https://github.com/Laomai-codefee/pdfjs-annotation-extension
