import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { <PERSON><PERSON>, <PERSON><PERSON>, Intent } from "@blueprintjs/core";
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker with specific version for better compatibility
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

/**
 * RobustPDFViewer - Designed to handle problematic PDF files
 * Uses multiple fallback methods and robust error handling
 */
const RobustPDFViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  const [pdfData, setPdfData] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [loadingStage, setLoadingStage] = useState('initializing');
  const [tryAlternativeMethod, setTryAlternativeMethod] = useState(false);

  // Load PDF file with multiple fallback methods
  useEffect(() => {
    async function loadPdf() {
      setLoading(true);
      setLoadingStage('initializing');
      setError(null);
      
      // Method 1: Try using filePath directly if available
      if (filePath && !tryAlternativeMethod) {
        try {
          setLoadingStage('using direct path');
          setPdfData(filePath);
          setLoading(false);
          return;
        } catch (err) {
          console.warn('Direct path method failed, trying API method:', err);
          // Continue to next method
        }
      }
      
      // Method 2: Fetch file from API
      if (!fileId) {
        setError('No file ID provided');
        setLoading(false);
        return;
      }
      
      try {
        setLoadingStage('fetching from API');
        console.log('Fetching PDF with ID:', fileId);
        
        // Fetch as arrayBuffer for more reliable PDF handling
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'session-id': localStorage.getItem('session_id')
          }
        });
        
        if (!response.ok) {
          throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        
        setLoadingStage('processing file');
        
        if (tryAlternativeMethod) {
          // Alternative Method: Get as blob and create object URL
          const blob = await response.blob();
          const url = URL.createObjectURL(blob);
          setPdfData(url);
        } else {
          // Primary Method: Get as arrayBuffer for direct PDF.js handling
          const arrayBuffer = await response.arrayBuffer();
          setPdfData(arrayBuffer);
        }
        
        setLoading(false);
        console.log('PDF loaded successfully');
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(`Failed to load PDF: ${err.message}`);
        setLoading(false);
      }
    }
    
    loadPdf();
    
    // Cleanup function
    return () => {
      if (pdfData && typeof pdfData === 'string' && pdfData.startsWith('blob:')) {
        URL.revokeObjectURL(pdfData);
      }
    };
  }, [fileId, filePath, tryAlternativeMethod, pdfData]);
  
  // Document load handlers
  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setCurrentPage(1);
    setLoading(false);
    setError(null);
    console.log(`PDF loaded with ${numPages} pages`);
  };
  
  const onDocumentLoadError = (err) => {
    console.error('Error rendering PDF:', err);
    setError(`Error rendering PDF: ${err.message}`);
    setLoading(false);
    
    // If not already trying alternative method, suggest it
    if (!tryAlternativeMethod) {
      console.log('Will suggest alternative loading method');
    }
  };
  
  // Navigation
  const goToPrevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1));
  const goToNextPage = () => setCurrentPage(prev => Math.min(prev + 1, numPages || 1));
  
  // Try alternative loading method
  const handleTryAlternative = () => {
    setTryAlternativeMethod(true);
    setError(null);
    setLoading(true);
  };

  return (
    <div style={{ 
      height: '100%', 
      width: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      background: isDarkTheme ? '#1e1e1e' : '#f5f5f5',
      color: isDarkTheme ? '#fff' : '#000',
      ...style 
    }}>
      {/* Controls */}
      {numPages > 0 && (
        <div style={{ 
          padding: '10px', 
          display: 'flex', 
          justifyContent: 'center',
          borderBottom: `1px solid ${isDarkTheme ? '#444' : '#ddd'}`
        }}>
          <Button
            icon="arrow-left"
            onClick={goToPrevPage}
            disabled={currentPage <= 1 || loading}
            minimal
            small
          />
          <span style={{ margin: '0 10px' }}>
            Page {currentPage} of {numPages}
          </span>
          <Button
            icon="arrow-right"
            onClick={goToNextPage}
            disabled={currentPage >= numPages || loading}
            minimal
            small
          />
        </div>
      )}
      
      {/* Document container */}
      <div style={{ 
        flex: 1, 
        overflow: 'auto', 
        display: 'flex', 
        justifyContent: 'center',
        alignItems: 'flex-start',
        padding: '20px'
      }}>
        {loading ? (
          <div style={{ 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center', 
            justifyContent: 'center',
            height: '100%',
            textAlign: 'center'
          }}>
            <Spinner size={40} />
            <div style={{ marginTop: '20px' }}>
              Loading PDF... ({loadingStage})
            </div>
          </div>
        ) : error ? (
          <div style={{ 
            textAlign: 'center', 
            maxWidth: '500px',
            margin: '0 auto',
            padding: '20px'
          }}>
            <h3 style={{ color: 'red' }}>Error Loading PDF</h3>
            <p>{error}</p>
            
            {!tryAlternativeMethod && (
              <Button
                intent={Intent.PRIMARY}
                text="Try Alternative Loading Method"
                onClick={handleTryAlternative}
                style={{ marginTop: '15px' }}
              />
            )}
            
            <div style={{ marginTop: '20px', fontSize: '0.9em', opacity: 0.8 }}>
              {tryAlternativeMethod ? 
                "Both loading methods have been tried. The PDF might be corrupted or incompatible." :
                "If the problem persists, the PDF file might be corrupted or using features not supported by the viewer."}
            </div>
          </div>
        ) : (
          <Document
            file={pdfData}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <Spinner size={30} />
                <div style={{ marginTop: '10px' }}>Rendering document...</div>
              </div>
            }
            options={{
              cMapUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/cmaps/',
              cMapPacked: true,
              standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@3.4.120/standard_fonts/'
            }}
          >
            <div style={{ 
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)', 
              background: '#fff',
              display: 'inline-block'
            }}>
              <Page
                key={`page_${currentPage}`}
                pageNumber={currentPage}
                renderTextLayer={true}
                renderAnnotationLayer={true}
                className={isDarkTheme ? 'dark-page' : ''}
              />
            </div>
          </Document>
        )}
      </div>
      
      {/* Dark mode styles */}
      {isDarkTheme && (
        <style dangerouslySetInnerHTML={{
          __html: `
            .dark-page canvas {
              filter: invert(85%) hue-rotate(180deg) brightness(95%) contrast(85%);
            }
            .react-pdf__Page__textContent {
              filter: invert(100%) hue-rotate(180deg);
              color: black !important;
            }
          `
        }} />
      )}
    </div>
  );
};

export default RobustPDFViewer;
