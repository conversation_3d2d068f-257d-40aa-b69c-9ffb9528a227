/* PDF Viewer Styles */

/* Improve PDF page rendering during zoom */
.react-pdf__Page {
  transition: transform 0.3s ease-out;
  margin: 0 auto; /* Center pages */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  background-color: white;
  will-change: transform; /* Optimize for animations */
}

/* Current page highlight */
.pdf-page.current-page {
  box-shadow: 0 0 10px rgba(0, 100, 255, 0.5);
}

.current-page-container {
  scroll-margin-top: 20px;
}

/* Highlight styles for search results */
.pdf-text-highlight {
  background-color: rgba(251, 179, 96, 0.4);
  border-radius: 2px;
  padding: 0 1px;
}

.pdf-text-highlight.multi-line {
  display: inline-block;
  padding: 2px;
  background-color: rgba(251, 179, 96, 0.6);
}

/* React PDF Highlighter styles */
.Highlight {
  cursor: pointer;
  background-color: rgba(251, 179, 96, 0.4) !important;
}

.Highlight:hover {
  background-color: rgba(251, 179, 96, 0.6) !important;
}

.Highlight[data-isscrolledto="true"] {
  background-color: rgba(255, 136, 0, 0.7) !important;
  box-shadow: 0 0 0 2px rgba(255, 136, 0, 0.7);
}

/* Smooth transitions for page navigation */
.pdf-page-transition {
  transition: opacity 0.3s ease-in-out;
}

/* Toolbar styling */
.pdf-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

/* Dark mode support */
.bp5-dark .react-pdf__Page {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
}

/* Loading spinner container */
.pdf-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  flex-direction: column;
}

/* Error message styling */
.pdf-error-message {
  color: #d13913;
  text-align: center;
  padding: 20px;
  max-width: 500px;
  margin: 0 auto;
}

/* Search window styling */
.search-window {
  z-index: 1000;
  transition: all 0.3s ease;
}

.search-window-header {
  cursor: move;
  user-select: none;
}

/* Go to page window styling */
.goto-window {
  z-index: 1000;
  transition: all 0.3s ease;
}

.goto-window-header {
  cursor: move;
  user-select: none;
}

/* Virtualized list styling */
.react-window-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

.react-window-list::-webkit-scrollbar {
  width: 8px;
}

.react-window-list::-webkit-scrollbar-track {
  background: transparent;
}

.react-window-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
}

/* Smooth zoom transition */
.pdf-container {
  transition: transform 0.2s ease-out;
}

/* React Transform Wrapper styles */
.react-transform-wrapper {
  width: 100% !important;
  height: 100% !important;
}

.react-transform-component {
  width: 100% !important;
}

/* Improve PDF text layer */
.react-pdf__Page__textContent {
  opacity: 0.5;
  user-select: text;
}

.react-pdf__Page__textContent span {
  color: transparent;
  cursor: text;
}

.react-pdf__Page__textContent span::selection {
  background-color: rgba(0, 100, 255, 0.3);
}

/* Blueprint dialog customization */
.bp5-dialog {
  border-radius: 6px;
  overflow: hidden;
}

.bp5-dark .bp5-dialog {
  background-color: #30404D;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
}
