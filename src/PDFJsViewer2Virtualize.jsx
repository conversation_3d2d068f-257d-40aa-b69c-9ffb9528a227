import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { useVirtualizer, elementScroll } from "@tanstack/react-virtual";
import { Card, Spinner, Button, ButtonGroup, HTMLSelect, Popover, InputGroup, Menu, MenuItem } from "@blueprintjs/core";
import { RotateCcw, RotateCw, MousePointer, Hand, ZoomIn, ZoomOut, Search, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, X } from "lucide-react";
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const ZOOM_LEVELS = [0.1, 0.2, 0.3, 0.4, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3, 3.5, 4];
const PAGE_PADDING = 10;
const EXTRA_HEIGHT = 10;
const DEFAULT_HEIGHT = 600;
const MAGNIFIER_SIZE = 200;
const MAGNIFIER_ZOOM = 2;

const THRESHOLD = [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1];
const INDEX_ATTRIBUTE = "data-index";

function easeInOutQuint(t) {
  return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;
}

const usePageObserver = ({
  parentRef,
  setCurrentPage,
  numPages,
}) => {
  const [visibilities, setVisibilities] = useState(() =>
    Array(numPages).fill(-1)
  );

  useEffect(() => {
    if (!numPages) return;
    setVisibilities(Array(numPages).fill(-1));
  }, [numPages]);

  useEffect(() => {
    const maxVisibilityIndex = visibilities.indexOf(Math.max(...visibilities));
    if (maxVisibilityIndex !== -1) {
      setCurrentPage(maxVisibilityIndex + 1);
    }
  }, [visibilities, setCurrentPage]);

  const pageObserver = useMemo(() => {
    const io = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const ratio = entry.isIntersecting ? entry.intersectionRatio : -1;
          const target = entry.target;
          const indexAttribute = target.getAttribute(INDEX_ATTRIBUTE);
          if (!indexAttribute) {
            return;
          }
          const index = parseInt(indexAttribute, 10);
          if (0 <= index && index < numPages) {
            setVisibilities((old) => {
              const newVisibilities = [...old];
              newVisibilities[index] = ratio;
              return newVisibilities;
            });
          }
        });
      },
      {
        root: parentRef.current,
        threshold: THRESHOLD,
      }
    );
    return io;
  }, [parentRef, numPages]);

  useEffect(() => {
    return () => pageObserver?.disconnect();
  }, [pageObserver]);

  return { pageObserver };
};

const Magnifier = ({ x, y, image }) => {
  const magnifierStyle = {
    position: 'absolute',
    left: x - MAGNIFIER_SIZE / 2,
    top: y - MAGNIFIER_SIZE / 2,
    width: MAGNIFIER_SIZE,
    height: MAGNIFIER_SIZE,
    border: '2px solid #ccc',
    borderRadius: '50%',
    pointerEvents: 'none',
    overflow: 'hidden',
    zIndex: 1000,
  };

  const imageStyle = {
    position: 'absolute',
    left: -x * MAGNIFIER_ZOOM + MAGNIFIER_SIZE / 2,
    top: -y * MAGNIFIER_ZOOM + MAGNIFIER_SIZE / 2,
    transform: `scale(${MAGNIFIER_ZOOM})`,
    transformOrigin: 'top left',
  };

  return (
    <div style={magnifierStyle}>
      <img src={image} alt="Magnified view" style={imageStyle} />
    </div>
  );
};

const PageWithObserver = React.memo(({
  pageNumber,
  scale,
  rotation,
  containerWidth,
  isSelectMode,
  isMagnifierMode,
  pageObserver,
}) => {
  const pageRef = useRef(null);
  const [magnifierPos, setMagnifierPos] = useState({ x: 0, y: 0 });
  const [showMagnifier, setShowMagnifier] = useState(false);
  const [pageImage, setPageImage] = useState(null);

  useEffect(() => {
    if (pageRef.current && pageObserver) {
      pageObserver.observe(pageRef.current);
    }
    return () => {
      if (pageRef.current && pageObserver) {
        pageObserver.unobserve(pageRef.current);
      }
    };
  }, [pageObserver]);

  const handleMouseMove = (e) => {
    if (isMagnifierMode) {
      const rect = e.currentTarget.getBoundingClientRect();
      setMagnifierPos({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
      setShowMagnifier(true);
    }
  };

  const handleMouseLeave = () => {
    setShowMagnifier(false);
  };

  const onPageRenderSuccess = useCallback((page) => {
    const viewport = page.getViewport({ scale: 1 });
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.height = viewport.height;
    canvas.width = viewport.width;
    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };
    page.render(renderContext);
    setPageImage(canvas.toDataURL());
  }, []);

  return (
    <div 
      ref={pageRef}
      data-index={pageNumber - 1}
      style={{ 
        marginBottom: `${PAGE_PADDING}px`, 
        display: 'flex', 
        justifyContent: 'center',
        userSelect: isSelectMode ? 'text' : 'none',
        cursor: isMagnifierMode ? 'none' : 'default',
        position: 'relative',
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
    >
      <Page
        pageNumber={pageNumber}
        scale={scale}
        rotate={rotation}
        width={containerWidth}
        renderAnnotationLayer={true}
        renderTextLayer={true}
        onRenderSuccess={onPageRenderSuccess}
      />
      {isMagnifierMode && showMagnifier && pageImage && (
        <Magnifier x={magnifierPos.x} y={magnifierPos.y} image={pageImage} />
      )}
    </div>
  );
});

const PDFJsViewer = ({ fileId, isDarkTheme }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(null);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [pageHeights, setPageHeights] = useState({});
  const [containerWidth, setContainerWidth] = useState(0);
  const [isSelectMode, setIsSelectMode] = useState(true);
  const [isMagnifierMode, setIsMagnifierMode] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);

  const parentRef = useRef(null);
  const pdfContainerRef = useRef(null);
  const scrollingRef = useRef(null);
  const parentClassName = isDarkTheme ? "bp5-dark" : "";

  const { pageObserver } = usePageObserver({
    parentRef,
    setCurrentPage,
    numPages,
  });

  const scrollToFn = useCallback((offset, canSmooth, instance) => {
    const duration = 1000;
    const start = parentRef.current?.scrollTop || 0;
    const startTime = (scrollingRef.current = Date.now());

    const run = () => {
      if (scrollingRef.current !== startTime) return;
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = easeInOutQuint(Math.min(elapsed / duration, 1));
      const interpolated = start + (offset - start) * progress;

      if (elapsed < duration) {
        elementScroll(interpolated, canSmooth, instance);
        requestAnimationFrame(run);
      } else {
        elementScroll(interpolated, canSmooth, instance);
      }
    };

    requestAnimationFrame(run);
  }, []);

  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();

    const fetchFile = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: {
              "session-id": localStorage.getItem("session_id"),
            },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        if (isMounted) {
          const pdfUrl = URL.createObjectURL(response.data);
          setPdfFile(pdfUrl);
          setLoading(false);
        }
      } catch (error) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(error)) {
            console.log("Request canceled:", error.message);
          } else {
            console.error("Failed to fetch the PDF:", error);
            setError("Failed to load the PDF. Please try again.");
          }
        }
      }
    };

    if (fileId) {
      fetchFile();
    }

    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId]);

  useEffect(() => {
    const updateContainerWidth = () => {
      if (pdfContainerRef.current) {
        const newWidth = pdfContainerRef.current.clientWidth;
        setContainerWidth(newWidth);
      }
    };

    const resizeObserver = new ResizeObserver((entries) => {
      if (!entries || entries.length === 0) return;
      updateContainerWidth();
    });

    if (pdfContainerRef.current) {
      resizeObserver.observe(pdfContainerRef.current);
    }

    updateContainerWidth();

    return () => {
      if (pdfContainerRef.current) {
        resizeObserver.unobserve(pdfContainerRef.current);
      }
      resizeObserver.disconnect();
    };
  }, []);

  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    setNumPages(numPages);
    setCurrentPage(1);
    setLoading(false);
    updatePageHeights(numPages);
  }, []);

  const onDocumentLoadError = useCallback((error) => {
    console.error("Error loading PDF:", error);
    setError("Failed to load the PDF. Please check if the file is corrupted or try again.");
    setLoading(false);
  }, []);

  const updatePageHeights = useCallback(async (pages) => {
    if (!pdfFile) return;

    try {
      const pdf = await pdfjs.getDocument(pdfFile).promise;
      const newPageHeights = {};

      for (let i = 1; i <= pages; i++) {
        const page = await pdf.getPage(i);
        const viewport = page.getViewport({ scale: 1, rotation });
        const aspectRatio = viewport.width / viewport.height;
        newPageHeights[i - 1] = (containerWidth / aspectRatio) * scale + PAGE_PADDING + EXTRA_HEIGHT;
      }

      setPageHeights(newPageHeights);
    } catch (error) {
      console.error("Error updating page heights:", error);
    }
  }, [pdfFile, containerWidth, scale, rotation]);

  useEffect(() => {
    if (numPages > 0 && pdfFile) {
      updatePageHeights(numPages);
    }
  }, [numPages, pdfFile, updatePageHeights]);

  const updateZoom = useCallback((newZoomLevel) => {
    setZoomLevel(newZoomLevel);
    setScale(newZoomLevel);
  }, []);

  const handleZoomChange = useCallback((event) => {
    const newZoomLevel = parseFloat(event.target.value);
    updateZoom(newZoomLevel);
  }, [updateZoom]);

  const zoomIn = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex < ZOOM_LEVELS.length - 1) {
      updateZoom(ZOOM_LEVELS[currentIndex + 1]);
    }
  }, [zoomLevel, updateZoom]);

  const zoomOut = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex > 0) {
      updateZoom(ZOOM_LEVELS[currentIndex - 1]);
    }
  }, [zoomLevel, updateZoom]);

  const rotateLeft = useCallback(() => {
    setRotation((prevRotation) => (prevRotation - 90 + 360) % 360);
  }, []);

  const rotateRight = useCallback(() => {
    setRotation((prevRotation) => (prevRotation + 90) % 360);
  }, []);

  const resetView = useCallback(() => {
    updateZoom(1.0);
    setRotation(0);
    setIsMagnifierMode(false);
  }, [updateZoom]);

  const toggleMagnifierMode = useCallback(() => {
    setIsMagnifierMode((prev) => !prev);
    if (isSelectMode) {
      setIsSelectMode(false);
    }
  }, [isSelectMode]);

  const estimateSize = useCallback((index) => {
    return pageHeights[index] || DEFAULT_HEIGHT;
  }, [pageHeights]);

  const virtualizer = useVirtualizer({
    count: numPages,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 5,
    scrollToFn,
  });

  const memoizedOptions = useMemo(() => ({
    cMapUrl: "https://unpkg.com/pdfjs-dist@2.9.359/cmaps/",
    cMapPacked: true,
  }), []);

  useEffect(() => {
    const handleError = (event) => {
      if (event.message === 'ResizeObserver loop completed with undelivered notifications.') {
        event.stopImmediatePropagation();
      }
    };

    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('error', handleError);
    };
  }, []);

  const handleSearch = useCallback(async () => {
    if (!pdfFile || !searchQuery) return;

    const pdf = await pdfjs.getDocument(pdfFile).promise;
    const results = [];

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const text = textContent.items.map(item => item.str).join(' ');

      if (text.toLowerCase().includes(searchQuery.toLowerCase())) {
        results.push(i);
      }
    }

    setSearchResults(results);
    setCurrentSearchIndex(results.length > 0 ? 0 : -1);
    if (results.length > 0) {
      setCurrentPage(results[0]);
      virtualizer.scrollToIndex(results[0] - 1);
    }
  }, [pdfFile, searchQuery, virtualizer]);

  const navigateSearch = useCallback((direction) => {
    if (searchResults.length === 0) return;

    let newIndex;
    if (direction === 'next') {
      newIndex = (currentSearchIndex + 1) % searchResults.length;
    } else {
      newIndex = (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
    }

    setCurrentSearchIndex(newIndex);
    setCurrentPage(searchResults[newIndex]);
    virtualizer.scrollToIndex(searchResults[newIndex] - 1);
  }, [searchResults, currentSearchIndex, virtualizer]);

  const toggleSearch = useCallback(() => {
    setIsSearchOpen((prev) => !prev);
  }, []);

  const closeSearch = useCallback(() => {
    setIsSearchOpen(false);
  }, []);

  if (loading) {
    return (
      <div style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <Spinner intent="primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
        <p>{error}</p>
        <Button onClick={() => window.location.reload()}>Retry</Button>
      </div>
    );
  }

  return (
    <div className={parentClassName} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      <Card
        style={{
          padding: "4px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          background: isDarkTheme ? "#1C2127" : "white",
        }}
      >
        <Popover
          isOpen={isSearchOpen}
          onInteraction={(state) => setIsSearchOpen(state)}
          content={
            <Menu>
              <MenuItem
                icon={<Search size={16} />}
                text={
                  <InputGroup
                    placeholder="Search..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    rightElement={
                      <Button minimal={true} icon={<Search size={16} />} onClick={handleSearch} />
                    }
                  />
                }
              />
              <MenuItem
                icon={<ChevronUp size={16} />}
                text="Previous"
                onClick={() => navigateSearch('prev')}
                disabled={searchResults.length === 0}
              />
              <MenuItem
                icon={<ChevronDown size={16} />}
                text="Next"
                onClick={() => navigateSearch('next')}
                disabled={searchResults.length === 0}
              />
              <MenuItem
                icon={<X size={16} />}
                text="Close"
                onClick={closeSearch}
              />
            </Menu>
          }
          position="bottom-left"
          minimal={true}
          autoFocus={false}
          enforceFocus={false}
        >
          <Button
            icon={<Search size={16} />}
            onClick={toggleSearch}
            active={isSearchOpen}
            minimal={true}
          />
        </Popover>
        <div style={{ display: "flex", alignItems: "center", gap: "5px" }}>
          <ButtonGroup minimal={true}>
            <Button icon={<RotateCcw size={16} />} onClick={rotateLeft} />
            <Button icon={<RotateCw size={16} />} onClick={rotateRight} />
          </ButtonGroup>
          <ButtonGroup minimal={true}>
            <Button
              icon={<MousePointer size={16} />}
              onClick={() => setIsSelectMode(true)}
              intent={isSelectMode ? "primary" : "none"}
            />
            <Button
              icon={<Hand size={16} />}
              onClick={() => setIsSelectMode(false)}
              intent={!isSelectMode && !isMagnifierMode ? "primary" : "none"}
            />
          </ButtonGroup>
          <ButtonGroup minimal={true}>
            <Button icon={<ZoomOut size={16} />} onClick={zoomOut} />
            <Button icon={<ZoomIn size={16} />} onClick={zoomIn} />
            <HTMLSelect
              options={ZOOM_LEVELS.map(level => ({ label: `${level * 100}%`, value: level }))}
              value={zoomLevel}
              onChange={handleZoomChange}
              style={{ width: '100px' }}
              minimal={true}
              icon={<ChevronDown size={14} />}
            />
          </ButtonGroup>
          <Button text="Reset" onClick={resetView} minimal={true} small={true} />
          <span style={{ marginLeft: "10px" }}>
            Page {currentPage} of {numPages}
          </span>
        </div>
        <div style={{ width: "32px" }} /> {/* Placeholder to balance the toolbar */}
      </Card>

      <div
        ref={pdfContainerRef}
        className="pdf-container"
        style={{
          flex: 1,
          overflow: "hidden",
          position: "relative",
        }}
      >
        {pdfFile && (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={<Spinner />}
            options={memoizedOptions}
          >
            <div
              ref={parentRef}
              style={{
                height: "100%",
                width: "100%",
                overflow: "auto",
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
              }}
            >
              <div
                style={{
                  height: `${virtualizer.getTotalSize()}px`,
                  width: "100%",
                  position: "relative",
                }}
              >
                {virtualizer.getVirtualItems().map((virtualItem) => (
                  <div
                    key={virtualItem.key}
                    data-index={virtualItem.index}
                    ref={virtualizer.measureElement}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                  >
                    <PageWithObserver
                      pageNumber={virtualItem.index + 1}
                      scale={scale}
                      rotation={rotation}
                      containerWidth={containerWidth}
                      isSelectMode={isSelectMode}
                      isMagnifierMode={isMagnifierMode}
                      pageObserver={pageObserver}
                    />
                  </div>
                ))}
              </div>
            </div>
          </Document>
        )}
      </div>
    </div>
  );
};

export default PDFJsViewer;