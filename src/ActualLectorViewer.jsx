import React, { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import { Spinner } from '@blueprintjs/core';

// NOTE: This will only work after installing lector with:
// npm install @anaralabs/lector pdfjs-dist
// or yarn add @anaralabs/lector pdfjs-dist

// Uncomment these imports after installing the package
// import { Root, Pages, Page, CanvasLayer, TextLayer } from '@anaralabs/lector';

const ActualLectorViewer = ({ fileId, filePath, isDarkTheme }) => {
  const [pdfSource, setPdfSource] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showThumbnails, setShowThumbnails] = useState(false);
  
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  useEffect(() => {
    async function fetchFile() {
      try {
        setLoading(true);
        console.log('Fetching PDF for Lector:', { fileId, filePath });
        
        const apiUrl = `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`;
        const response = await fetch(apiUrl, {
          headers: {
            'session-id': localStorage.getItem('session_id'),
          },
        });
        
        if (!response.ok) {
          throw new Error(`Server error: ${response.status}`);
        }
        
        const blob = await response.blob();
        const url = URL.createObjectURL(blob);
        
        setPdfSource(url);
        setLoading(false);
        
        if (dispatch && typeof dispatch === 'function') {
          try {
            dispatch({
              type: 'updatePdfViewer',
              payload: {
                fileId,
                filePath,
                isVisible: inView
              }
            });
          } catch (e) {
            console.warn('Could not dispatch update:', e);
          }
        }
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(`Failed to load PDF: ${err.message}`);
        setLoading(false);
      }
    }
    
    if (fileId) {
      fetchFile();
    }
    
    return () => {
      if (pdfSource && pdfSource.startsWith('blob:')) {
        URL.revokeObjectURL(pdfSource);
      }
    };
  }, [fileId, filePath, dispatch, inView, pdfSource]);
  
  // This would be used with the actual Lector library
  const renderWithLector = () => {
    // Until Lector is installed, show this message
    return (
      <div style={{ 
        padding: '20px', 
        textAlign: 'center', 
        backgroundColor: '#f0f0f0',
        border: '1px solid #ddd',
        borderRadius: '4px'
      }}>
        <h3>@anaralabs/lector Required</h3>
        <p>Please install the Lector package to view PDFs:</p>
        <pre style={{ 
          backgroundColor: '#333', 
          color: '#fff', 
          padding: '10px',
          borderRadius: '4px',
          textAlign: 'left'
        }}>
          npm install @anaralabs/lector pdfjs-dist
          {'\n'}# or{'\n'}
          yarn add @anaralabs/lector pdfjs-dist
        </pre>
        <p>After installation, uncomment the imports at the top of this file.</p>
        
        {/* This is the code that would be used with the actual Lector library */}
        {/* 
        <Root
          source={pdfSource}
          className="w-full h-full border overflow-hidden rounded-lg"
          loader={<div className="p-4"><Spinner size={50} />Loading...</div>}
        >
          <button 
            onClick={() => setShowThumbnails(!showThumbnails)}
            style={{
              padding: '5px 10px',
              margin: '5px',
              backgroundColor: '#f0f0f0',
              border: '1px solid #ccc',
              borderRadius: '4px'
            }}
          >
            {showThumbnails ? 'Hide' : 'Show'} Thumbnails
          </button>
          
          <div style={{ display: 'flex', height: 'calc(100% - 40px)' }}>
            {showThumbnails && (
              <div style={{ width: '150px', overflow: 'auto', padding: '10px', borderRight: '1px solid #ddd' }}>
                Thumbnails would go here
              </div>
            )}
            
            <div style={{ flex: 1, overflow: 'auto' }}>
              <Pages className={isDarkTheme ? 
                'dark:invert-[94%] dark:hue-rotate-180 dark:brightness-[80%] dark:contrast-[228%]' : 
                ''}
              >
                <Page>
                  <CanvasLayer />
                  <TextLayer />
                </Page>
              </Pages>
            </div>
          </div>
        </Root>
        */}
      </div>
    );
  };
  
  return (
    <div 
      ref={ref}
      style={{ 
        height: '100%', 
        width: '100%',
        overflow: 'auto',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {loading ? (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100%' 
        }}>
          <Spinner size={50} />
          <div style={{ marginLeft: '15px' }}>Loading PDF...</div>
        </div>
      ) : error ? (
        <div style={{ color: 'red', padding: '20px' }}>
          <h3>Error Loading PDF</h3>
          <p>{error}</p>
        </div>
      ) : (
        renderWithLector()
      )}
    </div>
  );
};

export default ActualLectorViewer;
