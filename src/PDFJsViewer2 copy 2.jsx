import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Spin<PERSON>, SpinnerSize } from "@blueprintjs/core";
import { useInView } from 'react-intersection-observer';
import "@blueprintjs/core/lib/css/blueprint.css";
import {
    updatePdfViewer,
    setViewerVisibility,
    setFocusedViewer,
    selectVisiblePDFs
} from './redux/currentPdfSlice';

export default function PDFJsViewer({ fileId, filePath, isDarkTheme }) {
    const dispatch = useDispatch();
    const [pdfFile, setPdfFile] = useState(null);
    const [downloadProgress, setDownloadProgress] = useState(0);
    const [hasLoaded, setHasLoaded] = useState(false);
    
    const { ref, inView } = useInView({
        threshold: 0.1,
    });

    const currentViewerState = useSelector(state => state.currentPdf.viewers[fileId]);
    const visiblePDFs = useSelector(selectVisiblePDFs);

    useEffect(() => {
        dispatch(setViewerVisibility({ fileId, isVisible: inView }));
        
        if (inView) {
            dispatch(setFocusedViewer(fileId));
            console.log('PDF Viewer Visible:', {
                fileId,
                filePath,
                currentPage: currentViewerState?.page || 1
            });
        }
    }, [inView, fileId, dispatch, filePath, currentViewerState]);

    useEffect(() => {
        console.log('Currently visible PDFs:', visiblePDFs);
    }, [visiblePDFs]);

    useEffect(() => {
        async function fetchFile() {
            if (hasLoaded) return;
            
            setPdfFile(null);
            setDownloadProgress(0);

            try {
                const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
                    headers: {
                        'Accept-Encoding': 'gzip, deflate, br',
                        'session-id': localStorage.getItem('session_id'),
                    },
                });
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }

                const contentLength = response.headers.get('Content-Length');
                const reader = response.body.getReader();
                let receivedLength = 0;
                const chunks = [];

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    chunks.push(value);
                    receivedLength += value.length;

                    if (contentLength) {
                        setDownloadProgress(receivedLength / contentLength);
                    }
                }

                const blob = new Blob(chunks);
                const pdfUrl = URL.createObjectURL(blob);
                setPdfFile(pdfUrl);
                setHasLoaded(true);

                dispatch(updatePdfViewer({
                    fileId,
                    filePath,
                    page: 1,
                    isVisible: inView
                }));

                console.log('PDF Loaded:', {
                    fileId,
                    filePath,
                    initialPage: 1,
                    isVisible: inView
                });
            } catch (error) {
                console.error("Failed to fetch the PDF:", error);
            }
        }

        fetchFile();

        return () => {
            if (pdfFile && !hasLoaded) {
                URL.revokeObjectURL(pdfFile);
            }
        };
    }, [fileId, filePath]);

    const setupPageChangeListener = (iframeDocument) => {
        const script = iframeDocument.createElement('script');
        script.textContent = `
            function setupPageChangeTracking() {
                if (typeof PDFViewerApplication !== 'undefined') {
                    PDFViewerApplication.eventBus.on('pagesloaded', function(evt) {
                        console.log('PDF Pages Loaded:', {
                            filePath: "${filePath}",
                            totalPages: PDFViewerApplication.pagesCount,
                            currentPage: PDFViewerApplication.page
                        });

                        window.parent.postMessage({
                            type: 'pdfState',
                            action: 'pagesloaded',
                            data: {
                                page: PDFViewerApplication.page,
                                totalPages: PDFViewerApplication.pagesCount
                            }
                        }, '*');
                    });

                    PDFViewerApplication.eventBus.on('pagechanging', function(evt) {
                        const pageNumber = evt.pageNumber;
                        console.log('Page Changing:', {
                            filePath: "${filePath}",
                            previousPage: PDFViewerApplication.page,
                            newPage: pageNumber,
                            totalPages: PDFViewerApplication.pagesCount
                        });
                        
                        window.parent.postMessage({
                            type: 'pdfState',
                            action: 'pagechanging',
                            data: {
                                page: pageNumber,
                                scale: PDFViewerApplication.pdfViewer.currentScale,
                                scrollTop: document.getElementById('viewerContainer').scrollTop
                            }
                        }, '*');
                    });

                    PDFViewerApplication.eventBus.on('scalechanging', function(evt) {
                        window.parent.postMessage({
                            type: 'pdfState',
                            action: 'scalechanging',
                            data: {
                                scale: evt.scale,
                                scrollTop: document.getElementById('viewerContainer').scrollTop
                            }
                        }, '*');
                    });
                }
            }

            if (typeof PDFViewerApplication !== 'undefined') {
                PDFViewerApplication.initializedPromise.then(setupPageChangeTracking);
            } else {
                document.addEventListener('webviewerloaded', function() {
                    PDFViewerApplication.initializedPromise.then(setupPageChangeTracking);
                });
            }
        `;
        iframeDocument.head.appendChild(script);
    };

    useEffect(() => {
        const handleMessage = (event) => {
            if (event.data.type === 'pdfState' && inView) {
                const { action, data } = event.data;
                
                switch (action) {
                    case 'pagesloaded':
                        dispatch(updatePdfViewer({
                            fileId,
                            page: data.page,
                            filePath,
                            isVisible: inView
                        }));
                        break;

                    case 'pagechanging':
                        dispatch(updatePdfViewer({
                            fileId,
                            page: data.page,
                            scale: data.scale,
                            scrollTop: data.scrollTop,
                            filePath,
                            isVisible: inView
                        }));
                        console.log('Redux State Updated:', {
                            fileId,
                            filePath,
                            page: data.page,
                            isVisible: inView
                        });
                        break;

                    case 'scalechanging':
                        dispatch(updatePdfViewer({
                            fileId,
                            scale: data.scale,
                            scrollTop: data.scrollTop,
                            filePath,
                            isVisible: inView
                        }));
                        break;
                }
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [dispatch, fileId, filePath, inView]);

    const simulateClickOnHandTool = (iframeDocument) => {
        const script = iframeDocument.createElement('script');
        script.textContent = `
            function simulateClick() {
                const handToolButton = document.getElementById('cursorHandTool');
                if (handToolButton) {
                    handToolButton.click();
                }
            }
            document.addEventListener('DOMContentLoaded', simulateClick);
            if (document.readyState === 'complete') {
                simulateClick();
            }
        `;
        iframeDocument.head.appendChild(script);
    };

    const setThemeInIframe = (iframeDocument, theme) => {
        const setThemeFunctionString = `
            function setTheme(theme) {
                if (theme == 'Dark') {
                    PDFViewerApplicationOptions.set('viewerCssTheme', 1);
                }
                else {
                    PDFViewerApplicationOptions.set('viewerCssTheme', 2);
                }
                PDFViewerApplication._forceCssTheme();
            }
        `;

        const script = iframeDocument.createElement('script');
        script.textContent = setThemeFunctionString;
        iframeDocument.head.appendChild(script);

        iframeDocument.defaultView.setTheme(isDarkTheme ? 'Dark' : 'Light');
    };

    if (!pdfFile) {
        return (
            <div className="spinner-container">
                <Spinner
                    className="spinner-center"
                    intent="primary"
                    size={SpinnerSize.STANDARD}
                    value={downloadProgress}
                />
            </div>
        );
    }

    return (
        <div ref={ref} id={`pdf-viewer-${fileId}`} style={{height:"calc(100vh)"}}>
            <iframe
                title={`PDF Viewer for ${filePath}`}
                width="100%"
                height="100%"
                src={`${process.env.PUBLIC_URL}/pdfjs-3.11.174-dist/web/viewer.html?file=${encodeURIComponent(pdfFile)}`}
                className={isDarkTheme ? "dark-theme" : "light-theme"}
                onLoad={(event) => {
                    const iframeDocument = event.target.contentDocument || event.target.contentWindow.document;
                    const customStyle = iframeDocument.createElement('style');
                    customStyle.innerHTML = `
                        body { 
                            font-family: 'NeueHaasDisplayRoman', sans-serif !important; 
                            font-size: 0.7em !important; 
                        }
                    `;
                    iframeDocument.head.appendChild(customStyle);
                    setThemeInIframe(iframeDocument, isDarkTheme ? 'Dark' : 'Light');
                    simulateClickOnHandTool(iframeDocument);
                    setupPageChangeListener(iframeDocument);

                    if (currentViewerState) {
                        const script = iframeDocument.createElement('script');
                        script.textContent = `
                            PDFViewerApplication.initializedPromise.then(() => {
                                if (${currentViewerState.page}) {
                                    PDFViewerApplication.page = ${currentViewerState.page};
                                }
                                if (${currentViewerState.scale}) {
                                    PDFViewerApplication.pdfViewer.currentScale = ${currentViewerState.scale};
                                }
                                if (${currentViewerState.scrollTop}) {
                                    document.getElementById('viewerContainer').scrollTop = ${currentViewerState.scrollTop};
                                }
                            });
                        `;
                        iframeDocument.head.appendChild(script);
                    }
                }}
            />
        </div>
    );
}
