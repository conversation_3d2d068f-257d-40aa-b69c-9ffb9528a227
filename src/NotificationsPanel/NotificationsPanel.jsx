import React, { useState, useEffect, useRef, useContext, useMemo } from "react";
import { Dialog, Button, Tag,Icon } from "@blueprintjs/core"; // Adjust this import based on your actual UI library
import { AgGridReact } from "ag-grid-react";





const NotificationsPanel = ({ isOverlayOpen, toggleOverlay, isDarkTheme, notifications }) => {
    const transformErrorsToTableData = (errors) => {
        // Given that errors is now ensured to be an array, this should not throw an error
        return errors.map(error => ({
            tagName: error.combination[3], // The 4th item in the combination array
            template: error.template,
            columnMissMatch: error.failed_columns.join(', ') // Join failed_columns to a comma-separated string
        }));
    };

    // Ensuring notifications is defined and has an 'errors' property before proceeding
    const safeNotificationsErrors = useMemo(() => notifications && notifications.errors ? notifications.errors : []);
    
    const tableData = useMemo(() => transformErrorsToTableData(safeNotificationsErrors), [safeNotificationsErrors]);




    const CellTagsRenderer = ({ value }) => {
        // This assumes `value` is always a string; adjust as necessary
        return (
            <>
                {value.split(', ').map((tag, index) => (
                    <Tag key={index} intent="warning" style={{ marginRight: "5px" }}>
                        {tag}
                    </Tag>
                ))}
            </>
        );
    };



  const columnDefs = [
    { headerName: "Tag Name", field: "tagName" },
    { headerName: "Template", field: "template",width: 100},
    { headerName: "Inconsistent Data Entries", field: "columnMissMatch", width: 300, cellRenderer: CellTagsRenderer }
];
  
  


  
  return (
        <Dialog
            isOpen={isOverlayOpen}
            onClose={toggleOverlay}
            className={isDarkTheme ? "bp5-dark" : ""}
            title={
                <>
                    Notifications
                    <span style={{ paddingLeft: "8px" }}> {/* Add padding on the left side of the icon */}
                        <Icon icon="notifications" intent="warning" />
                    </span>
                </>
            }
            style={{ width: "700px",}}
        >
            <div className="bp5-dialog-body">
            <div className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"} style={{ width: '100%' }}
          >
              <AgGridReact
                id="ncrs-grid"
                headerHeight={35}
                columnDefs={columnDefs}
                rowData={tableData}
                getRowHeight={() => 24}
                enableCellTextSelection={true}
                rowSelection="single"
                domLayout='autoHeight'
                suppressDragLeaveHidesColumns={true}
                
         
                
              />
            </div>
            </div>
        </Dialog>
    );

        }
export default NotificationsPanel;

