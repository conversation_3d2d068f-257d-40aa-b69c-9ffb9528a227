// Import direct patch first to ensure it runs before any other code
import './pdfjs-direct-patch';

import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import { Provider } from 'react-redux';
import store from './redux/store';
import { IconSizeFix } from './IconSizeFix';
import { patchPdfJsVersions, monkeyPatchPdfViewer } from './pdfjs-version-fix';

// Apply patch for Blueprint IconSize issue
const IconSize = {
  STANDARD: 16,
  LARGE: 20,
};

// Make it available globally for Blueprint internal modules
if (typeof window !== 'undefined') {
  window.IconSize = IconSize;
  
  // Apply PDF.js version fix to resolve version mismatch errors
  patchPdfJsVersions();
  monkeyPatchPdfViewer();
  
  // Also patch the PDF.js version on load event for cases where PDF.js loads later
  window.addEventListener('load', () => {
    patchPdfJsVersions();
  });
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <IconSizeFix />
      <App />
    </Provider>
  </React.StrictMode>
);
