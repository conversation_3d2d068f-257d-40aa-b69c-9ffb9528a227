import React from "react";



const IconEXCEL = () => {
  return(
    <svg
      className="svg-icon"
      style={{ width: '1.25em', height: '1.35em', verticalAlign: 'middle', fill: 'currentColor', overflow: 'hidden' }}
      viewBox="0 0 1024 1024"
      xmlns="http://www.w3.org/2000/svg"
    >
     <path d="M661.944889 73.144889H146.289778a36.679111 36.679111 0 0 0-36.579556 36.565333v804.579556a36.679111 36.679111 0 0 0 36.579556 36.565333h731.420444a36.679111 36.679111 0 0 0 36.579556-36.565333v-588.8L661.944889 73.144889z" fill="#1DB87B" /><path d="M661.944889 288.910222a36.679111 36.679111 0 0 0 36.565333 36.579556h215.779556L661.944889 73.144889v215.765333z" fill="#63CE9E" /><path d="M568.689778 596.110222l113.365333-175.544889h-96.910222l-40.234667 73.144889c-9.144889 14.634667-16.455111 31.089778-25.6 51.2-5.475556-9.144889-9.144889-18.275556-14.620444-25.6-5.489778-9.144889-10.979556-18.275556-14.634667-25.6l-43.889778-73.144889h-102.4l113.379556 171.889778-120.689778 182.855111H435.2l45.710222-78.620444c9.144889-14.634667 16.455111-31.089778 29.255111-53.034667 5.489778 9.144889 9.144889 16.455111 14.634667 23.779556 5.489778 9.130667 10.965333 20.110222 16.455111 29.255111l47.544889 78.620444h102.4l-122.510222-179.2z" fill="#FFFFFF" /></svg>
  );
};



  export default IconEXCEL ;