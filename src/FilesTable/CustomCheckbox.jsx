import { BsSquare, BsCheckSquare } from "react-icons/bs";
import React, { useState, useEffect } from "react";
import styled, { css } from "styled-components";


const Input = styled.input`
  ${({ theme }) => css`
    border: 1px solid white;
position: absolute;

    /* &:checked + svg {
     
fill:blue;
      } */
   /* background-color:transparent
  
      accent-background:transparent; */
    }
  `}
`;




const CustomCheckBox = ({ check = true, color = "blue", ...props }) => {
    const [checked, setChecked] = useState(check || false);
  
    // Update internal checked state when check prop changes
    useEffect(() => {
      setChecked(check);
    }, [check]);
  
    const checkHandler = (e) => {
      // If onChange is passed as a prop, call it
      if (props.onChange) {
        props.onChange(e);
      }
      setChecked(e.currentTarget.checked);
    };
  
    return (
      <>
        <label htmlFor="checkbox">
          <input
            type="checkbox"
            {...props}
            checked={checked}
            onChange={checkHandler}
            id="checkbox"
            style={{ opacity: 0, position: 'absolute', zIndex: -1 }} // Properly hide the checkbox
          />
          {!checked && <BsSquare fill="none" stroke={color} size={24} />}
          {checked && <BsCheckSquare fill={color} size={24} />}
        </label>
      </>
    );
  };
  
  export default CustomCheckBox;
