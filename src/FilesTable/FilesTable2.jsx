import React, {
  useState,
  useEffect,
  useMemo,
  useRef,
  useContext,
  Component,
} from "react";
import { AgGridReact } from "ag-grid-react";
import {
  Card,
  Icon,
  H5,
  Tag,
  CompoundTag,
  Dialog,
  Position,
  Tooltip,
  Popover,
  Button,
  Filter,
  FilterX
} from "@blueprintjs/core";
import "../ag-grid.css";
import "../ag-theme-alpine.css";
import "../ag-theme-balham.css";
import { useCopyPaste } from "../hooks/useCopyPaste";
import { grid, quantum } from "ldrs";

import { bouncy } from "ldrs";
import { lineSpinner } from "ldrs";
import IconPDF from "./IconPDF";
import IconDWG from "./IconDWG";
import IconEXCEL from "./IconEXCEL";
import IconWORD from "./IconWORD";
import axios from "axios";
import { Checkbox } from "primereact/checkbox";
import { AppContext } from "../AppContextProvider";
lineSpinner.register();
quantum.register();

function FilesTable({ onFileClick, isDarkTheme }) {
  const [data, setData] = useState([]);
  const [filters, setFilters] = useState({ col1: "", col2: "", col3: "" });
  const [filesGridApi, setFilesGridApi] = useState(null);
  const gridContainerRef = useRef(null);
  const { allCurrentInspectionsFiles, setAllCurrentInspectionsFiles } =
    useContext(AppContext);
  const { currentInspectionFiles, setCurrentInspectionFiles } =
    useContext(AppContext);

  const { currentAssetFiles, setCurrentAssetFiles } = useContext(AppContext);

  const [rowCount, setRowCount] = useState(0);

  const {currentGeneralFile, setCurrentGeneralFile} = useContext(AppContext);

  const {allCurrentGeneralFiles, setAllCurrentGeneralFiles} = useContext(AppContext);

  const { setQuickFilterText } = useContext(AppContext);

  const {activeTab, setActiveTab} = useContext(AppContext);

  //set asset invetory filter

  const makeApiCallWithRetry = async (url, options, maxTotalTime = 180000) => {
    let totalWaitTime = 0;

    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
    const fixedIncrement = 5000; // 5 seconds in milliseconds

    while (true) {
      try {
        console.log(`Calling ${url}`);
        return await axios(url, options);
      } catch (error) {
        const isRetryableError =
          error.response &&
          (error.response.status === 503 ||
            error.response.status === 429 ||
            error.response.status === 500);
        let waitTime = fixedIncrement + Math.random() * 1000; // 5 seconds + a random amount up to 1 second

        if (!isRetryableError || totalWaitTime + waitTime > maxTotalTime) {
          console.log(`Error: `, error);
          throw error;
        }

        console.log(`Waiting ${waitTime}ms before next attempt`);
        await delay(waitTime);
        totalWaitTime += waitTime;
        console.log(`Total wait time: ${totalWaitTime / 1000}s`);
      }
    }
  };

  const getFileType = (mimeType) => {
    switch (mimeType) {
      case "application/vnd.google-apps.folder":
        return "Folder";
      case "application/vnd.google-apps.document":
        return "Document";
      case "application/pdf":
        return "PDF";
      case "application/vnd.google-apps.spreadsheet":
        return "Spreadsheet";
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        return "Excel";
      case "application/msword":
        return "Word";
      case "image/vnd.dwg":
        return "DWG";
      default:
        return "File";
    }
  };

  useEffect(() => {
    if (filesGridApi && data.length > 0) {
      // Show loading overlay
      filesGridApi.showLoadingOverlay();
  
      // Assuming `data` is your dataset
      const greenRows = data.filter(row => 
        currentAssetFiles.includes(row.path) || 
        row.GreenMapped === "TRUE"
      );
      const otherRows = data.filter(row => 
        !currentAssetFiles.includes(row.path) && 
        row.GreenMapped !== "TRUE"
      );
  
      // Use a timeout to simulate asynchronous operation and avoid blocking UI updates
      setTimeout(() => {
        // Pin "green" rows to the top
        filesGridApi.setPinnedTopRowData(greenRows);
  
        // Set the remaining rows
        filesGridApi.setRowData(otherRows);
        setRowCount(greenRows.length + otherRows.length);
  
        // Hide loading overlay after the operation
        filesGridApi.hideOverlay();
      }, 700); // A timeout of 0 ms ensures this runs on the next event loop, allowing the UI to update
    }
  }, [currentAssetFiles]);
  

  useEffect(() => {
    // Existing useEffects for fetching data and setting up the grid...
    if (filesGridApi) {
      // Update row count when the grid API is available and after fetching data
      setRowCount(filesGridApi.getDisplayedRowCount());
    }
  }, [filesGridApi, data]); //

  // Include an effect to update the row count when the grid's model is updated
  useEffect(() => {
    if (!filesGridApi) return;

    const updateRowCount = () => {
      setRowCount(filesGridApi.getDisplayedRowCount());
    };

    updateRowCount(); // Initial update
    filesGridApi.addEventListener("modelUpdated", updateRowCount);

    return () => {
      filesGridApi.removeEventListener("modelUpdated", updateRowCount);
    };
  }, [filesGridApi]);

  useEffect(() => {
    if (filesGridApi) {
      // This forces the grid to refresh all cells, you can customize the call as needed
      filesGridApi.redrawRows();
    }
  }, [currentInspectionFiles, allCurrentInspectionsFiles]); // Dependencies that trigger the refresh

  class CheckboxCellRenderer extends Component {
    constructor(props) {
      super(props);
      this.state = {
        checked: this.isChecked(props),
        checkboxClass: this.getCheckboxClass(props),
      };
    }

    // React to changes in props, especially context
    componentDidUpdate(prevProps) {
      // Destructure for clarity
      const { context: prevContext, data: prevData } = prevProps;
      const { context: currentContext, data: currentData } = this.props;

      // Check if relevant props have changed
      if (
        prevData.path !== currentData.path ||
        prevContext !== currentContext
      ) {
        this.setState({
          checked: this.isChecked(this.props),
          checkboxClass: this.getCheckboxClass(this.props),
        });
      }
    }

    isChecked = ({ context, data }) => {
      return (
        context.currentInspectionFiles.includes(data.path) ||
        context.allCurrentInspectionsFiles.includes(data.path)
      );
    };

    getCheckboxClass = ({ context, data }) => {
      const { currentInspectionFiles, allCurrentInspectionsFiles } = context;
      if (currentInspectionFiles.includes(data.path)) {
        return "p-checkbox-green";
      } else if (allCurrentInspectionsFiles.includes(data.path)) {
        return "p-checkbox-blue";
      } else if (data.Mapped === "TRUE") {
        return "p-checkbox-blue";
      }
      return "p-checkbox-red";
    };

    render() {
      const { checked, checkboxClass } = this.state;
      return (
        <div style={{ textAlign: "center" }}>
          <Checkbox
            className={`p-checkbox p-component ${checkboxClass}`}
            checked={true}
            onChange={() => {}}
            disabled
          />
        </div>
      );
    }
  }

  useEffect(() => {
    if (filesGridApi) {
      filesGridApi.showLoadingOverlay();
    }

    const fetchFiles = async () => {
      try {
        const response = await makeApiCallWithRetry(
          `${process.env.REACT_APP_FILES_API}/list-files-static/`,
          { method: "GET" }
        );
        const data = await response.data;
        setData(data.files);
        if (filesGridApi) {
          filesGridApi.hideOverlay();
        }
      } catch (error) {
        console.error("Failed to fetch files:", error);
        // Handle error (e.g., show error message)
        if (filesGridApi) {
          filesGridApi.hideOverlay();
        }
      }
    };

    fetchFiles();
  }, [filesGridApi]);

  const filteredData = useMemo(() => {
    return data.filter(
      (item) =>
        !item.name.endsWith("_Converted.pdf") &&
        getFileType(item.mimeType) !== "Folder" &&
        item.name.toLowerCase().includes(filters.col1.toLowerCase()) &&
        item.path.toLowerCase().includes(filters.col2.toLowerCase()) &&
        getFileType(item.mimeType)
          .toLowerCase()
          .includes(filters.col3.toLowerCase())
    );
  }, [data, filters]);

  const handleRowSelection = () => {
    const selectedNodes = filesGridApi.getSelectedNodes();
    const selectedData = selectedNodes.map((node) => node.data)[0];
    onFileClick(selectedData.id, selectedData.name, selectedData.path);
  };

  const onGridReady = (params) => {
    params.api.addEventListener('rowClicked', onRowClicked);
    setFilesGridApi(params.api);
    params.api.refreshCells({ force: true });
  
    // Example of adding a click event listener to manage selection state
    params.api.addEventListener('rowClicked', event => {
      if (event.node.rowPinned) {
        // Manually toggle selection state for pinned rows
        event.node.setSelected(!event.node.isSelected());
      }
    });
  };

  function IconCellRenderer(params) {
    const fileType = getFileType(params.value);
    switch (fileType) {
      case "PDF":
        return (
          <div style={{ textAlign: "center" }}>
            <IconPDF />
          </div>
        );
      case "DWG":
        return (
          <div style={{ textAlign: "center" }}>
            <IconDWG />
          </div>
        );
      case "Excel":
        return (
          <div style={{ textAlign: "center" }}>
            <IconEXCEL />
          </div>
        );
      case "Word":
        return (
          <div style={{ textAlign: "center" }}>
            <IconWORD />
          </div>
        );
      default:
        return <div style={{ textAlign: "center" }}></div>;
    }
  }

  const getColumnWidth = (header) => {
    switch (header) {
      case "Path":
        return 250;
      case "Name":
        return 280;
      case "Mapped":
        return 50;
      default:
        return 100; // Default width
    }
  };

  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      resizable: true,
      filter: true,
      tooltipValueGetter: (params) => {
        return params.value;
      }
    }),
    []
  );

  const columnDefs = useMemo(() => {
    // Custom cell renderer component
    const ButtonCellRenderer = (props) => {
      const handleClick = () => {
        // Use context to access setQuickFilterText and update it
        setQuickFilterText(props.data.tagName);
        console.log("Asset Tag Name Clicked: ", props.data.tagName);
      };

      return (
        <Button
          className="searchButton"
          icon="search"
          intent="primary"
          onClick={handleClick}
        />
      );
    };

    const PopoverContent = ({ data }) => (
      <Card className="no-padding-card" style={{ width: "500px" }}>
        <div className="bp5-dialog-body">
          <div
            className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"}
            style={{ width: "100%", height: "100%" }}
          >
            <AgGridReact
              columnDefs={[
                { field: "tagName", headerName: "Tag Name", width: "150" },
                {
                  field: "assetType",
                  headerName: "Asset Classification",
                  width: "140",
                },
                { field: "module", headerName: "Mod.", width: "70" },
                {
                  field: "searchButtonField",
                  headerName: "",
                  width: "60",
                  cellRenderer: ButtonCellRenderer,
                },
              ]}
              rowData={data}
              rowHeight={25}
              domLayout="autoHeight"
            />
          </div>
        </div>
      </Card>
    );

    const svgCellRenderer = (params) => {

      
      // Parse the data right here to pass it to the Popover content
      let parsedData = [];
      try {
        const infoData = params.data.info; // Assuming 'info' is the field name and is in valid JSON format
        const jsonData = JSON.parse(infoData || "{}");
        parsedData = Object.entries(jsonData).map(([key, value]) => {
          const parts = value.split(", ");
          const assetType = parts[0]; // Assuming the first part is always the asset type
          const module = parts.slice(1).join(", "); // Re-join the remaining parts, if any, to form the module
          return { tagName: key, assetType, module };
        });
      } catch (error) {
        console.error("Error parsing Info column data:", error);
      }

      // Determine the fill color based on your conditions
      let fillColor = "#FFFFFF"; // Default color, can be adjusted
      if (currentInspectionFiles.includes(params.data.path) && activeTab === "previousInspections") {
        fillColor = "#B45DC3";
      } else if (currentGeneralFile.includes(params.data.path)) {
        fillColor = "#B45DC3";
      } else if (currentAssetFiles.includes(params.data.path)) {
        fillColor = "#47914B";
      } else if (params.data["Mapped"] === "TRUE") {
        fillColor = "#2772FE";
      } else {
        fillColor = "grey";
      }

      // Use Popover instead of Tooltip
      return (
        <Popover
          style={{ width: "100%" }}
          content={<PopoverContent data={parsedData} />}
          setQuickFilterText={setQuickFilterText}
          interactionKind="hover"
          usePortal={true}
          fill={true}
          matchTargetWidth={true}
          minimal={true}
          canEscapeKeyClose={true}
          position={Position.RIGHT_TOP}
          positioningStrategy="aboslute"
        >
          <svg
            width="14"
            height="14"
            style={{
              cursor: "pointer",
              display: "block",
              marginRight: "0px",
              marginLeft: "0px",
            }}
          >
            <rect
              width="14"
              height="14"
              style={{ fill: fillColor, stroke: "black", strokeWidth: 1 }}
              rx="5"
              ry="5"
            />
          </svg>
        </Popover>
      );
    };
    // Define common properties for all columns
    const commonProps = (field) => ({
      filter: true,
      resizable: true,
      editable: false,
      floatingFilter: true,
      cellRenderer: field === "mapped" ? svgCellRenderer : undefined,
    });

    return [
      /*
      {
        headerName: "",
        valueGetter: "node.rowIndex + 1",
        cellStyle: {
          color: "white",
          fontsize: "5em",
          textAlign: "left",
          backgroundColor: "#383E47",
          color: "grey",
          pinned: "left",
        },
        width: 43,
      },*/

      {
        headerName: "",
        field: "Mapped",
        //  cellRenderer: "checkboxCellRenderer",
        ...commonProps("mapped"),
        hide: false, // Example of additional specific property
        width: getColumnWidth("Mapped"),
        floatingFilter: false,
        resizable: true,

        /*cellRenderer: "checkboxCellRenderer",*/
      },
      {
        headerName: "Name",
        field: "name",
        ...commonProps("name"),
        tooltipValueGetter: (params) => {
          return params.value;
        }
      },
      {
        headerName: "Path",
        field: "path",
        ...commonProps("path"),
        width: getColumnWidth("Path"),
        tooltipValueGetter: (params) => {
          return params.value;
        }
      },
      // Continue for other columns, applying commonProps as needed
      {
        headerName: "Plant",
        field: "plant",
        ...commonProps("plant"),
        hide: true, // Example of additional specific property
      },
      {
        headerName: "",
        field: "mimeType",
        filter: true,
        cellRenderer: IconCellRenderer,
        width: 60,
        editable: false,
      },

      // More columns definitions
    ];
  }, [currentAssetFiles, currentInspectionFiles,currentGeneralFile, isDarkTheme,activeTab,allCurrentGeneralFiles ]);

  const onRowClicked = event => {
    if (event.node) {
      // Clear previous selection
      filesGridApi.deselectAll();
  
      // Select the new row
      event.node.setSelected(true);
  
      // Assuming `onFileClick` opens the file and each row data has fileId, fileName, filePath
      const { data } = event.node;
      onFileClick(data.id, data.name, data.path);
    }
  };
  

  const gridOptions = useMemo(
    () => ({
      context: {
        currentInspectionFiles, // Add this line
        allCurrentInspectionsFiles, // And this line
      },
      components: {
        checkboxCellRenderer: CheckboxCellRenderer,
      },

      overlayLoadingTemplate:
        // 3 dots bouncing
        '<span class=""><l-line-spinner size="25" speed="1.0" color="white"></l-bouncy></span>',
      //  '<span class=""><l-quantum size="30" speed="1.75" color="white"></l-quantum><span>',/
    }),
    []
  ); // Depend on these values to recompute gridOptions

  useCopyPaste(filesGridApi, gridContainerRef);

  useEffect(() => {
    const adjustGridHeight = () => {
      if (gridContainerRef.current) {
        const offsetTop = gridContainerRef.current.offsetTop;
        const totalHeight = window.innerHeight;
        const calculatedHeight = totalHeight - offsetTop - 53; // Adjust 10px or more based on your footer height or additional margins
        gridContainerRef.current.style.height = `${calculatedHeight}px`;
      }
    };
  
    window.addEventListener('resize', adjustGridHeight);
    adjustGridHeight(); // Initial adjustment
  
    return () => {
      window.removeEventListener('resize', adjustGridHeight);
    };
  }, []);

  const isFilterActive = Object.values(filters).some((value) => value !== "");

  return (
    <div>
      <Card
        className="no-padding-card"
        style={{ marginBottom: "0px", padding: "0px" }} // Adjust styling as needed
      >
        <CompoundTag
          style={{ fontWeight: "bold" }} // Apply CSS styles
          intent={null} // Set the intent or theme
          leftContent="Total" // Set left side content
          children={rowCount} // Dynamically display row count
        />
        <Button
          icon={isFilterActive ? <FilterX size={14} /> : <Filter size={14} />}
          minimal={true}
          onClick={() => filesGridApi && filesGridApi.setFilterModel(null)}
          className={`icon-button filter-button ${isFilterActive ? 'has-filters' : ''}`}
        />
      </Card>

      <Card
        className="no-padding-card"
        style={{ overflowY: "auto", height: "100vh" }}
      >
        <div
          ref={gridContainerRef}
          className={isDarkTheme ? "ag-theme-alpine-dark" : "ag-theme-alpine"}
          style={{ height: "100%", width: "100%" }}
        >
          <AgGridReact
            usePortal={true}
            id="filesTable"
            columnDefs={columnDefs}
            rowData={filteredData}
            onGridReady={onGridReady}
            gridOptions={gridOptions}
            rowSelection="single"
            onSelectionChanged={handleRowSelection}
            rowHeight={22}
            headerHeight={30}
            enableCellTextSelection={true}
            defaultColDef={defaultColDef}
          />
        </div>
      </Card>
    </div>
  );
}

export default React.memo(FilesTable);
