import React from "react";

const IconDWG = () => {
  return (
    <svg
      className="svg-icon"
      style={{ width: '1.25em', height: '1.35em', verticalAlign: 'middle', fill: 'currentColor', overflow: 'hidden' }}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Main document shape */}
      <path
        d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"
        fill="#0097A7"
      />
      {/* Folded corner */}
      <path
        d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z"
        fill="#0097A7"
        fillOpacity="0.3"
      />
      {/* DWG text */}
      <text
        x="50%"
        y="55%"
        dominantBaseline="middle"
        textAnchor="middle"
        fill="white"
        style={{
          fontFamily: 'Arial, sans-serif',
          fontSize: '6px',
          fontWeight: 'bold'
        }}
      >
        DWG
      </text>
    </svg>
  );
};

export default IconDWG;