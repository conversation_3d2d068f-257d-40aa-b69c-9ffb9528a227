import React from "react";
import { Layout, Model } from "flexlayout-react";
import "./Styles/vaim-flexlayout-dark.css";
import {
  Navbar,
  NavbarGroup,
  Card,
  Button,
  NonIdealState,
  Icon,
} from "@blueprintjs/core";
import PDF<PERSON>sViewer from "./EnhancedPDFViewer";
import { useSelector, useDispatch } from "react-redux";
import { Tag } from "primereact/tag";

import { BiUnlink } from "react-icons/bi";
import FilesTable from "./FilesTable/FilesTable";
import {
  setSelectedFile,
  setIsFileLinked,
  setCurrentInspectionFiles,
  updateFilesColumn,
} from "./filesHandlerSlice";

const layoutModel = Model.fromJson({
  global: {},
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        weight: 67,
        children: [
          {
            type: "tab",
            component: "mainContent",
            name: "File Viewer",
            enableClose: false,
            enableRename: false,
            icons: ["popout", "maximize"],
          },
        ],
      },
      {
        type: "tabset",
        weight: 33,
        children: [
          {
            type: "tab",
            component: "sidebar",
            name: "Files Table",
            enableClose: false,
            enableRename: false,
          },
        ],
      },
    ],
  },
});

function FilesHandler({ isDarkTheme, windowMode }) {
  const dispatch = useDispatch();
  const {
    selectedFile,
    isFileLinked,
    currentInspectionFiles,
    activeModule,
    activeTab,
  } = useSelector((state) => state.filesHandler);

  const factory = (node) => {
    const component = node.getComponent();
    if (component === "mainContent") {
      return (
        <div>
          {selectedFile && selectedFile.id ? (
            <>
              <Navbar>
                <NavbarGroup
                  className="centered-items"
                  style={{ paddingLeft: "25px" }}
                >
                  <Tag
                    style={{
                      background: "transparent",
                      fontSize: "11px",
                      color: isDarkTheme ? "white" : "black",
                    }}
                    value={selectedFile.path}
                    rounded
                  />
                  <Navbar.Divider />
                  <Button
                    icon={isFileLinked ? <BiUnlink /> : <Icon icon="link" />}
                    intent={isFileLinked ? "warning" : "primary"}
                    onClick={() => dispatch(setIsFileLinked(!isFileLinked))}
                    disabled={activeTab !== "previousInspections"}
                    className={`link-button ${
                      isFileLinked ? "my-prime-button-break" : ""
                    }`}
                  />
                </NavbarGroup>
              </Navbar>
              <Card className="no-padding-card" style={{ height: "100%" }}>
                <PDFJsViewer
                  fileId={selectedFile.id}
                  isDarkTheme={isDarkTheme}
                  style={{ padding: "15px" }}
                />
              </Card>
            </>
          ) : (
            <div className="centered-non-ideal" style={{ height: "100%" }}>
              <NonIdealState
                icon="document-open"
                title="No File Selected"
                description="Select a file from the table to be displayed"
              />
            </div>
          )}
        </div>
      );
    } else if (component === "sidebar") {
      return (
        <FilesTable
          onFileClick={(fileId, filename, path) =>
            dispatch(setSelectedFile({ id: fileId, name: filename, path }))
          }
          isDarkTheme={isDarkTheme}
        />
      );
    }
    return null; // For unrecognized components
  };

  return (
    <div
      className={isDarkTheme ? "bp5-dark" : ""}
      style={{
        position: "relative",
        height: "calc(100% - 2px)",
        width: "100%",
      }}
    >
      <Layout model={layoutModel} factory={factory} />
    </div>
  );
}

export default FilesHandler;
