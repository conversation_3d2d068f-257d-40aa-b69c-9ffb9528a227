import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { <PERSON>, <PERSON>ner, <PERSON>ton, ButtonGroup, HTMLSelect } from "@blueprintjs/core";
import { Input } from 'antd';
import { RotateCcw, RotateCw, ZoomIn, ZoomOut, Search } from "lucide-react";
import { useDispatch, useSelector } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import {
  PdfLoader,
  PdfHighlighter,
  Highlight,
  Popup,
  Tip
} from "react-pdf-highlighter";

// Import Redux actions
import {
  updatePdfViewer,
  setViewerVisibility,
  setFocusedViewer,
} from './redux/currentPdfSlice';

// Import CSS
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf-highlighter/dist/style.css";
import "./pdf-viewer.css";

// Constants
const ZOOM_LEVELS = [0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3];

// Main PDF Viewer component
const HighlighterPDFViewer = ({ fileId, filePath, isDarkTheme }) => {
  // State for PDF
  const [pdfUrl, setPdfUrl] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  
  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  
  // Refs
  const containerRef = useRef(null);
  const highlighterRef = useRef(null);
  
  // Redux and visibility
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  // Fetch PDF file
  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();
    let objectUrl = null;
    
    // Reset states
    setPdfUrl(null);
    setLoading(true);
    setError(null);
    setSearchResults([]);
    setCurrentSearchIndex(-1);
    
    const fetchFile = async () => {
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: { "session-id": localStorage.getItem("session_id") },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );
        
        if (isMounted) {
          objectUrl = URL.createObjectURL(response.data);
          setPdfUrl(objectUrl);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(err)) {
            console.log("Request canceled:", err.message);
          } else {
            console.error("Failed to fetch the PDF:", err);
            setError(`Failed to load the PDF. Please try again.`);
          }
        }
      }
    };
    
    if (fileId) {
      fetchFile();
    } else {
      setLoading(false);
      setError('No file ID provided. Cannot load the PDF.');
    }
    
    // Cleanup function
    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [fileId]);
  
  // Redux/Visibility effects
  useEffect(() => {
    dispatch(setViewerVisibility({ fileId, isVisible: inView }));
    if (inView) {
      dispatch(setFocusedViewer(fileId));
    }
  }, [inView, fileId, dispatch, filePath]);
  
  // Update Redux store when page changes
  useEffect(() => {
    if (numPages > 0) {
      dispatch(updatePdfViewer({
        fileId,
        filePath,
        page: pageNumber,
        isVisible: inView
      }));
    }
  }, [pageNumber, fileId, filePath, inView, dispatch, numPages]);
  
  // Document load handlers
  const onDocumentLoad = useCallback((pdfDocument) => {
    console.log(`Document loaded with ${pdfDocument.numPages} pages`);
    setNumPages(pdfDocument.numPages);
    setLoading(false);
    setPageNumber(1);
  }, []);
  
  // Navigation functions
  const goToPrevPage = useCallback(() => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  }, []);
  
  const goToNextPage = useCallback(() => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  }, [numPages]);
  
  // Zoom functions
  const zoomIn = useCallback(() => {
    setScale(prevScale => {
      const currentIndex = ZOOM_LEVELS.findIndex(level => level >= prevScale);
      const nextIndex = Math.min(currentIndex + 1, ZOOM_LEVELS.length - 1);
      return ZOOM_LEVELS[nextIndex];
    });
  }, []);
  
  const zoomOut = useCallback(() => {
    setScale(prevScale => {
      const currentIndex = ZOOM_LEVELS.findIndex(level => level >= prevScale);
      const prevIndex = Math.max(currentIndex - 1, 0);
      return ZOOM_LEVELS[prevIndex];
    });
  }, []);
  
  // Search functions
  const handleSearchChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);
  
  const handleSearch = useCallback(async () => {
    if (!searchQuery.trim() || !highlighterRef.current) {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      return;
    }
    
    try {
      // This is a simplified search implementation
      // In a real app, you would use the PDF.js text layer to find text positions
      const results = [];
      
      // For now, we'll just create dummy highlights for demonstration
      results.push({
        id: `search-0`,
        content: { text: searchQuery },
        position: {
          boundingRect: {
            x1: 100,
            y1: 150,
            x2: 300,
            y2: 180,
            width: 200,
            height: 30,
            pageNumber: pageNumber
          },
          rects: [
            {
              x1: 100,
              y1: 150,
              x2: 300,
              y2: 180,
              width: 200,
              height: 30,
              pageNumber: pageNumber
            }
          ],
          pageNumber: pageNumber
        }
      });
      
      setSearchResults(results);
      
      if (results.length > 0) {
        setCurrentSearchIndex(0);
        
        // Scroll to the first result
        if (highlighterRef.current) {
          highlighterRef.current.scrollTo(results[0]);
        }
      } else {
        setCurrentSearchIndex(-1);
      }
    } catch (error) {
      console.error('Error during search:', error);
    }
  }, [searchQuery, pageNumber]);
  
  // Handle highlight click
  const handleHighlightClick = useCallback((highlight) => {
    console.log('Highlight clicked:', highlight);
  }, []);
  
  return (
    <div ref={ref} className={isDarkTheme ? 'bp5-dark' : ''} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      {/* Toolbar */}
      <Card style={{ padding: '5px', marginBottom: '5px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Navigation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <ButtonGroup>
              <Button icon="chevron-left" onClick={goToPrevPage} disabled={pageNumber <= 1} />
              <div style={{ padding: '0 10px', display: 'flex', alignItems: 'center' }}>
                Page {pageNumber} of {numPages || '?'}
              </div>
              <Button icon="chevron-right" onClick={goToNextPage} disabled={pageNumber >= numPages} />
            </ButtonGroup>
          </div>
          
          {/* Zoom and rotation controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <ButtonGroup>
              <Button icon={<RotateCcw size={16} />} onClick={() => setRotation(prev => (prev - 90 + 360) % 360)} />
              <Button icon={<RotateCw size={16} />} onClick={() => setRotation(prev => (prev + 90) % 360)} />
            </ButtonGroup>
            
            <ButtonGroup>
              <Button icon={<ZoomOut size={16} />} onClick={zoomOut} />
              <HTMLSelect
                options={ZOOM_LEVELS.map(level => ({ label: `${Math.round(level * 100)}%`, value: level }))}
                value={scale}
                onChange={e => setScale(parseFloat(e.target.value))}
                style={{ width: '100px' }}
              />
              <Button icon={<ZoomIn size={16} />} onClick={zoomIn} />
            </ButtonGroup>
          </div>
          
          {/* Search controls */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <Input
              placeholder="Search..."
              value={searchQuery}
              onChange={handleSearchChange}
              onPressEnter={handleSearch}
              style={{ width: '200px' }}
            />
            <Button icon={<Search size={16} />} onClick={handleSearch} />
          </div>
        </div>
      </Card>
      
      {/* PDF Viewer */}
      <div ref={containerRef} style={{ flex: 1, overflow: 'auto', position: 'relative' }}>
        {error ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
            <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>
            <Button intent="primary" onClick={() => window.location.reload()}>Retry</Button>
          </div>
        ) : loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
            <Spinner size={50} />
          </div>
        ) : (
          <PdfLoader
            url={pdfUrl}
            beforeLoad={<Spinner size={50} />}
            onError={(error) => setError(`Failed to load PDF: ${error.message}`)}
            onDocumentLoad={onDocumentLoad}
          >
            {(pdfDocument) => (
              <PdfHighlighter
                ref={highlighterRef}
                pdfDocument={pdfDocument}
                enableAreaSelection={false}
                highlights={searchResults}
                onScrollChange={() => {}}
                scrollRef={containerRef}
                onSelectionFinished={() => {}}
                highlightTransform={(highlight, index, setTip, hideTip, viewportToScaled, screenshot, isScrolledTo) => {
                  const isSearchResult = highlight.id && highlight.id.startsWith('search-');
                  
                  return (
                    <Highlight
                      key={highlight.id || index}
                      position={highlight.position}
                      onClick={() => handleHighlightClick(highlight)}
                      isScrolledTo={isScrolledTo}
                    />
                  );
                }}
                getPageWidth={() => Math.floor(800 * scale)}
                getPageHeight={() => Math.floor(1100 * scale)}
                getScale={() => scale}
                getRotation={() => rotation}
              />
            )}
          </PdfLoader>
        )}
      </div>
    </div>
  );
};

export default HighlighterPDFViewer;
