INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:root:File MIME type: application/pdf
INFO:root:Directly convertible to PDF: application/pdf
INFO:root:Exporting to PDF...
WARNING:googleapiclient.http:Encountered 403 Forbidden with reason "fileNotExportable"
ERROR:root:Error on file V-161.pdf: <HttpError 403 when requesting https://www.googleapis.com/drive/v3/files/15U8AExppNlwncz_KGUFyzYRAH76GupbG/export?mimeType=application%2Fpdf&alt=media returned "Export only supports Docs Editors files.". Details: "[{'message': 'Export only supports Docs Editors files.', 'domain': 'global', 'reason': 'fileNotExportable'}]">
INFO:root:File MIME type: application/pdf
INFO:root:Directly convertible to PDF: application/pdf
INFO:root:Exporting to PDF...
WARNING:googleapiclient.http:Encountered 403 Forbidden with reason "fileNotExportable"
ERROR:root:Error on file V-127.pdf: <HttpError 403 when requesting https://www.googleapis.com/drive/v3/files/17kF9ILUn_RTYtVD_dmNUcTMNJZ5WFSis/export?mimeType=application%2Fpdf&alt=media returned "Export only supports Docs Editors files.". Details: "[{'message': 'Export only supports Docs Editors files.', 'domain': 'global', 'reason': 'fileNotExportable'}]">
INFO:root:File MIME type: application/pdf
INFO:root:Directly convertible to PDF: application/pdf
INFO:root:Exporting to PDF...
WARNING:googleapiclient.http:Encountered 403 Forbidden with reason "fileNotExportable"
ERROR:root:Error on file V-111.pdf: <HttpError 403 when requesting https://www.googleapis.com/drive/v3/files/17Yb7PDuv3JJYpi9Ai-zwpPYStPhj6TNO/export?mimeType=application%2Fpdf&alt=media returned "Export only supports Docs Editors files.". Details: "[{'message': 'Export only supports Docs Editors files.', 'domain': 'global', 'reason': 'fileNotExportable'}]">
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
