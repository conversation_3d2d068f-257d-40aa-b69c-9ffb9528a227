#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Tue Oct  3 12:57:39 2023

@author: lui<PERSON><PERSON><PERSON>
"""

from fastapi import FastAPI, HTTPException, Response, Depends
import nest_asyncio
from fastapi.middleware.cors import CORSMiddleware
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseDownload
import gspread
import uvicorn
from oauth2client.service_account import ServiceAccountCredentials
import io
import pandas as pd 
import time
import gspread

# Apply nest_asyncio to allow asynchronous operations in Jupyter
nest_asyncio.apply()

app = FastAPI()

scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/spreadsheets",
         "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_name("gsheetsKeys.json", scope)
client = gspread.authorize(creds)


origins = [
    "http://localhost:3000", "http://**********:3000/","http://localhost:3001", # React development server
]


app.add_middleware(
    CORSMiddleware,
    allow_origins=origins ,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Build the Google Drive service
service = build('drive', 'v3', credentials=creds)

def fetch_data_from_sheet_with_gspread(sheet_name="HE_Data Collection template Fix Equipment", tab_name="Files"):
    # Open the Google Sheet using its name
    sheet = client.open(sheet_name)

    # Select the tab/sheet
    worksheet = sheet.worksheet(tab_name)

    # Get the records
    records = worksheet.get_all_records()
  
    return  records



def fetch_files_from_folder(folder_id, current_path=""):
    items = []
    # Pagination
    page_token = None
    while True:
        # Query the files in the current folder with pagination support
        response = service.files().list(q=f"'{folder_id}' in parents and trashed=false", 
                                        fields="nextPageToken, files(id, name, mimeType, parents)",
                                        pageToken=page_token).execute()
        
        files = response.get('files', [])
        for file in files:
            file_path = f"{current_path}/{file['name']}"
            items.append({
                "id": file['id'],
                "name": file['name'],
                "mimeType": file['mimeType'],
                "path": file_path
            })

            # If the current item is a folder, fetch its content recursively
            if file['mimeType'] == "application/vnd.google-apps.folder":
                items.extend(fetch_files_from_folder(file['id'], file_path))

        # Update the page token for the next iteration
        page_token = response.get('nextPageToken', None)
        if page_token is None:
            break
        

    return items

@app.get("/list-files/")
def list_files():
    root_folder_id = "1Re9r_zZqz9W19dl4INwkKKOyBkFOK3Fm"
    return {"files": fetch_files_from_folder(root_folder_id)}


@app.get("/list-files-static/")
def file_list_static():
    try:
        data = fetch_data_from_sheet_with_gspread()
        return {"files": data}
    except gspread.exceptions.GSpreadException as ge:
        raise HTTPException(status_code=400, detail=str(ge))
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching the data: {str(e)}")







MS_MIME_TYPES = {
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'application/vnd.google-apps.spreadsheet',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'application/vnd.google-apps.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'application/vnd.google-apps.presentation',
    'application/msword': 'application/vnd.google-apps.document'
}

@app.get("/get-file/{file_id}")
def get_file(file_id: str):
    try:
        # Fetch file metadata
        file_info = service.files().get(fileId=file_id).execute()

        # Log the file's MIME type
        print(f"File MIME type: {file_info['mimeType']}")

        # If the file is already in PDF format
        if file_info['mimeType'] == 'application/pdf':
            print("File is already in PDF format...")
            request = service.files().get_media(fileId=file_id)
            fh = io.BytesIO()
            downloader = MediaIoBaseDownload(fh, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk()
            fh.seek(0)
            return Response(content=fh.read(), media_type='application/pdf')

        # If the file is an image format
        elif file_info['mimeType'] in ['image/jpeg', 'image/png', 'image/gif', 'image/bmp', 'image/tiff']:
            print("Converting image to PDF...")
            request = service.files().get_media(fileId=file_id)
            image_data = io.BytesIO()
            downloader = MediaIoBaseDownload(image_data, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk()
            image = Image.open(image_data)
            pdf_data = io.BytesIO()
            image.save(pdf_data, "PDF")
            pdf_data.seek(0)
            return Response(content=pdf_data.read(), media_type='application/pdf')

        # If it's a Microsoft Office file, convert it to the corresponding Google format
        elif file_info['mimeType'] in MS_MIME_TYPES:
            print(f"Converting {file_info['mimeType']} to Google Apps format...")
            converted_file = service.files().copy(
                fileId=file_id, 
                body={"name": file_info['name']+"_temp", "mimeType": MS_MIME_TYPES[file_info['mimeType']]}
            ).execute()
            temp_file_id = converted_file['id']

            # Wait until the converted file is ready
            for _ in range(10):
                check_file = service.files().get(fileId=temp_file_id).execute()
                if 'mimeType' in check_file:
                    print(_)
                    break
                time.sleep(1)

            # Export the file (either the original or the converted one) to PDF
            print("Exporting to PDF...")
            request_id = temp_file_id if temp_file_id else file_id
            request = service.files().export_media(fileId=request_id, mimeType='application/pdf')

            fh = io.BytesIO()
            downloader = MediaIoBaseDownload(fh, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk()

            fh.seek(0)

            # If we created a temporary Google file, delete it
            if temp_file_id:
                print("Deleting temporary file...")
                service.files().delete(fileId=temp_file_id).execute()

            return Response(content=fh.read(), media_type='application/pdf')

    except HttpError as e:
        error_details = e._get_reason()
        print(f"HTTP Error: {error_details}")
        raise HTTPException(status_code=400, detail=f"Error fetching the file from Google Drive: {error_details}")
    except Exception as e:
        print(f"General Error: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Error fetching the file: {str(e)}")

    try:
        # Fetch file metadata
        file_info = service.files().get(fileId=file_id).execute()

        # Log the file's MIME type
        print(f"File MIME type: {file_info['mimeType']}")

        temp_file_id = None

        # If it's a Microsoft Office file, convert it to the corresponding Google format
        if file_info['mimeType'] in MS_MIME_TYPES:
            print(f"Converting {file_info['mimeType']} to Google Apps format...")
            converted_file = service.files().copy(
                fileId=file_id, 
                body={"name": file_info['name']+"_temp", "mimeType": MS_MIME_TYPES[file_info['mimeType']]}
            ).execute()
            temp_file_id = converted_file['id']

            # Wait until the converted file is ready
            for _ in range(10):
                check_file = service.files().get(fileId=temp_file_id).execute()
                if 'mimeType' in check_file:
                    print(_)
                    break
                time.sleep(1)

        # Export the file (either the original or the converted one) to PDF
        print("Exporting to PDF...")
        request_id = temp_file_id if temp_file_id else file_id
        request = service.files().export_media(fileId=request_id, mimeType='application/pdf')

        fh = io.BytesIO()
        downloader = MediaIoBaseDownload(fh, request)
        done = False
        while done is False:
            status, done = downloader.next_chunk()

        fh.seek(0)

        # If we created a temporary Google file, delete it
        if temp_file_id:
            print("Deleting temporary file...")
            service.files().delete(fileId=temp_file_id).execute()

        return Response(content=fh.read(), media_type='application/pdf')

    except HttpError as e:
        error_details = e._get_reason()
        print(f"HTTP Error: {error_details}")
        raise HTTPException(status_code=400, detail=f"Error fetching the file from Google Drive: {error_details}")
    except Exception as e:
        print(f"General Error: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Error fetching the file: {str(e)}")

# Start Uvicorn running on port 8020

uvicorn.run(app, host="**********", port=8020)
