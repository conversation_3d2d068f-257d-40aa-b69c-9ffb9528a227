{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" * Serving Flask app '__main__'\n", " * Debug mode: off\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.\n", " * Running on http://*************:3000\n", "Press CTRL+C to quit\n", "************* - - [02/Oct/2023 16:06:33] \"GET / HTTP/1.1\" 401 -\n", "************* - - [02/Oct/2023 16:06:34] \"GET / HTTP/1.1\" 401 -\n", "************* - - [02/Oct/2023 16:06:35] \"GET /favicon.ico HTTP/1.1\" 401 -\n"]}], "source": ["from flask import Flask, send_from_directory\n", "from flask_httpauth import HTTPBasicAuth\n", "import os\n", "\n", "app = Flask(__name__, static_folder='/Users/<USER>/Documents/DataCollectionApp/test/build')\n", "auth = HTTPBasicAuth()\n", "\n", "PASSWORD = \"visualaim\"\n", "\n", "@auth.verify_password\n", "def verify_password(username, password):\n", "    return password == PASSWORD\n", "\n", "@app.route('/<path:filename>')\n", "@auth.login_required\n", "def serve_static(filename):\n", "    return send_from_directory(app.static_folder, filename)\n", "\n", "@app.route('/')\n", "@auth.login_required\n", "def index():\n", "    return send_from_directory(app.static_folder, 'index.html')\n", "\n", "if __name__ == '__main__':\n", "    app.run(host='*************', port=3000)\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}