{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'Construction Code': [{'key': 'API 12B', 'Template': 'Data ST'}, {'key': 'API 12D', 'Template': 'Data ST'}, {'key': 'API 12F', 'Template': 'Data ST'}, {'key': 'API 620', 'Template': 'Data ST'}, {'key': 'API 650', 'Template': 'Data ST'}, {'key': 'ASME SECTION VIlI Div 1', 'Template': 'Data PV'}, {'key': 'ASME SECTION I', 'Template': 'Data PV'}, {'key': 'ASME B31.1', 'Template': 'Data Piping'}, {'key': 'ASME B31.3', 'Template': 'Data Piping'}, {'key': 'ASME B31.4', 'Template': 'Data Piping'}, {'key': 'ASME B31.8', 'Template': 'Data Piping'}, {'key': 'UNKNOWN', 'Template': 'Data ST, Data Piping, Data PV, HEAT EXCHANGER, PRESSURE RELIEF DEVICE'}, {'key': 'ASME STS-1', 'Template': 'Data ST'}, {'key': 'ASTM D1998', 'Template': 'Data ST'}, {'key': 'API 520', 'Template': 'PRESSURE RELIEF DEVICE'}], 'Equipment Type': [{'key': 'CONDENSER', 'Template': 'Data PV'}, {'key': 'EVAPORATOR', 'Template': 'Data PV'}, {'key': 'HEAT EXCHANGER', 'Template': 'Data PV'}, {'key': 'REBOILER', 'Template': 'Data PV'}, {'key': 'CHUTE', 'Template': 'MISCELLANEOUS'}, {'key': 'CONTAINMENT', 'Template': 'MISCELLANEOUS'}, {'key': 'DESUPERHEATER', 'Template': 'MISCELLANEOUS'}, {'key': 'DUCT', 'Template': 'MISCELLANEOUS'}, {'key': 'EXPANSION JOINT', 'Template': 'MISCELLANEOUS'}, {'key': 'FLAME ARRESTOR', 'Template': 'MISCELLANEOUS'}, {'key': 'FLUME', 'Template': 'MISCELLANEOUS'}, {'key': 'HOPPER', 'Template': 'MISCELLANEOUS'}, {'key': 'PIT', 'Template': 'MISCELLANEOUS'}, {'key': 'WELL', 'Template': 'MISCELLANEOUS'}, {'key': 'BACKFLOW PREVENTER', 'Template': 'Data Piping'}, {'key': 'BLOW DOWN PIPING', 'Template': 'Data Piping'}, {'key': 'BLOW LINE', 'Template': 'Data Piping'}, {'key': 'BOILER EXTERNAL PIPING', 'Template': 'Data Piping'}, {'key': 'CHECK VALVE', 'Template': 'Data Piping'}, {'key': 'COIL', 'Template': 'Data Piping'}, {'key': 'HOSE', 'Template': 'Data Piping'}, {'key': 'HP PNEUMATIC PIPING', 'Template': 'Data Piping'}, {'key': 'LP PNEUMATIC PIPING', 'Template': 'Data Piping'}, {'key': 'PIPE', 'Template': 'Data Piping'}, {'key': 'PROCESS SEWER', 'Template': 'Data Piping'}, {'key': 'SANITARY SEWER', 'Template': 'Data Piping'}, {'key': 'UNDERGROUND PIPING', 'Template': 'Data Piping'}, {'key': 'VALVE', 'Template': 'Data Piping'}, {'key': 'CONSERVATION VENT', 'Template': 'PRESSURE RELIEF DEVICE'}, {'key': 'EXPLOSION HATCH', 'Template': 'PRESSURE RELIEF DEVICE'}, {'key': 'RUPTURE DISC', 'Template': 'PRESSURE RELIEF DEVICE'}, {'key': 'RUPTURE PIN', 'Template': 'PRESSURE RELIEF DEVICE'}, {'key': 'VACUUM BREAKER', 'Template': 'PRESSURE RELIEF DEVICE'}, {'key': 'AIR RECEIVER', 'Template': 'Data PV'}, {'key': 'BATCH DIGESTER', 'Template': 'Data PV'}, {'key': 'BIOMASS BOILER', 'Template': 'Data PV'}, {'key': 'BOILER', 'Template': 'Data PV'}, {'key': 'CONTINUOUS DIGESTER', 'Template': 'Data PV'}, {'key': 'CYCLONE', 'Template': 'Data PV'}, {'key': 'DRYER CAN', 'Template': 'Data PV'}, {'key': 'ECONOMIZER', 'Template': 'Data PV'}, {'key': 'FILTER', 'Template': 'Data PV'}, {'key': 'FIRED PRESSURE VESSEL', 'Template': 'Data PV'}, {'key': 'HOT WATER HEATER', 'Template': 'Data PV'}, {'key': 'JACKET', 'Template': 'Data PV'}, {'key': 'POWER BOILER', 'Template': 'Data PV'}, {'key': 'PRE HEATER', 'Template': 'Data PV'}, {'key': 'REACTOR', 'Template': 'Data PV'}, {'key': 'RECOVERY BOILER', 'Template': 'Data PV'}, {'key': 'SCRUBBER', 'Template': 'Data PV'}, {'key': 'SEAL POT', 'Template': 'Data PV'}, {'key': 'SEPARATOR', 'Template': 'Data PV'}, {'key': 'SLAKER', 'Template': 'Data PV'}, {'key': 'SMELT SPOUT', 'Template': 'Data PV'}, {'key': 'STACK', 'Template': 'Data PV'}, {'key': 'STRIPPER', 'Template': 'Data PV'}, {'key': 'UNFIRED PRESSURE VESSEL', 'Template': 'Data PV'}, {'key': 'VESSEL', 'Template': 'Data PV'}, {'key': 'YANKEE DRYER', 'Template': 'Data PV'}, {'key': 'ATMOSPHERIC STORAGE TANK', 'Template': 'Data ST'}, {'key': 'CHEST', 'Template': 'Data ST'}, {'key': 'ABOVEGROUND STORAGE TANK', 'Template': 'Data ST'}, {'key': 'FIBERGLASS TANK', 'Template': 'Data ST'}, {'key': 'POLYETHYLENE TANK', 'Template': 'Data ST'}, {'key': 'CLARIFIER', 'Template': 'Data ST'}, {'key': 'DISSOLVING TANK', 'Template': 'Data ST'}, {'key': 'LOW PRESSURE STORAGE TANK', 'Template': 'Data ST'}, {'key': 'PULPER', 'Template': 'Data ST'}, {'key': 'SILO', 'Template': 'Data ST'}, {'key': 'STAND PIPE', 'Template': 'Data ST'}, {'key': 'STORAGE TANK', 'Template': 'Data ST'}], 'Component Type': [{'key': 'SHELL COURSE', 'Template': 'Data ST'}, {'key': 'ROOF', 'Template': 'Data ST'}, {'key': 'BOTTOM', 'Template': 'Data ST'}, {'key': 'SHELL', 'Template': 'Data PV'}, {'key': 'HEAD', 'Template': 'Data PV'}, {'key': 'CHANNEL', 'Template': 'Data PV'}]}\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_1337/**********.py:95: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  value = row[i] if i < len(df.columns) else None\n", "/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_1337/**********.py:96: FutureWarning: Series.__getitem__ treating keys as positions is deprecated. In a future version, integer keys will always be treated as labels (consistent with DataFrame behavior). To access a value by position, use `ser.iloc[pos]`\n", "  asset_classification = row[i+1] if i+1 < len(df.columns) else None\n"]}], "source": ["import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "from google.cloud import secretmanager\n", "from google.oauth2 import service_account\n", "import json\n", "# Replace this with the JSON credentials for the main service account\n", "main_credentials_json = {\n", "    \"type\": \"service_account\",\n", "    \"project_id\": \"ocr-331616\",\n", "    \"private_key_id\": \"d0f23c19acbd18a918a986754ef4779ebb759153\",\n", "    \"private_key\": \"-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDdeySX6oUv7G1U\\n2YlX1mwqFQv+n4RFe3WS5c+c3eLSiBOIzWIMwJ+oKXLMFxYIgcEuViySPsuVXXUS\\nDFam28anFj5MycXyVyHHGIx+io5KgAgPiuplFzhJHh6iJYspO6LqkJH5Q1qvxwH5\\n8LZ+Psa5qglHKStWHxKnag0aQshUtbY8dUjNkj3i9TqDf6JAC/xp86VyKwfvyWbD\\nkvhYMi48O2q2M3sy2+DOm06aTelqPtJL6zvqIvJT1SpoUPegTeG4TFl0lDbLGTyQ\\nOy8lLeYfMO1PjduLb3QH/f6/yK9X+8EBOLTrrZ5ym1aJpsbqUEc7i8mhIQK6Q2F9\\nZOrs8of/AgMBAAECggEAB4FY8UWduMzavEbY4XSfMA023ayF0h2dE4Vyc8mNxP5J\\nJy11NqMVEKDtm8k70mhPOw+ULWqFAe1A/oO+MKOpnNeN9JKGgNqqBtyn73vTgZEg\\nH3gLebHVPZ5l1wR4WEUwlUv2iQtawzJ2Yy613cZT0U6CnPo3VpDxVAytyqeGXAnH\\nho5sJPeePHNBxae5TI0T/OSfgRUGxR+d2s9ZBqgHhcde+hz1Eim+JdkHLB50OKaM\\nNLgfL+eM5LuVxYmqoEChw+BaFH9Bt8tQ1flqbb7Sl0VcAIDAHu5SF3Hz1q9lpyTy\\n78HBCEwpbPqaVcTGeCcvOdq2N+lYoumwCAtWVhI6hQKBgQD0WtYKEHSIK7/6ADih\\nn9YqzW4XNOy1S5Blja4d7hOcXc7bVUYG5jQGOjmQMKAdHz9qiWo5CTCNA5jLBhoj\\noikCfoVtVQvkKY95Urz6zCIMUQ9gn1qkX3DQqCt170o1Lco0JUpEGP9HYcg4ivDR\\nLCxvrQ5e4aE9JmF0os24i1QNLQKBgQDoCT5CSVVc3xfLKCLU3hs0pxNVNqteNYRW\\nZ6/2VD/wjGuW1a++Skt8LjsClEu4TOg2uBxjuTBloGj2kWfvL+SCfQZYoXttCqmQ\\nEH0Z84BGdSCnUBKPX1rpLeeO3ptfHAqxuPGO4kI/MCzlqTRizGCpdme8yc76NTnd\\ncDWr1LrdWwKBgGSPuYR8zyh3MNOXIbdKS15kGsmMK1pVFcDldOG2Z/WzGX1kc6RF\\nOuXRMn1ntY0mnfefQ8rjQErYTxpGkZ1WkCAJp6meFX25qOUaKtHgDqcY+QYfrrL0\\n4D3O58vqRZTrGkspiY3K58wdrsOuUnsS/dqb0s0zeCLtV7AjjBxForqlAoGAeU5L\\nxC++dt0S/VywSQUMyAwIqWX5VTk5RErUzTziaImgvaxdQmho6jTpGMIai/F4/RJn\\nlSL90EN6XF1MLtmk5yshpDhX5HvMhcp4/JY2m58kQRQ1f8rkJj+fbSunoqNwW9VK\\nQ965r3sqHa7F+ljKuA100lfW4yvsYn4FMR33yz0CgYEA35qgFkSQqrt9xXhh1BD+\\nPYjM8vJG6bFrA+oTBDBI4QMXaBQMGLBH0ReWGbd7OpFMqUsrryDnT1X/Z1t6qbog\\nHkbSjPTTCmSQO1wV9ueyyT5Wfre0wZsizTfFm+PD8PWbjq3EvllKx/gzlt4HYTDd\\nkdRqT995CggY2SbkR8sakRE=\\n-----END PRIVATE KEY-----\\n\",\n", "    \"client_email\": \"<EMAIL>\",\n", "    \"client_id\": \"106632634099696580807\",\n", "    \"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\n", "    \"token_uri\": \"https://oauth2.googleapis.com/token\",\n", "    \"auth_provider_x509_cert_url\": \"https://www.googleapis.com/oauth2/v1/certs\",\n", "    \"client_x509_cert_url\": \"https://www.googleapis.com/robot/v1/metadata/x509/luigi-367%40ocr-331616.iam.gserviceaccount.com\",\n", "    \"universe_domain\": \"googleapis.com\"\n", "}\n", "\n", "main_credentials = service_account.Credentials.from_service_account_info(main_credentials_json)\n", "\n", "# Initialize the Secret Manager client with the main service account\n", "secret_manager_client = secretmanager.SecretManagerServiceClient(credentials=main_credentials)\n", "\n", "def get_user_specific_secret(user, project_id):\n", "    # Map the user's email to the secret name\n", "    # This is a simple example; consider using a more secure/complex mapping strategy\n", "    secret_id_mapping = {\n", "        '<EMAIL>':  'lsalemi-DEV',\n", "        # Add more mappings for other users if needed\n", "    }\n", "    secret_id = secret_id_mapping.get(user)\n", "    if not secret_id:\n", "        raise ValueError(f\"No secret found for user {user}\")\n", "\n", "    # Construct the resource name of the secret version\n", "    name = f\"projects/{project_id}/secrets/{secret_id}/versions/latest\"\n", "\n", "    # Access the secret version and decode the payload\n", "    response = secret_manager_client.access_secret_version(request={\"name\": name})\n", "    user_secret_json = response.payload.data.decode(\"UTF-8\")\n", "\n", "    # Parse the JSON string into a dictionary\n", "    user_secret_dict = json.loads(user_secret_json)\n", "\n", "    return user_secret_dict\n", "\n", "scope = [\"https://spreadsheets.google.com/feeds\", \"https://www.googleapis.com/auth/spreadsheets\",\n", "         \"https://www.googleapis.com/auth/drive.file\", \"https://www.googleapis.com/auth/drive\"]\n", "\n", "# Get the credentials for the user '<EMAIL>'\n", "user_credentials_dict = get_user_specific_secret('<EMAIL>', 'ocr-331616')\n", "\n", "# Create credentials using the user-specific credentials\n", "user_credentials = ServiceAccountCredentials.from_json_keyfile_dict(user_credentials_dict, scope)\n", "client = gspread.authorize(user_credentials)\n", "\n", "# Open Google Sheet using its specific name\n", "sheet_name = \"DEV_Demo_Data Collection template Fix Equipment\"\n", "sheet = client.open(sheet_name)\n", "\n", "# Get data from the desired worksheet\n", "worksheet_name = \"Dropdown Rules\"  # Replace with the desired worksheet name\n", "ws = sheet.worksheet(worksheet_name)\n", "data = ws.get_all_values()\n", "\n", "# get data as a df and print it\n", "\n", "\n", "import pandas as pd\n", "\n", "# 'data' should be a list of lists from the worksheet\n", "# Convert the 'data' to a DataFrame\n", "df = pd.DataFrame(data[2:], columns=data[0])\n", "\n", "# Remove empty columns that are full of empty strings\n", "df.replace('', pd.NA, inplace=True)\n", "df.dropna(how='all', axis=1, inplace=True)\n", "\n", "# Initialize the resulting JSON structure\n", "result_json = {}\n", "\n", "# Iterate over the DataFrame by stepping through each pair of columns\n", "for i in range(0, len(df.columns), 2):\n", "    main_header = df.columns[i]  # This is your 'Construction Code', 'Asset Classification', etc.\n", "    sub_header = df.columns[i+1]  # This is your 'Asset Classification' for each main header\n", "\n", "    # Prepare the list for the current main header\n", "    result_json[main_header] = []\n", "\n", "    # Iterate over each row for the current pair of columns\n", "    for index, row in df.iterrows():\n", "        # Get the value and asset classification for the current row\n", "        value = row[i] if i < len(df.columns) else None\n", "        asset_classification = row[i+1] if i+1 < len(df.columns) else None\n", "\n", "        # If both are None (or <PERSON><PERSON> after replacement), skip this row\n", "        if pd.isna(value) and pd.isna(asset_classification):\n", "            continue\n", "\n", "        # Create a dictionary for the current row and append it to the result\n", "        result_json[main_header].append({\n", "            'key': value,\n", "            'Template': asset_classification.replace(\"STORAGE TANK\",\"Data ST\").replace(\"PRESSURE VESSEL\",\"Data PV\").replace(\"PIPE\",\"Data Piping\")\n", "        })\n", "\n", "# Remove any main headers with empty lists\n", "result_json = {k: v for k, v in result_json.items() if v}\n", "\n", "# Print or use the result_json as needed\n", "print(result_json)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Construction Code': [{'key': 'API 12B', 'Template': 'Data ST'},\n", "  {'key': 'API 12D', 'Template': 'Data ST'},\n", "  {'key': 'API 12F', 'Template': 'Data ST'},\n", "  {'key': 'API 620', 'Template': 'Data ST'},\n", "  {'key': 'API 650', 'Template': 'Data ST'},\n", "  {'key': 'ASME SECTION VIlI Div 1', 'Template': 'Data PV'},\n", "  {'key': 'ASME SECTION I', 'Template': 'Data PV'},\n", "  {'key': 'ASME B31.1', 'Template': 'Data Piping'},\n", "  {'key': 'ASME B31.3', 'Template': 'Data Piping'},\n", "  {'key': 'ASME B31.4', 'Template': 'Data Piping'},\n", "  {'key': 'ASME B31.8', 'Template': 'Data Piping'},\n", "  {'key': 'UNKNOWN',\n", "   'Template': 'Data ST, Data Piping, Data PV, HEAT EXCHANGER, PRESSURE RELIEF DEVICE'},\n", "  {'key': 'ASME STS-1', 'Template': 'Data ST'},\n", "  {'key': 'ASTM D1998', 'Template': 'Data ST'},\n", "  {'key': 'API 520', 'Template': 'PRESSURE RELIEF DEVICE'}],\n", " 'Equipment Type': [{'key': 'CONDENSER', 'Template': 'Data PV'},\n", "  {'key': 'EVAPORATOR', 'Template': 'Data PV'},\n", "  {'key': 'HEAT EXCHANGER', 'Template': 'Data PV'},\n", "  {'key': 'RE<PERSON><PERSON><PERSON>', 'Template': 'Data PV'},\n", "  {'key': 'CHUTE', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'CONTAINMENT', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'DESUPERHEATER', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'DUCT', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'EXPANSION JOINT', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'FLAME ARRESTOR', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'FLUME', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'HOPPER', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'PIT', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'WELL', 'Template': 'MISCELLANEOUS'},\n", "  {'key': 'BACKFLOW PREVENTER', 'Template': 'Data Piping'},\n", "  {'key': 'BLOW DOWN PIPING', 'Template': 'Data Piping'},\n", "  {'key': 'BLOW LINE', 'Template': 'Data Piping'},\n", "  {'key': 'BOILER EXTERNAL PIPING', 'Template': 'Data Piping'},\n", "  {'key': 'CHECK VALVE', 'Template': 'Data Piping'},\n", "  {'key': 'COIL', 'Template': 'Data Piping'},\n", "  {'key': 'HOSE', 'Template': 'Data Piping'},\n", "  {'key': 'HP PNEUMATIC PIPING', 'Template': 'Data Piping'},\n", "  {'key': 'LP PNEUMATIC PIPING', 'Template': 'Data Piping'},\n", "  {'key': 'PIPE', 'Template': 'Data Piping'},\n", "  {'key': 'PROCESS SEWER', 'Template': 'Data Piping'},\n", "  {'key': 'SANITARY SEWER', 'Template': 'Data Piping'},\n", "  {'key': 'UNDERGROUND PIPING', 'Template': 'Data Piping'},\n", "  {'key': 'VALVE', 'Template': 'Data Piping'},\n", "  {'key': 'CONSERVATION VENT', 'Template': 'PRESSURE RELIEF DEVICE'},\n", "  {'key': 'EXPL<PERSON><PERSON> HATCH', 'Template': 'PRESSURE RELIEF DEVICE'},\n", "  {'key': 'RUPTURE DISC', 'Template': 'PRESSURE RELIEF DEVICE'},\n", "  {'key': 'RUPTURE PIN', 'Template': 'PRESSURE RELIEF DEVICE'},\n", "  {'key': 'VACUUM BREAKER', 'Template': 'PRESSURE RELIEF DEVICE'},\n", "  {'key': 'AIR RECEIVER', 'Template': 'Data PV'},\n", "  {'key': 'BATCH DIGESTER', 'Template': 'Data PV'},\n", "  {'key': 'BIOMASS BOILER', 'Template': 'Data PV'},\n", "  {'key': 'BOILER', 'Template': 'Data PV'},\n", "  {'key': 'CONTINUOUS DIGESTER', 'Template': 'Data PV'},\n", "  {'key': 'CYCLONE', 'Template': 'Data PV'},\n", "  {'key': 'DRYER CAN', 'Template': 'Data PV'},\n", "  {'key': '<PERSON><PERSON><PERSON><PERSON>Z<PERSON>', 'Template': 'Data PV'},\n", "  {'key': 'FILTER', 'Template': 'Data PV'},\n", "  {'key': 'FIRED PRESSURE VESSEL', 'Template': 'Data PV'},\n", "  {'key': 'HOT WATER HEATER', 'Template': 'Data PV'},\n", "  {'key': 'JACKET', 'Template': 'Data PV'},\n", "  {'key': 'POWER BOILER', 'Template': 'Data PV'},\n", "  {'key': 'PRE HEATER', 'Template': 'Data PV'},\n", "  {'key': 'REACTOR', 'Template': 'Data PV'},\n", "  {'key': 'RECOVERY BOILER', 'Template': 'Data PV'},\n", "  {'key': 'SCRUBBER', 'Template': 'Data PV'},\n", "  {'key': 'SEAL POT', 'Template': 'Data PV'},\n", "  {'key': 'SEPARATOR', 'Template': 'Data PV'},\n", "  {'key': 'SLAKER', 'Template': 'Data PV'},\n", "  {'key': 'SMELT SPOUT', 'Template': 'Data PV'},\n", "  {'key': 'STACK', 'Template': 'Data PV'},\n", "  {'key': 'STRIPPER', 'Template': 'Data PV'},\n", "  {'key': 'UNFIRED PRESSURE VESSEL', 'Template': 'Data PV'},\n", "  {'key': 'VESSEL', 'Template': 'Data PV'},\n", "  {'key': 'YANKE<PERSON> DRYER', 'Template': 'Data PV'},\n", "  {'key': 'ATMOSPHERIC STORAGE TANK', 'Template': 'Data ST'},\n", "  {'key': 'CHEST', 'Template': 'Data ST'},\n", "  {'key': 'ABOVEGROUND STORAGE TANK', 'Template': 'Data ST'},\n", "  {'key': 'FIBERGLASS TANK', 'Template': 'Data ST'},\n", "  {'key': '<PERSON>OL<PERSON>ETHYLENE TANK', 'Template': 'Data ST'},\n", "  {'key': '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'Template': 'Data ST'},\n", "  {'key': 'DISSOLVING TANK', 'Template': 'Data ST'},\n", "  {'key': 'LOW PRESSURE STORAGE TANK', 'Template': 'Data ST'},\n", "  {'key': 'PULPER', 'Template': 'Data ST'},\n", "  {'key': 'SIL<PERSON>', 'Template': 'Data ST'},\n", "  {'key': 'STAND PIPE', 'Template': 'Data ST'},\n", "  {'key': 'STORAGE TANK', 'Template': 'Data ST'}],\n", " 'Component Type': [{'key': 'SHELL COURSE', 'Template': 'Data ST'},\n", "  {'key': 'ROOF', 'Template': 'Data ST'},\n", "  {'key': 'BOTTOM', 'Template': 'Data ST'},\n", "  {'key': 'SHELL', 'Template': 'Data PV'},\n", "  {'key': 'HEAD', 'Template': 'Data PV'},\n", "  {'key': 'CHANNEL', 'Template': 'Data PV'}]}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result_json"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Construction Code</th>\n", "      <th></th>\n", "      <th>Equipment Type</th>\n", "      <th></th>\n", "      <th>Component Type</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>API 12B</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>CONDENSER</td>\n", "      <td>PRESSURE VESSEL</td>\n", "      <td>SHELL COURSE</td>\n", "      <td>STORAGE TANK</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>API 12D</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>EVAPORATOR</td>\n", "      <td>PRESSURE VESSEL</td>\n", "      <td>ROOF</td>\n", "      <td>STORAGE TANK</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>API 12F</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>HEAT EXCHANGER</td>\n", "      <td>PRESSURE VESSEL</td>\n", "      <td>BOTTOM</td>\n", "      <td>STORAGE TANK</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>API 620</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>REBOILER</td>\n", "      <td>PRESSURE VESSEL</td>\n", "      <td>SHELL</td>\n", "      <td>PRESSURE VESSEL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>API 650</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>CHUTE</td>\n", "      <td>MISCELLANEOUS</td>\n", "      <td>HEAD</td>\n", "      <td>PRESSURE VESSEL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>LOW PRESSURE STORAGE TANK</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>PULPER</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>SILO</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>STAND PIPE</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "    <tr>\n", "      <th>70</th>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>STORAGE TANK</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>&lt;NA&gt;</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>71 rows × 6 columns</p>\n", "</div>"], "text/plain": ["   Construction Code                           Equipment Type  \\\n", "0            API 12B  STORAGE TANK                  CONDENSER   \n", "1            API 12D  STORAGE TANK                 EVAPORATOR   \n", "2            API 12F  STORAGE TANK             HEAT EXCHANGER   \n", "3            API 620  STORAGE TANK                   REBOILER   \n", "4            API 650  STORAGE TANK                      CHUTE   \n", "..               ...           ...                        ...   \n", "66              <NA>          <NA>  LOW PRESSURE STORAGE TANK   \n", "67              <NA>          <NA>                     PULPER   \n", "68              <NA>          <NA>                       SILO   \n", "69              <NA>          <NA>                 STAND PIPE   \n", "70              <NA>          <NA>               STORAGE TANK   \n", "\n", "                    Component Type                   \n", "0   PRESSURE VESSEL   SHELL COURSE     STORAGE TANK  \n", "1   PRESSURE VESSEL           ROOF     STORAGE TANK  \n", "2   PRESSURE VESSEL         BOTTOM     STORAGE TANK  \n", "3   PRESSURE VESSEL          SHELL  PRESSURE VESSEL  \n", "4     MISCELLANEOUS           HEAD  PRESSURE VESSEL  \n", "..              ...            ...              ...  \n", "66     STORAGE TANK           <NA>             <NA>  \n", "67     STORAGE TANK           <NA>             <NA>  \n", "68     STORAGE TANK           <NA>             <NA>  \n", "69     STORAGE TANK           <NA>             <NA>  \n", "70     STORAGE TANK           <NA>             <NA>  \n", "\n", "[71 rows x 6 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}