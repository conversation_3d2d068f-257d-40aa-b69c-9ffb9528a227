#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sun Oct  1 20:54:29 2023
@author: lui<PERSON><PERSON><PERSON>
"""
import uuid
import nest_asyncio
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI, BackgroundTasks,Response
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import uvicorn
import pandas as pd
from googleapiclient.errors import HttpError
import numpy as np
from queue import Queue
import threading
import time
from google.oauth2 import id_token
from google.auth.transport import requests as google_requests
from fastapi import HTTPException
import json
import jwt
from jwt import PyJWTError
from fastapi.security import OAuth2PasswordBearer
from dotenv import load_dotenv
from datetime import datetime, timedelta 
from fastapi import Cookie
import logging
from google.cloud import secretmanager
from google.oauth2 import service_account
from fastapi import WebSocket
import os
import sqlite3
from typing import List
logging.basicConfig(level=logging.DEBUG)
from fastapi import WebSocketDisconnect

import asyncio
# Apply nest_asyncio to allow asynchronous operations in the notebook
nest_asyncio.apply()
lock = threading.Lock()
app = FastAPI()
load_dotenv()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")
CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")

ALGORITHM = "HS256"
JWT_CUSTOM_CLAIM_KEY = os.getenv("JWT_CUSTOM_CLAIM_KEY")
JWT_CUSTOM_CLAIM_VALUE = os.getenv("JWT_CUSTOM_CLAIM_VALUE")
SECRET_KEY=os.getenv("SECRET_KEY")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000","http://localhost","https://DEV_Demodatacollectionapp.netlify.app"],  # Adjust to match your client's origin
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/spreadsheets",
         "https://www.googleapis.com/auth/drive.file", "https://www.googleapis.com/auth/drive"]

connected_clients: List[WebSocket] = []

main_credentials_json = ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

main_credentials = service_account.Credentials.from_service_account_info(main_credentials_json)

# Initialize the Secret Manager client with the main service account
secret_manager_client = secretmanager.SecretManagerServiceClient(credentials=main_credentials)

def get_user_specific_secret(user, project_id):
    # Map the user's email to the secret name
    # This is a simple example; consider using a more secure/complex mapping strategy
    secret_id_mapping = {
        '<EMAIL>':  'lsalemi-DEV',
        '<EMAIL>':    'etovar-OCI',
        '<EMAIL>':     'adiaz-OCI',
        '<EMAIL>':  'rfortier-OCI',
        '<EMAIL>': 'lsalemi-DEV',
    }
    secret_id = secret_id_mapping.get(user)
    if not secret_id:
        raise ValueError(f"No secret found for user {user}")

    # Construct the resource name of the secret version
    name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"

    # Access the secret version and decode the payload
    response = secret_manager_client.access_secret_version(request={"name": name})
    user_secret_json = response.payload.data.decode("UTF-8")

    # Parse the JSON string into a dictionary
    user_secret_dict = json.loads(user_secret_json)
    print("user_secret_found")
    return user_secret_dict

def getCredentialsFromClient(user):
    
    creds = ServiceAccountCredentials.from_json_keyfile_dict(get_user_specific_secret(user,"ocr-331616"), scope)
    client = gspread.authorize(creds)
    return client
    
sheets_info = {
    "Data Piping": {"start_row": 4, "type": "Piping"},
    "Data PV":     {"start_row": 6, "type": "Pressure Vessel"},
    "Data ST":     {"start_row": 5, "type": "Storage Tank"},
    "Lookup":      {"start_row": 1, "type": "LookupValues"},
    "Users":       {"start_row": 1, "type": "UsersEmails"},
    "Dropdown Rules": {"start_row": 1, "type": "DropdownRules"}
}

CLIENT_ID = "************-egs6hdpha97hb5m9023snajp7l2cu4e7.apps.googleusercontent.com"

def clean_dataframe(df):
    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    df.dropna(how='all', inplace=True)
    df.fillna("", inplace=True)
    return df

def extract_desired_columns_from_df(df, desired_columns):
    return df[desired_columns + ["Template"]]

def get_column_letter(col_num):
    string = ""
    while col_num > 0:
        col_num, remainder = divmod(col_num - 1, 26)
        string = chr(65 + remainder) + string
    return string

# Queue setup
task_queue = Queue()

def worker():
    while True:
        task = task_queue.get()
        if task is None:
            break
        task()
        task_queue.task_done()
        time.sleep(1)

threading.Thread(target=worker, daemon=True).start()

user = {}

def create_jwt_token(user_email: str):
    payload = {
        "sub": user_email,
        "exp": datetime.utcnow() + timedelta(hours=1)
    }
    
    # Optionally add custom claim from environment variables
    if JWT_CUSTOM_CLAIM_KEY and JWT_CUSTOM_CLAIM_VALUE:
        # Convert the value to boolean if applicable
        claim_value = JWT_CUSTOM_CLAIM_VALUE.lower() == "true" if JWT_CUSTOM_CLAIM_VALUE.lower() in ["true", "false"] else JWT_CUSTOM_CLAIM_VALUE
        payload[JWT_CUSTOM_CLAIM_KEY] = claim_value

    return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)


async def update_current_asset(session_id, email, current_asset):
    conn = sqlite3.connect('users_assets.db')
    c = conn.cursor()
    # Ensure the table exists
    print("creating table")
    c.execute('''CREATE TABLE IF NOT EXISTS user_sessions (
                    session_id TEXT PRIMARY KEY,
                    email TEXT NOT NULL,
                    current_asset TEXT,
                    UNIQUE(email, current_asset))''')
    # Update or insert the user's current asset
    c.execute('REPLACE INTO user_sessions (session_id, email, current_asset) VALUES (?, ?, ?)',
              (session_id, email, current_asset))
    conn.commit()
    conn.close()

    # Broadcast the updated current asset to all connected clients
    data = {'user_email': email, 'tag': current_asset}
    for client in connected_clients.values():
        await client.send_json(data)  # Use send_json to send JSON data
        print(f"Broadcasted data: {data}")


# Global variables to track connected clients and their current selections
connected_clients = {}
current_selections = {}
async_lock = asyncio.Lock()

@app.websocket("/ws/{userName}/{session_token}")
async def websocket_endpoint(websocket: WebSocket, userName: str, session_token: str):
    session_id = f"{userName}_{session_token}"
    await websocket.accept()
    
    async with async_lock:
        connected_clients[session_id] = websocket
        for sel_session_id, tag_name in current_selections.items():
            await websocket.send_json({
                'action': 'update',
                'user_name': sel_session_id.split('_')[0],  # Changed from user_email to user_name
                'session_token': sel_session_id.split('_')[1],
                'tag': tag_name
            })

    try:
        while True:
            data = await websocket.receive_text()
            data_json = json.loads(data)
            async with async_lock:
                current_selections[session_id] = data_json['tag']
                broadcast_data = json.dumps({
                    'action': 'update',
                    'user_name': userName,  # Changed from user_email to user_name
                    'session_token': session_token,
                    'tag': data_json['tag']
                })
                for client_id, client_ws in list(connected_clients.items()):
                    if client_id != session_id:
                        await client_ws.send_text(broadcast_data)
    
    except WebSocketDisconnect:
        print(f"WebSocket disconnected for {session_id}")
    except Exception as e:
        print(f"WebSocket Error: {e}")
    finally:
        await cleanup_connection(session_id)

async def cleanup_connection(session_id: str):
    async with async_lock:
        if session_id in connected_clients:
            disconnect_data = json.dumps({
                'action': 'disconnect',
                'user_name': session_id.split('_')[0],  # Changed from user_email to user_name
                'session_token': session_id.split('_')[1]
            })
            for client_id, client_ws in list(connected_clients.items()):
                if client_id != session_id:
                    await client_ws.send_text(disconnect_data)
            del connected_clients[session_id]
            current_selections.pop(session_id, None)
            print(f"WebSocket connection closed and cleaned up for {session_id}")

@app.get("/set-test-cookie")
def set_test_cookie(response: Response):
    response.set_cookie(key="testcookie", value="testvalue", httponly=True, max_age=3600, secure=False, samesite='Lax')
    return {"message": "Test cookie set"}

@app.get("/get-test-cookie-1")
def get_test_cookie(testingvaimtoken: str = Cookie(None)):
    return {"testcookie-1": testingvaimtoken}


@app.get("/get-test-cookie-2")
def get_test_cookie2(testcookie: str = Cookie(None)):
    return {"testcookie-2": testcookie}

@app.get("/validate_token/")
def validate_token(token: str, response: Response):
    try:
        google_request = google_requests.Request()
        idinfo = id_token.verify_oauth2_token(token, google_request, CLIENT_ID)
        
        user_email = idinfo.get("email")
        if not user_email:
            raise HTTPException(status_code=400, detail="Invalid token: No email claim found")
        
        print("Email Found", user_email)
        
        #new_token = create_jwt_token(user_email)
        
        response.set_cookie(key="testingvaimtoken", value="testvalue", httponly=True, max_age=3600, secure=True, samesite='None')
        
        return {"isValid": True, "message": "Token is valid", "user_email": user_email}
    except ValueError as e:
        import traceback
        traceback.print_exc()
        return {"isValid": False, "message": str(e)}

DATABASE_URL = "users_assets.db"    

def get_db_connection():
    conn = sqlite3.connect(DATABASE_URL)
    conn.row_factory = sqlite3.Row
    print("Connection Open")
    return conn

def update_current_open_asset(user_email, current_open_asset):
    print(f"Updating asset for {user_email} to {current_open_asset}")
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("INSERT OR REPLACE INTO user_sessions (user_email, current_open_asset) VALUES (?, ?)",
                   (user_email, current_open_asset))
    conn.commit()
    conn.close()

async def get_current_user(testingvaimtoken: str = Cookie(None)):
    
    print('cookie',testingvaimtoken)
    print("trying to get user")
    if testingvaimtoken is None:
        raise HTTPException(status_code=403, detail="Not authenticated")
    try:
        payload = jwt.decode(testingvaimtoken, SECRET_KEY, algorithms=[ALGORITHM])
        user_email = payload.get("sub")
        if user_email is None:
            raise HTTPException(status_code=403, detail="Invalid authentication credentials")
        return user_email
    except PyJWTError:
        raise HTTPException(status_code=403, detail="Invalid authentication credentials")

@app.get("/data/")
def read_data(user:str):
    client = getCredentialsFromClient(user)
    # Open Google Sheet using its specific name
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    dfs = {}
    lookup_dict = {}
    users_data = {}
    dropdown_rules = {}
    validation_errors = {"errors": []}
    
    # Create a batch request for all worksheets
    ranges = {
        "Data Piping": "A:K",
        "Data PV": "A:BA",
        "Data ST": "A:BI",
        "Lookup":"A:ZZ",
        "Users":"A:A",
        "Dropdown Rules":"A:ZZ"
        }
    response = sheet.values_batch_get([f"{ws_name}!{range_val}" for ws_name, range_val in ranges.items()])
    # Extract values from the response
    all_data = {ws_name: r.get('values', []) for ws_name, r in zip(ranges.keys(), response['valueRanges'])}
    

    for worksheet_name, info in sheets_info.items():
        print(worksheet_name)


        if worksheet_name == "Dropdown Rules":
            dropdown_data = all_data["Dropdown Rules"]

            for row in dropdown_data:
                while len(row) < 6:
                    row.append("")

            dropdown_dataNoHeaders = dropdown_data[2:]
            dropdown_headers = dropdown_data[0]

            
            df_dropdownRules = pd.DataFrame(dropdown_dataNoHeaders, columns=dropdown_headers)

            # Remove empty columns that are full of empty strings
            df_dropdownRules.replace('', pd.NA, inplace=True)
            df_dropdownRules.dropna(how='all', axis=1, inplace=True)

            # Initialize the resulting JSON structure
            result_json = {}

            # Iterate over the DataFrame by stepping through each pair of columns
            for i in range(0, len(df_dropdownRules.columns), 2):
                main_header = df_dropdownRules.columns[i]  # This is your 'Construction Code', 'Asset Classification', etc.
                sub_header = df_dropdownRules.columns[i+1]  # This is your 'Asset Classification' for each main header

                # Prepare the list for the current main header
                result_json[main_header] = []

                # Iterate over each row for the current pair of columns
                for index, row in df_dropdownRules.iterrows():
                    # Get the value and asset classification for the current row
                    value = row[i] if i < len(df_dropdownRules.columns) else None
                    asset_classification = row[i+1] if i+1 < len(df_dropdownRules.columns) else None

                    # If both are None (or NaN after replacement), skip this row
                    if pd.isna(value) and pd.isna(asset_classification):
                        continue

                    # Create a dictionary for the current row and append it to the result
                    result_json[main_header].append({
                        'key': value,
                        'Template': asset_classification.replace("STORAGE TANK","Storage Tank").replace("PRESSURE VESSEL","Pressure Vessel").replace("PIPE","Piping")
                    })

            # Remove any main headers with empty lists
            result_json = {k: v for k, v in result_json.items() if v}
            dropdown_rules = result_json
          
            

        elif worksheet_name == "Users":
            
            print("getting Users Data")
            
            # Fetching all data from the "Lookup" sheet
            user_headers = all_data["Users"][0]
            users_df = pd.DataFrame(all_data["Users"][1:], columns=user_headers)
            df_users_final = clean_dataframe(users_df)
            # Constructing the dictionary with header: list values
            users_data =   df_users_final.to_dict(orient='list')
            


        elif worksheet_name == "Lookup":
            lookup_headers = all_data["Lookup"][0]
            lookup_df = pd.DataFrame(all_data["Lookup"][1:], columns=lookup_headers)
            df_lookup_final = clean_dataframe(lookup_df)
            lookup_dict =  df_lookup_final.to_dict(orient='list')
        


        else:

            headers = all_data[worksheet_name][info["start_row"] - 1]
            adjusted_data = [row + [""] * (len(headers) - len(row)) for row in all_data[worksheet_name][info["start_row"]:]]
            df = pd.DataFrame(adjusted_data, columns=headers)
            
            # Add the "type" column
            df["Template"] = info["type"]

            print(info["type"])

            dfs[worksheet_name] = df

            
    print(dfs)


    # Validate unique combinations
    columns_to_validate = ["Plant", "Unit", "System", "Tag Name", "Asset Classification", "Equipment Type"]

    for ws_name in ["Data PV", "Data ST"]:
        for comb in dfs[ws_name][columns_to_validate].drop_duplicates().values:
            condition = (dfs[ws_name][columns_to_validate].values == comb).all(axis=1)
     
            matching_rows = dfs[ws_name][condition]
            
            if len(matching_rows) == 1:
                continue
            
            failed_columns = []
            for col in matching_rows.columns.difference(columns_to_validate):
                if matching_rows[col].nunique() > 1:
                    failed_columns.append(col)
                    print("Failed Column")
            
            if failed_columns:
                # Append error details to the errors list
                validation_errors["errors"].append({
                    "template": ws_name,
                    "combination": comb.tolist(),
                    "failed_columns": failed_columns
                })
                
            
                
            


    # Extracting the first row of each unique combination
    final_df = pd.concat([dfs["Data PV"].drop_duplicates(subset=columns_to_validate),
                          dfs["Data ST"].drop_duplicates(subset=columns_to_validate),
                          dfs["Data Piping"].drop_duplicates(subset=columns_to_validate)], ignore_index=True)
    
    
   

    desired_columns = ["Email","Owner","Ready","QC","QCV","Tag Name", "Asset Classification", "Equipment Type", "Plant", "Unit", "System",]
    final_df = extract_desired_columns_from_df(final_df, desired_columns)

    final_df = clean_dataframe(final_df)
    mask = (final_df[columns_to_validate] == '').all(axis=1)
    final_df = final_df[~mask]
    

    def clean_empty_strings(data):

        if isinstance(data, dict):
            return {k: clean_empty_strings(v) for k, v in data.items() if v != ""}
        elif isinstance(data, list):
            return [clean_empty_strings(item) for item in data if item != ""]
        return data

    # Your result dictionary with potentially empty strings
    result = {
        "headers": list(final_df.columns),
        "data": final_df.values.tolist(),
        "lookup": lookup_dict,
        "users": users_data,
        "dropdown_rules": dropdown_rules,
        "validation": validation_errors  # This will always be returned, but might be empty if there are no errors.
    }

    # Clean the result dictionary
    

    return result

@app.get("/data/combined/")
def get_combined_data(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str,
    user: str,
    template: str
):
    client = getCredentialsFromClient(user)
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    # Define ranges for batch_get, considering the different header rows for each sheet
    range_info = {
        "data_rows": f"{template}!A{sheets_info[template]['start_row']}:ZZ",
        "previous_inspections": "Previous Inspections!A2:Z",
        "ncrs": "NCRs!A2:Z",
        "general_files": "General Files!A2:Z",
    }

    ranges_to_fetch = list(range_info.values())
    response = sheet.values_batch_get(ranges=ranges_to_fetch)
    all_data = {key: value['values'] for key, value in zip(range_info.keys(), response['valueRanges'])}

    combined_result = {}

    # Process and filter data for each sheet type, ensuring structured response
    for key, data in all_data.items():
        if not data:
            combined_result[key] = {"error": "No data found"}
            continue

        headers = data[0]  # Extract headers
        adjusted_data_rows = [row[:len(headers)] + [""] * (len(headers) - len(row)) for row in data[1:]]
        df = pd.DataFrame(adjusted_data_rows, columns=headers)

        condition = (
            (df["Plant"] == plant) &
            (df["Asset Classification"] == asset_classification) &
            (df["Unit"] == unit) &
            (df["System"] == system) &
            (df["Tag Name"] == tag_name) &
            (df['Equipment Type'] == equipment_type)
        )
        filtered_df = df[condition]

        if not filtered_df.empty:
            rows_list = filtered_df.to_dict(orient='records')
            
            
            sheets_info.get("start_row",)
            
            header_offset = 0
            # Calculate row indices with correct offset for header row
            if key == "data_rows":
                for key2, value2 in sheets_info.items():
                    print(value2)
                    if value2["type"] == template:
               
                        header_offset = (value2["start_row"]) - 1
                        print("Header offset")
                        break

                
                
            else:
                header_offset = 0
            
            row_indices = [i + header_offset for i in filtered_df.index.tolist()]

            # Attach rowIndex to each row in rows_list
            for idx, row in enumerate(rows_list):
                row["rowIndex"] = row_indices[idx]  

            combined_result[key] = {
                "rows": rows_list,
                "rowIndices": row_indices
            }
        else:
            combined_result[key] = {
                "error": "No rows found matching the criteria.",
                "rows": [],
                "rowIndices": []
            }

    return combined_result

@app.get("/data/row/")
def get_row(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str,
    template: str,
    user:str
):
    
    client = getCredentialsFromClient(user)
    # Open Google Sheet using its specific name
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    worksheet_name = None
    header_start_row = None
    for key, value in sheets_info.items():
        if value["type"] == template:
            worksheet_name = key
            header_start_row = value["start_row"] - 1
            break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    range_name = f"{worksheet_name}"  # Get the entire range of the sheet
    response = sheet.values_get(range_name)
    data = response.get('values', [])
    headers = data[header_start_row]
    adjusted_data = [row + [""] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]
    df = pd.DataFrame(adjusted_data, columns=headers)

    condition = (
        (df["Plant"] == plant) &
        (df["Asset Classification"] == asset_classification) &
        (df["Unit"] == unit) &
        (df["System"] == system) &
        (df["Tag Name"] == tag_name) &
        (df['Equipment Type'] == equipment_type)
    )
    filtered_df = df[condition]
    num_rows = len(filtered_df)
    if num_rows > 0:
        row_dict = filtered_df.to_dict(orient='records')[0]
        row_dict["rowIndex"] = list(map(int, filtered_df.index))
        return row_dict
    elif num_rows == 0:
        return {"error": "No rows found matching the criteria."}
    
# update cell
@app.put("/data/update_cell/")
def update_cells(
    indexrows: str,
    template: str,
    header: str,
    new_value: str,
    user:str
):
    # Convert comma-separated string to a list of integers
    indexrows_list = [int(index) for index in indexrows.split(",")]
    client = getCredentialsFromClient(user)
    
    


    if template == "Previous Inspections":
        worksheet_name = "Previous Inspections"
        header_start_row = 1  # Assuming headers are in the 2nd row
        
    elif template == "General Files":
       worksheet_name = "General Files"
       header_start_row = 1
       
    elif template == "NCRs":
        worksheet_name = "NCRs"
        header_start_row = 1

    else:
        # Determine the worksheet name and header_start_row from the template
        worksheet_name, header_start_row = None, None
        for ws_name, info in sheets_info.items():
            if info["type"] == template:
                worksheet_name = ws_name
                header_start_row = info["start_row"] - 1
                break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    # Open the Google Sheet using its specific name
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)
    
    # Get the correct worksheet
    ws = sheet.worksheet(worksheet_name)

    # Fetch headers
    headers = ws.row_values(header_start_row + 1)  # +1 because Google Sheets index starts from 1

    # Identify the column index for the given header
    try:
        col_index = headers.index(header) + 1  # +1 because Google Sheets index starts from 1
    except ValueError:
        return {"error": f"Header {header} not found in worksheet {worksheet_name}"}

    # Update the cells for all specified rows
    for indexrow in indexrows_list:
        ws.update_cell(indexrow, col_index, new_value)

    return {"message": f"Cells updated successfully in worksheet {worksheet_name} for header {header}."}


def col_index_to_letter(col_index):
    """Convert a zero-based column index to a string indicating the column letter(s)."""
    column_letter = ""
    while col_index > 0:
        col_index, remainder = divmod(col_index - 1, 26)
        column_letter = chr(65 + remainder) + column_letter
    return column_letter


@app.put("/data/bulk_update_cells/")
def bulk_update_cells(updatesjson: str, template: str, user: str):
    updates = json.loads(updatesjson)
    client = getCredentialsFromClient(user)

    # Determine the worksheet name and header_start_row from the template
    worksheet_name, header_start_row = None, None
    for ws_name, info in sheets_info.items():
        if info["type"] == template:
            worksheet_name = ws_name
            header_start_row = info["start_row"] - 1
            break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)
    ws = sheet.worksheet(worksheet_name)

    # Fetch headers to find column indexes for each header
    headers = ws.row_values(header_start_row + 1)  # Assuming headers are in the first row
    print(headers)
    header_to_col_index = {header: idx + 1 for idx, header in enumerate(headers)}

    # Perform updates in a loop
    for update in updates:
        col_index = header_to_col_index.get(update['header'])
        if col_index is None:
            print(f"Header {update['header']} not found")
            continue  # Optionally handle the error or log it

        try:
            # Convert column index to letter to identify the cell properly
            column_letter = col_index_to_letter(col_index)  # Adjusted for zero-based index
            
            rowNumber = int(update['indexrow'].replace('"',"")) + header_start_row + 2
            print(rowNumber)
            cell_id = f"{column_letter}{rowNumber}"
            ws.update_acell(cell_id, update['new_value'])  # Using update_acell for A1 notation
            print(f"Updated cell {cell_id} with value {update['new_value']}")
        except Exception as e:
            print(f"Error updating cell {cell_id}: {e}")
            return {"error": f"Failed to update cell {cell_id}"}

    return {"message": "Cells updated successfully."}

@app.get("/data/rows/")
def get_rows(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str,
    template: str,
    user:str
):
    # Open Google Sheet using its specific name
    client = getCredentialsFromClient(user)
    
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    worksheet_name = None
    header_start_row = None
    for key, value in sheets_info.items():
        if value["type"] == template:
            worksheet_name = key
            header_start_row = value["start_row"] - 1
            break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    range_name = f"{worksheet_name}"  # Get the entire range of the sheet
    response = sheet.values_get(range_name)
    data = response.get('values', [])
    headers = data[header_start_row]
    adjusted_data = [row + [""] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]
    df = pd.DataFrame(adjusted_data, columns=headers)

    condition = (
        (df["Plant"] == plant) &
        (df["Asset Classification"] == asset_classification) &
        (df["Unit"] == unit) &
        (df["System"] == system) &
        (df["Tag Name"] == tag_name) &
        (df['Equipment Type'] == equipment_type)
    )
    filtered_df = df[condition]
    num_rows = len(filtered_df)
    if num_rows > 0:
        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string
        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices
        # Attach rowIndex to each individual row
        for idx, row in enumerate(rows_list):
            row["rowIndex"] = row_indices[idx]

        return {"rows": rows_list, "rowIndices": row_indices}
    elif num_rows == 0:
        return {"error": "No rows found matching the criteria.","rows":[],"rowIndices":[]}

#Get Data from Previous Inspections Sheet
@app.get("/data/inspections")
def get_previous_inspection_row(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str,
    user:str
):
    client = getCredentialsFromClient(user)
    
    # Open Google Sheet using its specific name
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    worksheet_name = "Previous Inspections"
    header_start_row = 1  # Assuming headers are in the 2nd row

    # Get data from the specified range in the "Previous Inspections" worksheet
    range_name = f"{worksheet_name}"  # Get the entire range of the sheet
    response = sheet.values_get(range_name)
    data = response.get('values', [])
    headers = data[header_start_row]
    adjusted_data = [row + [""] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]
    df = pd.DataFrame(adjusted_data, columns=headers)
    
    condition = (
        (df["Plant"] == plant) &
        (df["Asset Classification"] == asset_classification) &
        (df["Unit"] == unit) &
        (df["System"] == system) &
        (df["Tag Name"] == tag_name)&
        (df['Equipment Type'] == equipment_type)
    )
    filtered_df = df[condition]
    num_rows = len(filtered_df)
    if num_rows > 0:
        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string
        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices
        
        # Attach rowIndex to each individual row
        for idx, row in enumerate(rows_list):
            row["rowIndex"] = row_indices[idx]

        return {"rows": rows_list, "rowIndices": row_indices}
    elif num_rows == 0:
        return {"error": "No rows found matching the criteria.","rows":[],"rowIndices":[]}

# Get Data From NCRs Sheet
@app.get("/data/ncrs")
def get_ncrs_rows(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str,
    user:str
):
    client = getCredentialsFromClient(user)
    
    # Open Google Sheet using its specific name
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    worksheet_name = "NCRs"
    header_start_row = 1  # Assuming headers are in the 2nd row

    # Get data from the specified range in the "Previous Inspections" worksheet
    range_name = f"{worksheet_name}"  # Get the entire range of the sheet
    response = sheet.values_get(range_name)
    data = response.get('values', [])
    headers = data[header_start_row]
    adjusted_data = [row + [""] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]
    df = pd.DataFrame(adjusted_data, columns=headers)

    
    
    
    condition = (
        (df["Plant"] == plant) &
        (df["Asset Classification"] == asset_classification) &
        (df["Unit"] == unit) &
        (df["System"] == system) &
        (df["Tag Name"] == tag_name)&
        (df['Equipment Type'] == equipment_type)
    )
    filtered_df = df[condition]
    num_rows = len(filtered_df)
    if num_rows > 0:
        print("asset Found")
        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string
        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices
        
        # Attach rowIndex to each individual row
        for idx, row in enumerate(rows_list):
            row["rowIndex"] = row_indices[idx]

        return {"rows": rows_list, "rowIndices": row_indices}
    elif num_rows == 0:
        return {"rows":[]}

# Get Data From General Files Sheet
@app.get("/data/generalfiles")
def get_generalfiles_rows(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str,
    user:str
):
    client = getCredentialsFromClient(user)
    
    # Open Google Sheet using its specific name
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    worksheet_name = "General Files"
    header_start_row = 1  # Assuming headers are in the 2nd row

    # Get data from the specified range in the "Previous Inspections" worksheet
    range_name = f"{worksheet_name}"  # Get the entire range of the sheet
    response = sheet.values_get(range_name)
    data = response.get('values', [])
    headers = data[header_start_row]
    adjusted_data = [row + [""] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]
    df = pd.DataFrame(adjusted_data, columns=headers)


    condition = (
        (df["Plant"] == plant) &
        (df["Asset Classification"] == asset_classification) &
        (df["Unit"] == unit) &
        (df["System"] == system) &
        (df["Tag Name"] == tag_name)&
        (df['Equipment Type'] == equipment_type)
    )
    filtered_df = df[condition]
    num_rows = len(filtered_df)
    if num_rows > 0:
        print("asset Found")
        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string
        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices
        
        # Attach rowIndex to each individual row
        for idx, row in enumerate(rows_list):
            row["rowIndex"] = row_indices[idx]

        return {"rows": rows_list, "rowIndices": row_indices}
    elif num_rows == 0:
        return {"rows":[]}
    
    
@app.get("/data/new_inspection/")
def new_inspection(plant: str, unit: str, system: str, tag_name: str, 
                   asset_classification: str, equipment_type: str,components:str,user:str,
                   background_tasks: BackgroundTasks):

    def task_function():
        try:

            client = getCredentialsFromClient(user)

            # Open the Google Sheet using its specific name
            sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
            sheet = client.open(sheet_name)

            # Define worksheet name as we know it
            worksheet_name = "Previous Inspections"
            ws = sheet.worksheet(worksheet_name)

            # Construct the data to insert into the new row
            row_data = [plant, unit, system, tag_name, asset_classification, equipment_type,"In Service Inspection","","","",'{"External Visual":""}',"ALL"]

            # Determine the new row's position (append to the end)
            num_rows = ws.row_count
            new_row_index = num_rows + 1

            # Insert the new row at the end
            ws.add_rows(1)

            # Add data to the new row
            ws.update('A' + str(new_row_index), [row_data])

            print(f"New inspection data added successfully in worksheet {worksheet_name} at row {new_row_index}.")
        except HttpError as e:
            print(f"Google Sheets API Error: {e.resp.reason}")
        except Exception as e:
            print(f"An error occurred: {str(e)}")

    # Enqueue the task function for processing
    background_tasks.add_task(task_function)

    return {"message": "Task to add new inspection data has been enqueued for processing"}
    



@app.put("/data/duplicate_row/")
def duplicate_row(rowIndex: int, template: str,user:str):

    client = getCredentialsFromClient(user)
    
    try:
        # Open the Google Sheet using its specific name
        sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
        sheet = client.open(sheet_name)

        # Determine the worksheet name from the template
        worksheet_name = None
        if template == "Previous Inspections":
            worksheet_name = "Previous Inspections"
        else:
            for ws_name, info in sheets_info.items():
                if info["type"] == template:
                    worksheet_name = ws_name
                    break

        if not worksheet_name:
            return {"error": f"No worksheet found for template: {template}"}

        # Get the correct worksheet
        ws = sheet.worksheet(worksheet_name)

        # Retrieve the data from the specified row
        row_data = ws.row_values(rowIndex)
        
        row_data = list(row_data)
        
        print("row_Data",row_data)

        # Determine the new row's position (append to the end)
        num_rows = ws.row_count
        new_row_index = num_rows + 1

        # Insert the new row at the end
        ws.add_rows(1,)
        
     
        
        if worksheet_name == "Data PV":
            print("rowData51",row_data[53])
            row_data[53] = f'{row_data[53]}'+"_Duplicate"

            
                
        elif worksheet_name == "Data ST":
            print("rowData0",row_data[61])
            row_data[61] = f'{row_data[61]}'+"_Duplicate"
    
        
        # Add data to the new row
        ws.update('A' + str(new_row_index), [row_data])

        return {"message": f"Row {rowIndex} duplicated successfully in worksheet {worksheet_name}."}
    
    except HttpError as e:
        return {"error": f"Google Sheets API Error: {e.resp.reason}"}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}


@app.delete("/data/delete_row/")
def delete_row(rowIndex: int, template: str,user:str):
    worksheet_name = None
    client = getCredentialsFromClient(user)
    
    
    if template == "Previous Inspections":
        worksheet_name = "Previous Inspections"
        
    elif template == "General Files":
        worksheet_name = "General Files" 
        
    elif template == "NCRs":
        worksheet_name = "NCRs" 
        
    else:
    
        for ws_name, info in sheets_info.items():
            if info["type"] == template:
                worksheet_name = ws_name
                break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    try:
        sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
        sheet = client.open(sheet_name)
        ws = sheet.worksheet(worksheet_name)

        # Get the total number of columns in the sheet
        num_cols = len(ws.row_values(rowIndex))

        # Create a list of blank values for all columns
        blank_values = [''] * num_cols

        # Set the entire row to blank using a batch update
        end_column_letter = get_column_letter(num_cols)
        range_name = f"A{rowIndex}:{end_column_letter}{rowIndex}"  # Corrected range format
        ws.update(range_name, [blank_values])

        return {"message": f"Row {rowIndex} cleared successfully in worksheet {worksheet_name}."}
    except HttpError as e:
        return {"error": f"Google Sheets API Error: {e.resp.reason}"}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}


@app.get("/update_ready/")
def update_ready(
    plant: str,
    tag_name: str,
    unit: str,
    system: str,
    asset_classification: str,
    equipment_type: str,
    template: str,
    ready: str,
    user: str
):
    client = getCredentialsFromClient(user)
    # Determine the worksheet name and header start row based on the template
    worksheet_name, header_start_row = None, None
    for key, value in sheets_info.items():
        if value["type"] == template:
            worksheet_name = key
            header_start_row = value["start_row"] - 1  # -1 to convert from 1-indexed to 0-indexed
            break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    # Open the Google Sheet and the specific worksheet
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name).worksheet(worksheet_name)

    # Get all values from the worksheet
    all_data = sheet.get_all_values()

    # Convert the data to a DataFrame, using the header row identified earlier
    df = pd.DataFrame(all_data[header_start_row + 1:], columns=all_data[header_start_row])

    # Determine the index of the 'Ready' column
    try:
        ready_column_index = df.columns.get_loc('Ready') + 1  # +1 to convert from 0-indexed to 1-indexed
    except KeyError:
        return {"error": f"'Ready' column not found in worksheet {worksheet_name}."}

    # Iterate through the DataFrame rows to find matching rows and update the 'Ready' column
    for row_index, row in df.iterrows():
        if (
            row['Plant'] == plant and
            row['Tag Name'] == tag_name and
            row['Unit'] == unit and
            row['System'] == system and
            row['Asset Classification'] == asset_classification and
            row['Equipment Type'] == equipment_type
        ):
            # Update the 'Ready' column for this row
            sheet.update_cell(row_index + header_start_row + 2, ready_column_index, ready)  # +2 because sheet row indices are 1-indexed and we skip the header row

    return {"message": "Ready column updated for matching rows."}


@app.get("/update_qc/")
def update_qc(
    plant: str,
    tag_name: str,
    unit: str,
    system: str,
    asset_classification: str,
    equipment_type: str,
    template: str,
    qc: str,
    user: str
):
    client = getCredentialsFromClient(user)
    # Determine the worksheet name and header start row based on the template
    worksheet_name, header_start_row = None, None
    for key, value in sheets_info.items():
        if value["type"] == template:
            worksheet_name = key
            header_start_row = value["start_row"] - 1  # -1 to convert from 1-indexed to 0-indexed
            break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    # Open the Google Sheet and the specific worksheet
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name).worksheet(worksheet_name)

    # Get all values from the worksheet
    all_data = sheet.get_all_values()

    # Convert the data to a DataFrame, using the header row identified earlier
    df = pd.DataFrame(all_data[header_start_row + 1:], columns=all_data[header_start_row])

    # Determine the index of the 'Ready' column
    try:
        ready_column_index = df.columns.get_loc('QC') + 1  # +1 to convert from 0-indexed to 1-indexed
    except KeyError:
        return {"error": f"'QC' column not found in worksheet {worksheet_name}."}

    # Iterate through the DataFrame rows to find matching rows and update the 'Ready' column
    for row_index, row in df.iterrows():
        if (
            row['Plant'] == plant and
            row['Tag Name'] == tag_name and
            row['Unit'] == unit and
            row['System'] == system and
            row['Asset Classification'] == asset_classification and
            row['Equipment Type'] == equipment_type
        ):
            # Update the 'Ready' column for this row
            sheet.update_cell(row_index + header_start_row + 2, ready_column_index, qc)  # +2 because sheet row indices are 1-indexed and we skip the header row

    return {"message": "Ready column updated for matching rows."}

@app.get("/update_owner/")
def update_owner(
    plant: str,
    tag_name: str,
    unit: str,
    system: str,
    asset_classification: str,
    equipment_type: str,
    template: str,
    owner: str,
    user: str

):
    client = getCredentialsFromClient(user)
    # Determine the worksheet name and header start row based on the template
    worksheet_name, header_start_row = None, None
    for key, value in sheets_info.items():
        if value["type"] == template:
            worksheet_name = key
            header_start_row = value["start_row"] - 1  # -1 to convert from 1-indexed to 0-indexed
            break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    # Open the Google Sheet and the specific worksheet
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name).worksheet(worksheet_name)

    # Get all values from the worksheet
    all_data = sheet.get_all_values()

    # Convert the data to a DataFrame, using the header row identified earlier
    df = pd.DataFrame(all_data[header_start_row + 1:], columns=all_data[header_start_row])

    # Determine the index of the 'Owner' column
    try:
        owner_column_index = df.columns.get_loc('Owner') + 1  # +1 to convert from 0-indexed to 1-indexed
    except KeyError:
        return {"error": f"'Owner' column not found in worksheet {worksheet_name}."}

    # Iterate through the DataFrame rows to find matching rows and update the 'Owner' column
    for row_index, row in df.iterrows():
        if (
            row['Plant'] == plant and
            row['Tag Name'] == tag_name and
            row['Unit'] == unit and
            row['System'] == system and
            row['Asset Classification'] == asset_classification and
            row['Equipment Type'] == equipment_type
        ):
            # Update the 'Owner' column for this row
            sheet.update_cell(row_index + header_start_row + 2, owner_column_index, owner)  # +2 because sheet row indices are 1-indexed and we skip the header row

    return {"message": "Owner column updated for matching rows."}


@app.get("/data/new_general/")
def new_general(plant: str, unit: str, system: str, tag_name: str, 
                   asset_classification: str, equipment_type: str, user:str,
                   background_tasks: BackgroundTasks):


    def task_function():
        try:

            client = getCredentialsFromClient(user)

            # Open the Google Sheet using its specific name
            sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
            sheet = client.open(sheet_name)

            # Define worksheet name as we know it
            worksheet_name = "General Files"
            ws = sheet.worksheet(worksheet_name)

            # Construct the data to insert into the new row
            row_data = [plant, unit, system, tag_name, asset_classification, equipment_type,"","General",""]

            # Determine the new row's position (append to the end)
            num_rows = ws.row_count
            new_row_index = num_rows + 1

            # Insert the new row at the end
            ws.add_rows(1)

            # Add data to the new row
            ws.update('A' + str(new_row_index), [row_data])

            print(f"New inspection data added successfully in worksheet {worksheet_name} at row {new_row_index}.")
        except HttpError as e:
            print(f"Google Sheets API Error: {e.resp.reason}")
        except Exception as e:
            print(f"An error occurred: {str(e)}")

    # Enqueue the task function for processing
    background_tasks.add_task(task_function)

    return {"message": "Task to add new inspection data has been enqueued for processing"}


"""
@app.get("/users/")
def read_users(user:str):
    
    client = getCredentialsFromClient(user)
    # Open Google Sheet using its specific name
    sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name).worksheet('Users')

    # Fetching all data from the "Lookup" sheet
    all_data = sheet.get_all_values()
    headers = all_data[0]
    df = pd.DataFrame(all_data[1:], columns=headers)

    # Constructing the dictionary with header: list values
    data_dict = df.to_dict(orient='list')

    return data_dict

"""



@app.get("/data/new_ncr/")
def new_ncr(plant: str, unit: str, system: str, tag_name: str, 
                   asset_classification: str, equipment_type: str, 
                   background_tasks: BackgroundTasks, user:str):


    def task_function():
        try:

            client = getCredentialsFromClient(user)

            # Open the Google Sheet using its specific name
            sheet_name = "DEV_Demo_Data Collection template Fix Equipment"
            sheet = client.open(sheet_name)

            # Define worksheet name as we know it
            worksheet_name = "NCRs"
            ws = sheet.worksheet(worksheet_name)

            # Construct the data to insert into the new row
            row_data = [plant, unit, system, tag_name, asset_classification, equipment_type,"","","Pending",""]

            # Determine the new row's position (append to the end)
            num_rows = ws.row_count
            new_row_index = num_rows + 1

            # Insert the new row at the end
            ws.add_rows(1)

            # Add data to the new row
            ws.update('A' + str(new_row_index), [row_data])

            print(f"New inspection data added successfully in worksheet {worksheet_name} at row {new_row_index}.")
        except HttpError as e:
            print(f"Google Sheets API Error: {e.resp.reason}")
        except Exception as e:
            print(f"An error occurred: {str(e)}")

    # Enqueue the task function for processing
    background_tasks.add_task(task_function)

    return {"message": "Task to add new inspection data has been enqueued for processing"}





uvicorn.run(app, host="0.0.0.0", port=8020, ws_ping_interval=300, ws_ping_timeout=300)
