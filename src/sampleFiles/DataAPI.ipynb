{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: python-dotenv in /Users/<USER>/anaconda3/lib/python3.10/site-packages (0.21.0)\n"]}], "source": ["! pip install python-dotenv"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:     Started server process [54501]\n", "INFO:     Waiting for application startup.\n", "INFO:     Application startup complete.\n", "INFO:     <PERSON><PERSON><PERSON> running on http://0.0.0.0:8010 (Press CTRL+C to quit)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:57650 - \"OPTIONS /data/update_cell/?indexrows=0,1,2&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57650 - \"PUT /data/update_cell/?indexrows=0,1,2&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 253, in update_cells\n", "    ws.update_cell(indexrow, col_index, new_value)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 699, in update_cell\n", "    range_name = absolute_range_name(self.title, rowcol_to_a1(row, col))\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 277, in rowcol_to_a1\n", "    raise IncorrectCellLabel(\"({}, {})\".format(row, col))\n", "gspread.exceptions.IncorrectCellLabel: (0, 1)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:57649 - \"PUT /data/update_cell/?indexrows=7,12,18&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58302 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58313 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58313 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58302 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58329 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58302 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58398 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58389 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58413 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58398 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58398 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58398 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58398 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58398 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58555 - \"OPTIONS /data/update_cell/?indexrows=7,12,18&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58553 - \"PUT /data/update_cell/?indexrows=0,1,2&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 253, in update_cells\n", "    ws.update_cell(indexrow, col_index, new_value)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 699, in update_cell\n", "    range_name = absolute_range_name(self.title, rowcol_to_a1(row, col))\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 277, in rowcol_to_a1\n", "    raise IncorrectCellLabel(\"({}, {})\".format(row, col))\n", "gspread.exceptions.IncorrectCellLabel: (0, 1)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58555 - \"PUT /data/update_cell/?indexrows=7,12,18&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58808 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58814 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58808 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58814 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58808 - \"OPTIONS /data/update_cell/?indexrows=7,12,18&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58814 - \"PUT /data/update_cell/?indexrows=0,1,2&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 253, in update_cells\n", "    ws.update_cell(indexrow, col_index, new_value)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 699, in update_cell\n", "    range_name = absolute_range_name(self.title, rowcol_to_a1(row, col))\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 277, in rowcol_to_a1\n", "    raise IncorrectCellLabel(\"({}, {})\".format(row, col))\n", "gspread.exceptions.IncorrectCellLabel: (0, 1)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58808 - \"PUT /data/update_cell/?indexrows=7,12,18&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59377 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59380 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59377 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59380 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59417 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59415 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59421 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59417 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59417 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59417 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59417 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59417 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59417 - \"OPTIONS /data/update_cell/?indexrows=2,3,4&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59464 - \"PUT /data/update_cell/?indexrows=7,12,18&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59417 - \"PUT /data/update_cell/?indexrows=2,3,4&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59636 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59636 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59636 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3802, in get_loc\n", "    return self._engine.get_loc(casted_key)\n", "  File \"pandas/_libs/index.pyx\", line 138, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/index.pyx\", line 165, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "KeyError: 'Plant'\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 430, in get_previous_inspection_row\n", "    (df[\"Plant\"] == plant) &\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/frame.py\", line 3807, in __getitem__\n", "    indexer = self.columns.get_loc(key)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3804, in get_loc\n", "    raise <PERSON><PERSON><PERSON><PERSON>(key) from err\n", "KeyError: 'Plant'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59677 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59675 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59687 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59677 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59677 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59677 - \"GET /data/inspections/?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59677 - \"GET /data/inspections?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3802, in get_loc\n", "    return self._engine.get_loc(casted_key)\n", "  File \"pandas/_libs/index.pyx\", line 138, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/index.pyx\", line 165, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "KeyError: 'Plant'\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 430, in get_previous_inspection_row\n", "    (df[\"Plant\"] == plant) &\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/frame.py\", line 3807, in __getitem__\n", "    indexer = self.columns.get_loc(key)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3804, in get_loc\n", "    raise <PERSON><PERSON><PERSON><PERSON>(key) from err\n", "KeyError: 'Plant'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59727 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59727 - \"GET /data/inspections/?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59727 - \"GET /data/inspections?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3802, in get_loc\n", "    return self._engine.get_loc(casted_key)\n", "  File \"pandas/_libs/index.pyx\", line 138, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/index.pyx\", line 165, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "KeyError: 'Plant'\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 430, in get_previous_inspection_row\n", "    (df[\"Plant\"] == plant) &\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/frame.py\", line 3807, in __getitem__\n", "    indexer = self.columns.get_loc(key)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3804, in get_loc\n", "    raise <PERSON><PERSON><PERSON><PERSON>(key) from err\n", "KeyError: 'Plant'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59770 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59768 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59778 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59770 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59798 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59798 - \"GET /data/inspections/?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59798 - \"GET /data/inspections?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3802, in get_loc\n", "    return self._engine.get_loc(casted_key)\n", "  File \"pandas/_libs/index.pyx\", line 138, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/index.pyx\", line 165, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "KeyError: 'Plant'\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 430, in get_previous_inspection_row\n", "    (df[\"Plant\"] == plant) &\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/frame.py\", line 3807, in __getitem__\n", "    indexer = self.columns.get_loc(key)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3804, in get_loc\n", "    raise <PERSON><PERSON><PERSON><PERSON>(key) from err\n", "KeyError: 'Plant'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60045 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60043 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60057 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60045 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60045 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60045 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60045 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3802, in get_loc\n", "    return self._engine.get_loc(casted_key)\n", "  File \"pandas/_libs/index.pyx\", line 138, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/index.pyx\", line 165, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "KeyError: 'Plant'\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 430, in get_previous_inspection_row\n", "    (df[\"Plant\"] == plant) &\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/frame.py\", line 3807, in __getitem__\n", "    indexer = self.columns.get_loc(key)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3804, in get_loc\n", "    raise <PERSON><PERSON><PERSON><PERSON>(key) from err\n", "KeyError: 'Plant'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60265 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60264 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60275 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60265 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60286 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60286 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60286 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3802, in get_loc\n", "    return self._engine.get_loc(casted_key)\n", "  File \"pandas/_libs/index.pyx\", line 138, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/index.pyx\", line 165, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "KeyError: 'Plant'\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 430, in get_previous_inspection_row\n", "    (df[\"Plant\"] == plant) &\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/frame.py\", line 3807, in __getitem__\n", "    indexer = self.columns.get_loc(key)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3804, in get_loc\n", "    raise <PERSON><PERSON><PERSON><PERSON>(key) from err\n", "KeyError: 'Plant'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60580 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60579 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60590 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60580 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60580 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60580 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60580 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60580 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60652 - \"OPTIONS /data/update_cell/?indexrows=2,3&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60654 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60654 - \"PUT /data/update_cell/?indexrows=2,3&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60652 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61342 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61342 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61359 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61342 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61363 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61359 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61342 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61384 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61384 - \"GET /data/inspections/?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61384 - \"GET /data/inspections?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3802, in get_loc\n", "    return self._engine.get_loc(casted_key)\n", "  File \"pandas/_libs/index.pyx\", line 138, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/index.pyx\", line 165, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "KeyError: 'Plant'\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 430, in get_previous_inspection_row\n", "    (df[\"Plant\"] == plant) &\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/frame.py\", line 3807, in __getitem__\n", "    indexer = self.columns.get_loc(key)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3804, in get_loc\n", "    raise <PERSON><PERSON><PERSON><PERSON>(key) from err\n", "KeyError: 'Plant'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61412 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61412 - \"GET /data/inspections/?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61412 - \"GET /data/inspections?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3802, in get_loc\n", "    return self._engine.get_loc(casted_key)\n", "  File \"pandas/_libs/index.pyx\", line 138, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/index.pyx\", line 165, in pandas._libs.index.IndexEngine.get_loc\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5745, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "  File \"pandas/_libs/hashtable_class_helper.pxi\", line 5753, in pandas._libs.hashtable.PyObjectHashTable.get_item\n", "KeyError: 'Plant'\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 430, in get_previous_inspection_row\n", "    (df[\"Plant\"] == plant) &\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/frame.py\", line 3807, in __getitem__\n", "    indexer = self.columns.get_loc(key)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/pandas/core/indexes/base.py\", line 3804, in get_loc\n", "    raise <PERSON><PERSON><PERSON><PERSON>(key) from err\n", "KeyError: 'Plant'\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61530 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61536 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61542 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61536 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61611 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61611 - \"GET /data/inspections/?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61611 - \"GET /data/inspections?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61611 - \"GET /data/rows/?plant=Kudu&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61658 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61660 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61660 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61658 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61658 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61658 - \"GET /data/inspections/?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61658 - \"GET /data/inspections?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61658 - \"GET /data/rows/?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator&template=Storage+Tank HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62181 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62180 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62184 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62186 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62188 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62180 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62194 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62188 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62180 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62194 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62188 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62194 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62180 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62188 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62194 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62188 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62433 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62435 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62433 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62435 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62492 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62492 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62492 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62492 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62540 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62539 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62540 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62540 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62616 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62617 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62616 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63202 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63273 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63202 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63273 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63202 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63273 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63335 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63332 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63346 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63335 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63362 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63362 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63362 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63362 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63391 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63392 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63391 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63449 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63448 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63448 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63516 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63516 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63516 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63516 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63536 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63536 - \"GET /data/inspections/?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63536 - \"GET /data/inspections?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63536 - \"GET /data/rows/?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator&template=Storage+Tank HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63556 - \"OPTIONS /data/update_cell/?indexrows=8,9&template=Storage+Tank&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63556 - \"PUT /data/update_cell/?indexrows=8,9&template=Storage+Tank&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63556 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63556 - \"GET /data/inspections/?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63556 - \"GET /data/inspections?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63556 - \"GET /data/rows/?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator&template=Storage+Tank HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63556 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63556 - \"GET /data/inspections/?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63556 - \"GET /data/inspections?plant=Kudu&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 412, in get_previous_inspection_row\n", "    sheet = client.open(sheet_name)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 177, in open\n", "    return Spreadsheet(self, properties)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 34, in __init__\n", "    metadata = self.fetch_sheet_metadata()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 243, in fetch_sheet_metadata\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'ReadRequestsPerMinutePerUser', 'quota_metric': 'sheets.googleapis.com/read_requests', 'consumer': 'projects/773685984842', 'service': 'sheets.googleapis.com', 'quota_limit_value': '60', 'quota_location': 'global'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63669 - \"PUT /data/update_cell/?indexrows=8,9&template=Storage+Tank&header=Plant&new_value=Javelina HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 237, in update_cells\n", "    sheet = client.open(sheet_name)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 177, in open\n", "    return Spreadsheet(self, properties)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 34, in __init__\n", "    metadata = self.fetch_sheet_metadata()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 243, in fetch_sheet_metadata\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'ReadRequestsPerMinutePerUser', 'quota_metric': 'sheets.googleapis.com/read_requests', 'consumer': 'projects/773685984842', 'service': 'sheets.googleapis.com', 'quota_limit_value': '60', 'quota_location': 'global'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63686 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63686 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63686 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63686 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63757 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63757 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63757 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63757 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63866 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63872 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63866 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63872 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63898 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63896 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63913 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63898 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "Task exception was never retrieved\n", "future: <Task finished name='Task-1' coro=<Server.serve() done, defined at /Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/server.py:63> exception=SystemExit(1)>\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/server.py\", line 160, in startup\n", "    server = await loop.create_server(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/base_events.py\", line 1513, in create_server\n", "    raise OSError(err.errno, 'error while attempting '\n", "OSError: [<PERSON>rrno 48] error while attempting to bind on address ('0.0.0.0', 8010): address already in use\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/IPython/core/interactiveshell.py\", line 3460, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 455, in <module>\n", "    uvicorn.run(app, host=\"0.0.0.0\", port=8010)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/main.py\", line 587, in run\n", "    server.run()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/server.py\", line 61, in run\n", "    return asyncio.run(self.serve(sockets=sockets))\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/nest_asyncio.py\", line 35, in run\n", "    return loop.run_until_complete(task)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/nest_asyncio.py\", line 84, in run_until_complete\n", "    self._run_once()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/nest_asyncio.py\", line 120, in _run_once\n", "    handle._run()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/events.py\", line 80, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 315, in __wakeup\n", "    self.__step()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/nest_asyncio.py\", line 196, in step\n", "    step_orig(task, exc)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 232, in __step\n", "    result = coro.send(None)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/server.py\", line 78, in serve\n", "    await self.startup(sockets=sockets)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/server.py\", line 170, in startup\n", "    sys.exit(1)\n", "SystemExit: 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63948 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63946 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63966 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63948 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:64007 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64007 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:64007 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64007 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:64057 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64055 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64057 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64055 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64091 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64092 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64091 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64092 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64091 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64091 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64091 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=%3CSelect%3E HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64092 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=%3CSelect%3E HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64091 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=%3CSelect%3E HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64092 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=%3CSelect%3E HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64092 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64273 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64275 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64273 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64275 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64285 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64273 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64273 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64313 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64273 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:64326 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64313 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:64345 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64345 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:64345 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64345 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:64375 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64374 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64374 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64387 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64375 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64375 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65114 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65122 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65114 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65122 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65122 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65148 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65150 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65122 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65122 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65150 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65122 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65122 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49341 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49347 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49341 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49347 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49347 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49341 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49381 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49341 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49392 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49381 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49418 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49418 - \"GET /data/inspections/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49418 - \"GET /data/inspections?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49418 - \"GET /data/rows/?plant=Javelina&unit=Unit+A&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49451 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49418 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49451 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49451 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49510 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49509 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49509 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49509 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Unit&new_value=Unit+B HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49510 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Unit&new_value=Unit+B HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49510 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Unit&new_value=Unit+B HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49509 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Unit&new_value=Unit+B HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49509 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49550 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=System&new_value=System+B HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49551 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=System&new_value=System+B HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49551 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=System&new_value=System+B HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 243, in update_cells\n", "    headers = ws.row_values(header_start_row + 1)  # +1 because Google Sheets index starts from 1\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 639, in row_values\n", "    data = self.get(\"A{}:{}\".format(row, row), **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 850, in get\n", "    response = self.spreadsheet.values_get(range_name, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 171, in values_get\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'consumer': 'projects/773685984842', 'quota_metric': 'sheets.googleapis.com/read_requests', 'quota_limit': 'ReadRequestsPerMinutePerUser', 'service': 'sheets.googleapis.com', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49550 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=System&new_value=System+B HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49571 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=System&new_value=System+A HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49573 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=System&new_value=System+A HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49571 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=System&new_value=System+A HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 243, in update_cells\n", "    headers = ws.row_values(header_start_row + 1)  # +1 because Google Sheets index starts from 1\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 639, in row_values\n", "    data = self.get(\"A{}:{}\".format(row, row), **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 850, in get\n", "    response = self.spreadsheet.values_get(range_name, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 171, in values_get\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'consumer': 'projects/773685984842', 'quota_metric': 'sheets.googleapis.com/read_requests', 'quota_limit': 'ReadRequestsPerMinutePerUser', 'service': 'sheets.googleapis.com', 'quota_limit_value': '60'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49573 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=System&new_value=System+A HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49594 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Unit&new_value=Unit+A HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49595 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Unit&new_value=Unit+A HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49595 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Unit&new_value=Unit+A HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 237, in update_cells\n", "    sheet = client.open(sheet_name)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 177, in open\n", "    return Spreadsheet(self, properties)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 34, in __init__\n", "    metadata = self.fetch_sheet_metadata()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 243, in fetch_sheet_metadata\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'sheets.googleapis.com/read_requests', 'quota_limit_value': '60', 'service': 'sheets.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/773685984842', 'quota_limit': 'ReadRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49594 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Unit&new_value=Unit+A HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 240, in update_cells\n", "    ws = sheet.worksheet(worksheet_name)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 332, in worksheet\n", "    sheet_data = self.fetch_sheet_metadata()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 243, in fetch_sheet_metadata\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'sheets.googleapis.com/read_requests', 'quota_limit_value': '60', 'service': 'sheets.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/773685984842', 'quota_limit': 'ReadRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49736 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Unit&new_value=Unit+B HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 237, in update_cells\n", "    sheet = client.open(sheet_name)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 177, in open\n", "    return Spreadsheet(self, properties)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 34, in __init__\n", "    metadata = self.fetch_sheet_metadata()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 243, in fetch_sheet_metadata\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'sheets.googleapis.com/read_requests', 'quota_limit_value': '60', 'service': 'sheets.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/773685984842', 'quota_limit': 'ReadRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49735 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Unit&new_value=Unit+B HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 237, in update_cells\n", "    sheet = client.open(sheet_name)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 177, in open\n", "    return Spreadsheet(self, properties)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 34, in __init__\n", "    metadata = self.fetch_sheet_metadata()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 243, in fetch_sheet_metadata\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_metric': 'sheets.googleapis.com/read_requests', 'quota_limit_value': '60', 'service': 'sheets.googleapis.com', 'quota_location': 'global', 'consumer': 'projects/773685984842', 'quota_limit': 'ReadRequestsPerMinutePerUser'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:50231 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50228 - \"GET /lookup/ HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 83, in read_lookup\n", "    all_data = sheet.get_all_values()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 484, in get_all_values\n", "    return self.get_values(**kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 449, in get_values\n", "    vals = fill_gaps(self.get(range_name, **kwargs))\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 850, in get\n", "    response = self.spreadsheet.values_get(range_name, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 171, in values_get\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'service': 'sheets.googleapis.com', 'quota_limit_value': '60', 'quota_location': 'global', 'consumer': 'projects/773685984842', 'quota_limit': 'ReadRequestsPerMinutePerUser', 'quota_metric': 'sheets.googleapis.com/read_requests'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:50246 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50231 - \"GET /lookup/ HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 83, in read_lookup\n", "    all_data = sheet.get_all_values()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 484, in get_all_values\n", "    return self.get_values(**kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 449, in get_values\n", "    vals = fill_gaps(self.get(range_name, **kwargs))\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/utils.py\", line 705, in wrapper\n", "    return f(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py\", line 850, in get\n", "    response = self.spreadsheet.values_get(range_name, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 171, in values_get\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_location': 'global', 'quota_limit': 'ReadRequestsPerMinutePerUser', 'consumer': 'projects/773685984842', 'quota_metric': 'sheets.googleapis.com/read_requests', 'quota_limit_value': '60', 'service': 'sheets.googleapis.com'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51009 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51008 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51026 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51009 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51064 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51064 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+A&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51064 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+A&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51064 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+A&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51064 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51064 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 500 Internal Server Error\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py\", line 237, in update_cells\n", "    sheet = client.open(sheet_name)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 177, in open\n", "    return Spreadsheet(self, properties)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 34, in __init__\n", "    metadata = self.fetch_sheet_metadata()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/spreadsheet.py\", line 243, in fetch_sheet_metadata\n", "    r = self.client.request(\"get\", url, params=params)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/client.py\", line 93, in request\n", "    raise APIError(response)\n", "gspread.exceptions.APIError: {'code': 429, 'message': \"Quota exceeded for quota metric 'Read requests' and limit 'Read requests per minute per user' of service 'sheets.googleapis.com' for consumer 'project_number:773685984842'.\", 'status': 'RESOURCE_EXHAUSTED', 'details': [{'@type': 'type.googleapis.com/google.rpc.ErrorInfo', 'reason': 'RATE_LIMIT_EXCEEDED', 'domain': 'googleapis.com', 'metadata': {'quota_limit': 'ReadRequestsPerMinutePerUser', 'quota_location': 'global', 'quota_limit_value': '60', 'consumer': 'projects/773685984842', 'service': 'sheets.googleapis.com', 'quota_metric': 'sheets.googleapis.com/read_requests'}}, {'@type': 'type.googleapis.com/google.rpc.Help', 'links': [{'description': 'Request a higher quota limit.', 'url': 'https://cloud.google.com/docs/quota#requesting_higher_quota'}]}]}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51173 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51212 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51279 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51315 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51536 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51534 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51550 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51536 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51572 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51572 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51572 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51572 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51622 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51623 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Kudu HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51622 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51878 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51888 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51878 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51888 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51980 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51979 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51986 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51980 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51979 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51986 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51980 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51986 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51980 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52003 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51986 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51980 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51986 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52003 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51980 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51986 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:52025 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51980 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:52025 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:52025 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52025 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:52025 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52025 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator&template=Storage+Tank HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:52886 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52886 - \"GET /data/inspections/?plant=Kudu&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:52886 - \"GET /data/inspections?plant=Kudu&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52886 - \"GET /data/rows/?plant=Kudu&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:52918 - \"OPTIONS /data/update_cell/?indexrows=7&template=Pressure+Vessel&header=Component+Tag+Name&new_value=D HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52918 - \"PUT /data/update_cell/?indexrows=7&template=Pressure+Vessel&header=Component+Tag+Name&new_value=D HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53100 - \"OPTIONS /data/update_cell/?indexrows=7&template=Pressure+Vessel&header=Component+Tag+Name&new_value=TOP+HEAD HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53100 - \"PUT /data/update_cell/?indexrows=7&template=Pressure+Vessel&header=Component+Tag+Name&new_value=TOP+HEAD HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53232 - \"OPTIONS /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53233 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53232 - \"PUT /data/update_cell/?indexrows=3,4,5&template=Previous+Inspections&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53233 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53393 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Methods&new_value=UT+Thickness,+External+Visual HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53393 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Methods&new_value=UT+Thickness,+External+Visual HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53393 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Methods&new_value=UT+Thickness,+External+Visual,+Internal+Visual HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53393 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Methods&new_value=UT+Thickness,+External+Visual,+Internal+Visual HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53436 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Next+Inspection+Date&new_value=1%2F20%2F2019 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53436 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Next+Inspection+Date&new_value=1%2F20%2F2019 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53461 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Next+Inspection+Date&new_value=1%2F20%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53461 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Next+Inspection+Date&new_value=1%2F20%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53496 - \"OPTIONS /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Next+Inspection+Date&new_value=01%2F02%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53496 - \"PUT /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Next+Inspection+Date&new_value=01%2F02%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53573 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Methods&new_value=UT+Thickness,+External+Visual HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53573 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Methods&new_value=UT+Thickness,+External+Visual HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53592 - \"OPTIONS /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspection+Methods&new_value=UT+Thickness,+External+Visual HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53592 - \"PUT /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspection+Methods&new_value=UT+Thickness,+External+Visual HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53797 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=P%26ID+Drawing+Number&new_value=D82.0108-ISO.dwg HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53797 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=P%26ID+Drawing+Number&new_value=D82.0108-ISO.dwg HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53797 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=National+Board%2FRegister+Number&new_value=123456 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53797 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=National+Board%2FRegister+Number&new_value=123456 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53823 - \"OPTIONS /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Manufacturer&new_value=Exterran HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53823 - \"PUT /data/update_cell/?indexrows=7,11,12&template=Pressure+Vessel&header=Manufacturer&new_value=Exterran HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53891 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53890 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:53900 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53891 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:53927 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53927 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:53927 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53927 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:53998 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53996 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54006 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53998 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54130 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54129 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54140 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54130 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54329 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54329 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54329 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54329 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54361 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54361 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:55190 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:55190 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:55190 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:55190 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:55839 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:55839 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:55839 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:55839 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58140 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58137 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58158 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58140 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58191 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58191 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58191 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58191 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58625 - \"OPTIONS /data/duplicate_row/?rowIndex=3&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58625 - \"PUT /data/duplicate_row/?rowIndex=3&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58625 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58625 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Row 3 duplicated successfully in worksheet Previous Inspections.\n", "INFO:     127.0.0.1:58625 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58625 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58674 - \"OPTIONS /data/duplicate_row/?rowIndex=4&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58674 - \"PUT /data/duplicate_row/?rowIndex=4&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58674 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58674 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Row 4 duplicated successfully in worksheet Previous Inspections.\n", "INFO:     127.0.0.1:58674 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58674 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58708 - \"OPTIONS /data/update_cell/?indexrows=6&template=Previous+Inspections&header=Inspection+Date&new_value=1%2F1%2F2021 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58708 - \"PUT /data/update_cell/?indexrows=6&template=Previous+Inspections&header=Inspection+Date&new_value=1%2F1%2F2021 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58747 - \"OPTIONS /data/update_cell/?indexrows=7&template=Previous+Inspections&header=Inspection+Date&new_value=1%2F1%2F2023 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58747 - \"PUT /data/update_cell/?indexrows=7&template=Previous+Inspections&header=Inspection+Date&new_value=1%2F1%2F2023 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58793 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspector&new_value=Duke HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58793 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspector&new_value=Duke HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58793 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspector&new_value=Duke HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58793 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspector&new_value=Duke HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58852 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58852 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58864 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58864 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58864 - \"OPTIONS /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58864 - \"PUT /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58890 - \"OPTIONS /data/update_cell/?indexrows=6&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58890 - \"PUT /data/update_cell/?indexrows=6&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58890 - \"OPTIONS /data/update_cell/?indexrows=7&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58890 - \"PUT /data/update_cell/?indexrows=7&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58912 - \"PUT /data/update_cell/?indexrows=7&template=Previous+Inspections&header=Company&new_value=Mistras HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59031 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Summary&new_value=1 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59037 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Summary&new_value=1 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59031 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Summary&new_value=1 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59037 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Summary&new_value=1 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59213 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Date&new_value=01%2F01%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59213 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Date&new_value=01%2F01%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59248 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Date&new_value=1%2F1%2F2023 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59248 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Date&new_value=1%2F1%2F2023 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59274 - \"OPTIONS /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspection+Summary&new_value=2 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59274 - \"PUT /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspection+Summary&new_value=2 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59291 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Summary&new_value=Text HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59291 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Summary&new_value=Text HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59372 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Next+Inspection+Date&new_value=01%2F02%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59372 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Next+Inspection+Date&new_value=01%2F02%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59415 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Next+Inspection+Date&new_value=01%2F01%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59415 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Next+Inspection+Date&new_value=01%2F01%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59415 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Next+Inspection+Date&new_value=01%2F01%2F2020 HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59596 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59593 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59609 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59596 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59649 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59649 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59649 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59649 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59649 - \"PUT /data/duplicate_row/?rowIndex=4&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59649 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59649 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Row 4 duplicated successfully in worksheet Previous Inspections.\n", "INFO:     127.0.0.1:59649 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59682 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59715 - \"OPTIONS /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspection+Summary&new_value= HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59715 - \"PUT /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspection+Summary&new_value= HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59744 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Summary&new_value= HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59744 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Summary&new_value= HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59744 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Summary&new_value= HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59744 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Summary&new_value= HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59787 - \"OPTIONS /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspector&new_value=Duke HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59787 - \"PUT /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspector&new_value=Duke HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59818 - \"OPTIONS /data/update_cell/?indexrows=6&template=Previous+Inspections&header=Inspector&new_value=MarmaDuke HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59818 - \"PUT /data/update_cell/?indexrows=6&template=Previous+Inspections&header=Inspector&new_value=MarmaDuke HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59840 - \"OPTIONS /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59840 - \"PUT /data/update_cell/?indexrows=4&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59840 - \"OPTIONS /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59840 - \"PUT /data/update_cell/?indexrows=5&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59864 - \"OPTIONS /data/update_cell/?indexrows=7&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59864 - \"PUT /data/update_cell/?indexrows=7&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60044 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60044 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60044 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60044 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61760 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61759 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61784 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61760 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61794 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61794 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61794 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61794 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61852 - \"OPTIONS /data/delete_row/?rowIndex=8&template=Previous+Inspections HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61852 - \"DELETE /data/delete_row/?rowIndex=8&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61852 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61852 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61852 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61852 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61852 - \"OPTIONS /data/delete_row/?rowIndex=7&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61852 - \"DELETE /data/delete_row/?rowIndex=7&template=Previous+Inspections HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61852 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61852 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61852 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61852 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61945 - \"OPTIONS /data/delete_row/?rowIndex=6&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61945 - \"DELETE /data/delete_row/?rowIndex=6&template=Previous+Inspections HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61945 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61945 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61945 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61945 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62042 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62042 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62042 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62042 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62116 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62113 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62128 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62116 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62116 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62116 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62116 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62116 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54855 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54855 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54855 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54855 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61589 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61588 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61613 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61589 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51935 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51932 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51956 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51935 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51979 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51979 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:51979 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51979 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63854 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63852 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63871 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63871 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63871 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63871 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63876 - \"OPTIONS /data/duplicate_row/?rowIndex=4&template=Previous+Inspections HTTP/1.1\" 400 Bad Request\n", "INFO:     127.0.0.1:63876 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63876 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63876 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63876 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63879 - \"OPTIONS /data/duplicate_row/?rowIndex=3&template=Previous+Inspections HTTP/1.1\" 400 Bad Request\n", "INFO:     127.0.0.1:63879 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63879 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63879 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63879 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:63895 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63894 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65427 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65425 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65440 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65427 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65427 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65427 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65427 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65427 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65449 - \"OPTIONS /data/duplicate_row/?rowIndex=3&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65449 - \"PUT /data/duplicate_row/?rowIndex=3&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65449 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65449 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Row 3 duplicated successfully in worksheet Previous Inspections.\n", "INFO:     127.0.0.1:65449 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:65449 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65521 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65524 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65527 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:65531 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49165 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49164 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49172 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49165 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49165 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49165 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49165 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49165 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49181 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49181 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49181 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49181 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49187 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49187 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49187 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49187 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49204 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49202 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49210 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49204 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49204 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49204 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49204 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49204 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49228 - \"OPTIONS /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49228 - \"PUT /data/update_cell/?indexrows=3&template=Previous+Inspections&header=Inspection+Type&new_value=In+Service+Inspection HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49236 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49236 - \"GET /data/inspections/?plant=Javelina&unit=&system=&tag_name=Piping-4&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49236 - \"GET /data/inspections?plant=Javelina&unit=&system=&tag_name=Piping-4&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49236 - \"GET /data/rows/?plant=Javelina&unit=&system=&tag_name=Piping-4&asset_classification=Pipe&equipment_type=Unknown&template=Piping HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49248 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49247 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49256 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49248 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49266 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49266 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49266 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49266 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49272 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49272 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49272 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49272 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49272 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49272 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49272 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49272 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator&template=Storage+Tank HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49272 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49272 - \"GET /data/inspections/?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49272 - \"GET /data/inspections?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49272 - \"GET /data/rows/?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown&template=Piping HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49226 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49224 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49241 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49226 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49248 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49248 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49248 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49248 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49253 - \"OPTIONS /data/delete_row/?rowIndex=9&template=Previous+Inspections HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49253 - \"DELETE /data/delete_row/?rowIndex=9&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49253 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49253 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49253 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49253 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49266 - \"OPTIONS /data/duplicate_row/?rowIndex=4&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49266 - \"PUT /data/duplicate_row/?rowIndex=4&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49266 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49266 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Row 4 duplicated successfully in worksheet Previous Inspections.\n", "INFO:     127.0.0.1:49266 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49266 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49282 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49282 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:49282 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49282 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54013 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54013 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54013 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54013 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54030 - \"OPTIONS /data/duplicate_row/?rowIndex=10&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54030 - \"PUT /data/duplicate_row/?rowIndex=10&template=Previous+Inspections HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54030 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54030 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Row 10 duplicated successfully in worksheet Previous Inspections.\n", "INFO:     127.0.0.1:54030 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54030 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54038 - \"OPTIONS /data/duplicate_row/?rowIndex=7&template=Pressure+Vessel HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54038 - \"PUT /data/duplicate_row/?rowIndex=7&template=Pressure+Vessel HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54038 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54038 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Row 7 duplicated successfully in worksheet Data PV.\n", "INFO:     127.0.0.1:54038 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54038 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54042 - \"OPTIONS /data/update_cell/?indexrows=13&template=Pressure+Vessel&header=Component+Tag+Name&new_value=BOTTOM+HEAD HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54042 - \"PUT /data/update_cell/?indexrows=13&template=Pressure+Vessel&header=Component+Tag+Name&new_value=BOTTOM+HEAD HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58310 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58308 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58323 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58310 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58310 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58310 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58310 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58310 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58327 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58327 - \"GET /data/inspections/?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58327 - \"GET /data/inspections?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58327 - \"GET /data/rows/?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown&template=Piping HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58418 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58420 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58513 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58515 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58617 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58619 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58629 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58631 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58639 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58641 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58661 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58659 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58672 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58661 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58661 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58661 - \"GET /data/inspections/?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58661 - \"GET /data/inspections?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58661 - \"GET /data/rows/?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown&template=Piping HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58825 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58825 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58825 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58825 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58832 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58832 - \"GET /data/inspections/?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58832 - \"GET /data/inspections?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58832 - \"GET /data/rows/?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown&template=Piping HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58873 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58871 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58879 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58873 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58873 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58873 - \"GET /data/inspections/?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58873 - \"GET /data/inspections?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58873 - \"GET /data/rows/?plant=Javelina&unit=&system=&tag_name=Piping-1&asset_classification=Pipe&equipment_type=Unknown&template=Piping HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59093 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59092 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59101 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59093 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59104 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59104 - \"GET /data/inspections/?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59104 - \"GET /data/inspections?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59104 - \"GET /data/rows/?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown&template=Piping HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61927 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61924 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61938 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61927 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61938 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61940 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61953 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61952 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61961 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61953 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61961 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61963 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61970 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61961 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61983 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61970 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61986 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61986 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:61986 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61986 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62052 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62050 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62055 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62052 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62076 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62076 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62076 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62076 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62090 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62090 - \"GET /data/inspections/?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62090 - \"GET /data/inspections?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62090 - \"GET /data/rows/?plant=Javelina&unit=&system=&tag_name=Piping-3&asset_classification=Pipe&equipment_type=Unknown&template=Piping HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62092 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62092 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62092 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62092 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62092 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62092 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62092 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62092 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62351 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62351 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:62351 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:62351 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator&template=Storage+Tank HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58095 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58093 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58109 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58095 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58111 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58111 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58111 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58111 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58116 - \"OPTIONS /data/duplicate_row/?rowIndex=10&template=Pressure+Vessel HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58116 - \"PUT /data/duplicate_row/?rowIndex=10&template=Pressure+Vessel HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58116 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58116 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "/Users/<USER>/anaconda3/lib/python3.10/site-packages/gspread/worksheet.py:1046: UserWarning: [Deprecated][in version 6.0.0]: method signature will change to: 'Worksheet.update(value = [[]], range_name=)' arguments 'range_name' and 'values' will swap, values will be mandatory of type: 'list(list(...))'\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Row 10 duplicated successfully in worksheet Data PV.\n", "INFO:     127.0.0.1:58116 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58116 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58137 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58137 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58137 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58137 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator&template=Storage+Tank HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58149 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58149 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:58149 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58149 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=System+B&tag_name=D82-0108&asset_classification=Pressure+Vessel&equipment_type=Separator&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59347 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59347 - \"GET /data/inspections/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59347 - \"GET /data/inspections?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59347 - \"GET /data/rows/?plant=Javelina&unit=Unit+B&system=%3CSelect%3E&tag_name=Tank+2&asset_classification=Tank&equipment_type=Separator&template=Storage+Tank HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59355 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59355 - \"GET /data/inspections/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 307 Temporary Redirect\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59355 - \"GET /data/inspections?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59355 - \"GET /data/rows/?plant=&unit=Unit+B&system=System+B&tag_name=PV-2&asset_classification=Filter&equipment_type=Unknown&template=Pressure+Vessel HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:300: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59359 - \"OPTIONS /data/update_cell/?indexrows=10,14&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59359 - \"PUT /data/update_cell/?indexrows=10,14&template=Pressure+Vessel&header=Plant&new_value=Javelina HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59387 - \"GET /lookup/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59388 - \"GET /data/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:59387 - \"GET /data/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59396 - \"GET /lookup/ HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_54501/**********.py:88: UserWarning: DataFrame columns are not unique, some columns will be omitted.\n", "  data_dict = df.to_dict(orient='list')\n", "INFO:     Shutting down\n", "INFO:     Waiting for application shutdown.\n", "INFO:     Application shutdown complete.\n", "INFO:     Finished server process [54501]\n"]}], "source": ["import nest_asyncio\n", "from fastapi.middleware.cors import CORSMiddleware\n", "from fastapi import FastAPI, BackgroundTasks\n", "import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "import uvicorn\n", "import pandas as pd\n", "from typing import List\n", "from googleapiclient.errors import HttpError\n", "import numpy as np\n", "from queue import Queue\n", "import threading\n", "import time\n", "\n", "# Apply nest_asyncio to allow asynchronous operations in the notebook\n", "nest_asyncio.apply()\n", "\n", "app = FastAPI()\n", "\n", "origins = [\n", "    \"*\",  # React development server\n", "]\n", "app.add_middleware(\n", "    CORSMiddleware,\n", "    allow_origins=origins,\n", "    allow_credentials=True,\n", "    allow_methods=[\"*\"],\n", "    allow_headers=[\"*\"],\n", ")\n", "\n", "scope = [\"https://spreadsheets.google.com/feeds\", \"https://www.googleapis.com/auth/spreadsheets\",\n", "         \"https://www.googleapis.com/auth/drive.file\", \"https://www.googleapis.com/auth/drive\"]\n", "creds = ServiceAccountCredentials.from_json_keyfile_name(\"gsheetsKeys.json\", scope)\n", "client = gspread.authorize(creds)\n", "\n", "sheets_info = {\n", "        \"Data Piping\": {\"start_row\": 4, \"type\": \"Piping\"},\n", "        \"Data PV\"    : {\"start_row\": 6, \"type\": \"Pressure Vessel\"},\n", "        \"Data ST\"    : {\"start_row\": 5, \"type\": \"Storage Tank\"},\n", "\n", "\n", "    }\n", "\n", "def clean_dataframe(df):\n", "    df.replace([np.inf, -np.inf], np.nan, inplace=True)\n", "    df.dropna(how='all', inplace=True)\n", "    df.fillna(\"\", inplace=True)\n", "    return df\n", "\n", "def extract_desired_columns_from_df(df, desired_columns):\n", "    return df[desired_columns + [\"Template\"]]\n", "\n", "def get_column_letter(col_num):\n", "    string = \"\"\n", "    while col_num > 0:\n", "        col_num, remainder = divmod(col_num - 1, 26)\n", "        string = chr(65 + remainder) + string\n", "    return string\n", "\n", "# Queue setup\n", "task_queue = Queue()\n", "\n", "def worker():\n", "    while True:\n", "        task = task_queue.get()\n", "        if task is None:\n", "            break\n", "        task()\n", "        task_queue.task_done()\n", "        time.sleep(1)\n", "\n", "threading.Thread(target=worker, daemon=True).start()\n", "     \n", "\n", "\n", "@app.get(\"/lookup/\")\n", "def read_lookup():\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name).worksheet('Lookup')\n", "\n", "    # Fetching all data from the \"Lookup\" sheet\n", "    all_data = sheet.get_all_values()\n", "    headers = all_data[0]\n", "    df = pd.DataFrame(all_data[1:], columns=headers)\n", "\n", "    # Constructing the dictionary with header: list values\n", "    data_dict = df.to_dict(orient='list')\n", "\n", "    return data_dict\n", "\n", "@app.get(\"/data/\")\n", "def read_data():\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "\n", "    dfs = {}\n", "    validation_errors = []\n", "\n", "    # Create a batch request for all worksheets\n", "    ranges = {\n", "        \"Data Piping\": \"A:F\",\n", "        \"Data PV\": \"A:AV\",\n", "        \"Data ST\": \"A:BD\"\n", "    }\n", "    response = sheet.values_batch_get([f\"{ws_name}!{range_val}\" for ws_name, range_val in ranges.items()])\n", "    # Extract values from the response\n", "    all_data = {ws_name: r.get('values', []) for ws_name, r in zip(ranges.keys(), response['valueRanges'])}\n", "\n", "    for worksheet_name, info in sheets_info.items():\n", "        headers = all_data[worksheet_name][info[\"start_row\"] - 1]\n", "        adjusted_data = [row + [\"\"] * (len(headers) - len(row)) for row in all_data[worksheet_name][info[\"start_row\"]:]]\n", "        df = pd.DataFrame(adjusted_data, columns=headers)\n", "        \n", "        # Add the \"type\" column\n", "        df[\"Template\"] = info[\"type\"]\n", "\n", "        dfs[worksheet_name] = df\n", "\n", "    # Validate unique combinations\n", "    columns_to_validate = [\"Plant\", \"Unit\", \"System\", \"Tag Name\", \"Asset Classification\", \"Equipment Type\"]\n", "\n", "    for ws_name in [\"Data PV\", \"Data ST\"]:\n", "        for comb in dfs[ws_name][columns_to_validate].drop_duplicates().values:\n", "            condition = (dfs[ws_name][columns_to_validate].values == comb).all(axis=1)\n", "            matching_rows = dfs[ws_name][condition]\n", "            \n", "            # If only one row for the combination, just proceed\n", "            if len(matching_rows) == 1:\n", "                continue\n", "            \n", "            if not matching_rows.drop(columns=columns_to_validate).duplicated(keep=False).all():\n", "                validation_errors.append({\"template\": ws_name, \"combination\": comb.tolist()})\n", "                # Remove the rows with validation errors from dfs\n", "                dfs[ws_name] = dfs[ws_name][~condition]\n", "\n", "    # Extracting the first row of each unique combination\n", "    final_df = pd.concat([dfs[\"Data PV\"].drop_duplicates(subset=columns_to_validate),\n", "                          dfs[\"Data ST\"].drop_duplicates(subset=columns_to_validate),\n", "                          dfs[\"Data Piping\"].drop_duplicates(subset=columns_to_validate)], ignore_index=True)\n", "\n", "    desired_columns = [\"Plant\", \"Unit\", \"System\", \"Tag Name\", \"Asset Classification\", \"Equipment Type\"]\n", "    final_df = extract_desired_columns_from_df(final_df, desired_columns)\n", "\n", "    final_df = clean_dataframe(final_df)\n", "    mask = (final_df[columns_to_validate] == '').all(axis=1)\n", "    final_df = final_df[~mask]\n", "\n", "    result = {\n", "        \"headers\": list(final_df.columns),\n", "        \"data\": final_df.values.tolist(),\n", "        \"validation\": validation_errors  # This will always be returned, but might be empty if there are no errors.\n", "    }\n", "\n", "    return result\n", "\n", "\n", "@app.get(\"/data/row/\")\n", "def get_row(\n", "    plant: str,\n", "    unit: str,\n", "    system: str,\n", "    tag_name: str,\n", "    asset_classification: str,\n", "    equipment_type: str,\n", "    template: str\n", "):\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "\n", "    worksheet_name = None\n", "    header_start_row = None\n", "    for key, value in sheets_info.items():\n", "        if value[\"type\"] == template:\n", "            worksheet_name = key\n", "            header_start_row = value[\"start_row\"] - 1\n", "            break\n", "\n", "    if not worksheet_name:\n", "        return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "    range_name = f\"{worksheet_name}\"  # Get the entire range of the sheet\n", "    response = sheet.values_get(range_name)\n", "    data = response.get('values', [])\n", "    headers = data[header_start_row]\n", "    adjusted_data = [row + [\"\"] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]\n", "    df = pd.DataFrame(adjusted_data, columns=headers)\n", "\n", "    condition = (\n", "        (df[\"Plant\"] == plant) &\n", "        (df[\"Asset Classification\"] == asset_classification) &\n", "        (df[\"Unit\"] == unit) &\n", "        (df[\"System\"] == system) &\n", "        (df[\"Tag Name\"] == tag_name) &\n", "        (df['Equipment Type'] == equipment_type)\n", "    )\n", "    filtered_df = df[condition]\n", "    num_rows = len(filtered_df)\n", "    if num_rows > 0:\n", "        row_dict = filtered_df.to_dict(orient='records')[0]\n", "        row_dict[\"rowIndex\"] = list(map(int, filtered_df.index))\n", "        return row_dict\n", "    elif num_rows == 0:\n", "        return {\"error\": \"No rows found matching the criteria.\"}\n", "    \n", "\n", "@app.put(\"/data/update_cell/\")\n", "def update_cells(\n", "    indexrows: str,\n", "    template: str,\n", "    header: str,\n", "    new_value: str\n", "):\n", "    # Convert comma-separated string to a list of integers\n", "    indexrows_list = [int(index) for index in indexrows.split(\",\")]\n", "\n", "    if template == \"Previous Inspections\":\n", "        worksheet_name = \"Previous Inspections\"\n", "        header_start_row = 1  # Assuming headers are in the 2nd row\n", "\n", "    else:\n", "        # Determine the worksheet name and header_start_row from the template\n", "        worksheet_name, header_start_row = None, None\n", "        for ws_name, info in sheets_info.items():\n", "            if info[\"type\"] == template:\n", "                worksheet_name = ws_name\n", "                header_start_row = info[\"start_row\"] - 1\n", "                break\n", "\n", "    if not worksheet_name:\n", "        return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "    # Open the Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "    \n", "    # Get the correct worksheet\n", "    ws = sheet.worksheet(worksheet_name)\n", "\n", "    # Fetch headers\n", "    headers = ws.row_values(header_start_row + 1)  # +1 because Google Sheets index starts from 1\n", "\n", "    # Identify the column index for the given header\n", "    try:\n", "        col_index = headers.index(header) + 1  # +1 because Google Sheets index starts from 1\n", "    except ValueError:\n", "        return {\"error\": f\"Header {header} not found in worksheet {worksheet_name}\"}\n", "\n", "    # Update the cells for all specified rows\n", "    for indexrow in indexrows_list:\n", "        ws.update_cell(indexrow, col_index, new_value)\n", "\n", "    return {\"message\": f\"Cells updated successfully in worksheet {worksheet_name} for header {header}.\"}\n", "\n", "@app.get(\"/data/rows/\")\n", "def get_rows(\n", "    plant: str,\n", "    unit: str,\n", "    system: str,\n", "    tag_name: str,\n", "    asset_classification: str,\n", "    equipment_type: str,\n", "    template: str\n", "):\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "\n", "    worksheet_name = None\n", "    header_start_row = None\n", "    for key, value in sheets_info.items():\n", "        if value[\"type\"] == template:\n", "            worksheet_name = key\n", "            header_start_row = value[\"start_row\"] - 1\n", "            break\n", "\n", "    if not worksheet_name:\n", "        return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "    range_name = f\"{worksheet_name}\"  # Get the entire range of the sheet\n", "    response = sheet.values_get(range_name)\n", "    data = response.get('values', [])\n", "    headers = data[header_start_row]\n", "    adjusted_data = [row + [\"\"] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]\n", "    df = pd.DataFrame(adjusted_data, columns=headers)\n", "\n", "    condition = (\n", "        (df[\"Plant\"] == plant) &\n", "        (df[\"Asset Classification\"] == asset_classification) &\n", "        (df[\"Unit\"] == unit) &\n", "        (df[\"System\"] == system) &\n", "        (df[\"Tag Name\"] == tag_name) &\n", "        (df['Equipment Type'] == equipment_type)\n", "    )\n", "    filtered_df = df[condition]\n", "    num_rows = len(filtered_df)\n", "    if num_rows > 0:\n", "        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n", "        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices\n", "        # Attach rowIndex to each individual row\n", "        for idx, row in enumerate(rows_list):\n", "            row[\"rowIndex\"] = row_indices[idx]\n", "\n", "        return {\"rows\": rows_list, \"rowIndices\": row_indices}\n", "    elif num_rows == 0:\n", "        return {\"error\": \"No rows found matching the criteria.\"}\n", "\n", "@app.put(\"/data/duplicate_row/\")\n", "def duplicate_row(rowIndex: int, template: str, background_tasks: BackgroundTasks):\n", "\n", "    # Define the task function with the logic to duplicate the row\n", "    def task_function():\n", "        try:\n", "            # Open the Google Sheet using its specific name\n", "            sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "            sheet = client.open(sheet_name)\n", "\n", "            # Determine the worksheet name from the template\n", "            worksheet_name = None\n", "            if template == \"Previous Inspections\":\n", "                worksheet_name = \"Previous Inspections\"\n", "            else:\n", "                for ws_name, info in sheets_info.items():\n", "                    if info[\"type\"] == template:\n", "                        worksheet_name = ws_name\n", "                        break\n", "\n", "            if not worksheet_name:\n", "                return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "            # Get the correct worksheet\n", "            ws = sheet.worksheet(worksheet_name)\n", "\n", "            # Retrieve the data from the specified row\n", "            row_data = ws.row_values(rowIndex)\n", "\n", "            # Determine the new row's position (append to the end)\n", "            num_rows = ws.row_count\n", "            new_row_index = num_rows + 1\n", "\n", "            # Insert the new row at the end\n", "            ws.add_rows(1)\n", "\n", "            # Add data to the new row\n", "            ws.update('A' + str(new_row_index), [row_data])\n", "\n", "            print(f\"Row {rowIndex} duplicated successfully in worksheet {worksheet_name}.\")\n", "        except HttpError as e:\n", "            print(f\"Google Sheets API Error: {e.resp.reason}\")\n", "        except Exception as e:\n", "            print(f\"An error occurred: {str(e)}\")\n", "\n", "    # Enqueue the task function for processing\n", "    task_queue.put(task_function)\n", "\n", "    return {\"message\": \"Task to duplicate row has been enqueued for processing\"}\n", "\n", "\n", "@app.delete(\"/data/delete_row/\")\n", "def delete_row(rowIndex: int, template: str):\n", "    worksheet_name = None\n", "    \n", "    \n", "    if template == \"Previous Inspections\":\n", "        worksheet_name = \"Previous Inspections\"\n", "    else:\n", "    \n", "        for ws_name, info in sheets_info.items():\n", "            if info[\"type\"] == template:\n", "                worksheet_name = ws_name\n", "                break\n", "\n", "    if not worksheet_name:\n", "        return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "    try:\n", "        sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "        sheet = client.open(sheet_name)\n", "        ws = sheet.worksheet(worksheet_name)\n", "\n", "        # Get the total number of columns in the sheet\n", "        num_cols = len(ws.row_values(rowIndex))\n", "\n", "        # Create a list of blank values for all columns\n", "        blank_values = [''] * num_cols\n", "\n", "        # Set the entire row to blank using a batch update\n", "        end_column_letter = get_column_letter(num_cols)\n", "        range_name = f\"A{rowIndex}:{end_column_letter}{rowIndex}\"  # Corrected range format\n", "        ws.update(range_name, [blank_values])\n", "\n", "        return {\"message\": f\"Row {rowIndex} cleared successfully in worksheet {worksheet_name}.\"}\n", "    except HttpError as e:\n", "        return {\"error\": f\"Google Sheets API Error: {e.resp.reason}\"}\n", "    except Exception as e:\n", "        return {\"error\": f\"An error occurred: {str(e)}\"}\n", "\n", "\n", "@app.get(\"/data/inspections\")\n", "def get_previous_inspection_row(\n", "    plant: str,\n", "    unit: str,\n", "    system: str,\n", "    tag_name: str,\n", "    asset_classification: str,\n", "    equipment_type: str\n", "):\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "\n", "    worksheet_name = \"Previous Inspections\"\n", "    header_start_row = 1  # Assuming headers are in the 2nd row\n", "\n", "    # Get data from the specified range in the \"Previous Inspections\" worksheet\n", "    range_name = f\"{worksheet_name}\"  # Get the entire range of the sheet\n", "    response = sheet.values_get(range_name)\n", "    data = response.get('values', [])\n", "    headers = data[header_start_row]\n", "    adjusted_data = [row + [\"\"] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]\n", "    df = pd.DataFrame(adjusted_data, columns=headers)\n", "\n", "    \n", "\n", "    \n", "    \n", "    condition = (\n", "        (df[\"Plant\"] == plant) &\n", "        (df[\"Asset Classification\"] == asset_classification) &\n", "        (df[\"Unit\"] == unit) &\n", "        (df[\"System\"] == system) &\n", "        (df[\"Tag Name\"] == tag_name)&\n", "        (df['Equipment Type'] == equipment_type)\n", "    )\n", "    filtered_df = df[condition]\n", "    num_rows = len(filtered_df)\n", "    if num_rows > 0:\n", "        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n", "        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices\n", "        \n", "        # Attach rowIndex to each individual row\n", "        for idx, row in enumerate(rows_list):\n", "            row[\"rowIndex\"] = row_indices[idx]\n", "\n", "        return {\"rows\": rows_list, \"rowIndices\": row_indices}\n", "    elif num_rows == 0:\n", "        return {\"error\": \"No rows found matching the criteria.\"}\n", "\n", "\n", "\n", "\n", "\n", "uvicorn.run(app, host=\"0.0.0.0\", port=8010)\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\n", "def col_index_to_letter(col_index):\n", "    \"\"\"Convert a zero-based column index to a string indicating the column letter(s).\"\"\"\n", "    column_letter = \"\"\n", "    while col_index > 0:\n", "        col_index, remainder = divmod(col_index - 1, 26)\n", "        column_letter = chr(65 + remainder) + column_letter\n", "    return column_letter"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["'GG'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["col_index_to_letter(189)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}