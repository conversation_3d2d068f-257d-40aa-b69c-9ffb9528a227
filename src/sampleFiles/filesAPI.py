from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Response, Depends
import nest_asyncio
from fastapi.middleware.cors import CORSMiddleware
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseDownload
import gspread
import uvicorn
from oauth2client.service_account import ServiceAccountCredentials
import io
import pandas as pd 
import time
import gspread
import os
import nest_asyncio
import json
# Apply nest_asyncio to allow asynchronous operations in Jupyter
nest_asyncio.apply()

app = FastAPI()

origins = [
    "http://localhost:3000",
    "https://visualaimdatacollection.netlify.app"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
MS_MIME_TYPES = {
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'application/vnd.google-apps.spreadsheet',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'application/vnd.google-apps.document',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'application/vnd.google-apps.presentation',
    'application/msword': 'application/vnd.google-apps.document'
}

def get_gspread_client():
    
    scope = [
        "https://spreadsheets.google.com/feeds",
        "https://www.googleapis.com/auth/spreadsheets",
        "https://www.googleapis.com/auth/drive.file",
        "https://www.googleapis.com/auth/drive"
    ]
    
    try:
        creds = ServiceAccountCredentials.from_json_keyfile_name("gsheetsKeys.json", scope)
        client = gspread.authorize(creds)
        return client
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error initializing gspread: {str(e)}")


def get_google_drive_service():
   

    scope = [
        "https://www.googleapis.com/auth/drive",
    ]
    
    try:
        creds = ServiceAccountCredentials.from_json_keyfile_name("gsheetsKeys.json", scope)
        service = build('drive', 'v3', credentials=creds)
        return service
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error initializing Google Drive service: {str(e)}")





# ... (Keep other routes and functions here)
def fetch_data_from_sheet_with_gspread(client: gspread.Client,
                                       sheet_name="DEV_HE_Data Collection template Fix Equipment",
                                       tab_name="Files"):
    try:
        # Open the Google Sheet using its name
        sheet = client.open(sheet_name)

        # Select the tab/sheet
        worksheet = sheet.worksheet(tab_name)

        # Get the records
        records = worksheet.get_all_records()
        return records
    except gspread.exceptions.GSpreadException as ge:
        raise HTTPException(status_code=400, detail=str(ge))
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching the data: {str(e)}")


@app.get("/list-files-static/")
def file_list_static(client: gspread.Client = Depends(get_gspread_client)):
    return {"files": fetch_data_from_sheet_with_gspread(client)}


@app.get("/get-file/{file_id}")
def get_file(file_id: str, service = Depends(get_google_drive_service)):
    try:
        # Fetch file metadata
        file_info = service.files().get(fileId=file_id).execute()
        # Check the file's MIME type
        if file_info['mimeType'] == 'application/pdf':
            # If the file is already in PDF format, fetch and return the file
            request = service.files().get_media(fileId=file_id)
            fh = io.BytesIO()
            downloader = MediaIoBaseDownload(fh, request)
            done = False
            while done is False:
                status, done = downloader.next_chunk()
            fh.seek(0)
            return Response(content=fh.read(), media_type='application/pdf')
        else:
            raise HTTPException(status_code=400, detail="File is not a PDF.")
    except HttpError as e:
        error_details = e._get_reason()
        raise HTTPException(status_code=400, detail=f"Error fetching the file from Google Drive: {error_details}")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Error fetching the file: {str(e)}")

uvicorn.run(app, host="0.0.0.0", port=8020)
