{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#!/usr/bin/env python3\n", "# -*- coding: utf-8 -*-\n", "\"\"\"\n", "Created on Sun Oct  1 20:54:29 2023\n", "\n", "@author: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "\"\"\"\n", "\n", "import nest_asyncio\n", "from fastapi.middleware.cors import CORSMiddleware\n", "from fastapi import FastAPI, BackgroundTasks,Response,Request,Depends\n", "from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials\n", "import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "import uvicorn\n", "import pandas as pd\n", "from googleapiclient.errors import HttpError\n", "import numpy as np\n", "from queue import Queue\n", "import threading\n", "import time\n", "import pandas as pd\n", "from google.oauth2 import id_token\n", "from google.auth.transport import requests as google_requests\n", "from fastapi import HTTPException\n", "from fastapi import FastAPI, HTTPException\n", "import requests\n", "import os\n", "import json\n", "import jwt\n", "from jwt import PyJWTError\n", "from fastapi.security import OAuth2PasswordBearer\n", "from dotenv import load_dotenv\n", "from datetime import datetime, timedelta \n", "from fastapi import Depends, Cookie, HTTPException\n", "import logging\n", "from google.cloud import secretmanager\n", "from google.oauth2 import service_account\n", "logging.basicConfig(level=logging.DEBUG)\n", "\n", "# Apply nest_asyncio to allow asynchronous operations in the notebook\n", "nest_asyncio.apply()\n", "lock = threading.Lock()\n", "app = FastAPI()\n", "load_dotenv()\n", "oauth2_scheme = OAuth2PasswordBearer(tokenUrl=\"token\")\n", "CLIENT_ID = os.getenv(\"GOOGLE_CLIENT_ID\")\n", "\n", "ALGORITHM = \"HS256\"\n", "JWT_CUSTOM_CLAIM_KEY = os.getenv(\"JWT_CUSTOM_CLAIM_KEY\")\n", "JWT_CUSTOM_CLAIM_VALUE = os.getenv(\"JWT_CUSTOM_CLAIM_VALUE\")\n", "SECRET_KEY=os.getenv(\"SECRET_KEY\")\n", "\n", "\n", "app.add_middleware(\n", "    CORSMiddleware,\n", "    allow_origins=[\"http://localhost:3000\",\"http://localhost\",\"https://ocidatacollectionapp.netlify.app\"],  # Adjust to match your client's origin\n", "    allow_credentials=True,\n", "    allow_methods=[\"*\"],\n", "    allow_headers=[\"*\"],\n", ")\n", "\n", "scope = [\"https://spreadsheets.google.com/feeds\", \"https://www.googleapis.com/auth/spreadsheets\",\n", "         \"https://www.googleapis.com/auth/drive.file\", \"https://www.googleapis.com/auth/drive\"]\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "# Load main service account credentials from environment variable\n", "main_credentials_json = json.loads(os.getenv('SECRET_MANAGER_CLIENT'))\n", "main_credentials = service_account.Credentials.from_service_account_info(main_credentials_json)\n", "\n", "# Initialize the Secret Manager client with the main service account\n", "secret_manager_client = secretmanager.SecretManagerServiceClient(credentials=main_credentials)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["<google.cloud.secretmanager_v1.services.secret_manager_service.client.SecretManagerServiceClient at 0x146044ee0>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["secret_manager_client"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def get_user_specific_secret(email, project_id):\n", "    # Map the user's email to the secret name\n", "    # This is a simple example; consider using a more secure/complex mapping strategy\n", "    secret_id_mapping = {\n", "        '<EMAIL>': 'lsalemi-OCI',\n", "    }\n", "    secret_id = secret_id_mapping.get(email)\n", "    if not secret_id:\n", "        raise ValueError(f\"No secret found for user {email}\")\n", "\n", "    # Construct the resource name of the secret version\n", "    name = f\"projects/{project_id}/secrets/{secret_id}/versions/latest\"\n", "\n", "    # Access the secret version and decode the payload\n", "    response = secret_manager_client.access_secret_version(request={\"name\": name})\n", "    user_secret_json = response.payload.data.decode(\"UTF-8\")\n", "\n", "    # Parse the JSON string into a dictionary\n", "    user_secret_dict = json.loads(user_secret_json)\n", "    return user_secret_dict\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["project_id = \"ocr-331616\"\n", "user_email = \"<EMAIL>\"\n", "    # Fetch user-specific secret containing J<PERSON><PERSON> key\n", "user_secret_dict = get_user_specific_secret(user_email, project_id)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'type': 'service_account',\n", " 'project_id': 'oci-datacollectionapp',\n", " 'private_key_id': '48fec1e60075a0d5756a077eed2ef1e83ed5d2b8',\n", " 'private_key': '-----BEGIN PRIVATE KEY-----\\nMIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDDeFrn+XYkbCQd\\nOlOf+nRLxts2afPQUcABIETZBqiO+FwSOoewAc2CIAnft5ZIWE4wi3uvH3RFHXWU\\nZFzSusi4LfulYuaCwwwBctQMYiX/cJhLAHhLZLnjlhulJ9tXN9Ua9H9igYN1n5oT\\ndHUQLyulbCkKf2b0Har8YPwMY1DyveV+34qj0XjZAYZPI1zI8llZ/GASJ2zd+IC2\\nyGECGCAxpXKKm0o7uFNIpsey7l1vwWpPq1VwbKSdfBcoS168a9frbrwz8+tEIYOY\\nj0b222oWVqd0WPgLKG1l/RI5PLYVtO/tKVDTOqG7HQSnvPcuqn1+mkPVJ0d/IsK+\\n8sK67KOrAgMBAAECggEAWEDfXmwp1LEErzOV7R2B43Kxp8Q3oj8+vuNoYkpY0VA+\\nFbbaa3Os60r22kzD6/7+snlPixZbAxeVbj3QEQ7nMA0G2LmqFLDmm0LSjZLE0Ctw\\nfzCDn5qnOSOdcLhpllvteiGY4AtUpMc+I/mJHxkFDkei0gwKmj5I5HEPa7JtqmKb\\nZBFh3J12ryyQouIC7hY6Lu+JQ+dwIsFHtdyRpV1lm/gCZOakmQ/v5xUknYbkNnGI\\nbrKD5Gl1S7gGLEussGZlP2vxf7nC+UtWP9vxHN9H+9MClVewGMiffHl7gYgxbL/d\\n9PaYSsMa16ScKiy2oXFF3nR6yyJZvE3Tpvj4f3CQvQKBgQDqR64J9zH2qo98ojVJ\\nieaFhR/PZ/yuxGD+SOaOvZxXc4A4IGXV8RC/Z8tfRVUnnpGuXuOV+Ic3FqoAH7ef\\nRifRUx7IJzYdru3E1XODUl3oj4wR5TAuGQjp6tOMNsvg4tlJHvy7H6JIrjo2xF3g\\n9lTCwD3h1Fv87WzNuUUfnQpvhwKBgQDVl5NRzteWHAYXilN/Ho7BgpPYfeYQnTK6\\n8gI8Jda6FoEfo3qA7hS3DT6Kuw/DPDb47chsBwksyMhtTAkw1ne/r+QTY5/BALOM\\nRxWc9COOKDlK2lhAsNx9oJtrT2nSKq8W0Pj4gn0lXOJs3Gu7o7mkIV9YCQvt7N1e\\n0kpTCYaLvQKBgEA03ty6U4Yfnm1w4xvBYF0Na/dir1ySU2a9Qfqyums5K1W2g1JL\\n2+QpJRJ6JOlTyuc3+DV8xEnzuymHh8Jt7rGujwwZuvoNm/pK6Xp/E1+OH7wzzK2F\\njOzWv5140w1R/mzMwkTiWY6DuU6TKItj1zz8hLkkgwVhaGVFNk2As4B3AoGAFwkN\\n3ljMb2f+UDOe5arKApyujT6mvUv22KqZCxuZmOAsdNkQsS78FPHcuOGLxaaM1zPt\\n1Zi8qJPO6dq1cwruR0IU+vJXnF0TACH6dr7txrsX0rbRpchtPy8/E8ewxfXMhE1t\\nTH/mN01ZLhxnkgkKUGU4/ymam8Jb7YoIcgQo+pECgYA8n39ieosvwR8ydEWaGijN\\n04mN+tbAOObunFKoWAYaBAFZftXTQbPUJAcj2dHvKo0FY0+EoIYiRFiYrpKzDJ7c\\nTuIiSNjepey7XobjzVnZGHOKFhi0UUA83orhe6y7dGD8uCXTpfgNx/Vglu835aHq\\nkzAUPzPTbweZ+69AzDKuPg==\\n-----END PRIVATE KEY-----\\n',\n", " 'client_email': '<EMAIL>',\n", " 'client_id': '103282656061590650264',\n", " 'auth_uri': 'https://accounts.google.com/o/oauth2/auth',\n", " 'token_uri': 'https://oauth2.googleapis.com/token',\n", " 'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs',\n", " 'client_x509_cert_url': 'https://www.googleapis.com/robot/v1/metadata/x509/lsalemi%40oci-datacollectionapp.iam.gserviceaccount.com',\n", " 'universe_domain': 'googleapis.com'}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["user_secret_dict"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}