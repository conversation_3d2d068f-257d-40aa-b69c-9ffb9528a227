{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install fastapi uvicorn pydrive\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["zsh:1: no matches found: pyjwt[crypto]\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install pyjwt[crypto]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generated SECRET_KEY: 271b5e02d57cb283043cff3d882ce518fb7ffc1c61b493b4\n"]}], "source": ["import os\n", "\n", "# Generate a random secret key\n", "secret_key = os.urandom(24).hex()\n", "print(f\"Generated SECRET_KEY: {secret_key}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from fastapi import FastAPI, HTTPException, Response, Depends\n", "import nest_asyncio\n", "from fastapi.middleware.cors import CORSMiddleware\n", "from google.oauth2.credentials import Credentials\n", "from googleapiclient.discovery import build\n", "from googleapiclient.errors import HttpError\n", "from googleapiclient.http import MediaIoBaseDownload\n", "import gspread\n", "import uvicorn\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "import io\n", "import pandas as pd \n", "# Apply nest_asyncio to allow asynchronous operations in Jupyter\n", "nest_asyncio.apply()\n", "\n", "app = FastAPI()\n", "\n", "# Setup Google Drive API with gspread authentication\n", "scope = [\"https://www.googleapis.com/auth/drive\"]\n", "creds = ServiceAccountCredentials.from_json_keyfile_name(\"gsheetsKeys.json\", scope)\n", "\n", "\n", "\n", "origins = [\n", "    \"http://localhost:3000\",  # React development server\n", "]\n", "\n", "\n", "app.add_middleware(\n", "    CORSMiddleware,\n", "    allow_origins=origins,\n", "    allow_credentials=True,\n", "    allow_methods=[\"*\"],\n", "    allow_headers=[\"*\"],\n", ")\n", "\n", "\n", "# Build the Google Drive service\n", "service = build('drive', 'v3', credentials=creds)\n", "\n", "def fetch_files_from_folder(folder_id, current_path=\"\"):\n", "    items = []\n", "    # Pagination\n", "    page_token = None\n", "    while True:\n", "        # Query the files in the current folder with pagination support\n", "        response = service.files().list(q=f\"'{folder_id}' in parents and trashed=false\", \n", "                                        fields=\"nextPageToken, files(id, name, mimeType, parents)\",\n", "                                        pageToken=page_token).execute()\n", "        \n", "        files = response.get('files', [])\n", "        for file in files:\n", "            file_path = f\"{current_path}/{file['name']}\"\n", "            items.append({\n", "                \"id\": file['id'],\n", "                \"name\": file['name'],\n", "                \"mimeType\": file['mimeType'],\n", "                \"path\": file_path\n", "            })\n", "\n", "            # If the current item is a folder, fetch its content recursively\n", "            if file['mimeType'] == \"application/vnd.google-apps.folder\":\n", "                items.extend(fetch_files_from_folder(file['id'], file_path))\n", "\n", "        # Update the page token for the next iteration\n", "        page_token = response.get('nextPageToken', None)\n", "        if page_token is None:\n", "            break\n", "    df = pd.DataFrame(items)\n", "    df.to_csv('files.csv')\n", "    return items\n", "\n", "@app.get(\"/list-files/\")\n", "def list_files():\n", "    root_folder_id = \"1Re9r_zZqz9W19dl4INwkKKOyBkFOK3Fm\"\n", "    return {\"files\": fetch_files_from_folder(root_folder_id)}\n", "\n", "@app.get(\"/get-file/{file_id}\")\n", "def get_file(file_id: str):\n", "    try:\n", "        # Fetch file metadata\n", "        file_info = service.files().get(fileId=file_id).execute()\n", "\n", "        # Try to export the file as PDF first\n", "        try:\n", "            request = service.files().export_media(fileId=file_id, mimeType='application/pdf')\n", "        except HttpError:\n", "            # If PDF export fails, download the file in its original format\n", "            request = service.files().get_media(fileId=file_id)\n", "\n", "        fh = io.BytesIO()\n", "        downloader = MediaIoBaseDownload(fh, request)\n", "        done = False\n", "        while done is False:\n", "            status, done = downloader.next_chunk()\n", "        \n", "        fh.seek(0)\n", "        \n", "        if 'application/pdf' in request.uri:\n", "            return Response(content=fh.read(), media_type='application/pdf')\n", "        else:\n", "            return Response(content=fh.read(), media_type=file_info['mimeType'])\n", "\n", "    except HttpError as e:\n", "        # Specific error handling for Google Drive related errors\n", "        error_details = e._get_reason()\n", "        raise HTTPException(status_code=400, detail=f\"Error fetching the file from Google Drive: {error_details}\")\n", "    except Exception as e:\n", "        # General error handling\n", "        raise HTTPException(status_code=400, detail=f\"Error fetching the file: {e}\")\n", "\n", "\n", "# Start Uvicorn running on port 8020\n", "\n", "uvicorn.run(app, host=\"0.0.0.0\", port=8020)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}