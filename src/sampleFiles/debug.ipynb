{"cells": [{"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['headers', 'data', 'validation'])\n", "[{'template': 'Data PV', 'combination': ['Javelina', 'Unit A', 'System C', 'PV-4', 'Pressure Vessel', 'Heat Exchanger']}]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Plant</th>\n", "      <th>Unit</th>\n", "      <th>System</th>\n", "      <th>Tag Name</th>\n", "      <th>Asset Classification</th>\n", "      <th>Equipment Type</th>\n", "      <th>Template</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Javelina</td>\n", "      <td>Unit PV-1</td>\n", "      <td>System PV-1</td>\n", "      <td>PV-1</td>\n", "      <td><PERSON><PERSON> Vessel</td>\n", "      <td>Reactor</td>\n", "      <td><PERSON><PERSON> Vessel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Javelina</td>\n", "      <td>Unit PV-2</td>\n", "      <td>System PV-2</td>\n", "      <td>PV-2</td>\n", "      <td><PERSON><PERSON> Vessel</td>\n", "      <td>Column</td>\n", "      <td><PERSON><PERSON> Vessel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Javelina</td>\n", "      <td>Unit PV-2</td>\n", "      <td>System PV-2</td>\n", "      <td>PV-3</td>\n", "      <td><PERSON><PERSON> Vessel</td>\n", "      <td>Column</td>\n", "      <td><PERSON><PERSON> Vessel</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Javelina</td>\n", "      <td>Tank-1 Unit</td>\n", "      <td>Tank-1 System</td>\n", "      <td>Tank-1</td>\n", "      <td>Tank</td>\n", "      <td>Low Pressure Tank</td>\n", "      <td>Storage Tank</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Javelina</td>\n", "      <td>Tank-2 Unit</td>\n", "      <td>Tank-2 System</td>\n", "      <td>Tank-2</td>\n", "      <td>Tank</td>\n", "      <td>Storage Tank</td>\n", "      <td>Storage Tank</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Javelina</td>\n", "      <td>Unit A</td>\n", "      <td>System A</td>\n", "      <td>Piping-3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Unknown</td>\n", "      <td>Piping</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Javelina</td>\n", "      <td>Old Unit</td>\n", "      <td>System C</td>\n", "      <td>Piping-2</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Unknown</td>\n", "      <td>Piping</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>Javelina</td>\n", "      <td>Unit D</td>\n", "      <td>System C</td>\n", "      <td>Piping-3</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Unknown</td>\n", "      <td>Piping</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Javelina</td>\n", "      <td>Unit B</td>\n", "      <td>Unit B</td>\n", "      <td>Piping-4</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Unknown</td>\n", "      <td>Piping</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Javelina</td>\n", "      <td>Unit A</td>\n", "      <td>Unit B</td>\n", "      <td>Piping-5</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Unknown</td>\n", "      <td>Piping</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Plant         Unit         System  Tag Name Asset Classification  \\\n", "0  Javelina    Unit PV-1    System PV-1      PV-1      Pressure Vessel   \n", "1  Javelina    Unit PV-2    System PV-2      PV-2      Pressure Vessel   \n", "2  Javelina    Unit PV-2    System PV-2      PV-3      Pressure Vessel   \n", "3  Javelina  Tank-1 Unit  Tank-1 System    Tank-1                 Tank   \n", "4  Javelina  Tank-2 Unit  Tank-2 System    Tank-2                 Tank   \n", "5  Javelina       Unit A       System A  Piping-3                 Pipe   \n", "6  Javelina     Old Unit       System C  Piping-2                 Pipe   \n", "7  Javelina       Unit D       System C  Piping-3                 Pipe   \n", "8  Javelina       Unit B         Unit B  Piping-4                 Pipe   \n", "9  Javelina       Unit A         Unit B  Piping-5                 Pipe   \n", "\n", "      Equipment Type         Template  \n", "0            Reactor  Pressure Vessel  \n", "1             Column  Pressure Vessel  \n", "2             Column  Pressure Vessel  \n", "3  Low Pressure Tank     Storage Tank  \n", "4       Storage Tank     Storage Tank  \n", "5            Unknown           Piping  \n", "6            Unknown           Piping  \n", "7            Unknown           Piping  \n", "8            Unknown           Piping  \n", "9            Unknown           Piping  "]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "import pandas as pd\n", "\n", "# Fetch data from the FastAPI endpoint\n", "response = requests.get(\"http://127.0.0.1:8010/data/\")\n", "data = response.json()\n", "\n", "print(data.keys())\n", "print(data['validation'])\n", "\n", "# Convert the data into a Pandas DataFrame\n", "df = pd.DataFrame(data['data'], columns=data['headers'])\n", "\n", "# Display the DataFrame\n", "df\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pydub\n", "  Downloading pydub-0.25.1-py2.py3-none-any.whl (32 kB)\n", "Collecting simpleaudio\n", "  Downloading simpleaudio-1.0.4.tar.gz (2.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m2.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m\n", "\u001b[?25h  Preparing metadata (setup.py) ... \u001b[?25ldone\n", "\u001b[?25hBuilding wheels for collected packages: simpleaudio\n", "  Building wheel for simpleaudio (setup.py) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for simpleaudio: filename=simpleaudio-1.0.4-cp310-cp310-macosx_11_0_arm64.whl size=2036387 sha256=2758602c28afcd05b10436f54aae1d89fa8db6c30779a955e557f2e2a646de68\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/2e/ac/7c/cb159901525517cff918e26d104f138e42d9ed65369b744b85\n", "Successfully built simpleaudio\n", "Installing collected packages: simpleaudio, pydub\n", "Successfully installed pydub-0.25.1 simpleaudio-1.0.4\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install pydub <PERSON>io"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'rows': [{'Allowable Stress (S)': '',\n", "           'Allowable Stress Unit': '',\n", "           'Ambient State': '',\n", "           'Assessment Category': '',\n", "           'Asset Classification': 'Pressure Vessel',\n", "           'Buried?': '',\n", "           'CO3 Concentration': '',\n", "           'CO3 Concentration Unit': '',\n", "           'Categorized Pressure': 'Design (Ontwerpcondities)',\n", "           'Cladding': '',\n", "           'Cladding Installation Date': '',\n", "           'Cladding Type': '',\n", "           'Component Lining': '',\n", "           'Component Tag Name': '',\n", "           'Component Type': '',\n", "           'Concentration': '',\n", "           'Conical Apex Angle': '',\n", "           'Conical Apex Angle Unit': '',\n", "           'Construction Code': '',\n", "           'Construction Date': '',\n", "           'Contains Acid Gas Treating Amines?': '',\n", "           'Corrosion Allowance': '',\n", "           'Corrosion Allowance Unit': '',\n", "           'Cracks Present?': '',\n", "           'Crown Radius': '',\n", "           'Crown Radius Unit': '',\n", "           'Date in Service': '',\n", "           'Description': '<PERSON>boiler',\n", "           'Design Minimum Thickness': '',\n", "           'Design Minimum Thickness Unit': '',\n", "           'Design Pressure': '',\n", "           'Design Pressure Unit': '',\n", "           'Design Temperature': '',\n", "           'Design Temperature Unit': '',\n", "           'Distance Between each support': '',\n", "           'Distance Between each support Unit': '',\n", "           'Do External Damage Conditions Apply? ': '',\n", "           'Elliptical Ratio': '',\n", "           'Equipment Type': 'Adsorber',\n", "           'Exchanger Type ': '',\n", "           'Exposed to Chloride Containing Mist, Fluids or Solid?': '',\n", "           'External Coating': '',\n", "           'External Coating Installation Date': '',\n", "           'External Coating Quality': '',\n", "           'External Coating Type': '',\n", "           'External Corrosion Rate Base Metal (Cr,bm)': '',\n", "           'External Corrosion Rate Base Metal Unit': '',\n", "           'External Corrosion Rate Long Term (CRst)': '',\n", "           'External Corrosion Rate Long Term Unit': '',\n", "           'External Corrosion Rate Short Term \\u200b\\u200b\\u200b\\u200b\\u200b\\u200b\\u200b(CRst)': '',\n", "           'External Corrosion Rate Short Term \\u200b\\u200b\\u200b\\u200b\\u200b\\u200b\\u200bUnit': '',\n", "           'External Corrosion Type': '<Select>',\n", "           'External Half Life': '',\n", "           'External Half Life Unit': 'years',\n", "           'External Last Thickness Reading (tri)': '',\n", "           'External Last Thickness Reading Date': '',\n", "           'External Last Thickness Reading Unit': '',\n", "           'External Remaining Corrosion Allowance': '',\n", "           'External Remaining Corrosion Allowance Unit': '',\n", "           'External Remaining Life': '',\n", "           'External Remaining Life Unit': 'years',\n", "           'Fluid Components': '',\n", "           'Fluid Group': 'Group 1',\n", "           'Fluid Hazardous': 'Toxic',\n", "           'Fluid Phase': '',\n", "           'Geometry': '',\n", "           'H2S Content of Water': '',\n", "           'H2S Content of Water Unit': '',\n", "           'Heat Trace?': '',\n", "           'Heat Treatment Temperature': '',\n", "           'Heat Treatment Temperature Unit': '',\n", "           'Heat Treatment Time': '',\n", "           'Hemispherical Radius': '',\n", "           'Hemispherical Radius Unit': '',\n", "           'Initial Commissioning Date': '',\n", "           'Inside Diameter': '',\n", "           'Inside Diameter Unit': '',\n", "           'Inspection Directive': '',\n", "           'Inspection Directive Details': '',\n", "           'Insulation': '',\n", "           'Insulation Type ': '',\n", "           'Internal Coating': '',\n", "           'Internal Coating Installation Date': '',\n", "           'Internal Coating Quality': '',\n", "           'Internal Coating Type': '',\n", "           'Internal Corrosion Rate Base Metal (Cr,bm)': '',\n", "           'Internal Corrosion Rate Base Metal Unit': '',\n", "           'Internal Corrosion Rate Long Term (CRst)': '',\n", "           'Internal Corrosion Rate Long Term Unit': '',\n", "           'Internal Corrosion Rate Short Term (CRst)': '',\n", "           'Internal Corrosion Rate Short Term Unit': '',\n", "           'Internal Corrosion Type': '<Select>',\n", "           'Internal Entry Restricted by Diameter or Construction': '',\n", "           'Internal Half Life': '',\n", "           'Internal Half Life Unit': 'years',\n", "           'Internal Last Thickness Reading (tri)': '',\n", "           'Internal Last Thickness Reading Date': '',\n", "           'Internal Last Thickness Reading Unit': '',\n", "           'Internal Remaining Corrosion Allowance': '',\n", "           'Internal Remaining Corrosion Allowance Unit': '',\n", "           'Internal Remaining Life': '',\n", "           'Internal Remaining Life Unit': 'years',\n", "           'Internals': '',\n", "           'Is in Cooling Water Service?': '',\n", "           'Joint Efficiency (E)': '',\n", "           'Knuckle Radius': '',\n", "           'Knuckle Radius Unit': '',\n", "           'Legs': '',\n", "           'Length': '',\n", "           'Length Unit': '',\n", "           'Lining Installation Date': '',\n", "           'Lining Type': '',\n", "           'Lugs': '',\n", "           'Mandatory': 'No',\n", "           'Mandatory Comment': '',\n", "           'Manual Minimum Thickness (tmin)': '',\n", "           'Manual Minimum Thickness Unit': '',\n", "           'Manufacturer': 'Tulsa PV Company',\n", "           'Material of Construction': '—',\n", "           'Maximum Allowable Working Pressure': '',\n", "           'Maximum Allowable Working Pressure (MAWP)': '',\n", "           'Maximum Allowable Working Pressure Unit': '',\n", "           'Maximum Process Temperature': '',\n", "           'Maximum Process Temperature Unit': '',\n", "           'May Operate at or Below MDMT/MAT?': '',\n", "           'Minimum Design Metal Temperature': '',\n", "           'Minimum Design Metal Temperature Unit': '',\n", "           'Minimum Process Temperature': '',\n", "           'Minimum Process Temperature Unit': '',\n", "           'Minimum Thickness Used (tmin)': '',\n", "           'Minimum Thickness Used Unit (tmin)': '',\n", "           'Model': '',\n", "           'Module': '',\n", "           'NaOH Concentration': '',\n", "           'National Board/Register Number': '12345',\n", "           'No. of Coils Turns & Center': '',\n", "           'Nominal Thickness (tnominal )': '',\n", "           'Nominal Thickness Unit': '',\n", "           'Normal Boiling Point': '',\n", "           'Normal Boiling Point Unit': '',\n", "           'Normal Operating State/Mode': '',\n", "           'Number of Operational Demands': '',\n", "           'Number of Periodic Test Demands': '',\n", "           'Number of Tubeside Passes ': '',\n", "           'Online Ability to Inspect': '',\n", "           'Operating Hydrogen Partial Pressure': '',\n", "           'Operating Hydrogen Partial Pressure Unit': '',\n", "           'Operating Pressure': '',\n", "           'Operating Pressure Unit': '',\n", "           'Operating Temperature': '',\n", "           'Operating Temperature Unit': '°C',\n", "           'Operational Time': '',\n", "           'Operational Time Unit': '',\n", "           'Orientation': '',\n", "           'Other': '',\n", "           'Outlet Operating Temperature': '',\n", "           'Outlet Operating Temperature Unit': '',\n", "           'Outside Diameter': '',\n", "           'Outside Diameter Unit': '',\n", "           'P&ID Drawing Number': 'That was fast',\n", "           'PED Table': '',\n", "           'PSM Critical': '',\n", "           'PWHT': '',\n", "           'Partially Buried?': '',\n", "           'Plant': 'Javelina',\n", "           'Presence of HF': '',\n", "           'Presence of Sulfides, Moisture and Oxygen?': '',\n", "           'Presence of Water': '',\n", "           'Pressurized Connections': '',\n", "           'Release Phase': '',\n", "           'Replacement Date': '',\n", "           'Representative Fluid Name': '',\n", "           'Require PED Calculation?': '',\n", "           'SAP Equipment Number': '12345',\n", "           'SAP Functional Location': 'sampleFloc',\n", "           'Select Minimum Thickness Used (tmin)': '<Select>',\n", "           'Skirt': '',\n", "           'Support Name': '',\n", "           'Surveillance Time': '',\n", "           'System': 'System PV-1',\n", "           'TEMA Type ': '',\n", "           'Tag Name': 'PV-1',\n", "           'Tensile Strength (TS)': '',\n", "           'Tensile Strength Unit': '',\n", "           'Tube Joint Design': '',\n", "           'Tube Quantity': '',\n", "           'Tube Type ': '',\n", "           'Tubesheet Cladding': '',\n", "           'Tubesheet Cladding Installation Date': '',\n", "           'Tubesheet Cladding Type': '',\n", "           'Tubesheet Material of Construction': '—',\n", "           'Unit': 'Unit PV-1',\n", "           'Volume': '',\n", "           'Volume Unit': '',\n", "           'Yield Strength (YS)': '',\n", "           'Yield Strength Unit': '',\n", "           'pH of process water': '',\n", "           'rowIndex': 0},\n", "          {'Allowable Stress (S)': '',\n", "           'Allowable Stress Unit': '',\n", "           'Ambient State': '',\n", "           'Assessment Category': '',\n", "           'Asset Classification': 'Pressure Vessel',\n", "           'Buried?': '',\n", "           'CO3 Concentration': '',\n", "           'CO3 Concentration Unit': '',\n", "           'Categorized Pressure': 'Design (Ontwerpcondities)',\n", "           'Cladding': '',\n", "           'Cladding Installation Date': '',\n", "           'Cladding Type': '',\n", "           'Component Lining': '',\n", "           'Component Tag Name': '',\n", "           'Component Type': '',\n", "           'Concentration': '',\n", "           'Conical Apex Angle': '',\n", "           'Conical Apex Angle Unit': '',\n", "           'Construction Code': '',\n", "           'Construction Date': '',\n", "           'Contains Acid Gas Treating Amines?': '',\n", "           'Corrosion Allowance': '',\n", "           'Corrosion Allowance Unit': '',\n", "           'Cracks Present?': '',\n", "           'Crown Radius': '',\n", "           'Crown Radius Unit': '',\n", "           'Date in Service': '',\n", "           'Description': '<PERSON>boiler',\n", "           'Design Minimum Thickness': '',\n", "           'Design Minimum Thickness Unit': '',\n", "           'Design Pressure': '',\n", "           'Design Pressure Unit': '',\n", "           'Design Temperature': '',\n", "           'Design Temperature Unit': '',\n", "           'Distance Between each support': '',\n", "           'Distance Between each support Unit': '',\n", "           'Do External Damage Conditions Apply? ': '',\n", "           'Elliptical Ratio': '',\n", "           'Equipment Type': 'Adsorber',\n", "           'Exchanger Type ': '',\n", "           'Exposed to Chloride Containing Mist, Fluids or Solid?': '',\n", "           'External Coating': '',\n", "           'External Coating Installation Date': '',\n", "           'External Coating Quality': '',\n", "           'External Coating Type': '',\n", "           'External Corrosion Rate Base Metal (Cr,bm)': '',\n", "           'External Corrosion Rate Base Metal Unit': '',\n", "           'External Corrosion Rate Long Term (CRst)': '',\n", "           'External Corrosion Rate Long Term Unit': '',\n", "           'External Corrosion Rate Short Term \\u200b\\u200b\\u200b\\u200b\\u200b\\u200b\\u200b(CRst)': '',\n", "           'External Corrosion Rate Short Term \\u200b\\u200b\\u200b\\u200b\\u200b\\u200b\\u200bUnit': '',\n", "           'External Corrosion Type': '<Select>',\n", "           'External Half Life': '',\n", "           'External Half Life Unit': 'years',\n", "           'External Last Thickness Reading (tri)': '',\n", "           'External Last Thickness Reading Date': '',\n", "           'External Last Thickness Reading Unit': '',\n", "           'External Remaining Corrosion Allowance': '',\n", "           'External Remaining Corrosion Allowance Unit': '',\n", "           'External Remaining Life': '',\n", "           'External Remaining Life Unit': 'years',\n", "           'Fluid Components': '',\n", "           'Fluid Group': 'Group 1',\n", "           'Fluid Hazardous': 'Toxic',\n", "           'Fluid Phase': '',\n", "           'Geometry': '',\n", "           'H2S Content of Water': '',\n", "           'H2S Content of Water Unit': '',\n", "           'Heat Trace?': '',\n", "           'Heat Treatment Temperature': '',\n", "           'Heat Treatment Temperature Unit': '',\n", "           'Heat Treatment Time': '',\n", "           'Hemispherical Radius': '',\n", "           'Hemispherical Radius Unit': '',\n", "           'Initial Commissioning Date': '',\n", "           'Inside Diameter': '',\n", "           'Inside Diameter Unit': '',\n", "           'Inspection Directive': '',\n", "           'Inspection Directive Details': '',\n", "           'Insulation': '',\n", "           'Insulation Type ': '',\n", "           'Internal Coating': '',\n", "           'Internal Coating Installation Date': '',\n", "           'Internal Coating Quality': '',\n", "           'Internal Coating Type': '',\n", "           'Internal Corrosion Rate Base Metal (Cr,bm)': '',\n", "           'Internal Corrosion Rate Base Metal Unit': '',\n", "           'Internal Corrosion Rate Long Term (CRst)': '',\n", "           'Internal Corrosion Rate Long Term Unit': '',\n", "           'Internal Corrosion Rate Short Term (CRst)': '',\n", "           'Internal Corrosion Rate Short Term Unit': '',\n", "           'Internal Corrosion Type': '<Select>',\n", "           'Internal Entry Restricted by Diameter or Construction': '',\n", "           'Internal Half Life': '',\n", "           'Internal Half Life Unit': 'years',\n", "           'Internal Last Thickness Reading (tri)': '',\n", "           'Internal Last Thickness Reading Date': '',\n", "           'Internal Last Thickness Reading Unit': '',\n", "           'Internal Remaining Corrosion Allowance': '',\n", "           'Internal Remaining Corrosion Allowance Unit': '',\n", "           'Internal Remaining Life': '',\n", "           'Internal Remaining Life Unit': 'years',\n", "           'Internals': '',\n", "           'Is in Cooling Water Service?': '',\n", "           'Joint Efficiency (E)': '',\n", "           'Knuckle Radius': '',\n", "           'Knuckle Radius Unit': '',\n", "           'Legs': '',\n", "           'Length': '',\n", "           'Length Unit': '',\n", "           'Lining Installation Date': '',\n", "           'Lining Type': '',\n", "           'Lugs': '',\n", "           'Mandatory': 'No',\n", "           'Mandatory Comment': '',\n", "           'Manual Minimum Thickness (tmin)': '',\n", "           'Manual Minimum Thickness Unit': '',\n", "           'Manufacturer': 'Tulsa PV Company',\n", "           'Material of Construction': '—',\n", "           'Maximum Allowable Working Pressure': '',\n", "           'Maximum Allowable Working Pressure (MAWP)': '',\n", "           'Maximum Allowable Working Pressure Unit': '',\n", "           'Maximum Process Temperature': '',\n", "           'Maximum Process Temperature Unit': '',\n", "           'May Operate at or Below MDMT/MAT?': '',\n", "           'Minimum Design Metal Temperature': '',\n", "           'Minimum Design Metal Temperature Unit': '',\n", "           'Minimum Process Temperature': '',\n", "           'Minimum Process Temperature Unit': '',\n", "           'Minimum Thickness Used (tmin)': '',\n", "           'Minimum Thickness Used Unit (tmin)': '',\n", "           'Model': '',\n", "           'Module': '',\n", "           'NaOH Concentration': '',\n", "           'National Board/Register Number': '12345',\n", "           'No. of Coils Turns & Center': '',\n", "           'Nominal Thickness (tnominal )': '',\n", "           'Nominal Thickness Unit': '',\n", "           'Normal Boiling Point': '',\n", "           'Normal Boiling Point Unit': '',\n", "           'Normal Operating State/Mode': '',\n", "           'Number of Operational Demands': '',\n", "           'Number of Periodic Test Demands': '',\n", "           'Number of Tubeside Passes ': '',\n", "           'Online Ability to Inspect': '',\n", "           'Operating Hydrogen Partial Pressure': '',\n", "           'Operating Hydrogen Partial Pressure Unit': '',\n", "           'Operating Pressure': '',\n", "           'Operating Pressure Unit': '',\n", "           'Operating Temperature': '',\n", "           'Operating Temperature Unit': '°C',\n", "           'Operational Time': '',\n", "           'Operational Time Unit': '',\n", "           'Orientation': '',\n", "           'Other': '',\n", "           'Outlet Operating Temperature': '',\n", "           'Outlet Operating Temperature Unit': '',\n", "           'Outside Diameter': '',\n", "           'Outside Diameter Unit': '',\n", "           'P&ID Drawing Number': 'That was fast',\n", "           'PED Table': '',\n", "           'PSM Critical': '',\n", "           'PWHT': '',\n", "           'Partially Buried?': '',\n", "           'Plant': 'Javelina',\n", "           'Presence of HF': '',\n", "           'Presence of Sulfides, Moisture and Oxygen?': '',\n", "           'Presence of Water': '',\n", "           'Pressurized Connections': '',\n", "           'Release Phase': '',\n", "           'Replacement Date': '',\n", "           'Representative Fluid Name': '',\n", "           'Require PED Calculation?': '',\n", "           'SAP Equipment Number': '12345',\n", "           'SAP Functional Location': 'sampleFloc',\n", "           'Select Minimum Thickness Used (tmin)': '<Select>',\n", "           'Skirt': '',\n", "           'Support Name': '',\n", "           'Surveillance Time': '',\n", "           'System': 'System PV-1',\n", "           'TEMA Type ': '',\n", "           'Tag Name': 'PV-1',\n", "           'Tensile Strength (TS)': '',\n", "           'Tensile Strength Unit': '',\n", "           'Tube Joint Design': '',\n", "           'Tube Quantity': '',\n", "           'Tube Type ': '',\n", "           'Tubesheet Cladding': '',\n", "           'Tubesheet Cladding Installation Date': '',\n", "           'Tubesheet Cladding Type': '',\n", "           'Tubesheet Material of Construction': '—',\n", "           'Unit': 'Unit PV-1',\n", "           'Volume': '',\n", "           'Volume Unit': '',\n", "           'Yield Strength (YS)': '',\n", "           'Yield Strength Unit': '',\n", "           'pH of process water': '',\n", "           'rowIndex': 1}]}\n"]}, {"ename": "KeyError", "evalue": "'headers'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[108], line 20\u001b[0m\n\u001b[1;32m     18\u001b[0m pprint(data)\n\u001b[1;32m     19\u001b[0m \u001b[39m# convert the data into a Pandas DataFrame\u001b[39;00m\n\u001b[0;32m---> 20\u001b[0m df \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39mDataFrame(data[\u001b[39m'\u001b[39m\u001b[39mrows\u001b[39m\u001b[39m'\u001b[39m], columns\u001b[39m=\u001b[39mdata[\u001b[39m'\u001b[39;49m\u001b[39mheaders\u001b[39;49m\u001b[39m'\u001b[39;49m])\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: 'headers'"]}], "source": ["import requests\n", "from pprint import pprint\n", "\n", "url = \"http://0.0.0.0:8010/data/rows/\"\n", "params = {\n", "    \"plant\": \"Javelina\",\n", "    \"unit\": \"Unit PV-1\",\n", "    \"system\": \"System PV-1\",\n", "    \"tag_name\": \"PV-1\",\n", "    \"asset_classification\": \"Pressure Vessel\",\n", "    \"equipment_type\": \"Adsorber\",\n", "    \"template\": \"Pressure Vessel\",\n", "}\n", "\n", "response = requests.get(url, params=params)\n", "\n", "data = response.json()\n", "pprint(data)\n", "# convert the data into a Pandas DataFrame\n", "df = pd.DataFrame(data['rows'], columns=data['headers'])\n"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'message': 'Cell updated successfully in worksheet Data Piping at row 5 for header Tag Name.'}\n"]}], "source": ["import requests\n", "\n", "base_url = \"http://0.0.0.0:8010\"\n", "\n", "# Parameters for the /data/update_cell/ endpoint\n", "params = {\n", "    \"indexrow\": 5,\n", "    \"template\": \"Piping\",\n", "    \"header\": \"Tag Name\",\n", "    \"new_value\": \"Piping-1-Test\"\n", "}\n", "\n", "response = requests.put(f\"{base_url}/data/update_cell/\", params=params)\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'error': 'No rows found matching the criteria.'}\n"]}], "source": ["import requests\n", "from pprint import pprint\n", "import pandas as pd\n", "\n", "# API endpoint\n", "url = \"http://0.0.0.0:8010/previous_inspections/row/\"\n", "\n", "# Query parameters\n", "params = {\n", "    \"plant\": \"Javelina\",\n", "    \"unit\": \"<Select>\",\n", "    \"system\": \"<Select>\",\n", "    \"tag_name\": \"D82-0108\",\n", "    \"asset_classification\": \"Pressure Vessel\",\n", "    \"equipment_type\": \"Separator\"\n", "}\n", "\n", "# Make the API call\n", "response = requests.get(url, params=params)\n", "\n", "# Check if the response is valid (status code 200)\n", "if response.status_code == 200:\n", "    data = response.json()\n", "    pprint(data)\n", "    \n", "    # If the response contains rows and headers, convert the data into a Pandas DataFrame\n", "    if 'rows' in data and 'headers' in data:\n", "        df = pd.DataFrame(data['rows'], columns=data['headers'])\n", "        print(df)\n", "else:\n", "    print(f\"Failed to fetch data. Status Code: {response.status_code}\")\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}