{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import sqlite3\n", "\n", "# Initialize and connect to the SQLite database\n", "conn = sqlite3.connect('users_assets.db')\n", "c = conn.cursor()\n", "\n", "# Create table to store user's current asset\n", "c.execute('''CREATE TABLE IF NOT EXISTS user_sessions (\n", "    session_id TEXT PRIMARY KEY,\n", "    email TEXT NOT NULL,\n", "    current_asset TEXT,\n", "    UNIQUE(email, current_asset)\n", ");''')\n", "conn.commit()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>session_id</th>\n", "      <th>email</th>\n", "      <th>current_asset</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>f9d33318-3320-453f-bb9e-dce9204e49ab</td>\n", "      <td><EMAIL></td>\n", "      <td>Piping Dev</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>b457ea07-2cdd-47ab-bb21-021500c831be</td>\n", "      <td><EMAIL></td>\n", "      <td>M200110</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             session_id                      email  \\\n", "0  f9d33318-3320-453f-bb9e-dce9204e49ab  <EMAIL>   \n", "1  b457ea07-2cdd-47ab-bb21-021500c831be         <EMAIL>   \n", "\n", "  current_asset  \n", "0    Piping Dev  \n", "1       M200110  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# convert db to df \n", "import pandas as pd\n", "df = pd.read_sql_query(\"SELECT * FROM user_sessions\", conn)\n", "df\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}