{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install fastapi uvicorn pydrive\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:     Started server process [85474]\n", "INFO:     Waiting for application startup.\n", "INFO:     Application startup complete.\n", "INFO:     <PERSON><PERSON><PERSON> running on http://0.0.0.0:8020 (Press CTRL+C to quit)\n", "ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_85474/292719046.py\", line 75, in list_files\n", "    return {\"files\": fetch_files_from_folder(root_folder_id)}\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_85474/292719046.py\", line 49, in fetch_files_from_folder\n", "    pageToken=page_token).execute()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/googleapiclient/_helpers.py\", line 130, in positional_wrapper\n", "    return wrapped(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/googleapiclient/http.py\", line 923, in execute\n", "    resp, content = _retry_request(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/googleapiclient/http.py\", line 222, in _retry_request\n", "    raise exception\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/googleapiclient/http.py\", line 191, in _retry_request\n", "    resp, content = http.request(uri, method, *args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/oauth2client/transport.py\", line 173, in new_request\n", "    resp, content = request(orig_request_method, uri, method, body,\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/oauth2client/transport.py\", line 280, in request\n", "    return http_callable(uri, method=method, body=body, headers=headers,\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/httplib2/__init__.py\", line 1724, in request\n", "    (response, content) = self._request(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/httplib2/__init__.py\", line 1444, in _request\n", "    (response, content) = self._conn_request(conn, request_uri, method, body, headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/httplib2/__init__.py\", line 1396, in _conn_request\n", "    response = conn.getresponse()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/http/client.py\", line 1374, in getresponse\n", "    response.begin()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/http/client.py\", line 318, in begin\n", "    version, status, reason = self._read_status()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/http/client.py\", line 279, in _read_status\n", "    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/socket.py\", line 705, in readinto\n", "    return self._sock.recv_into(b)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/ssl.py\", line 1274, in recv_into\n", "    return self.read(nbytes, buffer)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/ssl.py\", line 1130, in read\n", "    return self._sslobj.read(len, buffer)\n", "ssl.SSLError: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2548)\n", "ERROR:    Exception in ASGI application\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py\", line 408, in run_asgi\n", "    result = await app(  # type: ignore[func-returns-value]\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py\", line 84, in __call__\n", "    return await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/applications.py\", line 292, in __call__\n", "    await super().__call__(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/applications.py\", line 122, in __call__\n", "    await self.middleware_stack(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 184, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/errors.py\", line 162, in __call__\n", "    await self.app(scope, receive, _send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 91, in __call__\n", "    await self.simple_response(scope, receive, send, request_headers=headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/cors.py\", line 146, in simple_response\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 79, in __call__\n", "    raise exc\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/middleware/exceptions.py\", line 68, in __call__\n", "    await self.app(scope, receive, sender)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 20, in __call__\n", "    raise e\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/middleware/asyncexitstack.py\", line 17, in __call__\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 718, in __call__\n", "    await route.handle(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 276, in handle\n", "    await self.app(scope, receive, send)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/routing.py\", line 66, in app\n", "    response = await func(request)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 273, in app\n", "    raw_response = await run_endpoint_function(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/fastapi/routing.py\", line 192, in run_endpoint_function\n", "    return await run_in_threadpool(dependant.call, **values)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/starlette/concurrency.py\", line 41, in run_in_threadpool\n", "    return await anyio.to_thread.run_sync(func, *args)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/to_thread.py\", line 33, in run_sync\n", "    return await get_asynclib().run_sync_in_worker_thread(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 877, in run_sync_in_worker_thread\n", "    return await future\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 285, in __await__\n", "    yield self  # This tells Task to wait for completion.\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/tasks.py\", line 304, in __wakeup\n", "    future.result()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/asyncio/futures.py\", line 201, in result\n", "    raise self._exception.with_traceback(self._exception_tb)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/anyio/_backends/_asyncio.py\", line 807, in run\n", "    result = context.run(func, *args)\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_85474/292719046.py\", line 75, in list_files\n", "    return {\"files\": fetch_files_from_folder(root_folder_id)}\n", "  File \"/var/folders/dm/k2w5sl1n2014k6958b94dwxc0000gn/T/ipykernel_85474/292719046.py\", line 49, in fetch_files_from_folder\n", "    pageToken=page_token).execute()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/googleapiclient/_helpers.py\", line 130, in positional_wrapper\n", "    return wrapped(*args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/googleapiclient/http.py\", line 923, in execute\n", "    resp, content = _retry_request(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/googleapiclient/http.py\", line 222, in _retry_request\n", "    raise exception\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/googleapiclient/http.py\", line 191, in _retry_request\n", "    resp, content = http.request(uri, method, *args, **kwargs)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/oauth2client/transport.py\", line 173, in new_request\n", "    resp, content = request(orig_request_method, uri, method, body,\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/oauth2client/transport.py\", line 280, in request\n", "    return http_callable(uri, method=method, body=body, headers=headers,\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/httplib2/__init__.py\", line 1724, in request\n", "    (response, content) = self._request(\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/httplib2/__init__.py\", line 1444, in _request\n", "    (response, content) = self._conn_request(conn, request_uri, method, body, headers)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/site-packages/httplib2/__init__.py\", line 1396, in _conn_request\n", "    response = conn.getresponse()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/http/client.py\", line 1374, in getresponse\n", "    response.begin()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/http/client.py\", line 318, in begin\n", "    version, status, reason = self._read_status()\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/http/client.py\", line 279, in _read_status\n", "    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/socket.py\", line 705, in readinto\n", "    return self._sock.recv_into(b)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/ssl.py\", line 1274, in recv_into\n", "    return self.read(nbytes, buffer)\n", "  File \"/Users/<USER>/anaconda3/lib/python3.10/ssl.py\", line 1130, in read\n", "    return self._sslobj.read(len, buffer)\n", "ssl.SSLError: [SSL: WRONG_VERSION_NUMBER] wrong version number (_ssl.c:2548)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:60502 - \"GET /list-files/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60517 - \"GET /list-files/ HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60517 - \"GET /get-file/12Nh9gcMFfs0hwiiATFoqimXMWXSBhc6M HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60502 - \"GET /get-file/12Nh9gcMFfs0hwiiATFoqimXMWXSBhc6M HTTP/1.1\" 200 OK\n"]}], "source": ["from fastapi import FastAPI, HTTPException, Response, Depends\n", "import nest_asyncio\n", "from fastapi.middleware.cors import CORSMiddleware\n", "from google.oauth2.credentials import Credentials\n", "from googleapiclient.discovery import build\n", "from googleapiclient.errors import HttpError\n", "from googleapiclient.http import MediaIoBaseDownload\n", "import gspread\n", "import uvicorn\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "import io\n", "\n", "# Apply nest_asyncio to allow asynchronous operations in Jupyter\n", "nest_asyncio.apply()\n", "\n", "app = FastAPI()\n", "\n", "# Setup Google Drive API with gspread authentication\n", "scope = [\"https://www.googleapis.com/auth/drive\"]\n", "creds = ServiceAccountCredentials.from_json_keyfile_name(\"gsheetsKeys.json\", scope)\n", "\n", "\n", "\n", "origins = [\n", "    \"http://localhost:3000/\",  # React development server\n", "]\n", "\n", "\n", "app.add_middleware(\n", "    CORSMiddleware,\n", "    allow_origins=origins,\n", "    allow_credentials=True,\n", "    allow_methods=[\"*\"],\n", "    allow_headers=[\"*\"],\n", ")\n", "\n", "\n", "# Build the Google Drive service\n", "service = build('drive', 'v3', credentials=creds)\n", "\n", "def fetch_files_from_folder(folder_id, current_path=\"\"):\n", "    items = []\n", "    # Pagination\n", "    page_token = None\n", "    while True:\n", "        # Query the files in the current folder with pagination support\n", "        response = service.files().list(q=f\"'{folder_id}' in parents and trashed=false\", \n", "                                        fields=\"nextPageToken, files(id, name, mimeType, parents)\",\n", "                                        pageToken=page_token).execute()\n", "        \n", "        files = response.get('files', [])\n", "        for file in files:\n", "            file_path = f\"{current_path}/{file['name']}\"\n", "            items.append({\n", "                \"id\": file['id'],\n", "                \"name\": file['name'],\n", "                \"mimeType\": file['mimeType'],\n", "                \"path\": file_path\n", "            })\n", "\n", "            # If the current item is a folder, fetch its content recursively\n", "            if file['mimeType'] == \"application/vnd.google-apps.folder\":\n", "                items.extend(fetch_files_from_folder(file['id'], file_path))\n", "\n", "        # Update the page token for the next iteration\n", "        page_token = response.get('nextPageToken', None)\n", "        if page_token is None:\n", "            break\n", "\n", "    return items\n", "\n", "@app.get(\"/list-files/\")\n", "def list_files():\n", "    root_folder_id = \"1ebGQu6gTlhMMdUJn1TvMsoTZC7sQF6JG\"\n", "    return {\"files\": fetch_files_from_folder(root_folder_id)}\n", "\n", "@app.get(\"/get-file/{file_id}\")\n", "def get_file(file_id: str):\n", "    try:\n", "        # Fetch file metadata\n", "        file_info = service.files().get(fileId=file_id).execute()\n", "\n", "        # If the file is a Google Apps file (e.g., Google Docs, Sheets, Slides), it can't be fetched directly\n", "        if 'google-apps' in file_info['mimeType']:\n", "            raise HTTPException(status_code=400, detail=\"Google Apps files can't be directly fetched.\")\n", "\n", "        # Fetch file content\n", "        request = service.files().get_media(fileId=file_id)\n", "        fh = io.BytesIO()\n", "        downloader = MediaIoBaseDownload(fh, request)\n", "        done = False\n", "        while done is False:\n", "            status, done = downloader.next_chunk()\n", "\n", "        fh.seek(0)\n", "        return Response(content=fh.read(), media_type=file_info['mimeType'])\n", "\n", "    except HttpError as e:\n", "        # Specific error handling for Google Drive related errors\n", "        error_details = e._get_reason()\n", "        raise HTTPException(status_code=400, detail=f\"Error fetching the file from Google Drive: {error_details}\")\n", "    except Exception as e:\n", "        # General error handling\n", "        raise HTTPException(status_code=400, detail=f\"Error fetching the file: {e}\")\n", "\n", "# Start Uvicorn running on port 8020\n", "\n", "uvicorn.run(app, host=\"0.0.0.0\", port=8020)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}