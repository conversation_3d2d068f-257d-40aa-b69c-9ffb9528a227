{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["zsh:1: no matches found: fastapi[all]\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install fastapi[all] gspread oauth2client"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import nest_asyncio\n", "from fastapi.middleware.cors import CORSMiddleware\n", "from fastapi import FastAPI, BackgroundTasks\n", "import gspread\n", "from oauth2client.service_account import ServiceAccountCredentials\n", "import uvicorn\n", "import pandas as pd\n", "from typing import List\n", "from googleapiclient.errors import HttpError\n", "import numpy as np\n", "from queue import Queue\n", "import threading\n", "import time\n", "\n", "# Apply nest_asyncio to allow asynchronous operations in the notebook\n", "nest_asyncio.apply()\n", "\n", "app = FastAPI()\n", "\n", "origins = [\n", "    \"http://localhost:3000\",  # React development server\n", "]\n", "app.add_middleware(\n", "    CORSMiddleware,\n", "    allow_origins=origins,\n", "    allow_credentials=True,\n", "    allow_methods=[\"*\"],\n", "    allow_headers=[\"*\"],\n", ")\n", "\n", "scope = [\"https://spreadsheets.google.com/feeds\", \"https://www.googleapis.com/auth/spreadsheets\",\n", "         \"https://www.googleapis.com/auth/drive.file\", \"https://www.googleapis.com/auth/drive\"]\n", "creds = ServiceAccountCredentials.from_json_keyfile_name(\"gsheetsKeys.json\", scope)\n", "client = gspread.authorize(creds)\n", "\n", "sheets_info = {\n", "        \"Data Piping\": {\"start_row\": 4, \"type\": \"Piping\"},\n", "        \"Data PV\"    : {\"start_row\": 6, \"type\": \"Pressure Vessel\"},\n", "        \"Data ST\"    : {\"start_row\": 5, \"type\": \"Storage Tank\"},\n", "\n", "\n", "    }\n", "\n", "def clean_dataframe(df):\n", "    df.replace([np.inf, -np.inf], np.nan, inplace=True)\n", "    df.dropna(how='all', inplace=True)\n", "    df.fillna(\"\", inplace=True)\n", "    return df\n", "\n", "def extract_desired_columns_from_df(df, desired_columns):\n", "    return df[desired_columns + [\"Template\"]]\n", "\n", "def get_column_letter(col_num):\n", "    string = \"\"\n", "    while col_num > 0:\n", "        col_num, remainder = divmod(col_num - 1, 26)\n", "        string = chr(65 + remainder) + string\n", "    return string\n", "\n", "# Queue setup\n", "task_queue = Queue()\n", "\n", "def worker():\n", "    while True:\n", "        task = task_queue.get()\n", "        if task is None:\n", "            break\n", "        task()\n", "        task_queue.task_done()\n", "        time.sleep(1)\n", "\n", "threading.Thread(target=worker, daemon=True).start()\n", "     \n", "\n", "\n", "@app.get(\"/lookup/\")\n", "def read_lookup():\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name).worksheet('Lookup')\n", "\n", "    # Fetching all data from the \"Lookup\" sheet\n", "    all_data = sheet.get_all_values()\n", "    headers = all_data[0]\n", "    df = pd.DataFrame(all_data[1:], columns=headers)\n", "\n", "    # Constructing the dictionary with header: list values\n", "    data_dict = df.to_dict(orient='list')\n", "\n", "    return data_dict\n", "\n", "@app.get(\"/data/\")\n", "def read_data():\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "\n", "    dfs = {}\n", "    validation_errors = []\n", "\n", "    # Create a batch request for all worksheets\n", "    ranges = {\n", "        \"Data Piping\": \"A:F\",\n", "        \"Data PV\": \"A:AV\",\n", "        \"Data ST\": \"A:BD\"\n", "    }\n", "    response = sheet.values_batch_get([f\"{ws_name}!{range_val}\" for ws_name, range_val in ranges.items()])\n", "    # Extract values from the response\n", "    all_data = {ws_name: r.get('values', []) for ws_name, r in zip(ranges.keys(), response['valueRanges'])}\n", "\n", "    for worksheet_name, info in sheets_info.items():\n", "        headers = all_data[worksheet_name][info[\"start_row\"] - 1]\n", "        adjusted_data = [row + [\"\"] * (len(headers) - len(row)) for row in all_data[worksheet_name][info[\"start_row\"]:]]\n", "        df = pd.DataFrame(adjusted_data, columns=headers)\n", "        \n", "        # Add the \"type\" column\n", "        df[\"Template\"] = info[\"type\"]\n", "\n", "        dfs[worksheet_name] = df\n", "\n", "    # Validate unique combinations\n", "    columns_to_validate = [\"Plant\", \"Unit\", \"System\", \"Tag Name\", \"Asset Classification\", \"Equipment Type\"]\n", "\n", "    for ws_name in [\"Data PV\", \"Data ST\"]:\n", "        for comb in dfs[ws_name][columns_to_validate].drop_duplicates().values:\n", "            condition = (dfs[ws_name][columns_to_validate].values == comb).all(axis=1)\n", "            matching_rows = dfs[ws_name][condition]\n", "            \n", "            # If only one row for the combination, just proceed\n", "            if len(matching_rows) == 1:\n", "                continue\n", "            \n", "            if not matching_rows.drop(columns=columns_to_validate).duplicated(keep=False).all():\n", "                validation_errors.append({\"template\": ws_name, \"combination\": comb.tolist()})\n", "                # Remove the rows with validation errors from dfs\n", "                dfs[ws_name] = dfs[ws_name][~condition]\n", "\n", "    # Extracting the first row of each unique combination\n", "    final_df = pd.concat([dfs[\"Data PV\"].drop_duplicates(subset=columns_to_validate),\n", "                          dfs[\"Data ST\"].drop_duplicates(subset=columns_to_validate),\n", "                          dfs[\"Data Piping\"].drop_duplicates(subset=columns_to_validate)], ignore_index=True)\n", "\n", "    desired_columns = [\"Plant\", \"Unit\", \"System\", \"Tag Name\", \"Asset Classification\", \"Equipment Type\"]\n", "    final_df = extract_desired_columns_from_df(final_df, desired_columns)\n", "\n", "    final_df = clean_dataframe(final_df)\n", "    mask = (final_df[columns_to_validate] == '').all(axis=1)\n", "    final_df = final_df[~mask]\n", "\n", "    result = {\n", "        \"headers\": list(final_df.columns),\n", "        \"data\": final_df.values.tolist(),\n", "        \"validation\": validation_errors  # This will always be returned, but might be empty if there are no errors.\n", "    }\n", "\n", "    return result\n", "\n", "\n", "@app.get(\"/data/row/\")\n", "def get_row(\n", "    plant: str,\n", "    unit: str,\n", "    system: str,\n", "    tag_name: str,\n", "    asset_classification: str,\n", "    equipment_type: str,\n", "    template: str\n", "):\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "\n", "    worksheet_name = None\n", "    header_start_row = None\n", "    for key, value in sheets_info.items():\n", "        if value[\"type\"] == template:\n", "            worksheet_name = key\n", "            header_start_row = value[\"start_row\"] - 1\n", "            break\n", "\n", "    if not worksheet_name:\n", "        return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "    range_name = f\"{worksheet_name}\"  # Get the entire range of the sheet\n", "    response = sheet.values_get(range_name)\n", "    data = response.get('values', [])\n", "    headers = data[header_start_row]\n", "    adjusted_data = [row + [\"\"] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]\n", "    df = pd.DataFrame(adjusted_data, columns=headers)\n", "\n", "    condition = (\n", "        (df[\"Plant\"] == plant) &\n", "        (df[\"Asset Classification\"] == asset_classification) &\n", "        (df[\"Unit\"] == unit) &\n", "        (df[\"System\"] == system) &\n", "        (df[\"Tag Name\"] == tag_name) &\n", "        (df['Equipment Type'] == equipment_type)\n", "    )\n", "    filtered_df = df[condition]\n", "    num_rows = len(filtered_df)\n", "    if num_rows > 0:\n", "        row_dict = filtered_df.to_dict(orient='records')[0]\n", "        row_dict[\"rowIndex\"] = list(map(int, filtered_df.index))\n", "        return row_dict\n", "    elif num_rows == 0:\n", "        return {\"error\": \"No rows found matching the criteria.\"}\n", "    \n", "\n", "@app.put(\"/data/update_cell/\")\n", "def update_cells(\n", "    indexrows: str,\n", "    template: str,\n", "    header: str,\n", "    new_value: str\n", "):\n", "    # Convert comma-separated string to a list of integers\n", "    indexrows_list = [int(index) for index in indexrows.split(\",\")]\n", "\n", "    if template == \"Previous Inspections\":\n", "        worksheet_name = \"Previous Inspections\"\n", "        header_start_row = 1  # Assuming headers are in the 2nd row\n", "\n", "    else:\n", "        # Determine the worksheet name and header_start_row from the template\n", "        worksheet_name, header_start_row = None, None\n", "        for ws_name, info in sheets_info.items():\n", "            if info[\"type\"] == template:\n", "                worksheet_name = ws_name\n", "                header_start_row = info[\"start_row\"] - 1\n", "                break\n", "\n", "    if not worksheet_name:\n", "        return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "    # Open the Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "    \n", "    # Get the correct worksheet\n", "    ws = sheet.worksheet(worksheet_name)\n", "\n", "    # Fetch headers\n", "    headers = ws.row_values(header_start_row + 1)  # +1 because Google Sheets index starts from 1\n", "\n", "    # Identify the column index for the given header\n", "    try:\n", "        col_index = headers.index(header) + 1  # +1 because Google Sheets index starts from 1\n", "    except ValueError:\n", "        return {\"error\": f\"Header {header} not found in worksheet {worksheet_name}\"}\n", "\n", "    # Update the cells for all specified rows\n", "    for indexrow in indexrows_list:\n", "        ws.update_cell(indexrow, col_index, new_value)\n", "\n", "    return {\"message\": f\"Cells updated successfully in worksheet {worksheet_name} for header {header}.\"}\n", "\n", "@app.get(\"/data/rows/\")\n", "def get_rows(\n", "    plant: str,\n", "    unit: str,\n", "    system: str,\n", "    tag_name: str,\n", "    asset_classification: str,\n", "    equipment_type: str,\n", "    template: str\n", "):\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "\n", "    worksheet_name = None\n", "    header_start_row = None\n", "    for key, value in sheets_info.items():\n", "        if value[\"type\"] == template:\n", "            worksheet_name = key\n", "            header_start_row = value[\"start_row\"] - 1\n", "            break\n", "\n", "    if not worksheet_name:\n", "        return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "    range_name = f\"{worksheet_name}\"  # Get the entire range of the sheet\n", "    response = sheet.values_get(range_name)\n", "    data = response.get('values', [])\n", "    headers = data[header_start_row]\n", "    adjusted_data = [row + [\"\"] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]\n", "    df = pd.DataFrame(adjusted_data, columns=headers)\n", "\n", "    condition = (\n", "        (df[\"Plant\"] == plant) &\n", "        (df[\"Asset Classification\"] == asset_classification) &\n", "        (df[\"Unit\"] == unit) &\n", "        (df[\"System\"] == system) &\n", "        (df[\"Tag Name\"] == tag_name) &\n", "        (df['Equipment Type'] == equipment_type)\n", "    )\n", "    filtered_df = df[condition]\n", "    num_rows = len(filtered_df)\n", "    if num_rows > 0:\n", "        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n", "        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices\n", "        # Attach rowIndex to each individual row\n", "        for idx, row in enumerate(rows_list):\n", "            row[\"rowIndex\"] = row_indices[idx]\n", "\n", "        return {\"rows\": rows_list, \"rowIndices\": row_indices}\n", "    elif num_rows == 0:\n", "        return {\"error\": \"No rows found matching the criteria.\"}\n", "\n", "@app.put(\"/data/duplicate_row/\")\n", "def duplicate_row(rowIndex: int, template: str, background_tasks: BackgroundTasks):\n", "\n", "    # Define the task function with the logic to duplicate the row\n", "    def task_function():\n", "        try:\n", "            # Open the Google Sheet using its specific name\n", "            sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "            sheet = client.open(sheet_name)\n", "\n", "            # Determine the worksheet name from the template\n", "            worksheet_name = None\n", "            if template == \"Previous Inspections\":\n", "                worksheet_name = \"Previous Inspections\"\n", "            else:\n", "                for ws_name, info in sheets_info.items():\n", "                    if info[\"type\"] == template:\n", "                        worksheet_name = ws_name\n", "                        break\n", "\n", "            if not worksheet_name:\n", "                return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "            # Get the correct worksheet\n", "            ws = sheet.worksheet(worksheet_name)\n", "\n", "            # Retrieve the data from the specified row\n", "            row_data = ws.row_values(rowIndex)\n", "\n", "            # Determine the new row's position (append to the end)\n", "            num_rows = ws.row_count\n", "            new_row_index = num_rows + 1\n", "\n", "            # Insert the new row at the end\n", "            ws.add_rows(1)\n", "\n", "            # Add data to the new row\n", "            ws.update('A' + str(new_row_index), [row_data])\n", "\n", "            print(f\"Row {rowIndex} duplicated successfully in worksheet {worksheet_name}.\")\n", "        except HttpError as e:\n", "            print(f\"Google Sheets API Error: {e.resp.reason}\")\n", "        except Exception as e:\n", "            print(f\"An error occurred: {str(e)}\")\n", "\n", "    # Enqueue the task function for processing\n", "    task_queue.put(task_function)\n", "\n", "    return {\"message\": \"Task to duplicate row has been enqueued for processing\"}\n", "\n", "\n", "@app.delete(\"/data/delete_row/\")\n", "def delete_row(rowIndex: int, template: str):\n", "    worksheet_name = None\n", "    \n", "    \n", "    if template == \"Previous Inspections\":\n", "        worksheet_name = \"Previous Inspections\"\n", "    else:\n", "    \n", "        for ws_name, info in sheets_info.items():\n", "            if info[\"type\"] == template:\n", "                worksheet_name = ws_name\n", "                break\n", "\n", "    if not worksheet_name:\n", "        return {\"error\": f\"No worksheet found for template: {template}\"}\n", "\n", "    try:\n", "        sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "        sheet = client.open(sheet_name)\n", "        ws = sheet.worksheet(worksheet_name)\n", "\n", "        # Get the total number of columns in the sheet\n", "        num_cols = len(ws.row_values(rowIndex))\n", "\n", "        # Create a list of blank values for all columns\n", "        blank_values = [''] * num_cols\n", "\n", "        # Set the entire row to blank using a batch update\n", "        end_column_letter = get_column_letter(num_cols)\n", "        range_name = f\"A{rowIndex}:{end_column_letter}{rowIndex}\"  # Corrected range format\n", "        ws.update(range_name, [blank_values])\n", "\n", "        return {\"message\": f\"Row {rowIndex} cleared successfully in worksheet {worksheet_name}.\"}\n", "    except HttpError as e:\n", "        return {\"error\": f\"Google Sheets API Error: {e.resp.reason}\"}\n", "    except Exception as e:\n", "        return {\"error\": f\"An error occurred: {str(e)}\"}\n", "\n", "\n", "@app.get(\"/data/inspections\")\n", "def get_previous_inspection_row(\n", "    plant: str,\n", "    unit: str,\n", "    system: str,\n", "    tag_name: str,\n", "    asset_classification: str,\n", "    equipment_type: str\n", "):\n", "    # Open Google Sheet using its specific name\n", "    sheet_name = \"HE_Data Collection template Fix Equipment\"\n", "    sheet = client.open(sheet_name)\n", "\n", "    worksheet_name = \"Previous Inspections\"\n", "    header_start_row = 1  # Assuming headers are in the 2nd row\n", "\n", "    # Get data from the specified range in the \"Previous Inspections\" worksheet\n", "    range_name = f\"{worksheet_name}\"  # Get the entire range of the sheet\n", "    response = sheet.values_get(range_name)\n", "    data = response.get('values', [])\n", "    headers = data[header_start_row]\n", "    adjusted_data = [row + [\"\"] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]\n", "    df = pd.DataFrame(adjusted_data, columns=headers)\n", "\n", "    \n", "\n", "    \n", "    \n", "    condition = (\n", "        (df[\"Plant\"] == plant) &\n", "        (df[\"Asset Classification\"] == asset_classification) &\n", "        (df[\"Unit\"] == unit) &\n", "        (df[\"System\"] == system) &\n", "        (df[\"Tag Name\"] == tag_name)&\n", "        (df['Equipment Type'] == equipment_type)\n", "    )\n", "    filtered_df = df[condition]\n", "    num_rows = len(filtered_df)\n", "    if num_rows > 0:\n", "        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string\n", "        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices\n", "        \n", "        # Attach rowIndex to each individual row\n", "        for idx, row in enumerate(rows_list):\n", "            row[\"rowIndex\"] = row_indices[idx]\n", "\n", "        return {\"rows\": rows_list, \"rowIndices\": row_indices}\n", "    elif num_rows == 0:\n", "        return {\"error\": \"No rows found matching the criteria.\"}\n", "\n", "\n", "\n", "\n", "\n", "uvicorn.run(app, host=\"0.0.0.0\", port=8010)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 4}