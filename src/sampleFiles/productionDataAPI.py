#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Created on Sun Oct  1 20:54:29 2023

@author: lui<PERSON><PERSON><PERSON>
"""

import nest_asyncio
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI,  HTTPException, BackgroundTasks
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import uvicorn
import pandas as pd
from googleapiclient.errors import HttpError
import numpy as np
from queue import Queue
import threading
import time
import os
import json
# Apply nest_asyncio to allow asynchronous operations in the notebook
nest_asyncio.apply()
lock = threading.Lock()
app = FastAPI()

origins = [
    "http://localhost:3000", "http://**********:3000/","http://localhost:3001", # React development server
]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def get_gspread_client():
    json_key = os.environ.get("JSON_KEY")
    if not json_key:
        raise HTTPException(status_code=500, detail="JSON_KEY environment variable is not set or is empty.")
    
    try:
        key_dict = json.loads(json_key)
    except json.JSONDecodeError:
        raise HTTPException(status_code=500, detail="Invalid JSON_KEY provided in the environment variable.")

    scope = [
        "https://spreadsheets.google.com/feeds",
        "https://www.googleapis.com/auth/spreadsheets",
        "https://www.googleapis.com/auth/drive.file",
        "https://www.googleapis.com/auth/drive"
    ]
    
    try:
        creds = ServiceAccountCredentials.from_json_keyfile_dict(key_dict, scope)
        client = gspread.authorize(creds)
        return client
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error initializing gspread: {str(e)}")

client = get_gspread_client()  # Replace the original client creation line



sheets_info = {
        "Data Piping": {"start_row": 4, "type": "Piping"},
        "Data PV"    : {"start_row": 6, "type": "Pressure Vessel"},
        "Data ST"    : {"start_row": 5, "type": "Storage Tank"},


    }

def clean_dataframe(df):
    df.replace([np.inf, -np.inf], np.nan, inplace=True)
    df.dropna(how='all', inplace=True)
    df.fillna("", inplace=True)
    return df

def extract_desired_columns_from_df(df, desired_columns):
    return df[desired_columns + ["Template"]]

def get_column_letter(col_num):
    string = ""
    while col_num > 0:
        col_num, remainder = divmod(col_num - 1, 26)
        string = chr(65 + remainder) + string
    return string

# Queue setup
task_queue = Queue()

def worker():
    while True:
        task = task_queue.get()
        if task is None:
            break
        task()
        task_queue.task_done()
        time.sleep(1)

threading.Thread(target=worker, daemon=True).start()
     


@app.get("/lookup/")
def read_lookup():
    # Open Google Sheet using its specific name
    sheet_name = "HE_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name).worksheet('Lookup')

    # Fetching all data from the "Lookup" sheet
    all_data = sheet.get_all_values()
    headers = all_data[0]
    df = pd.DataFrame(all_data[1:], columns=headers)

    # Constructing the dictionary with header: list values
    data_dict = df.to_dict(orient='list')

    return data_dict

@app.get("/data/")
def read_data():
    # Open Google Sheet using its specific name
    sheet_name = "HE_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    dfs = {}
    validation_errors = []

    # Create a batch request for all worksheets
    ranges = {
        "Data Piping": "A:F",
        "Data PV": "A:AV",
        "Data ST": "A:BD"
    }
    response = sheet.values_batch_get([f"{ws_name}!{range_val}" for ws_name, range_val in ranges.items()])
    # Extract values from the response
    all_data = {ws_name: r.get('values', []) for ws_name, r in zip(ranges.keys(), response['valueRanges'])}

    for worksheet_name, info in sheets_info.items():
        headers = all_data[worksheet_name][info["start_row"] - 1]
        adjusted_data = [row + [""] * (len(headers) - len(row)) for row in all_data[worksheet_name][info["start_row"]:]]
        df = pd.DataFrame(adjusted_data, columns=headers)
        
        # Add the "type" column
        df["Template"] = info["type"]

        dfs[worksheet_name] = df

    # Validate unique combinations
    columns_to_validate = ["Plant", "Unit", "System", "Tag Name", "Asset Classification", "Equipment Type"]

    for ws_name in ["Data PV", "Data ST"]:
        for comb in dfs[ws_name][columns_to_validate].drop_duplicates().values:
            condition = (dfs[ws_name][columns_to_validate].values == comb).all(axis=1)
            matching_rows = dfs[ws_name][condition]
            
            # If only one row for the combination, just proceed
            if len(matching_rows) == 1:
                continue
            
            if not matching_rows.drop(columns=columns_to_validate).duplicated(keep=False).all():
                validation_errors.append({"template": ws_name, "combination": comb.tolist()})
                # Remove the rows with validation errors from dfs
                dfs[ws_name] = dfs[ws_name][~condition]

    # Extracting the first row of each unique combination
    final_df = pd.concat([dfs["Data PV"].drop_duplicates(subset=columns_to_validate),
                          dfs["Data ST"].drop_duplicates(subset=columns_to_validate),
                          dfs["Data Piping"].drop_duplicates(subset=columns_to_validate)], ignore_index=True)

    desired_columns = ["Plant", "Unit", "System", "Tag Name", "Asset Classification", "Equipment Type"]
    final_df = extract_desired_columns_from_df(final_df, desired_columns)

    final_df = clean_dataframe(final_df)
    mask = (final_df[columns_to_validate] == '').all(axis=1)
    final_df = final_df[~mask]

    result = {
        "headers": list(final_df.columns),
        "data": final_df.values.tolist(),
        "validation": validation_errors  # This will always be returned, but might be empty if there are no errors.
    }

    return result


@app.get("/data/row/")
def get_row(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str,
    template: str
):
    # Open Google Sheet using its specific name
    sheet_name = "HE_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    worksheet_name = None
    header_start_row = None
    for key, value in sheets_info.items():
        if value["type"] == template:
            worksheet_name = key
            header_start_row = value["start_row"] - 1
            break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    range_name = f"{worksheet_name}"  # Get the entire range of the sheet
    response = sheet.values_get(range_name)
    data = response.get('values', [])
    headers = data[header_start_row]
    adjusted_data = [row + [""] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]
    df = pd.DataFrame(adjusted_data, columns=headers)

    condition = (
        (df["Plant"] == plant) &
        (df["Asset Classification"] == asset_classification) &
        (df["Unit"] == unit) &
        (df["System"] == system) &
        (df["Tag Name"] == tag_name) &
        (df['Equipment Type'] == equipment_type)
    )
    filtered_df = df[condition]
    num_rows = len(filtered_df)
    if num_rows > 0:
        row_dict = filtered_df.to_dict(orient='records')[0]
        row_dict["rowIndex"] = list(map(int, filtered_df.index))
        return row_dict
    elif num_rows == 0:
        return {"error": "No rows found matching the criteria."}
    

@app.put("/data/update_cell/")
def update_cells(
    indexrows: str,
    template: str,
    header: str,
    new_value: str
):
    # Convert comma-separated string to a list of integers
    indexrows_list = [int(index) for index in indexrows.split(",")]

    if template == "Previous Inspections":
        worksheet_name = "Previous Inspections"
        header_start_row = 1  # Assuming headers are in the 2nd row

    else:
        # Determine the worksheet name and header_start_row from the template
        worksheet_name, header_start_row = None, None
        for ws_name, info in sheets_info.items():
            if info["type"] == template:
                worksheet_name = ws_name
                header_start_row = info["start_row"] - 1
                break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    # Open the Google Sheet using its specific name
    sheet_name = "HE_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)
    
    # Get the correct worksheet
    ws = sheet.worksheet(worksheet_name)

    # Fetch headers
    headers = ws.row_values(header_start_row + 1)  # +1 because Google Sheets index starts from 1

    # Identify the column index for the given header
    try:
        col_index = headers.index(header) + 1  # +1 because Google Sheets index starts from 1
    except ValueError:
        return {"error": f"Header {header} not found in worksheet {worksheet_name}"}

    # Update the cells for all specified rows
    for indexrow in indexrows_list:
        ws.update_cell(indexrow, col_index, new_value)

    return {"message": f"Cells updated successfully in worksheet {worksheet_name} for header {header}."}

@app.get("/data/rows/")
def get_rows(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str,
    template: str
):
    # Open Google Sheet using its specific name
    sheet_name = "HE_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    worksheet_name = None
    header_start_row = None
    for key, value in sheets_info.items():
        if value["type"] == template:
            worksheet_name = key
            header_start_row = value["start_row"] - 1
            break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    range_name = f"{worksheet_name}"  # Get the entire range of the sheet
    response = sheet.values_get(range_name)
    data = response.get('values', [])
    headers = data[header_start_row]
    adjusted_data = [row + [""] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]
    df = pd.DataFrame(adjusted_data, columns=headers)

    condition = (
        (df["Plant"] == plant) &
        (df["Asset Classification"] == asset_classification) &
        (df["Unit"] == unit) &
        (df["System"] == system) &
        (df["Tag Name"] == tag_name) &
        (df['Equipment Type'] == equipment_type)
    )
    filtered_df = df[condition]
    num_rows = len(filtered_df)
    if num_rows > 0:
        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string
        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices
        # Attach rowIndex to each individual row
        for idx, row in enumerate(rows_list):
            row["rowIndex"] = row_indices[idx]

        return {"rows": rows_list, "rowIndices": row_indices}
    elif num_rows == 0:
        return {"error": "No rows found matching the criteria."}
    
    
@app.get("/data/new_inspection/")
def new_inspection(plant: str, unit: str, system: str, tag_name: str, 
                   asset_classification: str, equipment_type: str, 
                   background_tasks: BackgroundTasks):

    def task_function():
        try:

            # Open the Google Sheet using its specific name
            sheet_name = "HE_Data Collection template Fix Equipment"
            sheet = client.open(sheet_name)

            # Define worksheet name as we know it
            worksheet_name = "Previous Inspections"
            ws = sheet.worksheet(worksheet_name)

            # Construct the data to insert into the new row
            row_data = [plant, unit, system, tag_name, asset_classification, equipment_type,"In Service Inspection","","","","External Visual"]

            # Determine the new row's position (append to the end)
            num_rows = ws.row_count
            new_row_index = num_rows + 1

            # Insert the new row at the end
            ws.add_rows(1)

            # Add data to the new row
            ws.update('A' + str(new_row_index), [row_data])

            print(f"New inspection data added successfully in worksheet {worksheet_name} at row {new_row_index}.")
        except HttpError as e:
            print(f"Google Sheets API Error: {e.resp.reason}")
        except Exception as e:
            print(f"An error occurred: {str(e)}")

    # Enqueue the task function for processing
    background_tasks.add_task(task_function)

    return {"message": "Task to add new inspection data has been enqueued for processing"}
    



@app.put("/data/duplicate_row/")
def duplicate_row(rowIndex: int, template: str, background_tasks: BackgroundTasks):

    # Define the task function with the logic to duplicate the row
    def task_function():
        try:
            # Open the Google Sheet using its specific name
            sheet_name = "HE_Data Collection template Fix Equipment"
            sheet = client.open(sheet_name)

            # Determine the worksheet name from the template
            worksheet_name = None
            if template == "Previous Inspections":
                worksheet_name = "Previous Inspections"
            else:
                for ws_name, info in sheets_info.items():
                    if info["type"] == template:
                        worksheet_name = ws_name
                        break

            if not worksheet_name:
                return {"error": f"No worksheet found for template: {template}"}

            # Get the correct worksheet
            ws = sheet.worksheet(worksheet_name)

            # Retrieve the data from the specified row
            row_data = ws.row_values(rowIndex)

            # Determine the new row's position (append to the end)
            num_rows = ws.row_count
            new_row_index = num_rows + 1

            # Insert the new row at the end
            ws.add_rows(1)

            # Add data to the new row
            ws.update('A' + str(new_row_index), [row_data])

            print(f"Row {rowIndex} duplicated successfully in worksheet {worksheet_name}.")
        except HttpError as e:
            print(f"Google Sheets API Error: {e.resp.reason}")
        except Exception as e:
            print(f"An error occurred: {str(e)}")

    # Enqueue the task function for processing
    task_queue.put(task_function)

    return {"message": "Task to duplicate row has been enqueued for processing"}


@app.delete("/data/delete_row/")
def delete_row(rowIndex: int, template: str):
    worksheet_name = None
    
    
    if template == "Previous Inspections":
        worksheet_name = "Previous Inspections"
    else:
    
        for ws_name, info in sheets_info.items():
            if info["type"] == template:
                worksheet_name = ws_name
                break

    if not worksheet_name:
        return {"error": f"No worksheet found for template: {template}"}

    try:
        sheet_name = "HE_Data Collection template Fix Equipment"
        sheet = client.open(sheet_name)
        ws = sheet.worksheet(worksheet_name)

        # Get the total number of columns in the sheet
        num_cols = len(ws.row_values(rowIndex))

        # Create a list of blank values for all columns
        blank_values = [''] * num_cols

        # Set the entire row to blank using a batch update
        end_column_letter = get_column_letter(num_cols)
        range_name = f"A{rowIndex}:{end_column_letter}{rowIndex}"  # Corrected range format
        ws.update(range_name, [blank_values])

        return {"message": f"Row {rowIndex} cleared successfully in worksheet {worksheet_name}."}
    except HttpError as e:
        return {"error": f"Google Sheets API Error: {e.resp.reason}"}
    except Exception as e:
        return {"error": f"An error occurred: {str(e)}"}


@app.get("/data/inspections")
def get_previous_inspection_row(
    plant: str,
    unit: str,
    system: str,
    tag_name: str,
    asset_classification: str,
    equipment_type: str
):
    # Open Google Sheet using its specific name
    sheet_name = "HE_Data Collection template Fix Equipment"
    sheet = client.open(sheet_name)

    worksheet_name = "Previous Inspections"
    header_start_row = 1  # Assuming headers are in the 2nd row

    # Get data from the specified range in the "Previous Inspections" worksheet
    range_name = f"{worksheet_name}"  # Get the entire range of the sheet
    response = sheet.values_get(range_name)
    data = response.get('values', [])
    headers = data[header_start_row]
    adjusted_data = [row + [""] * (len(headers) - len(row)) for row in data[header_start_row + 1:]]
    df = pd.DataFrame(adjusted_data, columns=headers)

    

    
    
    condition = (
        (df["Plant"] == plant) &
        (df["Asset Classification"] == asset_classification) &
        (df["Unit"] == unit) &
        (df["System"] == system) &
        (df["Tag Name"] == tag_name)&
        (df['Equipment Type'] == equipment_type)
    )
    filtered_df = df[condition]
    num_rows = len(filtered_df)
    if num_rows > 0:
        rows_list = filtered_df.astype(str).to_dict(orient='records')  # Convert all data to string
        row_indices = [int(idx) for idx in filtered_df.index]  # List of original indices
        
        # Attach rowIndex to each individual row
        for idx, row in enumerate(rows_list):
            row["rowIndex"] = row_indices[idx]

        return {"rows": rows_list, "rowIndices": row_indices}
    elif num_rows == 0:
        return {"error": "No rows found matching the criteria."}





uvicorn.run(app, host="**********", port=8010)
