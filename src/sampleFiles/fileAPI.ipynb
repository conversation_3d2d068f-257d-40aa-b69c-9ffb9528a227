{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:     Started server process [34466]\n", "INFO:     Waiting for application startup.\n", "INFO:     Application startup complete.\n", "INFO:     <PERSON><PERSON><PERSON> running on http://0.0.0.0:8000 (Press CTRL+C to quit)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:     127.0.0.1:54038 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54038 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54105 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54105 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54105 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54105 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57355 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57355 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57805 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57805 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57829 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57829 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58046 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58046 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58407 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58407 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58581 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:58581 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59097 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59097 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59430 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:59430 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60646 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:60646 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61085 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61085 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61685 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:61685 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53060 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:53060 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54687 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54687 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63493 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63494 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63530 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:63530 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49801 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49801 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50657 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50657 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50707 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50707 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50864 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50864 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50864 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:50864 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54123 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54123 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64117 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64117 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64648 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64648 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54109 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:54109 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56724 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56724 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56724 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56724 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56724 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56724 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56793 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56793 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56793 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56793 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56793 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56793 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56975 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:56975 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57041 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57041 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57321 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:57321 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64301 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64303 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64328 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:64328 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49994 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:49994 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51190 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:51190 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52182 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52182 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52572 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n", "INFO:     127.0.0.1:52572 - \"GET /get-block-data/undefined HTTP/1.1\" 200 OK\n"]}], "source": ["# Step 2: Define the FastAPI application\n", "from fastapi import FastAPI\n", "import json\n", "import nest_asyncio\n", "import uvicorn\n", "\n", "app = FastAPI()\n", "\n", "\n", "nest_asyncio.apply()\n", "\n", "@app.get(\"/get-block-data/{filename}\")\n", "def get_block_data(filename: str):\n", "    # remove extension form file\n", "    filename = filename.split(\".\")[0]\n", "    if filename == \"D82-0108-ITP\":\n", "        with open(\"combined_textract_output.json\", \"r\") as file:\n", "            data = json.load(file)\n", "        return data\n", "    else:\n", "        return {\"error\": \"File not found\"}\n", "\n", "\n", "\n", "\n", "nest_asyncio.apply()\n", "\n", "uvicorn.run(app, host=\"0.0.0.0\", port=8000)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pip install Flask-HTTPAuth"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["http://0.0.0.0:8000/get-block-data/D82-0108-ITP.pdf"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}