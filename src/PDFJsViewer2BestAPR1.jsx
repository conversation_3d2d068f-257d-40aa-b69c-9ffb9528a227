import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Spinner, SpinnerSize } from "@blueprintjs/core";
import { useInView } from 'react-intersection-observer';
import "@blueprintjs/core/lib/css/blueprint.css";
import {
    updatePdfViewer,
    setViewerVisibility,
    setFocusedViewer,
    selectVisiblePDFs
} from './redux/currentPdfSlice';

export default function PDFJsViewer({ fileId, filePath, isDarkTheme }) {
    const dispatch = useDispatch();
    const [pdfFile, setPdfFile] = useState(null);
    const [downloadProgress, setDownloadProgress] = useState(0);
    const [hasLoaded, setHasLoaded] = useState(false);
    const iframeRef = useRef(null);
    const [viewerReady, setViewerReady] = useState(false);
    const pendingPageNavigation = useRef(null);
    
    const { ref, inView } = useInView({
        threshold: 0.1,
    });

    const currentViewerState = useSelector(state => state.currentPdf.viewers[fileId]);
    const visiblePDFs = useSelector(selectVisiblePDFs);

    // Function to navigate to a specific page
    const navigateToPage = useCallback((pageNumber) => {
        if (iframeRef.current && iframeRef.current.contentWindow) {
            try {
                console.log(`Attempting to navigate PDF to page ${pageNumber}`);
                
                // Send a more comprehensive message to the iframe
                iframeRef.current.contentWindow.postMessage({
                    type: 'setPage',
                    pageNumber: parseInt(pageNumber, 10),
                    timestamp: Date.now(),
                    action: 'forceNavigate'
                }, '*');
                
                // Also update Redux state
                dispatch(updatePdfViewer({
                    fileId,
                    page: parseInt(pageNumber, 10),
                    filePath,
                    isVisible: inView
                }));
                
                console.log(`Successfully sent page navigation message for page ${pageNumber}`);
            } catch (err) {
                console.error('Error sending page navigation to iframe:', err);
            }
        } else {
            console.warn('PDF iframe not ready for navigation');
        }
    }, [dispatch, fileId, filePath, inView]);

    // Expose the navigation method to parent components
    useEffect(() => {
        // Listen for custom events for page navigation
        const handlePageNavigation = (event) => {
            if (event.detail && event.detail.pageNumber) {
                console.log('Received page navigation event:', event.detail);
                const pageNumber = parseInt(event.detail.pageNumber, 10);
                
                if (viewerReady) {
                    navigateToPage(pageNumber);
                } else {
                    console.log('Viewer not ready, storing pending navigation to page:', pageNumber);
                    pendingPageNavigation.current = pageNumber;
                }
            }
        };
        
        window.addEventListener('pdf-page-navigation', handlePageNavigation);
        
        // Also listen for window messages for cross-frame communication
        const handleWindowMessage = (event) => {
            if (event.data && event.data.type === 'setPage') {
                console.log('Received postMessage for page navigation:', event.data);
                const pageNumber = parseInt(event.data.pageNumber, 10);
                
                if (viewerReady) {
                    navigateToPage(pageNumber);
                } else {
                    console.log('Viewer not ready, storing pending navigation to page:', pageNumber);
                    pendingPageNavigation.current = pageNumber;
                }
            }
        };
        
        window.addEventListener('message', handleWindowMessage);
        
        return () => {
            window.removeEventListener('pdf-page-navigation', handlePageNavigation);
            window.removeEventListener('message', handleWindowMessage);
        };
    }, [navigateToPage, viewerReady]);

    useEffect(() => {
        console.log('Currently visible PDFs:', visiblePDFs);
    }, [visiblePDFs]);

    useEffect(() => {
        async function fetchFile() {
            if (hasLoaded) return;
            
            setPdfFile(null);
            setDownloadProgress(0);

            try {
                const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
                    headers: {
                        'Accept-Encoding': 'gzip, deflate, br',
                        'session-id': localStorage.getItem('session_id'),
                    },
                });
                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }

                const contentLength = response.headers.get('Content-Length');
                const reader = response.body.getReader();
                let receivedLength = 0;
                const chunks = [];

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    chunks.push(value);
                    receivedLength += value.length;

                    if (contentLength) {
                        setDownloadProgress(receivedLength / contentLength);
                    }
                }

                const blob = new Blob(chunks);
                const pdfUrl = URL.createObjectURL(blob);
                setPdfFile(pdfUrl);
                setHasLoaded(true);

                dispatch(updatePdfViewer({
                    fileId,
                    filePath,
                    page: 1,
                    isVisible: inView
                }));

                console.log('PDF Loaded:', {
                    fileId,
                    filePath,
                    initialPage: 1,
                    isVisible: inView
                });
            } catch (error) {
                console.error("Failed to fetch the PDF:", error);
            }
        }

        // Only fetch the file if we're not on the PDF viewer URL directly
        const currentPath = window.location.pathname;
        if (!currentPath.includes('/pdfjs-3.11.174-dist/web/')) {
            fetchFile();
        }

        return () => {
            if (pdfFile && !hasLoaded) {
                URL.revokeObjectURL(pdfFile);
            }
        };
    }, [fileId, filePath, dispatch, hasLoaded, inView, pdfFile]);

    useEffect(() => {
        const handleMessage = (event) => {
            if (event.data.type === 'pdfState' && inView) {
                const { action, data } = event.data;
                
                switch (action) {
                    case 'pagesloaded':
                        dispatch(updatePdfViewer({
                            fileId,
                            page: data.page,
                            filePath,
                            isVisible: inView
                        }));
                        break;

                    case 'pagechanging':
                        dispatch(updatePdfViewer({
                            fileId,
                            page: data.page,
                            scale: data.scale,
                            scrollTop: data.scrollTop,
                            filePath,
                            isVisible: inView
                        }));
                        console.log('Redux State Updated:', {
                            fileId,
                            filePath,
                            page: data.page,
                            isVisible: inView
                        });
                        break;

                    case 'scalechanging':
                        dispatch(updatePdfViewer({
                            fileId,
                            scale: data.scale,
                            scrollTop: data.scrollTop,
                            filePath,
                            isVisible: inView
                        }));
                        break;
                        
                    default:
                        // Handle unrecognized actions
                        console.log('Unhandled PDF.js action:', action);
                        break;
                }
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [dispatch, fileId, filePath, inView]);

    useEffect(() => {
        if (iframeRef.current) {
            // Expose the PDF viewer instance to the window object for external access
            window.pdfViewerInstance = {
                goToPage: (pageNumber) => {
                    try {
                        const iframe = iframeRef.current;
                        if (iframe && iframe.contentWindow) {
                            // Use the PDFViewerApplication from the iframe
                            const PDFViewerApplication = iframe.contentWindow.PDFViewerApplication;
                            if (PDFViewerApplication && PDFViewerApplication.page) {
                                // Convert from 1-indexed (user-facing) to 0-indexed (PDF.js internal)
                                // PDF.js expects a 1-indexed page number for the .page property
                                PDFViewerApplication.page = pageNumber;
                                console.log(`Navigated to page ${pageNumber}`);
                                return true;
                            }
                        }
                    } catch (error) {
                        console.error("Error navigating to page:", error);
                    }
                    return false;
                }
            };
        }
        
        // Cleanup function to remove the instance when component unmounts
        return () => {
            window.pdfViewerInstance = null;
        };
    }, [hasLoaded]);

    useEffect(() => {
        // Listen for messages from the iframe
        const handleIframeMessage = (event) => {
            if (event.data && event.data.source === 'pdfjs') {
                if (event.data.type === 'viewerReady') {
                    console.log('PDF.js viewer is now ready');
                    setViewerReady(true);
                    
                    // If we have a pending page navigation, do it now
                    if (pendingPageNavigation.current !== null) {
                        const pageNumber = pendingPageNavigation.current;
                        console.log('Executing pending navigation to page:', pageNumber);
                        navigateToPage(pageNumber);
                        pendingPageNavigation.current = null;
                    }
                }
            }
        };
        
        window.addEventListener('message', handleIframeMessage);
        return () => {
            window.removeEventListener('message', handleIframeMessage);
        };
    }, [navigateToPage]);

    useEffect(() => {
        const currentPath = window.location.pathname;
        if (currentPath.includes('/pdfjs-3.11.174-dist/web/')) {
            // Redirect to home page if accessed directly
            window.location.href = '/';
            return;
        }
        
        dispatch(setViewerVisibility({ fileId, isVisible: inView }));
        
        if (inView) {
            dispatch(setFocusedViewer(fileId));
            console.log('PDF Viewer Visible:', {
                fileId,
                filePath,
                currentPage: currentViewerState?.page || 1
            });
        }
    }, [inView, fileId, dispatch, filePath, currentViewerState]);

    if (!pdfFile) {
        return (
            <div className="spinner-container">
                <Spinner
                    className="spinner-center"
                    intent="primary"
                    size={SpinnerSize.STANDARD}
                    value={downloadProgress}
                />
            </div>
        );
    }

    return (
        <div ref={ref} id={`pdf-viewer-${fileId}`} style={{height: "100%", display: "flex", flexDirection: "column"}}>
            <div style={{flex: 1, position: "relative"}}>
                <iframe
                    ref={iframeRef}
                    title={`PDF Viewer for ${filePath}`}
                    width="100%"
                    height="100%"
                    src={`${process.env.PUBLIC_URL}/pdfjs-3.4.120-dist/web/viewer.html?file=${encodeURIComponent(pdfFile)}`}
                    className={isDarkTheme ? "dark-theme" : "light-theme"}
                    style={{ border: "none" }}
                    onLoad={(event) => {
                        const iframeDocument = event.target.contentDocument || event.target.contentWindow.document;
                        const customStyle = iframeDocument.createElement('style');
                        customStyle.innerHTML = `
                            #viewerContainer {
                                overflow-y: scroll !important;
                                padding: 40px 0 !important;
                                position: absolute !important;
                                top: 32px !important;
                                left: 0 !important;
                                right: 0 !important;
                                bottom: 0 !important;
                            }
                            #viewer {
                                margin: 0 !important;
                                padding: 0 !important;
                                position: relative !important;
                            }
                            .pdfViewer .page {
                                margin: 1px auto !important;
                                border: none !important;
                            }
                            .pdfViewer .page:first-child {
                                margin-top: 0 !important;
                            }
                            .pdfViewer .page:last-child {
                                margin-bottom: 40px !important;
                            }
                            #mainContainer {
                                position: absolute !important;
                                top: 0 !important;
                                left: 0 !important;
                                right: 0 !important;
                                bottom: 0 !important;
                                overflow: hidden !important;
                            }
                            #toolbarContainer {
                                position: absolute !important;
                                top: 0 !important;
                                left: 0 !important;
                                right: 0 !important;
                                height: 32px !important;
                                z-index: 2 !important;
                                background: var(--background-color) !important;
                            }
                            #outerContainer {
                                position: absolute !important;
                                top: 0 !important;
                                left: 0 !important;
                                right: 0 !important;
                                bottom: 0 !important;
                            }
                        `;
                        iframeDocument.head.appendChild(customStyle);
                        setThemeInIframe(iframeDocument, isDarkTheme ? 'Dark' : 'Light');
                        simulateClickOnHandTool(iframeDocument);
                        setupPageChangeListener(iframeDocument, filePath);

                        // After iframe loads, add a script to handle initialization and navigation
                        const script = iframeDocument.createElement('script');
                        script.textContent = `
                            // Wait for PDFViewerApplication to initialize
                            if (typeof PDFViewerApplication !== 'undefined') {
                                PDFViewerApplication.initializedPromise.then(() => {
                                    console.log('PDF.js viewer application initialized');
                                    
                                    // Let parent know viewer is ready
                                    window.parent.postMessage({ 
                                        type: 'viewerReady', 
                                        source: 'pdfjs'
                                    }, '*');
                                    
                                    // Function to handle navigation with better scrolling
                                    window.pdfJsNavigateToPage = function(pageNumber) {
                                        try {
                                            console.log('PDF.js executing navigateToPage function with page:', pageNumber);
                                            
                                            // First, set the page number
                                            PDFViewerApplication.page = pageNumber;
                                            
                                            // Then, ensure the PDF viewer is focused on that page
                                            setTimeout(() => {
                                                try {
                                                    // Get the page view and scroll to it
                                                    if (PDFViewerApplication.pdfViewer) {
                                                        // Get the page view (0-indexed)
                                                        const pageView = PDFViewerApplication.pdfViewer.getPageView(pageNumber - 1);
                                                        
                                                        if (pageView && pageView.div) {
                                                            console.log('Found page view, scrolling to page...');
                                                            
                                                            // Get the viewer container
                                                            const container = document.getElementById('viewerContainer');
                                                            if (container) {
                                                                // Calculate scroll position to center the page
                                                                const pageTop = pageView.div.offsetTop;
                                                                container.scrollTop = pageTop - 20; // Slight offset from top
                                                                
                                                                console.log('Scrolled container to page position:', pageTop);
                                                                
                                                                // Report success to parent
                                                                window.parent.postMessage({
                                                                    type: 'pageNavigationComplete',
                                                                    pageNumber: pageNumber,
                                                                    success: true,
                                                                    source: 'pdfjs'
                                                                }, '*');
                                                            }
                                                        } else {
                                                            console.error('Could not find page view for page:', pageNumber);
                                                        }
                                                    }
                                                } catch (err) {
                                                    console.error('Error during scroll operation:', err);
                                                }
                                            }, 100); // Small delay to ensure the page is loaded
                                            
                                            return true;
                                        } catch (err) {
                                            console.error('Error in pdfJsNavigateToPage:', err);
                                            return false;
                                        }
                                    };
                                    
                                    // Listen for page navigation commands
                                    window.addEventListener('message', function(event) {
                                        if (event.data && event.data.type === 'setPage') {
                                            const pageNumber = parseInt(event.data.pageNumber, 10);
                                            console.log('PDF.js received page navigation to:', pageNumber);
                                            
                                            // Use the new navigation function
                                            window.pdfJsNavigateToPage(pageNumber);
                                        }
                                    });
                                });
                            } else {
                                console.error('PDFViewerApplication not found in iframe');
                            }
                        `;
                        iframeDocument.body.appendChild(script);
                        
                        // Script to handle the initial page load
                        const initScript = iframeDocument.createElement('script');
                        initScript.textContent = `
                            window.parent.postMessage({ 
                                type: 'iframeLoaded', 
                                source: 'pdfjs'
                            }, '*');
                        `;
                        iframeDocument.body.appendChild(initScript);
                    }}
                />
            </div>
        </div>
    );
}

function simulateClickOnHandTool(iframeDocument) {
    const script = iframeDocument.createElement('script');
    script.textContent = `
        function simulateClick() {
            const handToolButton = document.getElementById('cursorHandTool');
            if (handToolButton) {
                handToolButton.click();
            }
        }
        document.addEventListener('DOMContentLoaded', simulateClick);
        if (document.readyState === 'complete') {
            simulateClick();
        }
    `;
    iframeDocument.head.appendChild(script);
}

function setThemeInIframe(iframeDocument, theme) {
    const setThemeFunctionString = `
        function setTheme(theme) {
            if (theme == 'Dark') {
                PDFViewerApplicationOptions.set('viewerCssTheme', 1);
            }
            else {
                PDFViewerApplicationOptions.set('viewerCssTheme', 2);
            }
            PDFViewerApplication._forceCssTheme();
        }
    `;

    const script = iframeDocument.createElement('script');
    script.textContent = setThemeFunctionString;
    iframeDocument.head.appendChild(script);

    iframeDocument.defaultView.setTheme(theme);
}

function setupPageChangeListener(iframeDocument, filePath) {
    const script = iframeDocument.createElement('script');
    script.textContent = `
        function setupPageChangeTracking() {
            if (typeof PDFViewerApplication !== 'undefined') {
                PDFViewerApplication.eventBus.on('pagesloaded', function(evt) {
                    console.log('PDF Pages Loaded:', {
                        filePath: "${filePath}",
                        totalPages: PDFViewerApplication.pagesCount,
                        currentPage: PDFViewerApplication.page
                    });

                    window.parent.postMessage({
                        type: 'pdfState',
                        action: 'pagesloaded',
                        data: {
                            page: PDFViewerApplication.page,
                            totalPages: PDFViewerApplication.pagesCount
                        }
                    }, '*');
                });

                PDFViewerApplication.eventBus.on('pagechanging', function(evt) {
                    const pageNumber = evt.pageNumber;
                    console.log('Page Changing:', {
                        filePath: "${filePath}",
                        previousPage: PDFViewerApplication.page,
                        newPage: pageNumber,
                        totalPages: PDFViewerApplication.pagesCount
                    });
                    
                    window.parent.postMessage({
                        type: 'pdfState',
                        action: 'pagechanging',
                        data: {
                            page: pageNumber,
                            scale: PDFViewerApplication.pdfViewer.currentScale,
                            scrollTop: document.getElementById('viewerContainer').scrollTop
                        }
                    }, '*');
                });

                PDFViewerApplication.eventBus.on('scalechanging', function(evt) {
                    window.parent.postMessage({
                        type: 'pdfState',
                        action: 'scalechanging',
                        data: {
                            scale: evt.scale,
                            scrollTop: document.getElementById('viewerContainer').scrollTop
                        }
                    }, '*');
                });
            }
        }

        if (typeof PDFViewerApplication !== 'undefined') {
            PDFViewerApplication.initializedPromise.then(setupPageChangeTracking);
        } else {
            document.addEventListener('webviewerloaded', function() {
                PDFViewerApplication.initializedPromise.then(setupPageChangeTracking);
            });
        }
    `;
    iframeDocument.head.appendChild(script);
}
