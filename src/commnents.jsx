import "@blueprintjs/core/lib/css/blueprint.css";
import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Divider,
  Elevation,
  FormGroup,
  H5,
  Intent,
  <PERSON>vbar,
  Spinner,
  TextArea,
} from "@blueprintjs/core";
import { IconNames } from "@blueprintjs/icons";
import { BlueprintProvider } from "@blueprintjs/core";

const Reply = ({ reply, onEditReply, onDeleteReply, currentUser }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(reply.text);

  const handleEditClick = () => {
    setIsEditing(true);
    setEditText(reply.text);
  };

  const handleEditSubmit = () => {
    if (editText.trim() && editText !== reply.text) {
      onEditReply(reply.id, editText);
      setIsEditing(false);
    }
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditText(reply.text);
  };

  const handleDeleteReply = () => {
    if (reply.author === currentUser) {
      onDeleteReply(reply.id);
    } else {
      alert("You can only delete your own replies.");
    }
  };

  return (
    <div style={{ marginLeft: "20px" }}>
      <p>
        <strong>{reply.author}</strong> - {reply.datetime}
      </p>
      {isEditing ? (
        <FormGroup>
          <TextArea
            fill={true}
            intent={Intent.PRIMARY}
            value={editText}
            onChange={(e) => setEditText(e.target.value)}
          />
          <Button
            icon={IconNames.TICK}
            intent={Intent.PRIMARY}
            onClick={handleEditSubmit}
            style={{ marginRight: "5px" }}
          >
            Save
          </Button>
          <Button
            icon={IconNames.CROSS}
            intent={Intent.DANGER}
            onClick={handleEditCancel}
          >
            Cancel
          </Button>
        </FormGroup>
      ) : (
        <>
          <p>{reply.text}</p>
          {reply.author === currentUser && (
            <>
              <Button
                icon={IconNames.EDIT}
                minimal={true}
                intent={Intent.PRIMARY}
                onClick={handleEditClick}
                style={{ marginRight: "5px" }}
              >
                Edit
              </Button>
              <Button
                icon={IconNames.TRASH}
                minimal={true}
                intent={Intent.DANGER}
                onClick={handleDeleteReply}
              >
                Delete
              </Button>
            </>
          )}
        </>
      )}
    </div>
  );
};

const Issue = ({
  issue,
  onEditIssue,
  onDeleteIssue,
  onReplySubmit,
  onEditReply,
  onDeleteReply,
  currentUser,
}) => {
  const [isReplying, setIsReplying] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [replyText, setReplyText] = useState("");
  const [editText, setEditText] = useState(issue.text);

  const handleReplyClick = () => {
    setIsReplying(true);
  };

  const handleReplySubmit = () => {
    if (replyText.trim()) {
      const newReply = {
        id: Date.now(),
        author: currentUser,
        text: replyText,
        datetime: new Date().toLocaleString(),
      };
      onReplySubmit(issue.id, newReply);
      setReplyText("");
      setIsReplying(false);
    }
  };

  const handleEditClick = () => {
    setIsEditing(true);
    setEditText(issue.text);
  };

  const handleEditSubmit = () => {
    if (editText.trim() && editText !== issue.text) {
      onEditIssue(issue.id, editText);
      setIsEditing(false);
    }
  };

  const handleEditCancel = () => {
    setIsEditing(false);
    setEditText(issue.text);
  };

  const handleDeleteIssue = () => {
    if (issue.author === currentUser) {
      onDeleteIssue(issue.id);
    } else {
      alert("You can only delete your own issues.");
    }
  };

  return (
    <Card
      interactive={true}
      elevation={Elevation.TWO}
      style={{ marginBottom: "10px" }}
    >
      <div>
        <p>
          <strong>{issue.author}</strong> - {issue.datetime}
        </p>
        {isEditing ? (
          <FormGroup>
            <TextArea
              fill={true}
              intent={Intent.PRIMARY}
              value={editText}
              onChange={(e) => setEditText(e.target.value)}
            />
            <Button
              icon={IconNames.TICK}
              intent={Intent.PRIMARY}
              onClick={handleEditSubmit}
              style={{ marginRight: "5px" }}
            >
              Save
            </Button>
            <Button
              icon={IconNames.CROSS}
              intent={Intent.DANGER}
              onClick={handleEditCancel}
            >
              Cancel
            </Button>
          </FormGroup>
        ) : (
          <>
            <H5>{issue.text}</H5>
            {issue.author === currentUser && (
              <>
                <Button
                  icon={IconNames.EDIT}
                  minimal={true}
                  intent={Intent.PRIMARY}
                  onClick={handleEditClick}
                  style={{ marginRight: "5px" }}
                >
                  Edit
                </Button>
                <Button
                  icon={IconNames.TRASH}
                  minimal={true}
                  intent={Intent.DANGER}
                  onClick={handleDeleteIssue}
                >
                  Delete
                </Button>
              </>
            )}
            <Button
              icon={IconNames.COMMENT}
              minimal={true}
              intent={Intent.PRIMARY}
              onClick={handleReplyClick}
              style={{ marginRight: "5px" }}
            >
              Reply
            </Button>
          </>
        )}
        {isReplying && (
          <FormGroup>
            <TextArea
              fill={true}
              intent={Intent.PRIMARY}
              value={replyText}
              onChange={(e) => setReplyText(e.target.value)}
            />
            <Button
              icon={IconNames.TICK}
              intent={Intent.PRIMARY}
              onClick={handleReplySubmit}
              style={{ marginRight: "5px" }}
            >
              Submit
            </Button>
            <Button
              icon={IconNames.CROSS}
              intent={Intent.DANGER}
              onClick={() => setIsReplying(false)}
            >
              Cancel
            </Button>
          </FormGroup>
        )}
      </div>
      <Divider />
      {issue.replies.length > 0 && (
        <div>
          {issue.replies.map((reply) => (
            <Reply
              key={reply.id}
              reply={reply}
              onEditReply={onEditReply}
              onDeleteReply={onDeleteReply}
              currentUser={currentUser}
            />
          ))}
        </div>
      )}
    </Card>
  );
};

const App = () => {
  const [issues, setIssues] = useState([
    {
      id: 1,
      author: "Luigi Salemi",
      text: "This is an example issue.",
      datetime: new Date().toLocaleString(),
      replies: [
        {
          id: Date.now(),
          author: "Jane Smith",
          text: "This is a reply to the example issue.",
          datetime: new Date().toLocaleString(),
        },
        {
          id: Date.now() + 1,
          author: "Bob Johnson",
          text: "Another reply to the example issue.",
          datetime: new Date().toLocaleString(),
        },
      ],
    },
    {
      id: 2,
      author: "Alice Williams",
      text: "This is another issue.",
      datetime: new Date().toLocaleString(),
      replies: [],
    },
  ]);
  const [isCreatingIssue, setIsCreatingIssue] = useState(false);
  const [newIssueText, setNewIssueText] = useState("");
  const [isLoadingIssues, setIsLoadingIssues] = useState(false);
  const [currentUser, setCurrentUser] = useState("Luigi Salemi");

  const handleNewIssueClick = () => {
    setIsCreatingIssue(true);
  };

  const handleNewIssueSubmit = () => {
    if (newIssueText.trim()) {
      const newIssue = {
        id: Date.now(),
        author: currentUser,
        text: newIssueText,
        datetime: new Date().toLocaleString(),
        replies: [],
      };
      setIssues([...issues, newIssue]);
      setNewIssueText("");
      setIsCreatingIssue(false);
    }
  };

  const handleNewIssueCancelClick = () => {
    setIsCreatingIssue(false);
    setNewIssueText("");
  };

  const handleReplySubmit = (issueId, newReply) => {
    const updatedIssues = issues.map((issue) => {
      if (issue.id === issueId) {
        return {
          ...issue,
          replies: [...issue.replies, newReply],
        };
      } else {
        return issue;
      }
    });
    setIssues(updatedIssues);
  };

  const handleEditIssue = (issueId, editedText) => {
    const updatedIssues = issues.map((issue) => {
      if (issue.id === issueId) {
        return {
          ...issue,
          text: editedText,
        };
      } else {
        return issue;
      }
    });
    setIssues(updatedIssues);
  };

  const handleDeleteIssue = (issueId) => {
    const updatedIssues = issues.filter((issue) => issue.id !== issueId);
    setIssues(updatedIssues);
  };

  const handleEditReply = (replyId, editedText) => {
    const updatedIssues = issues.map((issue) => {
      return {
        ...issue,
        replies: issue.replies.map((reply) => {
          if (reply.id === replyId) {
            return {
              ...reply,
              text: editedText,
            };
          } else {
            return reply;
          }
        }),
      };
    });
    setIssues(updatedIssues);
  };

  const handleDeleteReply = (replyId) => {
    const updatedIssues = issues.map((issue) => {
      return {
        ...issue,
        replies: issue.replies.filter((reply) => reply.id !== replyId),
      };
    });
    setIssues(updatedIssues);
  };

  return (
    <BlueprintProvider>
      <div style={{ maxWidth: "800px", margin: "0 auto", padding: "20px" }}>
        <Navbar>
          <Navbar.Group align="left">
            <Navbar.Heading>Issue Tracker</Navbar.Heading>
          </Navbar.Group>
          <Navbar.Group align="right">
            <p>Current User: {currentUser}</p>
          </Navbar.Group>
        </Navbar>
        {isCreatingIssue ? (
          <Card elevation={Elevation.TWO} style={{ marginBottom: "20px" }}>
            <FormGroup>
              <TextArea
                fill={true}
                intent={Intent.PRIMARY}
                value={newIssueText}
                onChange={(e) => setNewIssueText(e.target.value)}
                placeholder="Write a new issue..."
              />
              <Button
                icon={IconNames.TICK}
                intent={Intent.PRIMARY}
                onClick={handleNewIssueSubmit}
                style={{ marginRight: "5px" }}
              >
                Save
              </Button>
              <Button
                icon={IconNames.CROSS}
                intent={Intent.DANGER}
                onClick={handleNewIssueCancelClick}
              >
                Cancel
              </Button>
            </FormGroup>
          </Card>
        ) : (
          <Button
            icon={IconNames.PLUS}
            intent={Intent.PRIMARY}
            onClick={handleNewIssueClick}
            style={{ marginBottom: "20px" }}
          >
            New Issue
          </Button>
        )}
        {isLoadingIssues ? (
          <Spinner intent={Intent.PRIMARY} />
        ) : (
          issues.map((issue) => (
            <Issue
              key={issue.id}
              issue={issue}
              onEditIssue={handleEditIssue}
              onDeleteIssue={handleDeleteIssue}
              onReplySubmit={handleReplySubmit}
              onEditReply={handleEditReply}
              onDeleteReply={handleDeleteReply}
              currentUser={currentUser}
            />
          ))
        )}
      </div>
    </BlueprintProvider>
  );
};

export default App;
