import React, { useEffect, useState } from "react";
import { Provider, useSelector, useDispatch } from "react-redux";
import store from "./redux/store";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { setIsLoggedIn, setUser, setPlant } from "./redux/authSlice";
import axios from "axios";
import "./Styles/App.css";
import "./Styles/pdf-viewer.css";
import MainApp from "./MainApp";
import LoginApp from "./LoginApp";
import Dashboard from "./Dashboard";
import AntDesignConfig from './Styles/AntDesignConfig'; 
import { AppContextProvider } from "./AppContextProvider";
import "normalize.css";
import "@blueprintjs/core/lib/css/blueprint.css";
import "@blueprintjs/icons/lib/css/blueprint-icons.css";
import { RPConfig } from "@pdf-viewer/react";

const DASHBOARD_AUTHORIZED_USERS = [
  '<EMAIL>',
];

function App() {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const isLoggedIn = useSelector((state) => state.auth.isLoggedIn);
  const user = useSelector((state) => state.auth.user);

  // Check if we're on a PDF viewer path and redirect if needed
  useEffect(() => {
    const currentPath = window.location.pathname;
    if (currentPath.includes('/pdfjs-3.11.174-dist/web/')) {
      window.location.href = '/';
    }
  }, []);

  useEffect(() => {
    const validateSession = async () => {
      const session_id = localStorage.getItem("session_id");
      if (session_id) {
        try {
          await new Promise((resolve) => setTimeout(resolve, 2000));
          const response = await axios.post(
            `${process.env.REACT_APP_DATA_API}/validate_session`,
            { session_id },
            { withCredentials: true }
          );
          if (response.data.isValid) {
            dispatch(setIsLoggedIn(true));
            dispatch(setPlant(response.data.plant));
            const storedUser = localStorage.getItem("user_data");
            if (storedUser) {
              const userObject = JSON.parse(storedUser);
              dispatch(setUser(userObject));
            }
          } else {
            localStorage.removeItem("session_id");
            dispatch(setIsLoggedIn(false));
            dispatch(setUser(null));
          }
        } catch (error) {
          console.error("Session validation error:", error);
          localStorage.removeItem("session_id");
          dispatch(setIsLoggedIn(false));
          dispatch(setUser(null));
        }
      }
      setIsLoading(false);
    };
    validateSession();
  }, [dispatch]);

  if (isLoading) {
    return (
      <div style={{ backgroundColor: "#2F343C", height: "100vh", width: "100vw", position: "fixed", top: 0, left: 0, zIndex: 999 }}>
        <div className="loader" style={{ position: "fixed", top: "50%", left: "50%", transform: "translate(-50%, -50%)", zIndex: 1000 }}></div>
        <div className="loader2" style={{ position: "fixed", top: "55%", left: "50%", transform: "translate(-50%, -50%)", zIndex: 1000 }}></div>
      </div>
    );
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={isLoggedIn ? <Navigate to="/" /> : <LoginApp />} />
        <Route 
          path="/" 
          element={
            isLoggedIn && user ? (
              <MainApp user={user} />
            ) : (
              <Navigate to="/login" />
            )
          } 
        />
        <Route
          path="/dashboard"
          element={
            isLoggedIn && user ? (
              DASHBOARD_AUTHORIZED_USERS.includes(user.email) ? (
                <Dashboard />
              ) : (
                <Navigate to="/" />
              )
            ) : (
              <Navigate to="/login" />
            )
          }
        />
        {/* Redirect PDF viewer paths to the main app */}
        <Route path="/pdfjs-3.11.174-dist/web/viewer.html" element={<Navigate to="/" />} />
        <Route path="/pdfjs-3.11.174-dist/web/*" element={<Navigate to="/" />} />
        {/* Catch all other routes and redirect to main app */}
        <Route path="*" element={<Navigate to="/" />} />
      </Routes>
    </BrowserRouter>
  );
}

export default function WrappedApp() {
  return (
    <AppContextProvider>
      <AntDesignConfig>
        <Provider store={store}>
          <RPConfig>
            <App />
          </RPConfig>
        </Provider>
      </AntDesignConfig>
    </AppContextProvider>
  );
}
