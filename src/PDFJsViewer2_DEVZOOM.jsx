import React, { useEffect, useRef, useState, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { Document, Page, pdfjs } from "react-pdf";
import { Card, Spinner, Button, Popover, InputGroup } from "@blueprintjs/core";
import {
  FaRedoAlt,
  FaUndoAlt,
  FaSearchPlus,
  FaSearchMinus,
  FaExpandArrowsAlt,
  FaSearch,
  FaMousePointer,
  FaHandPaper,
  FaAlignLeft,
} from "react-icons/fa";
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import {
  setPdfFile,
  setNumPages,
  setPageNumber,
  setRotation,
  setSearchText,
  setSearchResults,
  setCurrentSearchIndex,
} from "./redux/pdfViewerSlice";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const PAN_SPEED_FACTOR = 0.5; // Reduce panning speed

const PageWithObserver = ({
  pageNumber,
  setPageVisibility,
  scale,
  rotation,
  customTextRenderer,
  orientation,
}) => {
  const [page, setPage] = useState();
  const [imgUrl, setImgUrl] = useState(null);

  const handleRenderSuccess = (pageCanvas) => {
    if (pageCanvas) {
      pageCanvas.toBlob((blob) => {
        if (blob) {
          const newImgUrl = URL.createObjectURL(blob);
          setImgUrl(newImgUrl);
        }
      });
    }
  };

  useEffect(() => {
    return () => {
      if (imgUrl) {
        URL.revokeObjectURL(imgUrl); // Clean up old object URL
      }
    };
  }, [imgUrl]);

  // Adjust width and height based on rotation
  const getAdjustedDimensions = (width, height, rotation) => {
    if (rotation % 180 !== 0) {
      return { width: height, height: width };
    }
    return { width, height };
  };

  const pageWidth = orientation === "landscape" ? 1200 : 800;
  const pageHeight = orientation === "landscape" ? 800 : 1200;
  const { width: adjustedWidth, height: adjustedHeight } = getAdjustedDimensions(
    pageWidth,
    pageHeight,
    rotation
  );

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        marginBottom: "10px",
        position: "relative",
      }}
    >
      {imgUrl && (
        <img
          src={imgUrl}
          alt={`Page ${pageNumber}`}
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            zIndex: 1,
            pointerEvents: "none",
          }}
        />
      )}
      <Page
        canvasRef={setPage}
        pageNumber={pageNumber}
        scale={scale}
        width={adjustedWidth * scale} // Apply adjusted width
        height={adjustedHeight * scale} // Apply adjusted height
        rotate={rotation}
        onRenderSuccess={() => handleRenderSuccess(page)}
        customTextRenderer={customTextRenderer}
        style={{
          display: "block",
          margin: "auto",
          zIndex: 2,
          opacity: imgUrl ? 0 : 1,
        }}
      />
    </div>
  );
};

const PDFJsViewer = ({ fileId, isDarkTheme, ownerDocument }) => {
  const dispatch = useDispatch();
  const pdfFile = useSelector((state) => state.pdfViewer.pdfFile);
  const numPages = useSelector((state) => state.pdfViewer.numPages);
  const pageNumber = useSelector((state) => state.pdfViewer.pageNumber);
  const searchText = useSelector((state) => state.pdfViewer.searchText);
  const searchResults = useSelector((state) => state.pdfViewer.searchResults);
  const currentSearchIndex = useSelector((state) => state.pdfViewer.currentSearchIndex);

  const pdfContainerRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [isSearchPopoverVisible, setIsSearchPopoverVisible] = useState(false);
  const [orientations, setOrientations] = useState([]);
  const [isPanMode, setIsPanMode] = useState(false);
  const [hasTextLayer, setHasTextLayer] = useState(false);
  const [localRotation, setLocalRotation] = useState(0);
  const [scale, setScale] = useState(1);
  const [isPanning, setIsPanning] = useState(false);
  const [startPanPosition, setStartPanPosition] = useState({ x: 0, y: 0 });

  const parentClassName = isDarkTheme ? "bp5-dark" : "";

  useEffect(() => {
    let cancelSource = axios.CancelToken.source();

    const fetchFile = async () => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: {
              "Accept-Encoding": "gzip, deflate, br",
              "session-id": localStorage.getItem("session_id"),
            },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        const fileReader = new FileReader();
        fileReader.onload = () => {
          dispatch(setPdfFile(fileReader.result));
          setLoading(false);
        };
        fileReader.onerror = (error) => {
          console.error("Error reading PDF file:", error);
          setLoading(false);
        };
        fileReader.readAsDataURL(response.data);
      } catch (error) {
        setLoading(false);
        if (axios.isCancel(error)) {
          console.log("Request canceled:", error.message);
        } else {
          console.error("Failed to fetch the PDF:", error);
        }
      }
    };

    if (fileId) {
      fetchFile();
    }

    return () => {
      cancelSource.cancel("Operation canceled by the user.");
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, dispatch]);

  useEffect(() => {
    const getOrientationAndCheckTextLayer = async () => {
      const pdf = await pdfjs.getDocument(pdfFile).promise;
      const orientations = [];
      let textLayerFound = false;

      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const viewport = page.getViewport({ scale: 1 });
        orientations.push(
          viewport.width > viewport.height ? "landscape" : "portrait"
        );

        const textContent = await page.getTextContent();
        if (textContent.items.length > 0) {
          textLayerFound = true;
        }
      }

      setOrientations(orientations);
      setHasTextLayer(textLayerFound);
    };

    if (pdfFile) {
      getOrientationAndCheckTextLayer();
    }
  }, [pdfFile]);

  const onDocumentLoadSuccess = ({ numPages }) => {
    dispatch(setNumPages(numPages));
    dispatch(setPageNumber(1));
  };

  const setPageVisibility = useCallback(
    (pageNumber, isIntersecting) => {
      if (isIntersecting) {
        dispatch(setPageNumber(pageNumber));
      }
    },
    [dispatch]
  );

  const handleWheel = (event) => {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      const delta = Math.sign(event.deltaY) * 0.1;
      const newScale = Math.min(Math.max(scale - delta, 0.5), 3); // Limit zoom scale between 0.5 and 3
      setScale(newScale);
    }
  };

  useEffect(() => {
    window.addEventListener("wheel", handleWheel, { passive: false });

    return () => {
      window.removeEventListener("wheel", handleWheel);
    };
  }, [scale]);

  const handleMouseDown = (event) => {
    if (isPanMode) {
      setIsPanning(true);
      setStartPanPosition({ x: event.clientX, y: event.clientY });
    }
  };

  const handleMouseMove = (event) => {
    if (isPanning) {
      const dx = (event.clientX - startPanPosition.x) * PAN_SPEED_FACTOR; // Adjust panning speed
      const dy = (event.clientY - startPanPosition.y) * PAN_SPEED_FACTOR; // Adjust panning speed
      pdfContainerRef.current.scrollLeft -= dx / scale; // Adjust for zoom level
      pdfContainerRef.current.scrollTop -= dy / scale; // Adjust for zoom level
      setStartPanPosition({ x: event.clientX, y: event.clientY });
    }
  };

  const handleMouseUp = () => {
    setIsPanning(false);
  };

  useEffect(() => {
    window.addEventListener("mousedown", handleMouseDown);
    window.addEventListener("mousemove", handleMouseMove);
    window.addEventListener("mouseup", handleMouseUp);

    return () => {
      window.removeEventListener("mousedown", handleMouseDown);
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isPanMode, isPanning]);

  const zoomIn = () => {
    setScale((prevScale) => Math.min(prevScale * 1.25, 3));
  };

  const zoomOut = () => {
    setScale((prevScale) => Math.max(prevScale * 0.8, 0.5));
  };

  const rotateLeft = () => {
    const newRotation = (localRotation - 90 + 360) % 360;
    setLocalRotation(newRotation);
  };

  const rotateRight = () => {
    const newRotation = (localRotation + 90) % 360;
    setLocalRotation(newRotation);
  };

  const fitToWidth = () => {
    const containerWidth = pdfContainerRef.current.clientWidth;
    setScale(containerWidth / 800); // Assume 800 as the default page width
  };

  const handleSearchSubmit = () => {
    if (searchText.trim() === "") return;
    searchDocument();
  };

  const handleSearchKeyPress = (e) => {
    if (e.key === "Enter") {
      handleSearchSubmit();
    }
  };

  const searchDocument = async () => {
    if (searchText.trim() === "") return;
    const results = [];
    for (let i = 1; i <= numPages; i++) {
      const page = await pdfjs
        .getDocument(pdfFile)
        .promise.then((pdf) => pdf.getPage(i));
      const textContent = await page.getTextContent();
      const textItems = textContent.items.map((item) => ({
        str: item.str,
        transform: item.transform,
        width: item.width,
        height: item.height,
        page: i,
      }));

      textItems.forEach((item) => {
        let match;
        const regex = new RegExp(searchText, "gi");
        while ((match = regex.exec(item.str)) !== null) {
          results.push({
            page: item.page,
            str: match[0],
            transform: item.transform,
            width: item.width,
            height: item.height,
          });
        }
      });
    }

    dispatch(setSearchResults(results));
    dispatch(setCurrentSearchIndex(0));
    if (results.length > 0) {
      const firstMatch = results[0].page;
      dispatch(setPageNumber(firstMatch));
      pdfContainerRef.current
        .querySelector(`.react-pdf__Page[data-page-number="${firstMatch}"]`)
        .scrollIntoView({ behavior: "smooth" });
    }
  };

  const nextSearchResult = () => {
    if (searchResults.length === 0) return;

    const newIndex = (currentSearchIndex + 1) % searchResults.length;
    dispatch(setCurrentSearchIndex(newIndex));
    const nextPage = searchResults[newIndex].page;
    dispatch(setPageNumber(nextPage));
    pdfContainerRef.current
      .querySelector(`.react-pdf__Page[data-page-number="${nextPage}"]`)
      .scrollIntoView({ behavior: "smooth" });
  };

  const prevSearchResult = () => {
    if (searchResults.length === 0) return;

    const newIndex =
      (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
    dispatch(setCurrentSearchIndex(newIndex));
    const prevPage = searchResults[newIndex].page;
    dispatch(setPageNumber(prevPage));
    pdfContainerRef.current
      .querySelector(`.react-pdf__Page[data-page-number="${prevPage}"]`)
      .scrollIntoView({ behavior: "smooth" });
  };

  const clearSearch = () => {
    dispatch(setSearchText(""));
    dispatch(setSearchResults([]));
    setIsSearchPopoverVisible(false);
  };

  const memoizedOptions = useMemo(
    () => ({
      ownerDocument: ownerDocument || document,
    }),
    [ownerDocument]
  );

  const highlightPattern = useCallback(
    (text, pattern) =>
      pattern
        ? text.replace(
            new RegExp(`(${pattern})`, "gi"),
            '<mark style="background-color: gold; padding: 0; margin: 0; border-radius: 2px;">$1</mark>'
          )
        : text,
    [searchText]
  );

  const textRenderer = useCallback(
    (textItem) => highlightPattern(textItem.str, searchText),
    [searchText]
  );

  return (
    <div className={parentClassName}>
      <Card
        style={{
          marginBottom: 0,
          padding: "2px",
          display: "flex",
          alignItems: "center",
          gap: "10px",
          background: isDarkTheme ? "#1C2127" : "white",
        }}
      >
        <Popover
          content={
            <div style={{ padding: "10px" }}>
              <InputGroup
                value={searchText}
                onChange={(e) => dispatch(setSearchText(e.target.value))}
                onKeyPress={handleSearchKeyPress}
                placeholder="Enter search text"
                className="bp4-input"
              />
              <div style={{ marginTop: "10px" }}>
                <Button
                  icon="arrow-left"
                  onClick={prevSearchResult}
                  disabled={searchResults.length === 0}
                >
                  Previous
                </Button>
                <Button
                  icon="arrow-right"
                  onClick={nextSearchResult}
                  disabled={searchResults.length === 0}
                >
                  Next
                </Button>
                <Button
                  icon="cross"
                  onClick={clearSearch}
                  style={{ marginLeft: "5px" }}
                >
                  Close
                </Button>
              </div>
              {searchResults.length > 0 && (
                <div style={{ marginTop: "10px" }}>
                  Match {currentSearchIndex + 1} of {searchResults.length}
                </div>
              )}
            </div>
          }
          isOpen={isSearchPopoverVisible}
          placement="bottom"
          onInteraction={(state) => setIsSearchPopoverVisible(state)}
        >
          <Button icon={<FaSearch />} minimal={true} />
        </Popover>
        <Button icon={<FaSearchPlus />} onClick={zoomIn} minimal={true} />
        <Button icon={<FaSearchMinus />} onClick={zoomOut} minimal={true} />
        <Button icon={<FaExpandArrowsAlt />} onClick={fitToWidth} minimal={true} />
        <Button icon={<FaUndoAlt />} onClick={rotateLeft} minimal={true} />
        <Button icon={<FaRedoAlt />} onClick={rotateRight} minimal={true} />
        <div style={{ display: "flex", alignItems: "center" }}>
          <Button
            icon={<FaMousePointer />}
            onClick={() => setIsPanMode(false)}
            minimal={!isPanMode}
            intent={!isPanMode ? "primary" : "none"}
          />
          <Button
            icon={<FaHandPaper />}
            onClick={() => setIsPanMode(true)}
            minimal={isPanMode}
            intent={isPanMode ? "primary" : "none"}
          />
        </div>
        {/* Icon for Text Layer Presence */}
        <Button
          icon={<FaAlignLeft color={hasTextLayer ? "#72CA9B" : "#808080"} />}
          minimal={true}
        />
        <span style={{ marginLeft: "10px" }}>
          Page {pageNumber} of {numPages}
        </span>
      </Card>

      <div
        ref={pdfContainerRef}
        className="pdf-container"
        style={{
          overflowY: "auto",
          overflowX: "auto",
          height: "calc(100vh - 200px)",
          marginTop: "10px",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          cursor: isPanMode ? "grab" : "auto",
          userSelect: isPanMode ? "none" : "auto",
        }}
      >
        {loading ? (
          <Spinner size={Spinner.SIZE_LARGE} />
        ) : (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            loading={<Spinner />}
            options={memoizedOptions}
            className="pdf-container"
          >
            {Array.from(new Array(numPages), (el, index) => (
              <PageWithObserver
                key={`page_${index + 1}`}
                pageNumber={index + 1}
                setPageVisibility={setPageVisibility}
                scale={scale} // Pass the scale for zoom
                rotation={localRotation}
                customTextRenderer={textRenderer}
                orientation={orientations[index]}
              />
            ))}
          </Document>
        )}
      </div>
    </div>
  );
};

export default PDFJsViewer;
