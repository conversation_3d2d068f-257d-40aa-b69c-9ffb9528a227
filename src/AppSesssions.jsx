import { useEffect, useState } from "react";
import { Provider, useSelector, useDispatch } from "react-redux";
import store from "./redux/store";
import { BlueprintProvider } from "@blueprintjs/core";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { setIsLoggedIn, setUser } from "./redux/authSlice";
import axios from "axios";
import "./Styles/App.css";
import MainApp from "./MainApp";
import LoginApp from "./LoginApp";
import { AppContextProvider } from './AppContextProvider';

const PrivateRoute = ({ children }) => {
  const isLoggedIn = useSelector((state) => state.auth.isLoggedIn);
  return isLoggedIn ? children : <Navigate to="/login" />;
};

function App() {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const validateSession = async () => {
      const session_id = localStorage.getItem("session_id");
      if (session_id) {
        try {
          // Simulate a delay of 5 seconds
          await new Promise((resolve) => setTimeout(resolve, 1000));
          const response = await axios.post(
            `http://0.0.0.0:8020/validate_session/`,
            { session_id },
            { withCredentials: true },
          );
          if (response.data.isValid) {
            dispatch(setIsLoggedIn(true));
            dispatch(setUser(response.data.user));
          } else {
            localStorage.removeItem("session_id");
            dispatch(setIsLoggedIn(false));
            dispatch(setUser(null));
          }
        } catch (error) {
          console.error("Session validation error:", error);
          localStorage.removeItem("session_id");
          dispatch(setIsLoggedIn(false));
          dispatch(setUser(null));
        }
      }
      setIsLoading(false);
    };
    validateSession();
  }, [dispatch]);

  if (isLoading) {
    return <div style={{}}>Loading...</div>;
  }

  return (
    <BrowserRouter>
      <Routes>
        <Route path="/login" element={<LoginApp />} />
        <Route
          path="/"
          element={
            <PrivateRoute>
              <MainApp />
            </PrivateRoute>
          }
        />
      </Routes>
    </BrowserRouter>
  );
}

export default function WrappedApp() {
  return (
    <BlueprintProvider>
          <AppContextProvider>
      <Provider store={store}>
        <App />
      </Provider>
      </AppContextProvider>
    </BlueprintProvider>
  );
}