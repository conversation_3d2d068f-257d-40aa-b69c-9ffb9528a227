import React, { useContext } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
    Dialog,
    Button,
    Intent,
    Classes,
    Icon,
    Callout
} from '@blueprintjs/core';
import {
    selectActions,
    selectIsHistoryOpen,
    setHistoryOpen,
    clearHistory
} from './redux/actionHistorySlice';
import { AppContext } from './AppContextProvider';

export default function ActionHistoryDialog() {
    const dispatch = useDispatch();
    const actions = useSelector(selectActions);
    const isOpen = useSelector(selectIsHistoryOpen);
    const { isDarkTheme } = useContext(AppContext);

    return (
        <Dialog
            isOpen={isOpen}
            onClose={() => dispatch(setHistoryOpen(false))}
            title={
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Icon icon="history" />
                    <span style={{ marginLeft: '10px' }}>Action History</span>
                </div>
            }
            className={isDarkTheme ? 'bp5-dark' : ''}
            style={{ width: '600px' }}
        >
            <div className={Classes.DIALOG_BODY}>
                {actions.length === 0 ? (
                    <Callout 
                        intent={Intent.PRIMARY}
                        icon="info-sign"
                    >
                        No actions recorded yet.
                    </Callout>
                ) : (
                    <div style={{ 
                        maxHeight: '400px', 
                        overflow: 'auto',
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '8px',
                        fontFamily: 'Geist Mono, monospace'
                    }}>
                        {actions.map((action) => (
                            <Callout
                                key={action.id}
                                intent={action.type === 'SUCCESS' ? Intent.SUCCESS :
                                       action.type === 'WARNING' ? Intent.WARNING :
                                       action.type === 'DANGER' ? Intent.DANGER :
                                       Intent.NONE}
                                icon={action.type === 'SUCCESS' ? 'tick-circle' :
                                      action.type === 'WARNING' ? 'warning-sign' :
                                      action.type === 'DANGER' ? 'error' :
                                      'info-sign'}
                            >
                                <div style={{ 
                                    display: 'flex', 
                                    flexDirection: 'column',
                                    gap: '4px'
                                }}>
                                    <div style={{ 
                                        fontSize: '0.9em',
                                        opacity: 0.8
                                    }}>
                                        {action.timestamp}
                                    </div>
                                    <div>
                                        {action.message}
                                    </div>
                                    {action.source && (
                                        <div style={{ 
                                            fontSize: '0.8em',
                                            opacity: 0.6,
                                            marginTop: '4px'
                                        }}>
                                            from {action.source}
                                        </div>
                                    )}
                                </div>
                            </Callout>
                        ))}
                    </div>
                )}
            </div>
            <div className={Classes.DIALOG_FOOTER}>
                <div className={Classes.DIALOG_FOOTER_ACTIONS} style={{ display: 'flex', gap: '10px' }}>
                    <Button
                        intent={Intent.DANGER}
                        onClick={() => dispatch(clearHistory())}
                        disabled={actions.length === 0}
                        text="Clear History"
                        small={true}
                    />
                    <Button 
                        intent={Intent.PRIMARY}
                        onClick={() => dispatch(setHistoryOpen(false))}
                        text="Close"
                        small={true}
                    />
                </div>
            </div>
        </Dialog>
    );
}