import { useEffect } from "react";

function GoogleSignInButton({ handleCredentialResponse }) {
  useEffect(() => {
    const initializeGoogleSignIn = () => {
      if (window.google) {
        window.google.accounts.id.initialize({
          client_id: `${process.env.REACT_APP_GOOGLE_CLIENT_ID}`,
          callback: handleCredentialResponse,
          auto_select: true, // Enable auto-select for One Tap
        });
        window.google.accounts.id.renderButton(
          document.getElementById("signInDiv"),
          {
            theme: "dark",
            size: "large",
            longtitle: true,
            logoAlignment: "left",
          }
        );
        
      } else {
        window.onload = initializeGoogleSignIn;
      }
    };

    initializeGoogleSignIn();

    // Cleanup function
    return () => {
      if (window.google) {
        window.google.accounts.id.cancel();
      }
    };
  }, [handleCredentialResponse]);

  return (
    <div
      id="signInDiv"
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        colorScheme: "light",
        width: "100%",
      }}
    ></div>
  );
}

export default GoogleSignInButton;