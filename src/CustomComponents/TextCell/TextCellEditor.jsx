import React, { useState, useEffect, useContext } from "react";
import { Dialog, DialogBody, DialogFooter, Button, Intent, Classes } from "@blueprintjs/core";
import { AppContext } from "../../AppContextProvider";

export const TextCellEditor = (props) => {
  const [value, setValue] = useState(props.value || "");
  const [initialValue] = useState(props.value || "");
  const [isDialogOpen, setIsDialogOpen] = useState(true);
  const { isDarkTheme } = useContext(AppContext);

  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  const handleChange = (event) => {
    setValue(event.target.value);
  };

  const handleSave = () => {
    if (value !== initialValue) {
      props.api.stopEditing();
      props.node.setDataValue(props.column.colId, value);
    }
    setIsDialogOpen(false);
  };

  const handleClose = () => {
    props.api.stopEditing(true);
    setIsDialogOpen(false);
  };

  return (
    <Dialog
      className={isDarkTheme ? "bp5-dark" : ""}
      usePortal={true}
      isOpen={isDialogOpen}
      onClose={handleClose}
      enforceFocus={true}
      canEscapeKeyClose={false}
      canOutsideClickClose={false}
    >
      <div className={`${Classes.DIALOG_HEADER} handle`}>
        <h5 className={Classes.HEADING}>Edit Text</h5>
        <Button icon="cross" minimal onClick={handleClose} />
      </div>
      <DialogBody>
        <textarea
          className={Classes.INPUT}
          style={{ width: "100%", height: "200px" }}
          value={value}
          onChange={handleChange}
          autoFocus
        />
      </DialogBody>
      <DialogFooter actions={
        <>
          <Button onClick={handleClose} intent="danger">Cancel</Button>
          <Button intent={Intent.PRIMARY} onClick={handleSave}>
            Save
          </Button>
        </>
      }/>
    </Dialog>
  );
};
