import React, { useState, useEffect, useCallback } from "react";
import { GlobalWorkerOptions } from "pdfjs-dist";
import { pdfjs } from "react-pdf";
import {
  Root,
  Pages,
  Page,
  CanvasLayer,
  TextLayer,
  Thumbnail,
  Thumbnails,
  ZoomIn,
  ZoomOut,
  CurrentZoom,
  CurrentPage,
  TotalPages,
  NextPage,
  PreviousPage
} from "@anaralabs/lector";
import { 
  Navbar, 
  NavbarGroup, 
  NavbarDivider, 
  Button, 
  ButtonGroup, 
  Spinner, 
  NonIdealState 
} from "@blueprintjs/core";
import axios from "axios";

// Set the worker source using unpkg CDN with the exact version
GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

const LectorViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  // State
  const [pdfUrl, setPdfUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showThumbnails, setShowThumbnails] = useState(true);

  // Fetch PDF if fileId is provided
  useEffect(() => {
    // If filePath is provided directly, use it and skip fetch
    if (filePath) {
      console.log(`Using direct filePath: ${filePath}`);
      setPdfUrl(filePath);
      setLoading(false);
      return;
    }

    // If fileId is provided, fetch from API
    if (fileId) {
      const fetchPdf = async () => {
        setLoading(true);
        setError(null);
        
        try {
          console.log(`Fetching PDF for fileId: ${fileId}`);
          const sessionId = localStorage.getItem("session_id");
          console.log(`Using session-id: ${sessionId ? '(exists)' : '(missing)'}`);
          
          const response = await axios.get(
            `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
            {
              headers: {
                "session-id": sessionId,
              },
              responseType: "blob",
            }
          );

          console.log(`Fetch response status: ${response.status}`);
          console.log(`Content-Type: ${response.headers['content-type']}`);
          console.log(`Blob size: ${response.data.size} bytes`);
          
          const url = URL.createObjectURL(response.data);
          console.log(`Created blob URL: ${url}`);
          setPdfUrl(url);
          setLoading(false);
        } catch (err) {
          console.error("Failed to fetch PDF:", err);
          const errorDetails = err.response ? 
            `Status: ${err.response.status}, ${err.response.statusText}` : 
            err.message || 'Unknown error';
          console.error(`Error details: ${errorDetails}`);
          setError(`Failed to load PDF: ${errorDetails}`);
          setLoading(false);
        }
      };
      
      fetchPdf();
    } else {
      setError("No PDF source provided");
      setLoading(false);
    }

    // Cleanup function to revoke object URL
    return () => {
      if (pdfUrl && pdfUrl.startsWith('blob:')) {
        URL.revokeObjectURL(pdfUrl);
      }
    };
  }, [fileId, filePath, pdfUrl]);

  // Lector callbacks
  const handleLoad = useCallback(() => {
    console.log("Lector processed PDF successfully");
    setLoading(false);
  }, []);

  const handleError = useCallback((err) => {
    console.error("Lector failed to load/render PDF:", err);
    setError(err.message || "Lector failed to render PDF");
    setLoading(false);
  }, []);

  // Styling
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa',
    color: isDarkTheme ? '#f5f8fa' : '#182026',
    ...style
  };

  const navbarStyle = {
    padding: '0 10px',
    backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  };

  const contentStyle = {
    display: 'grid',
    gridTemplateColumns: showThumbnails ? '180px 1fr' : '0px 1fr',
    flexGrow: 1,
    overflow: 'hidden',
    transition: 'grid-template-columns 0.3s ease'
  };

  const thumbnailsStyle = {
    borderRight: '1px solid #ccc',
    overflowY: 'auto',
    backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
    padding: '10px'
  };

  const pagesContainerStyle = {
    overflow: 'auto',
    height: '100%'
  };

  // If loading or error, show appropriate UI
  if (loading && !pdfUrl) {
    return (
      <div style={containerStyle}>
        <div style={{ display: "flex", alignItems: "center", justifyContent: "center", height: "100%", gap: '10px' }}>
          <Spinner size={50} /> <span>Loading PDF...</span>
        </div>
      </div>
    );
  }

  if (error && !pdfUrl) {
    return (
      <div style={containerStyle}>
        <NonIdealState
          icon="error"
          title="Error Loading PDF"
          description={error}
        />
      </div>
    );
  }

  return (
    <div style={containerStyle}>
      {pdfUrl ? (
        <Root
          source={pdfUrl}
          style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
          loader={
            <div style={{ display: "flex", alignItems: "center", justifyContent: "center", height: "100%", gap: '10px' }}>
              <Spinner size={30} /> <span>Processing PDF...</span>
            </div>
          }
          onLoad={handleLoad}
          onError={handleError}
        >
          {/* Control Bar */}
          <Navbar style={navbarStyle}>
            <NavbarGroup>
              <Button
                minimal
                small
                icon={showThumbnails ? "panel-table" : "th"}
                onClick={() => setShowThumbnails(!showThumbnails)}
                title={showThumbnails ? "Hide Thumbnails" : "Show Thumbnails"}
              />
              <NavbarDivider />
              
              <ButtonGroup minimal={true}>
                <PreviousPage>
                  <Button icon="chevron-left" small />
                </PreviousPage>
                
                <div style={{ display: 'flex', alignItems: 'center', margin: '0 10px' }}>
                  <span>Page</span>
                  <CurrentPage style={{ 
                    margin: '0 5px', 
                    padding: '2px 8px', 
                    border: '1px solid #ccc', 
                    borderRadius: '3px', 
                    minWidth: '30px', 
                    textAlign: 'center', 
                    backgroundColor: isDarkTheme ? '#202b33' : '#fff' 
                  }} />
                  <span>of</span>
                  <TotalPages style={{ margin: '0 5px' }} />
                </div>
                
                <NextPage>
                  <Button icon="chevron-right" small />
                </NextPage>
              </ButtonGroup>
              
              <NavbarDivider />
              
              <span>Zoom</span>
              <ButtonGroup minimal={true} style={{ marginLeft: '5px' }}>
                <ZoomOut>
                  <Button icon="zoom-out" small />
                </ZoomOut>
                <CurrentZoom>
                  {(props) => (
                    <span style={{ padding: '0 8px', minWidth: '50px', textAlign: 'center' }}>
                      {`${Math.round(props.scale * 100)}%`}
                    </span>
                  )}
                </CurrentZoom>
                <ZoomIn>
                  <Button icon="zoom-in" small />
                </ZoomIn>
              </ButtonGroup>
            </NavbarGroup>
          </Navbar>

          {/* Main Content */}
          <div style={contentStyle}>
            {/* Thumbnails Panel */}
            <div style={thumbnailsStyle}>
              {showThumbnails && (
                <Thumbnails>
                  {(props) => (
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '10px' }}>
                      {Array.from({ length: props.pagesCount || 0 }).map((_, index) => (
                        <Thumbnail
                          key={index}
                          pageIndex={index}
                          style={{
                            width: '120px',
                            border: '1px solid #aaa',
                            marginBottom: '10px',
                            cursor: 'pointer',
                            opacity: props.currentPage === index + 1 ? 1 : 0.7,
                            boxShadow: props.currentPage === index + 1 ? '0 0 5px blue' : 'none'
                          }}
                          onClick={() => props.scrollToPage(index + 1)}
                        />
                      ))}
                    </div>
                  )}
                </Thumbnails>
              )}
            </div>
            
            {/* PDF Viewer Panel */}
            <div style={pagesContainerStyle}>
              <Pages>
                <Page>
                  <CanvasLayer />
                  <TextLayer />
                </Page>
              </Pages>
            </div>
          </div>
        </Root>
      ) : (
        <NonIdealState
          icon="document"
          title="No PDF"
          description="No PDF source available."
        />
      )}
    </div>
  );
};

export default LectorViewer;
