/**
 * This script directly patches the react-pdf-highlighter component to bypass the version check
 * It's a more aggressive approach but should resolve the version mismatch error
 */

// Function to patch the PDFViewer constructor to skip version check
function patchPDFViewerConstructor() {
  console.log('Setting up direct patch for PDFViewer constructor...');
  
  // Wait for scripts to load
  setTimeout(() => {
    try {
      // Find the react-pdf-highlighter's PDF.js bundle
      const scripts = Array.from(document.querySelectorAll('script'));
      const pdfViewerScript = scripts.find(script => 
        script.src && script.src.includes('pdf_viewer.js')
      );
      
      if (pdfViewerScript) {
        console.log('Found PDF Viewer script:', pdfViewerScript.src);
        
        // Create a patch script that will run after the PDF viewer script loads
        const patchScript = document.createElement('script');
        patchScript.textContent = `
          (function() {
            // Wait for the PDFViewer constructor to be defined
            const checkInterval = setInterval(() => {
              if (window.pdfjsViewer && window.pdfjsViewer.PDFViewer) {
                clearInterval(checkInterval);
                console.log('Patching PDFViewer constructor...');
                
                // Save the original constructor
                const OriginalPDFViewer = window.pdfjsViewer.PDFViewer;
                
                // Replace with our patched version that skips version check
                window.pdfjsViewer.PDFViewer = function(...args) {
                  try {
                    // Skip version check by temporarily modifying the version check function
                    if (window.pdfjsLib && window.pdfjsLib._api) {
                      const originalVersionCheck = window.pdfjsLib._api.getVersions;
                      window.pdfjsLib._api.getVersions = function() {
                        return { version: '4.4.168', backendVersion: '4.4.168' };
                      };
                      
                      // Call the original constructor
                      const instance = new OriginalPDFViewer(...args);
                      
                      // Restore the original function
                      window.pdfjsLib._api.getVersions = originalVersionCheck;
                      
                      return instance;
                    } else {
                      // Fallback if we can't find the version check function
                      return new OriginalPDFViewer(...args);
                    }
                  } catch (err) {
                    console.error('Error in PDFViewer patch:', err);
                    return new OriginalPDFViewer(...args);
                  }
                };
                
                // Copy prototype and properties
                window.pdfjsViewer.PDFViewer.prototype = OriginalPDFViewer.prototype;
                Object.setPrototypeOf(window.pdfjsViewer.PDFViewer, OriginalPDFViewer);
                
                console.log('PDFViewer constructor successfully patched');
              }
            }, 100);
            
            // Timeout after 10 seconds
            setTimeout(() => clearInterval(checkInterval), 10000);
          })();
        `;
        
        // Add the patch script to the document
        document.head.appendChild(patchScript);
      } else {
        console.log('PDF Viewer script not found, trying alternative approach...');
        
        // Alternative approach: Patch the module directly
        const patchScript = document.createElement('script');
        patchScript.textContent = `
          (function() {
            // Override Error constructor to catch and fix the specific error
            const originalError = window.Error;
            window.Error = function(message, ...args) {
              if (typeof message === 'string' && message.includes('API version') && message.includes('does not match the Viewer version')) {
                console.log('Intercepted version mismatch error:', message);
                // Return a non-throwing error object that won't crash the application
                return {
                  name: 'Error',
                  message: 'Version mismatch intercepted and ignored',
                  toString: () => 'Version mismatch intercepted and ignored'
                };
              }
              return new originalError(message, ...args);
            };
            
            // Restore original Error after 10 seconds to avoid side effects
            setTimeout(() => {
              window.Error = originalError;
            }, 10000);
          })();
        `;
        
        // Add the patch script to the document
        document.head.appendChild(patchScript);
      }
    } catch (error) {
      console.error('Error setting up PDF.js patch:', error);
    }
  }, 500);
}

// Patch the react-pdf-highlighter's init method to skip version check
function patchPdfHighlighterInit() {
  console.log('Setting up direct patch for PdfHighlighter.init...');
  
  // Wait for the bundle to load
  setTimeout(() => {
    try {
      // Find all loaded modules
      const modules = Object.entries(window).filter(([key]) => 
        key.startsWith('__') && typeof window[key] === 'object' && window[key] !== null
      );
      
      // Look for the PdfHighlighter component
      for (const [key, module] of modules) {
        if (module.exports && 
            typeof module.exports === 'object' && 
            module.exports.PdfHighlighter) {
          
          console.log('Found PdfHighlighter module:', key);
          
          // Get the original prototype
          const proto = module.exports.PdfHighlighter.prototype;
          
          // Check if init method exists
          if (proto && proto.init) {
            console.log('Patching PdfHighlighter.init method...');
            
            // Save the original init method
            const originalInit = proto.init;
            
            // Replace with our patched version
            proto.init = function(...args) {
              try {
                // Try to patch pdfjsLib version before calling init
                if (window.pdfjsLib) {
                  console.log('Setting pdfjsLib version to 4.4.168');
                  window.pdfjsLib.version = '4.4.168';
                }
                
                // Call the original init method
                return originalInit.apply(this, args);
              } catch (error) {
                console.error('Error in patched init:', error);
                
                // If the error is about version mismatch, try to recover
                if (error.message && error.message.includes('API version') && 
                    error.message.includes('does not match the Viewer version')) {
                  
                  console.log('Caught version mismatch error, attempting recovery...');
                  
                  // Try to continue without crashing
                  this.viewer = { container: this.containerNode };
                  this.linkService = { setDocument: () => {}, navigateTo: () => {} };
                  
                  return null;
                }
                
                // Re-throw other errors
                throw error;
              }
            };
            
            console.log('PdfHighlighter.init successfully patched');
          }
        }
      }
    } catch (error) {
      console.error('Error patching PdfHighlighter.init:', error);
    }
  }, 1000);
}

// Export the patch functions
export function applyDirectPatches() {
  console.log('Applying direct patches to fix PDF.js version mismatch...');
  patchPDFViewerConstructor();
  patchPdfHighlighterInit();
}

// Apply patches immediately when this module is imported
applyDirectPatches();
