import { useEffect, useRef } from "react";

export const useCopyPaste = (gridApi, gridContainerRef) => {
  const gridApiRef = useRef(gridApi);

  useEffect(() => {
    gridApiRef.current = gridApi;
  }, [gridApi]);

  const handleCopy = (event) => {
    if (gridApiRef.current && (event.metaKey || event.ctrlKey) && event.key === 'c') {
      const focusedCell = gridApiRef.current.getFocusedCell();
      if (focusedCell) {
        const { rowIndex, column } = focusedCell;
        const rowNode = gridApiRef.current.getDisplayedRowAtIndex(rowIndex);
        const field = column.colId;
        const valueToCopy = rowNode.data[field];
        navigator.clipboard.writeText(valueToCopy).catch(() => {
          // Fallback for browsers where navigator.clipboard is not supported
          const textarea = document.createElement('textarea');
          textarea.value = valueToCopy;
          document.body.appendChild(textarea);
          textarea.select();
          document.execCommand('copy');
          document.body.removeChild(textarea);
        });
      }
    }
  };

  const handlePaste = async (event) => {
    // Check if the target is one of the ag-grid floating filter inputs
    if (event.target.classList.contains("ag-input-field-input") || event.target.classList.contains("ag-text-field-input")) {
      // Allow default paste action for ag-grid floating filter inputs
      return;
    }

    if (gridApiRef.current && (event.metaKey || event.ctrlKey) && event.key === 'v') {
      event.preventDefault(); // Prevent default paste action
      let clipboardData;
      try {
        clipboardData = await navigator.clipboard.readText();
      } catch {
        clipboardData = prompt('Paste the content here:');
      }

      const focusedCell = gridApiRef.current.getFocusedCell();
      if (focusedCell) {
        const { rowIndex, column } = focusedCell;
        if (column && column.colDef && column.colDef.editable) {
          const field = column.colId;
          const rowNode = gridApiRef.current.getDisplayedRowAtIndex(rowIndex);
          if (rowNode) {
            // Start editing the cell
            gridApiRef.current.startEditingCell({
              rowIndex: rowIndex,
              colKey: field
            });

            // Set the value
            rowNode.setDataValue(field, clipboardData);

            // Stop editing and (optionally) pass 'true' to indicate that the edit is complete
            gridApiRef.current.stopEditing(true);

            gridApiRef.current.refreshCells({ force: true }); // Force refresh if necessary
          }
        }
      }
    }
  };

  const handleKeydown = (event) => {
    handleCopy(event);
    handlePaste(event);
  };

  useEffect(() => {
    const gridElement = gridContainerRef.current;
    if (gridElement) {
      gridElement.addEventListener('keydown', handleKeydown);

      return () => {
        gridElement.removeEventListener('keydown', handleKeydown);
      };
    }
  }, [gridContainerRef]);
};
