// useUpdateCell.js
import makeApiCallWithRetry from '../DataCollectionApp/Helpers/apiHelper';
import { AppToaster } from '../CustomComponents/AppToaster';
import { useSelector } from "react-redux";
import { useState } from "react";

let startingRowDict = {"Previous Inspections":3};

export const useUpdateCell = () => {
  const { user } = useSelector(state => state.auth);
  const [isLoading, setIsLoading] = useState(false);

  const updateCell = async (property, value, singleRowIndex = null, template = null) => {
    console.log("value to update", property, value);
    if (value === null) {
      value = "";
    }

    let correspodingRow = singleRowIndex + startingRowDict[template];
    const url = `${process.env.REACT_APP_DATA_API}/data/update_cell/`;
    const options = {
      method: "PUT",
      params: {
        indexrows: correspodingRow,
        template: template,
        header: property,
        new_value: value,
        user: user.email,
      },
    };

    try {
      setIsLoading(true);
      const response = await makeApiCallWithRetry(url, options);
      console.log("API response:", response.data);
      AppToaster.show({
        message: `${property} successfully updated with value: ${value} at ${template}`,
        intent: "success",
        timeout: 6000,
        icon: "confirm",
      });
      return true;
    } catch (error) {
      console.error("Error updating cell:", error);
      AppToaster.show({
        message: `Error updating ${property} at ${template}. Please retype/reselect the value and hit enter.`,
        intent: "danger",
        timeout: 6000,
        icon: "warning-sign",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return { updateCell, isLoading };
};