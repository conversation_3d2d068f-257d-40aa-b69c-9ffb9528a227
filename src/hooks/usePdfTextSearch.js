import { useState, useEffect } from "react";
import { pdfjs } from "react-pdf";

export const usePdfTextSearch = (file, searchString) => {
  const [pagesText, setPagesText] = useState([]); // Store text content per page
  const [resultsList, setResultsList] = useState([]); // Store page numbers with matches
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Effect to extract text from all pages when the file changes
  useEffect(() => {
    if (!file) {
      setPagesText([]);
      setResultsList([]);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);
    setPagesText([]); // Clear previous text
    setResultsList([]); // Clear previous results

    pdfjs.getDocument(file).promise.then((docData) => {
      const pageCount = docData.numPages; // Use numPages directly

      const pagePromises = Array.from(
        { length: pageCount },
        (_, pageNumber) => {
          return docData.getPage(pageNumber + 1).then((pageData) => {
            return pageData.getTextContent().then((textContent) => {
              // Simple join for now, might need refinement for better multi-line matching context
              return textContent.items.map(({ str }) => str).join(" ");
            });
          });
        }
      );

      return Promise.all(pagePromises).then((pagesContent) => {
        setPagesText(pagesContent);
        setLoading(false);
      }).catch(err => {
        console.error("Error extracting text content:", err);
        setError("Failed to extract text from PDF.");
        setLoading(false);
      });
    }).catch(err => {
       console.error("Error loading PDF document for search:", err);
       setError("Failed to load PDF for search.");
       setLoading(false);
    });

  }, [file]); // Rerun only when the file changes

  // Effect to perform search when pagesText or searchString changes
  useEffect(() => {
    if (!searchString || !searchString.trim() || pagesText.length === 0) {
      setResultsList([]);
      return; // Exit if no search string, or no text extracted
    }

    // Escape regex special characters in search string
    const escapedSearchString = searchString.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // Simple case-insensitive regex. Note: The original hook had '*' which might be unintended.
    // Using the escaped string directly for exact (case-insensitive) match.
    const regex = new RegExp(escapedSearchString, "i");
    const updatedResults = [];

    pagesText.forEach((text, index) => {
      if (regex.test(text)) {
        updatedResults.push(index + 1); // Store 1-based page number
      }
    });

    setResultsList(updatedResults);
  }, [pagesText, searchString]); // Rerun when text or search string changes

  // Return the list of page numbers containing matches, loading state, and error state
  return { searchPageResults: resultsList, searchLoading: loading, searchError: error };
};
