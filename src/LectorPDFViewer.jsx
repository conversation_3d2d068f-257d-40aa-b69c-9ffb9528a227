import React, { useState, useCallback, useEffect } from "react";
import {
  Root,
  <PERSON>s,
  Page,
  CanvasLayer,
  TextLayer,
  AnnotationLayer,
  ZoomIn,
  ZoomOut,
  CurrentZoom,
  CurrentPage,
  TotalPages,
  NextPage,
  PreviousPage,
  Thumbnails,
  Thumbnail
} from "@anaralabs/lector";
import {
  Navbar,
  NavbarGroup,
  NavbarDivider,
  Button,
  ButtonGroup,
  Spinner,
  NonIdealState
} from "@blueprintjs/core";

// Import PDF.js directly to set up the worker
import * as pdfjsLib from 'pdfjs-dist/build/pdf';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';

// Set up the worker directly
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;
console.log("PDF.js worker set up with version:", pdfjsLib.version);

const LectorPDFViewer = ({ filePath, fileId, isDarkTheme, style }) => {
  // State
  const [pdfData, setPdfData] = useState(null);
  const [sourceProp, setSourceProp] = useState(filePath ? filePath : null);
  const [isLoading, setIsLoading] = useState(!filePath);
  const [lectorIsLoading, setLectorIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showThumbnails, setShowThumbnails] = useState(true);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [fetchAttempt, setFetchAttempt] = useState(0);

  // Fetch PDF if fileId is provided
  useEffect(() => {
    // If filePath is provided directly, use it and skip fetch
    if (filePath) {
      console.log(`Using direct filePath: ${filePath}`);
      setSourceProp(filePath);
      setIsLoading(false);
      return;
    }

    // If fileId is provided, fetch from API
    if (fileId) {
      const fetchPdf = async () => {
        setIsLoading(true);
        setError(null);
        setPdfData(null);
        setSourceProp(null);

        try {
          console.log(`Fetching PDF for fileId: ${fileId}`);
          const response = await fetch(
            `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
            {
              headers: {
                "session-id": localStorage.getItem("session_id"),
              },
            }
          );

          console.log(`Fetch response status: ${response.status}, Content-Type: ${response.headers.get('Content-Type')}`);

          if (!response.ok) {
            let errorText = response.statusText;
            try {
              errorText = await response.text();
              console.error("API Error Response Text:", errorText);
            } catch (textError) {
              console.warn("Could not read error response body as text.");
            }
            throw new Error(`HTTP error ${response.status}: ${errorText}`);
          }

          // Check if the content type is actually PDF
          const contentType = response.headers.get('Content-Type');
          if (!contentType || !contentType.includes('application/pdf')) {
            console.warn(`Expected Content-Type 'application/pdf' but got '${contentType}'. Attempting to process anyway.`);
          }

          // Get the response as a blob directly
          const blob = await response.blob();
          console.log(`Fetched PDF data size: ${blob.size} bytes`);

          // Store the blob for use with Lector
          setPdfData(blob);

          // Set the source to the blob directly - Lector can handle blobs
          setSourceProp(blob);
          console.log("PDF blob created successfully");
        } catch (err) {
          console.error("Failed to load PDF with first approach:", err);

          // If this is the first attempt, try the fallback approach
          if (fetchAttempt === 0) {
            console.log("Trying fallback approach with fetch reader...");
            setFetchAttempt(1);

            try {
              // Reset progress
              setDownloadProgress(0);

              // Use the fetch API with a reader to track progress
              const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
                headers: {
                  'Accept-Encoding': 'gzip, deflate, br',
                  'session-id': localStorage.getItem('session_id'),
                },
              });

              if (!response.ok) {
                throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
              }

              // Get content length for progress tracking
              const contentLength = response.headers.get('Content-Length');
              const reader = response.body.getReader();
              let receivedLength = 0;
              const chunks = [];

              // Read the response in chunks to track progress
              while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                chunks.push(value);
                receivedLength += value.length;

                // Update progress if content length is available
                if (contentLength) {
                  setDownloadProgress(receivedLength / parseInt(contentLength, 10));
                }
              }

              // Create a blob from all chunks with the correct MIME type
              const blob = new Blob(chunks, { type: 'application/pdf' });
              console.log(`Created PDF blob (fallback): ${blob.size} bytes, type: ${blob.type}`);

              // Store the blob for use with Lector
              setPdfData(blob);

              // Set the source to the blob directly - Lector can handle blobs
              setSourceProp(blob);
              console.log("PDF blob created successfully (fallback)");

              // Success with fallback approach
              console.log("Fallback approach successful");
            } catch (fallbackErr) {
              console.error("Fallback approach also failed:", fallbackErr);
              setError(`Failed to load PDF: ${err.message}. Fallback also failed: ${fallbackErr.message}`);
            }
          } else {
            // Both approaches failed
            setError(err.message || "Failed to fetch PDF data");
          }
        } finally {
          setIsLoading(false);
        }
      };
      fetchPdf();
    } else {
      // No filePath or fileId provided
      setError("No PDF source provided");
      setIsLoading(false);
    }
  }, [filePath, fileId]);

  // Lector callbacks
  const handleLoad = useCallback(() => {
    console.log("Lector processed PDF successfully");
    setLectorIsLoading(false);
  }, []);

  const handleError = useCallback((err) => {
    console.error("Lector failed to load/render PDF:", err);
    if (!error) {
      setError(err.message || "Lector failed to render PDF");
    }
    setLectorIsLoading(false);
  }, [error]);

  // Combined loading state
  const combinedLoading = isLoading || (sourceProp && lectorIsLoading);

  // Clean up blob URL when component unmounts or source changes
  useEffect(() => {
    return () => {
      // Only revoke if it's a blob URL (starts with 'blob:')
      if (typeof sourceProp === 'string' && sourceProp.startsWith('blob:')) {
        console.log(`Revoking blob URL: ${sourceProp}`);
        URL.revokeObjectURL(sourceProp);
      }
    };
  }, [sourceProp]);

  // Styling
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa',
    color: isDarkTheme ? '#f5f8fa' : '#182026',
    ...style
  };

  const navbarStyle = {
    padding: '0 10px',
    backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  };

  const contentStyle = {
    display: 'grid',
    gridTemplateColumns: showThumbnails ? '180px 1fr' : '0px 1fr',
    flexGrow: 1,
    overflow: 'hidden',
    transition: 'grid-template-columns 0.3s ease'
  };

  const thumbnailsStyle = {
    borderRight: '1px solid #ccc',
    overflowY: 'auto',
    backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
    padding: '10px'
  };

  const pagesContainerStyle = {
    overflow: 'auto',
    height: '100%'
  };

  // Main content render
  let mainContent;
  if (combinedLoading) {
    mainContent = (
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", height: "100%", gridColumn: '1 / -1' }}>
        <Spinner size={50} />
        <span style={{ marginTop: '10px' }}>Loading PDF...</span>
        {downloadProgress > 0 && downloadProgress < 1 && (
          <div style={{ width: '300px', marginTop: '20px' }}>
            <div style={{
              height: '6px',
              width: `${Math.round(downloadProgress * 100)}%`,
              backgroundColor: isDarkTheme ? '#48AFF0' : '#2B95D6',
              borderRadius: '3px',
              transition: 'width 0.3s ease'
            }} />
            <div style={{ textAlign: 'center', marginTop: '5px', fontSize: '12px' }}>
              {Math.round(downloadProgress * 100)}%
            </div>
          </div>
        )}
      </div>
    );
  } else if (error) {
    mainContent = (
      <div style={{ gridColumn: '1 / -1', height: '100%' }}>
        <NonIdealState
          icon="error"
          title="Error Loading PDF"
          description={error}
        />
      </div>
    );
  } else if (sourceProp) {
    mainContent = (
      <>
        {/* Thumbnails Panel */}
        <div style={thumbnailsStyle}>
          {showThumbnails && (
            <Thumbnails>
              {(props) => (
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '10px' }}>
                  {Array.from({ length: props.pagesCount || 0 }).map((_, index) => (
                    <Thumbnail
                      key={index}
                      pageIndex={index}
                      style={{
                        width: '120px',
                        border: '1px solid #aaa',
                        marginBottom: '10px',
                        cursor: 'pointer',
                        opacity: props.currentPage === index + 1 ? 1 : 0.7,
                        boxShadow: props.currentPage === index + 1 ? '0 0 5px blue' : 'none'
                      }}
                      onClick={() => props.scrollToPage(index + 1)}
                    />
                  ))}
                </div>
              )}
            </Thumbnails>
          )}
        </div>
        {/* PDF Viewer Panel */}
        <div style={pagesContainerStyle}>
          <Pages>
            <Page>
              <CanvasLayer />
              <TextLayer />
              <AnnotationLayer />
            </Page>
          </Pages>
        </div>
      </>
    );
  } else {
    mainContent = (
      <div style={{ gridColumn: '1 / -1', height: '100%' }}>
        <NonIdealState icon="document" title="No PDF" description="No PDF source available." />
      </div>
    );
  }

  return (
    <Root
      key={fileId || filePath}
      source={sourceProp}
      style={containerStyle}
      loader={null}
      onLoad={handleLoad}
      onError={handleError}
      pdfLib={pdfjsLib}
    >
      {/* Blueprint.js Navbar with Controls */}
      <Navbar style={navbarStyle}>
        <NavbarGroup>
          <Button
            minimal
            icon={showThumbnails ? "panel-table" : "th"}
            onClick={() => setShowThumbnails(!showThumbnails)}
            title={showThumbnails ? "Hide Thumbnails" : "Show Thumbnails"}
            disabled={combinedLoading || !!error}
          />
          <NavbarDivider />

          <ButtonGroup minimal={true}>
            <PreviousPage>
              <Button icon="chevron-left" disabled={combinedLoading || !!error} />
            </PreviousPage>

            <div style={{ display: 'flex', alignItems: 'center', margin: '0 10px' }}>
              <span>Page</span>
              <CurrentPage style={{
                margin: '0 5px',
                padding: '2px 8px',
                border: '1px solid #ccc',
                borderRadius: '3px',
                minWidth: '30px',
                textAlign: 'center',
                backgroundColor: isDarkTheme ? '#202b33' : '#fff'
              }} />
              <span>of</span>
              <TotalPages style={{ margin: '0 5px' }} />
            </div>

            <NextPage>
              <Button icon="chevron-right" disabled={combinedLoading || !!error} />
            </NextPage>
          </ButtonGroup>

          <NavbarDivider />

          <span>Zoom</span>
          <ButtonGroup minimal={true} style={{ marginLeft: '5px' }}>
            <ZoomOut disabled={combinedLoading || !!error}>
              <Button icon="zoom-out" />
            </ZoomOut>
            <CurrentZoom>
              {(props) => (
                <span style={{ padding: '0 8px', minWidth: '50px', textAlign: 'center' }}>
                  {!combinedLoading && !error ? `${Math.round(props.scale * 100)}%` : '---'}
                </span>
              )}
            </CurrentZoom>
            <ZoomIn disabled={combinedLoading || !!error}>
              <Button icon="zoom-in" />
            </ZoomIn>
          </ButtonGroup>
        </NavbarGroup>
      </Navbar>

      {/* Main Content Area */}
      <div style={contentStyle}>
        {mainContent}
      </div>
    </Root>
  );
};

export default LectorPDFViewer;
