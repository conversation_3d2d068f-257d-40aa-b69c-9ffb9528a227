import React, { useState, useEffect } from 'react';
import { 
  R<PERSON><PERSON><PERSON>, 
  RPDefaultLayout, 
  RPPages, 
  RPTheme,
  useDarkModeContext,
  useRotationContext,
  useViewModeContext,
  useZoomContext,
  ViewMode
} from '@pdf-viewer/react';
import { Spinner, SpinnerSize, Button, ButtonGroup } from "@blueprintjs/core";
import { useDispatch } from 'react-redux';
import { setNumPages, setPageNumber, setRotation, setScale } from './redux/pdfViewerSlice';

/**
 * DarkModeHandler component
 * This component synchronizes the PDF viewer's dark mode with the app's isDarkTheme prop
 */
const DarkModeHandler = ({ isDarkTheme }) => {
  const { darkMode, setDarkMode } = useDarkModeContext();
  
  // Set dark mode on initial render and when isDarkTheme changes
  useEffect(() => {
    console.log('Setting dark mode to:', isDarkTheme, 'Current dark mode:', darkMode);
    
    // Only update if the values are different
    if (darkMode !== isDarkTheme) {
      setDarkMode(isDarkTheme);
    }
  }, [isDarkTheme, setDarkMode, darkMode]);
  
  return null;
};

/**
 * Custom Rotation Tool component
 */
const CustomRotationTool = () => {
  const dispatch = useDispatch();
  const { rotate, setRotate } = useRotationContext();
  
  const rotateClockwise = () => {
    console.log('Rotating clockwise, current rotation:', rotate);
    const newRotation = (rotate + 90) % 360;
    setRotate(newRotation);
    dispatch(setRotation(newRotation));
  };
  
  const rotateCounterClockwise = () => {
    console.log('Rotating counterclockwise, current rotation:', rotate);
    const newRotation = (rotate - 90 + 360) % 360;
    setRotate(newRotation);
    dispatch(setRotation(newRotation));
  };
  
  return (
    <ButtonGroup minimal={true}>
      <Button
        icon={
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M1 4v6h6M23 20v-6h-6M20.49 9a9 9 0 00-14.85-3.36L1 10M23 14l-4.64 4.36A9 9 0 013.51 15" />
          </svg>
        }
        onClick={rotateCounterClockwise}
        title="Rotate counterclockwise"
      />
      <Button
        icon={
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M23 4v6h-6M1 20v-6h6M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15" />
          </svg>
        }
        onClick={rotateClockwise}
        title="Rotate clockwise"
      />
    </ButtonGroup>
  );
};

/**
 * Custom View Mode Tool component
 */
const CustomViewModeTool = () => {
  const { currentMode, switchTo } = useViewModeContext();
  
  const setHandTool = () => {
    console.log('Switching to hand tool mode');
    switchTo(ViewMode.HandTool);
  };
  
  const setTextSelectionTool = () => {
    console.log('Switching to text selection mode');
    switchTo(ViewMode.TextSelection);
  };
  
  const isHandToolActive = currentMode === ViewMode.HandTool;
  
  return (
    <ButtonGroup minimal={true}>
      <Button
        icon={
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M18 11V6a2 2 0 00-2-2v0a2 2 0 00-2 2v2 M14 10V4a2 2 0 00-2-2v0a2 2 0 00-2 2v14 M6 16v0a2 2 0 00-2-2v0a2 2 0 01-2-2v-2 M6 22h12" />
          </svg>
        }
        active={isHandToolActive}
        onClick={setHandTool}
        title="Hand tool"
      />
      <Button
        icon={
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="M9 15L3 9m0 0l6-6M3 9h18" />
          </svg>
        }
        active={!isHandToolActive}
        onClick={setTextSelectionTool}
        title="Text selection tool"
      />
    </ButtonGroup>
  );
};

/**
 * Custom Zoom Tool component
 */
const CustomZoomTool = () => {
  const dispatch = useDispatch();
  const { zoom, setZoom } = useZoomContext();
  
  // Store current page number to prevent page changes during zoom
  const [currentPage, setCurrentPage] = useState(null);
  
  // Effect to handle zoom operations and prevent page changes
  useEffect(() => {
    if (currentPage !== null && typeof window !== 'undefined') {
      // Use a timeout to ensure we're executing after the zoom operation
      const timer = setTimeout(() => {
        if (window.PDFViewerApplication && window.PDFViewerApplication.page !== currentPage) {
          console.log(`Restoring page number after zoom: ${currentPage}`);
          window.PDFViewerApplication.page = currentPage;
        }
      }, 100);
      
      return () => clearTimeout(timer);
    }
  }, [zoom, currentPage]);
  
  const zoomIn = () => {
    // Store current page before zooming
    if (window.PDFViewerApplication) {
      setCurrentPage(window.PDFViewerApplication.page);
    }
    
    console.log('Zooming in, current zoom:', zoom);
    const newZoom = Math.min(zoom + 0.1, 3);
    setZoom(newZoom);
    dispatch(setScale(newZoom));
  };
  
  const zoomOut = () => {
    // Store current page before zooming
    if (window.PDFViewerApplication) {
      setCurrentPage(window.PDFViewerApplication.page);
    }
    
    console.log('Zooming out, current zoom:', zoom);
    const newZoom = Math.max(zoom - 0.1, 0.1);
    setZoom(newZoom);
    dispatch(setScale(newZoom));
  };
  
  return (
    <ButtonGroup minimal={true}>
      <Button
        icon={
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            <line x1="11" y1="8" x2="11" y2="14"></line>
            <line x1="8" y1="11" x2="14" y2="11"></line>
          </svg>
        }
        onClick={zoomIn}
        title="Zoom in"
      />
      <Button
        icon={
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            <line x1="8" y1="11" x2="14" y2="11"></line>
          </svg>
        }
        onClick={zoomOut}
        title="Zoom out"
      />
    </ButtonGroup>
  );
};

/**
 * Basic PDF Viewer component with progressive enhancements
 * @param {Object} props - Component props
 * @param {string} props.src - URL of the PDF to display (default is the web-roadmap.pdf)
 * @param {boolean} props.isDarkTheme - Whether dark theme is enabled
 * @param {boolean} props.showCustomTools - Whether to show custom tools
 * @returns {JSX.Element} - Rendered component
 */
const AppPdfViewer = ({ 
  src = "https://cdn.codewithmosh.com/image/upload/v1721763853/guides/web-roadmap.pdf", 
  isDarkTheme = false,
  showCustomTools = true
}) => {
  const dispatch = useDispatch();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pdfSrc, setPdfSrc] = useState(src);
  
  // Define Blueprint 5 light theme colors
  const bp5LightColors = {
    backgroundColor: '#FFFFFF',
    textColor: '#182026',
    secondaryTextColor: '#5C7080',
    borderColor: '#BFCCD6',
    hoverBackgroundColor: '#F5F8FA',
    hoverTextColor: '#182026',
    activeBackgroundColor: '#E1E8ED',
    activeTextColor: '#182026',
    disabledBackgroundColor: '#F5F8FA',
    disabledTextColor: '#BFCCD6'
  };
  
  // Define Blueprint 5 dark theme colors
  const bp5DarkColors = {
    backgroundColor: '#1C2127',
    textColor: '#F5F8FA',
    secondaryTextColor: '#BFCCD6',
    borderColor: '#394B59',
    hoverBackgroundColor: '#252A31',
    hoverTextColor: '#F5F8FA',
    activeBackgroundColor: '#394B59',
    activeTextColor: '#F5F8FA',
    disabledBackgroundColor: '#252A31',
    disabledTextColor: '#394B59'
  };

  // Effect to handle source changes
  useEffect(() => {
    const loadPdf = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        if (!src) {
          setError('No PDF URL provided');
          setIsLoading(false);
          return;
        }
        
        console.log('Setting PDF source:', src);
        
        // If the URL is a local file path, get a blob URL
        if (typeof src === 'string' && src.startsWith('/')) {
          try {
            const response = await fetch(src);
            const blob = await response.blob();
            const fileUrl = URL.createObjectURL(blob);
            setPdfSrc(fileUrl);
          } catch (err) {
            console.error('Error creating blob URL:', err);
            setError(`Error loading PDF: ${err.message}`);
            setIsLoading(false);
          }
        } else {
          // Otherwise use the URL directly
          setPdfSrc(src);
        }
      } catch (err) {
        console.error('Error in loadPdf:', err);
        setError(`Error loading PDF: ${err.message}`);
        setIsLoading(false);
      }
    };
    
    loadPdf();
    
    // Cleanup function
    return () => {
      // Revoke any blob URLs to prevent memory leaks
      if (typeof pdfSrc === 'string' && pdfSrc.startsWith('blob:')) {
        URL.revokeObjectURL(pdfSrc);
      }
    };
  }, [src, pdfSrc]);

  /**
   * Handle viewer loaded event
   */
  const handleLoaded = (doc) => {
    console.log('PDF viewer loaded', doc);
    setIsLoading(false);
    
    // Update Redux with number of pages
    if (doc && doc.numPages) {
      dispatch(setNumPages(doc.numPages));
    }
  };
  
  /**
   * Handle loading error
   */
  const handleError = (err) => {
    console.error('Error loading PDF in viewer:', err);
    setError(`Error loading PDF: ${err.message || 'Unknown error'}`);
    setIsLoading(false);
  };
  
  /**
   * Handle page change event
   */
  const handlePageChange = (pageNumber) => {
    console.log('Page changed to:', pageNumber);
    dispatch(setPageNumber(pageNumber));
  };

  // Loading spinner component
  const LoadingSpinner = () => (
    <div 
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: isDarkTheme ? '#1C2127' : '#FFFFFF',
        zIndex: 10
      }}
    >
      <Spinner 
        size={SpinnerSize.LARGE} 
        intent="primary"
      />
    </div>
  );

  // Error component
  const ErrorDisplay = () => (
    <div 
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: isDarkTheme ? '#1C2127' : '#FFFFFF',
        color: isDarkTheme ? '#F5F8FA' : '#182026',
        zIndex: 10,
        padding: '20px',
        textAlign: 'center'
      }}
    >
      <div>
        <h3>Error Loading PDF</h3>
        <p>{error}</p>
      </div>
    </div>
  );

  // Basic version (similar to the example provided)
  if (!showCustomTools) {
    return (
      <div style={{ position: 'relative', height: '660px', width: '100%' }}>
        {isLoading && <LoadingSpinner />}
        {error && <ErrorDisplay />}
        {!error && pdfSrc && (
          <RPProvider 
            src={pdfSrc} 
            onLoad={handleLoaded}
            onError={handleError}
            onPageChange={handlePageChange}
            withCredentials={false}
          >
            <RPDefaultLayout style={{ height: '660px' }}>
              <RPPages />
            </RPDefaultLayout>
          </RPProvider>
        )}
      </div>
    );
  }
  
  // Enhanced version with custom tools and theme
  return (
    <div style={{ position: 'relative', height: '660px', width: '100%' }}>
      {isLoading && <LoadingSpinner />}
      {error && <ErrorDisplay />}
      {!error && pdfSrc && (
        <RPProvider 
          src={pdfSrc} 
          onLoad={handleLoaded}
          onError={handleError}
          onPageChange={handlePageChange}
          withCredentials={false}
        >
          <DarkModeHandler isDarkTheme={isDarkTheme} />
          <RPTheme
            customVariables={bp5LightColors}
            customDarkVariables={bp5DarkColors}
          >
            <RPDefaultLayout 
              style={{ height: '660px' }}
              slots={{
                downloadTool: false,
                printTool: false,
                openFileTool: false,
                themeSwitcher: false,
                rotateTool: () => <CustomRotationTool />,
                handTool: false,
                textSelectionTool: false,
                selectionModeTool: () => <CustomViewModeTool />,
                zoomTool: () => <CustomZoomTool />
              }}
            >
              <RPPages />
            </RPDefaultLayout>
          </RPTheme>
        </RPProvider>
      )}
    </div>
  );
};

export default AppPdfViewer;
