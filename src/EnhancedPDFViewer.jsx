import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import { Spinner, SpinnerSize, Button, ButtonGroup, InputGroup, Popover, Menu, MenuItem } from "@blueprintjs/core";
import "@blueprintjs/core/lib/css/blueprint.css";

import {
    updatePdfViewer,
    setViewerVisibility,
    setFocusedViewer,
    selectVisiblePDFs
} from './redux/currentPdfSlice';

/**
 * EnhancedPDFViewer - Enhanced PDF viewer with iframe-based PDF.js viewer
 * Based on PDFJsViewerBestMar5.jsx but with additional features like search highlighting
 * and annotation capabilities without version conflicts
 */

const EnhancedPDFViewer = ({ fileId, filePath, isDarkTheme }) => {
    const dispatch = useDispatch();
    const [pdfFile, setPdfFile] = useState(null);
    const [downloadProgress, setDownloadProgress] = useState(0);
    const [hasLoaded, setHasLoaded] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [showSearchPanel, setShowSearchPanel] = useState(false);
    const [showAnnotationPanel, setShowAnnotationPanel] = useState(false);
    const [annotations, setAnnotations] = useState([]);

    const iframeRef = useRef();
    const { ref, inView } = useInView({ threshold: 0.1 });

    const currentViewerState = useSelector(state => state.currentPdf.viewers[fileId]);
    const visiblePDFs = useSelector(selectVisiblePDFs);

    // Update visibility in Redux
    useEffect(() => {
        dispatch(setViewerVisibility({ fileId, isVisible: inView }));
        if (inView) {
            dispatch(setFocusedViewer(fileId));
        }
    }, [inView, fileId, dispatch]);

    // Fetch PDF file with progress tracking (same as PDFJsViewerBestMar5.jsx)
    useEffect(() => {
        async function fetchFile() {
            if (hasLoaded) return;

            setPdfFile(null);
            setDownloadProgress(0);

            try {
                const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
                    headers: {
                        'Accept-Encoding': 'gzip, deflate, br',
                        'session-id': localStorage.getItem('session_id'),
                    },
                });

                if (!response.ok) {
                    throw new Error("Network response was not ok");
                }

                const contentLength = response.headers.get('Content-Length');
                const reader = response.body.getReader();
                let receivedLength = 0;
                const chunks = [];

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    chunks.push(value);
                    receivedLength += value.length;

                    if (contentLength) {
                        setDownloadProgress(receivedLength / contentLength);
                    }
                }

                const blob = new Blob(chunks);
                const pdfUrl = URL.createObjectURL(blob);
                setPdfFile(pdfUrl);
                setHasLoaded(true);

                dispatch(updatePdfViewer({
                    fileId,
                    filePath,
                    page: 1,
                    isVisible: inView
                }));
            } catch (error) {
                console.error("Failed to fetch the PDF:", error);
            }
        }

        fetchFile();

        return () => {
            if (pdfFile && !hasLoaded) {
                URL.revokeObjectURL(pdfFile);
            }
        };
    }, [fileId, filePath, dispatch, hasLoaded, inView, pdfFile]);

    // Enhanced search functionality that works with PDF.js iframe
    const performSearch = useCallback((text) => {
        if (!text.trim() || !iframeRef.current) return;

        setSearchText(text);

        // Send search command to PDF.js iframe
        const iframeDocument = iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;
        if (iframeDocument) {
            const script = iframeDocument.createElement('script');
            script.textContent = `
                if (typeof PDFViewerApplication !== 'undefined') {
                    PDFViewerApplication.findController.executeCommand('find', {
                        query: "${text}",
                        phraseSearch: true,
                        caseSensitive: false,
                        entireWord: false,
                        highlightAll: true,
                        findPrevious: false
                    });
                }
            `;
            iframeDocument.head.appendChild(script);
        }
    }, []);

    // Navigate search results in PDF.js
    const navigateSearch = useCallback((direction) => {
        if (!searchText.trim() || !iframeRef.current) return;

        const iframeDocument = iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;
        if (iframeDocument) {
            const script = iframeDocument.createElement('script');
            script.textContent = `
                if (typeof PDFViewerApplication !== 'undefined') {
                    PDFViewerApplication.findController.executeCommand('find${direction === 'next' ? 'again' : 'previous'}', {
                        query: "${searchText}",
                        phraseSearch: true,
                        caseSensitive: false,
                        entireWord: false,
                        highlightAll: true,
                        findPrevious: ${direction === 'prev'}
                    });
                }
            `;
            iframeDocument.head.appendChild(script);
        }
    }, [searchText]);

    // Add annotation functionality
    const addAnnotation = useCallback((annotation) => {
        const newAnnotation = {
            ...annotation,
            id: `annotation-${Date.now()}`,
            createdAt: new Date().toISOString()
        };
        setAnnotations(prev => [...prev, newAnnotation]);
        return newAnnotation;
    }, []);

    // Delete annotation
    const deleteAnnotation = useCallback((annotationId) => {
        setAnnotations(prev => prev.filter(a => a.id !== annotationId));
    }, []);

    // Setup iframe event listeners and customizations
    const setupIframeCustomizations = useCallback((iframeDocument) => {
        // Set theme
        const setThemeScript = iframeDocument.createElement('script');
        setThemeScript.textContent = `
            function setTheme(theme) {
                if (typeof PDFViewerApplicationOptions !== 'undefined') {
                    PDFViewerApplicationOptions.set('viewerCssTheme', theme === 'Dark' ? 1 : 2);
                    if (typeof PDFViewerApplication !== 'undefined') {
                        PDFViewerApplication._forceCssTheme();
                    }
                }
            }

            if (document.readyState === 'complete') {
                setTheme('${isDarkTheme ? 'Dark' : 'Light'}');
            } else {
                document.addEventListener('DOMContentLoaded', () => {
                    setTheme('${isDarkTheme ? 'Dark' : 'Light'}');
                });
            }
        `;
        iframeDocument.head.appendChild(setThemeScript);

        // Add custom styles for better integration
        const customStyle = iframeDocument.createElement('style');
        customStyle.innerHTML = `
            #viewerContainer {
                overflow-y: scroll !important;
                padding: 40px 0 !important;
                position: absolute !important;
                top: 32px !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
            }
            #viewer {
                margin: 0 !important;
                padding: 0 !important;
                position: relative !important;
            }
            .pdfViewer .page {
                margin: 1px auto !important;
                border: none !important;
            }
            #toolbarContainer {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                height: 32px !important;
                z-index: 2 !important;
                background: var(--background-color) !important;
            }
            /* Hide some toolbar buttons we don't need */
            #secondaryToolbar,
            #sidebarToggle,
            #viewFind {
                display: none !important;
            }
        `;
        iframeDocument.head.appendChild(customStyle);

        // Setup page change tracking
        const pageTrackingScript = iframeDocument.createElement('script');
        pageTrackingScript.textContent = `
            function setupPageChangeTracking() {
                if (typeof PDFViewerApplication !== 'undefined') {
                    PDFViewerApplication.eventBus.on('pagechanging', function(evt) {
                        window.parent.postMessage({
                            type: 'pdfState',
                            action: 'pagechanging',
                            data: {
                                page: evt.pageNumber,
                                scale: PDFViewerApplication.pdfViewer.currentScale,
                                scrollTop: document.getElementById('viewerContainer').scrollTop
                            }
                        }, '*');
                    });
                }
            }

            if (typeof PDFViewerApplication !== 'undefined') {
                PDFViewerApplication.initializedPromise.then(setupPageChangeTracking);
            } else {
                document.addEventListener('webviewerloaded', function() {
                    PDFViewerApplication.initializedPromise.then(setupPageChangeTracking);
                });
            }
        `;
        iframeDocument.head.appendChild(pageTrackingScript);
    }, [isDarkTheme]);

    // Handle messages from iframe
    useEffect(() => {
        const handleMessage = (event) => {
            if (event.data.type === 'pdfState' && inView) {
                const { action, data } = event.data;

                if (action === 'pagechanging') {
                    dispatch(updatePdfViewer({
                        fileId,
                        page: data.page,
                        scale: data.scale,
                        scrollTop: data.scrollTop,
                        filePath,
                        isVisible: inView
                    }));
                }
            }
        };

        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, [dispatch, fileId, filePath, inView]);

    // Loading indicator
    if (!pdfFile) {
        return (
            <div className="spinner-container" style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%'
            }}>
                <Spinner
                    intent="primary"
                    size={SpinnerSize.STANDARD}
                    value={downloadProgress}
                />
                {downloadProgress > 0 && downloadProgress < 1 && (
                    <div style={{ width: '80%', marginTop: '20px' }}>
                        <div
                            style={{
                                height: '4px',
                                width: `${downloadProgress * 100}%`,
                                backgroundColor: isDarkTheme ? '#48AFF0' : '#2B95D6',
                                borderRadius: '2px'
                            }}
                        />
                        <div style={{ marginTop: '8px', textAlign: 'center' }}>
                            {Math.round(downloadProgress * 100)}%
                        </div>
                    </div>
                )}
                <div style={{ marginTop: '20px' }}>Loading PDF...</div>
            </div>
        );
    }

    return (
        <div
            ref={ref}
            id={`pdf-viewer-${fileId}`}
            style={{
                height: "100%",
                width: "100%",
                display: "flex",
                flexDirection: "column",
                backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa'
            }}
        >
            {/* Toolbar */}
            <div style={{
                padding: '8px',
                borderBottom: '1px solid #ccc',
                backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
                display: 'flex',
                alignItems: 'center',
                gap: '10px',
                flexWrap: 'wrap'
            }}>
                {/* Search */}
                <InputGroup
                    placeholder="Search in PDF..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && performSearch(searchText)}
                    style={{ width: '200px' }}
                    rightElement={
                        <ButtonGroup minimal>
                            <Button
                                icon="search"
                                onClick={() => performSearch(searchText)}
                                disabled={!searchText.trim()}
                            />
                            <Button
                                icon="chevron-up"
                                onClick={() => navigateSearch('prev')}
                                disabled={searchResults.length === 0}
                            />
                            <Button
                                icon="chevron-down"
                                onClick={() => navigateSearch('next')}
                                disabled={searchResults.length === 0}
                            />
                        </ButtonGroup>
                    }
                />

                {/* Search results counter */}
                {searchResults.length > 0 && (
                    <span style={{ fontSize: '12px', color: isDarkTheme ? '#f5f8fa' : '#182026' }}>
                        {currentSearchIndex + 1} of {searchResults.length}
                    </span>
                )}

                {/* Annotations toggle */}
                <Button
                    icon="annotation"
                    text="Annotations"
                    active={showAnnotationPanel}
                    onClick={() => setShowAnnotationPanel(!showAnnotationPanel)}
                />

                {/* Highlight colors */}
                <Popover
                    content={
                        <Menu>
                            {highlightColors.map(color => (
                                <MenuItem
                                    key={color.name}
                                    text={color.name}
                                    icon={
                                        <div style={{
                                            width: '12px',
                                            height: '12px',
                                            backgroundColor: color.value,
                                            borderRadius: '2px'
                                        }} />
                                    }
                                />
                            ))}
                        </Menu>
                    }
                    position="bottom"
                >
                    <Button icon="tint" text="Colors" />
                </Popover>
            </div>

            {/* Main content area */}
            <div style={{ display: 'flex', flex: 1, overflow: 'hidden' }}>
                {/* PDF Viewer */}
                <div style={{ flex: showAnnotationPanel ? '1' : '1', overflow: 'hidden' }}>
                    <iframe
                        ref={iframeRef}
                        title={`Enhanced PDF Viewer for ${filePath}`}
                        width="100%"
                        height="100%"
                        src={`${process.env.PUBLIC_URL}/pdfjs-3.11.174-dist/web/viewer.html?file=${encodeURIComponent(pdfFile)}`}
                        className={isDarkTheme ? "dark-theme" : "light-theme"}
                        style={{ border: "none" }}
                        onLoad={(event) => {
                            const iframeDocument = event.target.contentDocument || event.target.contentWindow.document;
                            setupIframeCustomizations(iframeDocument);

                            // Restore viewer state if available
                            if (currentViewerState) {
                                const script = iframeDocument.createElement('script');
                                script.textContent = `
                                    PDFViewerApplication.initializedPromise.then(() => {
                                        if (${currentViewerState.page}) {
                                            PDFViewerApplication.page = ${currentViewerState.page};
                                        }
                                        if (${currentViewerState.scale}) {
                                            PDFViewerApplication.pdfViewer.currentScale = ${currentViewerState.scale};
                                        }
                                        if (${currentViewerState.scrollTop}) {
                                            document.getElementById('viewerContainer').scrollTop = ${currentViewerState.scrollTop};
                                        }
                                    });
                                `;
                                iframeDocument.head.appendChild(script);
                            }
                        }}
                    />
                </div>

                {/* Annotations Panel */}
                {showAnnotationPanel && (
                    <div style={{
                        width: '300px',
                        borderLeft: '1px solid #ccc',
                        backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
                        padding: '10px',
                        overflow: 'auto'
                    }}>
                        <h4 style={{ margin: '0 0 10px 0', color: isDarkTheme ? '#f5f8fa' : '#182026' }}>
                            Annotations ({annotations.length})
                        </h4>

                        {/* Add annotation button */}
                        <Button
                            icon="plus"
                            text="Add Note"
                            fill
                            style={{ marginBottom: '10px' }}
                            onClick={() => {
                                const annotation = {
                                    text: 'New annotation',
                                    page: 1,
                                    type: 'note'
                                };
                                addAnnotation(annotation);
                            }}
                        />

                        {annotations.map((annotation, index) => (
                            <div
                                key={annotation.id}
                                style={{
                                    padding: '8px',
                                    marginBottom: '8px',
                                    border: '1px solid #ccc',
                                    borderRadius: '4px',
                                    backgroundColor: isDarkTheme ? '#30404d' : '#fff',
                                    cursor: 'pointer'
                                }}
                            >
                                <div style={{
                                    fontSize: '12px',
                                    color: isDarkTheme ? '#a7b6c2' : '#5c7080',
                                    marginBottom: '4px'
                                }}>
                                    Page {annotation.page || 1} • {annotation.type || 'note'}
                                </div>
                                <div style={{
                                    fontSize: '14px',
                                    color: isDarkTheme ? '#f5f8fa' : '#182026'
                                }}>
                                    {annotation.text || 'Empty annotation'}
                                </div>
                                <div style={{ marginTop: '8px' }}>
                                    <ButtonGroup minimal>
                                        <Button
                                            icon="edit"
                                            small
                                            onClick={() => {
                                                const newText = prompt('Edit annotation:', annotation.text);
                                                if (newText !== null) {
                                                    setAnnotations(prev =>
                                                        prev.map(a =>
                                                            a.id === annotation.id
                                                                ? { ...a, text: newText }
                                                                : a
                                                        )
                                                    );
                                                }
                                            }}
                                        />
                                        <Button
                                            icon="trash"
                                            small
                                            intent="danger"
                                            onClick={() => deleteAnnotation(annotation.id)}
                                        />
                                    </ButtonGroup>
                                </div>
                            </div>
                        ))}
                        {annotations.length === 0 && (
                            <div style={{
                                textAlign: 'center',
                                color: isDarkTheme ? '#a7b6c2' : '#5c7080',
                                fontStyle: 'italic',
                                marginTop: '20px'
                            }}>
                                No annotations yet. Click "Add Note" to create one.
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};
        if (pageInput >= 1 && pageInput <= totalPages) {
            onGoTo(pageInput);
            onClose();
        }
    };

    return (
        <Draggable handle=".goto-window-header">
            <div
                className={`goto-window ${isDarkTheme ? 'bp5-dark' : ''}`}
                style={{
                    position: 'absolute',
                    top: '100px',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    width: '250px',
                    backgroundColor: isDarkTheme ? '#30404D' : 'white',
                    border: `1px solid ${isDarkTheme ? '#394B59' : '#E1E8ED'}`,
                    borderRadius: '3px',
                    boxShadow: '0 0 10px rgba(0, 0, 0, 0.2)',
                    zIndex: 1000,
                    display: 'flex',
                    flexDirection: 'column',
                    overflow: 'hidden'
                }}
            >
                <div
                    className="goto-window-header"
                    style={{
                        padding: '8px 10px',
                        backgroundColor: isDarkTheme ? '#394B59' : '#F5F8FA',
                        borderBottom: `1px solid ${isDarkTheme ? '#30404D' : '#E1E8ED'}`,
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        cursor: 'move'
                    }}
                >
                    <span style={{ fontWeight: 'bold' }}>Go to Page</span>
                    <BpButton
                        icon={<X size={16} />}
                        minimal={true}
                        small={true}
                        onClick={onClose}
                    />
                </div>
                <div style={{ padding: '15px' }}>
                    <div style={{ marginBottom: '15px', display: 'flex', alignItems: 'center' }}>
                        <span style={{ marginRight: '10px' }}>Page:</span>
                        <InputNumber
                            min={1}
                            max={totalPages}
                            value={pageInput}
                            onChange={(value) => setPageInput(value)}
                            style={{ width: '100%' }}
                            onPressEnter={handleGoTo}
                        />
                        <span style={{ marginLeft: '10px' }}>of {totalPages}</span>
                    </div>
                    <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                        <BpButton
                            text="Cancel"
                            minimal={true}
                            onClick={onClose}
                            style={{ marginRight: '10px' }}
                        />
                        <BpButton
                            text="Go"
                            intent="primary"
                            onClick={handleGoTo}
                        />
                    </div>
                </div>
            </div>
        </Draggable>
    );
};

// Enhanced PDFPageRenderer component with better search highlighting
const PDFPageRenderer = React.memo(({ pdfFile, pageNumber, scale, rotation, width, onRenderSuccess, searchQuery, highlightColor, setRenderedScale, setRenderedPageNumber }) => {
    const [textItems, setTextItems] = useState([]);
    const [pageTextContent, setPageTextContent] = useState('');
    const [highlights, setHighlights] = useState([]);
    const pageRef = useRef(null);
    const [pdfError, setPdfError] = useState(null);

    // Extract text content from the page for search highlighting
    useEffect(() => {
        if (!pdfFile || !searchQuery || searchQuery.trim() === '') return;

        const extractTextContent = async () => {
            try {
                const pdfDocument = await pdfjs.getDocument(pdfFile).promise;
                const page = await pdfDocument.getPage(pageNumber);
                const textContent = await page.getTextContent({ includeMarkedContent: true });

                // Group text items by their y-position to maintain line structure
                const lineMap = new Map();
                textContent.items.forEach(item => {
                    // Round y position to account for slight variations in same line
                    const yPos = Math.round(item.transform[5] * 100) / 100;
                    if (!lineMap.has(yPos)) {
                        lineMap.set(yPos, []);
                    }
                    lineMap.get(yPos).push(item);
                });

                // Sort lines by y-position (top to bottom)
                const sortedLines = Array.from(lineMap.entries())
                    .sort((a, b) => b[0] - a[0]) // Reverse order (top to bottom)
                    .map(([_, items]) => {
                        // Sort items in each line by x-position (left to right)
                        return items.sort((a, b) => a.transform[4] - b.transform[4]);
                    });

                // Create a structured text representation with proper line breaks
                let structuredText = '';
                sortedLines.forEach(line => {
                    const lineText = line.map(item => item.str).join('');
                    structuredText += lineText + '\n';
                });

                setTextItems(textContent.items);
                setPageTextContent(structuredText);

                // Find matches in the structured text
                const regex = new RegExp(searchQuery, 'gim'); // Added 'm' flag for multi-line
                const matches = [];
                let match;

                while ((match = regex.exec(structuredText)) !== null) {
                    matches.push({
                        text: match[0],
                        index: match.index,
                        isMultiLine: match[0].includes('\n'),
                        lines: match[0].split('\n').length
                    });
                }

                setHighlights(matches);
            } catch (error) {
                console.error('Error extracting text content:', error);
            }
        };

        extractTextContent();
    }, [pdfFile, pageNumber, searchQuery]);

    const onPageRenderSuccess = (page) => {
        if (setRenderedPageNumber) setRenderedPageNumber(pageNumber);
        if (setRenderedScale) setRenderedScale(scale);
        if (onRenderSuccess) onRenderSuccess(page);

        // Apply highlights after page renders
        if (highlights.length > 0 && pageRef.current) {
            const textLayer = pageRef.current.querySelector('.react-pdf__Page__textContent');
            if (textLayer) {
                // Clear previous highlights
                const existingHighlights = textLayer.querySelectorAll('.pdf-text-highlight');
                existingHighlights.forEach(el => el.classList.remove('pdf-text-highlight', 'multi-line'));

                // Apply new highlights
                const textNodes = Array.from(textLayer.querySelectorAll('span'));
                highlights.forEach(highlight => {
                    // For multi-line highlights, we need a more complex approach
                    if (highlight.isMultiLine) {
                        // This file has been completely replaced to use AnnotatablePDFViewer, you might need
                        // a more sophisticated algorithm to find the exact text nodes
                        const highlightLines = highlight.text.split('\n');
                        let foundStart = false;

                        textNodes.forEach(node => {
                            if (!foundStart && node.textContent.includes(highlightLines[0])) {
                            const nodeText = node.textContent;
                            if (!foundStart && nodeText.includes(highlightLines[0])) {
                                foundStart = true;
                                node.classList.add('pdf-text-highlight', 'multi-line');
                            } else if (foundStart && highlightLines.some(line => nodeText.includes(line))) {
                                node.classList.add('pdf-text-highlight', 'multi-line');
                            }
                        });
                    } else {
                        // Single line highlights are simpler
                        textNodes.forEach(node => {
                            if (node.textContent.includes(highlight.text)) {
                                node.classList.add('pdf-text-highlight');
                            }
                        });
                    }
                });
            }
        }
    };

    const onDocumentLoadError = (error) => {
        console.error('Error loading PDF in PDFPageRenderer:', error);
        setPdfError(`Failed to load page ${pageNumber}: ${error.message}`);
    };

    return (
        <>
            {pdfError ? (
                <div style={{ padding: '20px', color: 'red' }}>{pdfError}</div>
            ) : (
                <Document
                    file={pdfFile}
                    loading={<div style={{ padding: '20px' }}>Loading page {pageNumber}...</div>}
                    onLoadError={onDocumentLoadError}
                >
                    <Page
                        key={`page_${pageNumber}_${scale}_${rotation}`}
                        pageNumber={pageNumber}
                        scale={scale}
                        rotate={rotation}
                        width={width}
                        onRenderSuccess={onPageRenderSuccess}
                        renderTextLayer={true}
                        renderAnnotationLayer={true}
                        renderInteractiveForms={true}
                        className="pdf-page-transition"
                        inputRef={pageRef}
                        loading={<div style={{ padding: '20px' }}>Rendering page {pageNumber}...</div>}
                        error={<div style={{ padding: '20px', color: 'red' }}>Error rendering page {pageNumber}</div>}
                    />
                </Document>
            )}
        </>
    );
});

// Enhanced usePageObserver hook for better page detection
const usePageObserver = ({ parentRef, setCurrentPage, numPages }) => {
    const [pageObserver, setPageObserver] = useState(null);

    useEffect(() => {
        if (!parentRef.current || !numPages) return;

        const options = {
            root: parentRef.current,
            rootMargin: '0px',
            threshold: THRESHOLD
        };

        const handleIntersect = (entries) => {
            const visibleEntries = entries.filter(entry => entry.isIntersecting);
            if (visibleEntries.length > 0) {
                // Sort by visibility ratio to get the most visible page
                const mostVisibleEntry = visibleEntries.reduce((prev, current) => {
                    return prev.intersectionRatio > current.intersectionRatio ? prev : current;
                });

                const pageIndex = parseInt(mostVisibleEntry.target.getAttribute(INDEX_ATTRIBUTE), 10);
                if (!isNaN(pageIndex)) {
                    const page = pageIndex + 1; // Convert from 0-based to 1-based
                    setCurrentPage(page);
                }
            }
        };

        const observer = new IntersectionObserver(handleIntersect, options);
        setPageObserver(observer);

        return () => {
            if (observer) {
                observer.disconnect();
            }
        };
    }, [parentRef, setCurrentPage, numPages]);

    return { pageObserver };
};

const PDFJsViewer = ({ fileId, filePath, isDarkTheme }) => {
    const [pdfFile, setPdfFile] = useState(null);
    const [numPages, setNumPages] = useState(0);
    const [pageNumber, setPageNumber] = useState(1);
    const [zoomLevel, setZoomLevel] = useState(1.0); // Initial zoom level, now controlled by state
    const [scale, setScale] = useState(1.0); // Separate scale state for PDF rendering
    const [rotation, setRotation] = useState(0);
    const [containerWidth, setContainerWidth] = useState(0);
    const [totalOccurrences, setTotalOccurrences] = useState(0);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [isSearchOpen, setIsSearchOpen] = useState(false);
    const [isGoToOpen, setIsGoToOpen] = useState(false);
    const [isZooming, setIsZooming] = useState(false);

    const [renderedPageNumber, setRenderedPageNumber] = useState(pageNumber);
    const [renderedScale, setRenderedScale] = useState(scale);

    const parentRef = useRef(null);
    const pdfContainerRef = useRef(null);
    const parentClassName = isDarkTheme ? "bp5-dark" : "";
    const transformComponentRef = useRef(null); // Ref for TransformWrapper
    const zoomTimeoutRef = useRef(null);

    const dispatch = useDispatch();
    const { ref, inView } = useInView({ threshold: 0.1 });
    const currentViewerState = useSelector(state => state.currentPdf.viewers[fileId]);
    const visiblePDFs = useSelector(selectVisiblePDFs);
    const pendingPageNavigation = useRef(null);
    const [viewerReady, setViewerReady] = useState(false);

    const { pageObserver } = usePageObserver({ parentRef, setCurrentPage: setPageNumber, numPages });
    const estimateSize = useCallback(() => DEFAULT_HEIGHT * scale, [scale]);
    const virtualizer = useVirtualizer({ count: numPages, getScrollElement: () => parentRef.current, estimateSize, overscan: 5 });

    const scrollToPage = useCallback((page) => {
        virtualizer.scrollToIndex(page - 1, { align: 'start', behavior: 'smooth' });
        dispatch(updatePdfViewer({ fileId, page: parseInt(page, 10), filePath, isVisible: inView }));
    }, [virtualizer, dispatch, fileId, filePath, inView]);

    const onDocumentLoadSuccess = useCallback(({ numPages }) => {
        console.log('PDF document loaded successfully with', numPages, 'pages');
        setNumPages(numPages);
        setPageNumber(1);
        setLoading(false);
        setViewerReady(true);
        setRenderedPageNumber(1);
        setRenderedScale(scale);
        if (pendingPageNavigation.current !== null) {
            const pageNumberToNavigate = pendingPageNavigation.current;
            console.log('Executing pending navigation to page:', pageNumberToNavigate);
            scrollToPage(pageNumberToNavigate);
            pendingPageNavigation.current = null;
        }
        dispatch(updatePdfViewer({ fileId, filePath, page: 1, isVisible: inView }));
        console.log('PDF Loaded:', { fileId, filePath, initialPage: 1, isVisible: inView });
    }, [dispatch, fileId, filePath, inView, pendingPageNavigation, scrollToPage, scale]);

    const onDocumentLoadError = useCallback((error) => {
        console.error("Error loading PDF:", error);
        setError("Failed to load the PDF. Please check if the file is corrupted or try again.");
        setLoading(false);
    }, []);


    const handleSearchInputChange = useCallback((e) => {
        setSearchQuery(e.target.value);
    }, []);

    const handleSearch = useCallback(async () => {
        if (!pdfFile || !searchQuery) return;

        const pdfDocument = await pdfjs.getDocument(pdfFile).promise;
        const pageResults = [];
        let totalOccurrencesCount = 0;

        for (let i = 1; i <= numPages; i++) {
            const page = await pdfDocument.getPage(i);
            const textContent = await page.getTextContent({ includeMarkedContent: true });

            // Improved text extraction that preserves structure
            // Group text items by their y-position to maintain line structure
            const lineMap = new Map();
            textContent.items.forEach(item => {
                // Round y position to account for slight variations in same line
                const yPos = Math.round(item.transform[5] * 100) / 100;
                if (!lineMap.has(yPos)) {
                    lineMap.set(yPos, []);
                }
                lineMap.get(yPos).push(item);
            });

            // Sort lines by y-position (top to bottom)
            const sortedLines = Array.from(lineMap.entries())
                .sort((a, b) => b[0] - a[0]) // Reverse order (top to bottom)
                .map(([_, items]) => {
                    // Sort items in each line by x-position (left to right)
                    return items.sort((a, b) => a.transform[4] - b.transform[4]);
                });

            // Create a structured text representation with proper line breaks
            let structuredText = '';
            sortedLines.forEach(line => {
                const lineText = line.map(item => item.str).join('');
                structuredText += lineText + '\n';
            });

            // Search for matches in the structured text
            const regex = new RegExp(searchQuery, 'gim'); // Added 'm' flag for multi-line
            const matches = structuredText.match(regex);

            if (matches) {
                totalOccurrencesCount += matches.length;

                // Store the page number for each match
                matches.forEach(match => {
                    // Find the position of this match in the structured text
                    const matchIndex = structuredText.indexOf(match);
                    if (matchIndex !== -1) {
                        pageResults.push({
                            str: match,
                            pageNumber: i,
                            matchIndex: matchIndex,
                            // Store additional info for highlighting
                            isMultiLine: match.includes('\n'),
                            lines: match.split('\n').length
                        });
                    }
                });
            }
        }

        setSearchResults(pageResults);
        setTotalOccurrences(totalOccurrencesCount);
        setCurrentSearchIndex(pageResults.length > 0 ? 0 : -1);
        if (pageResults.length > 0) {
            setPageNumber(pageResults[0].pageNumber);
            scrollToPage(pageResults[0].pageNumber);
        }
        setIsSearchOpen(true);
    }, [pdfFile, searchQuery, scrollToPage, numPages]);


    const navigateSearch = useCallback((direction) => {
        if (searchResults.length === 0) return;
        const newIndex = direction === 'next' ? (currentSearchIndex + 1) % searchResults.length : (currentSearchIndex - 1 + searchResults.length) % searchResults.length;
        setCurrentSearchIndex(newIndex);
        setPageNumber(searchResults[newIndex].pageNumber);
        scrollToPage(searchResults[newIndex].pageNumber);
    }, [searchResults, currentSearchIndex, scrollToPage]);

    const handleGoTo = useCallback((page) => {
        if (page >= 1 && page <= numPages) {
            setPageNumber(page);
            scrollToPage(page);
        }
    }, [numPages, scrollToPage]);


    const rotateLeft = useCallback(() => {
        setRotation((prevRotation) => (prevRotation - 90 + 360) % 360);
    }, []);
    const rotateRight = useCallback(() => {
        setRotation((prevRotation) => (prevRotation + 90) % 360);
    }, []);
    const resetView = useCallback(() => {
        setScale(1.0); // Reset scale state
        setZoomLevel(1.0); // Reset zoomLevel state
        setRotation(0);
        if (transformComponentRef.current) {
            transformComponentRef.current.resetTransform(); // Reset pan and zoom
        }
    }, []);

    // Enhanced zoom functions with smooth transitions
    const startZoomTransition = useCallback(() => {
        setIsZooming(true);
        // Clear any existing timeout
        if (zoomTimeoutRef.current) {
            clearTimeout(zoomTimeoutRef.current);
        }
        // Set a timeout to end the zooming state
        zoomTimeoutRef.current = setTimeout(() => {
            setIsZooming(false);
        }, 300); // Match this with CSS transition duration
    }, []);

    // Throttled zoom functions to prevent rapid state changes
    const zoomIn = useCallback(throttle(() => {
        startZoomTransition();
        setZoomLevel(prevZoom => {
            const currentIndex = ZOOM_LEVELS.findIndex(zl => zl >= prevZoom);
            const nextIndex = Math.min(currentIndex + 1, ZOOM_LEVELS.length - 1);
            const newZoom = ZOOM_LEVELS[nextIndex];

            if (transformComponentRef.current) {
                // Use the TransformWrapper API
                transformComponentRef.current.zoomIn(1.2);
            }
            // Update scale for PDF rendering
            setScale(newZoom);
            return newZoom;
        });
    }, 100), [startZoomTransition]);

    const zoomOut = useCallback(throttle(() => {
        startZoomTransition();
        setZoomLevel(prevZoom => {
            const currentIndex = ZOOM_LEVELS.findIndex(zl => zl >= prevZoom);
            const prevIndex = Math.max(currentIndex - 1, 0);
            const newZoom = ZOOM_LEVELS[prevIndex];

            if (transformComponentRef.current) {
                // Use the TransformWrapper API
                transformComponentRef.current.zoomOut(1.2);
            }
            // Update scale for PDF rendering
            setScale(newZoom);
            return newZoom;
        });
    }, 100), [startZoomTransition]);

    const updateZoom = useCallback((newZoomLevel) => {
        startZoomTransition();
        setZoomLevel(newZoomLevel); // Update zoomLevel state
        setScale(newZoomLevel); // Update scale for PDF rendering

        if (transformComponentRef.current) {
            // Use the TransformWrapper API
            transformComponentRef.current.setTransform(
                transformComponentRef.current.state.positionX,
                transformComponentRef.current.state.positionY,
                newZoomLevel
            );
        }
    }, [startZoomTransition]);


    // Effect to set the PDF file path directly
    useEffect(() => {
        if (!filePath) return;

        console.log('Setting PDF file path:', filePath);
        // Instead of fetching, just set the file path directly
        setPdfFile(filePath);
        setLoading(false);

        // Debug message to help troubleshoot
        console.log('Current state:', {
            fileId,
            filePath,
            pdfFile: filePath,
            numPages,
            loading: false,
            error: null
        });

    }, [filePath, fileId, numPages]);

    useEffect(() => {
        dispatch(setViewerVisibility({ fileId, isVisible: inView }));
        if (inView) {
            dispatch(setFocusedViewer(fileId));
            console.log('PDF Viewer Visible:', { fileId, filePath, currentPage: currentViewerState?.page || 1 });
        }
    }, [inView, fileId, dispatch, filePath, currentViewerState]);

    useEffect(() => {
        const handlePageNavigation = (event) => {
            if (event.detail && event.detail.pageNumber) {
                const pageNumber = parseInt(event.detail.pageNumber, 10);
                if (viewerReady) scrollToPage(pageNumber);
                else pendingPageNavigation.current = pageNumber;
            }
        };
        window.addEventListener('pdf-page-navigation', handlePageNavigation);
        const handleWindowMessage = (event) => {
            if (event.data && event.data.type === 'setPage') {
                const pageNumber = parseInt(event.data.pageNumber, 10);
                if (viewerReady) scrollToPage(pageNumber);
                else pendingPageNavigation.current = pageNumber;
            }
        };
        window.addEventListener('message', handleWindowMessage);
        return () => {
            window.removeEventListener('pdf-page-navigation', handlePageNavigation);
            window.removeEventListener('message', handleWindowMessage);
        };
    }, [scrollToPage, viewerReady]);


    return (
        <div ref={ref} className={parentClassName} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
            {/* Add a hidden Document component to load the PDF and get the number of pages */}
            {pdfFile && (
                <div style={{ display: 'none' }}>
                    <Document
                        file={pdfFile}
                        onLoadSuccess={onDocumentLoadSuccess}
                        onLoadError={onDocumentLoadError}
                    />
                </div>
            )}
            <Card
                style={{
                    padding: "4px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    background: isDarkTheme ? "#1C2127" : "white",
                }}
            >
                <div style={{ display: "flex", alignItems: "center", gap: "10px", flex: "1 1 25%" }}>
                    <Input
                        placeholder="Search..."
                        value={searchQuery}
                        onChange={handleSearchInputChange}
                        onPressEnter={handleSearch}
                        style={{ width: "200px" }}
                        className={isDarkTheme ? "bp5-dark" : ""}
                    />
                    <BpButton
                        icon={<Search size={16} color={isDarkTheme ? "white" : "black"} />}
                        onClick={handleSearch}
                        minimal={true}
                        style={{ color: isDarkTheme ? "white" : "black" }}
                    />
                </div>
                <div style={{ display: "flex", alignItems: "center", gap: "5px", flex: "1 1 50%", justifyContent: "center" }}>
                    <ButtonGroup minimal={true}>
                        <BpButton icon={<RotateCcw size={16} />} onClick={rotateLeft} className={isDarkTheme ? "bp5-dark" : ""} />
                        <BpButton icon={<RotateCw size={16} />} onClick={rotateRight} className={isDarkTheme ? "bp5-dark" : ""} />
                    </ButtonGroup>
                    <ButtonGroup minimal={true}>
                        <BpButton icon={<ZoomOut size={16} />} onClick={zoomOut} className={isDarkTheme ? "bp5-dark" : ""} />
                        <BpButton icon={<ZoomIn size={16} />} onClick={zoomIn} className={isDarkTheme ? "bp5-dark" : ""} />
                        <HTMLSelect
                            options={ZOOM_LEVELS.map(level => ({ label: `${level * 100}%`, value: level }))}
                            value={zoomLevel}
                            onChange={(event) => updateZoom(parseFloat(event.target.value))}
                            style={{ width: '100px' }}
                            minimal={true}
                            icon={<ChevronDown size={14} />}
                            className={isDarkTheme ? "bp5-dark" : ""}
                        />
                    </ButtonGroup>
                    <BpButton text="Reset" onClick={resetView} minimal={true} small={true} className={isDarkTheme ? "bp5-dark" : ""} />
                    <BpButton
                        text="Go to"
                        onClick={() => setIsGoToOpen(true)}
                        minimal={true}
                        small={true}
                        className={isDarkTheme ? "bp5-dark" : ""}
                    />
                </div>
                <div style={{ flex: "1 1 25%", textAlign: "right", paddingRight: "20px" }}>
                    <span>
                        Page {pageNumber} of {numPages}
                    </span>
                </div>
            </Card>

            <div
                ref={pdfContainerRef}
                className="pdf-container"
                style={{
                    flex: 1,
                    overflow: "hidden",
                    position: "relative",
                }}
            >
                {error ? (
                    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
                        <div style={{ color: 'red', marginBottom: '10px' }}>{error}</div>
                        <BpButton intent="primary" onClick={() => window.location.reload()}>Retry</BpButton>
                    </div>
                ) : !pdfFile ? (
                    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                        <div style={{ textAlign: 'center' }}>
                            <Spinner size={50} />
                            <div style={{ marginTop: '10px' }}>Loading PDF...</div>
                            <div style={{ marginTop: '5px', fontSize: '12px', color: '#666' }}>{filePath}</div>
                        </div>
                    </div>
                ) : (
                    <TransformWrapper
                        ref={transformComponentRef}
                        initialScale={1}
                        minScale={ZOOM_LEVELS[0]}
                        maxScale={ZOOM_LEVELS[ZOOM_LEVELS.length - 1]}
                        limitToBounds={false}
                        doubleClick={{ disabled: true }}
                        panning={{ velocityDisabled: true }}
                        className={isZooming ? 'zooming' : ''}
                    >
                        <TransformComponent
                            wrapperClassName="react-transform-wrapper"
                            contentClassName="react-transform-component"
                        >

                            <div ref={parentRef} className="pdf-virtualized-scroll" style={{ height: "100%", width: "100%", overflow: "auto" }}>
                                <div style={{ height: `${virtualizer.getTotalSize()}px`, width: "100%", position: "relative" }}>
                                    {virtualizer.getVirtualItems().map((virtualItem) => (
                                        <div
                                            key={virtualItem.key}
                                            data-index={virtualItem.index}
                                            ref={el => virtualizer.measureElement(el)}
                                            style={{
                                                position: 'absolute',
                                                top: 0,
                                                left: 0,
                                                width: '100%',
                                                transform: `translateY(${virtualItem.start}px)`,
                                            }}
                                        >
                                            <div style={{ marginBottom: `${PAGE_PADDING}px`, display: 'flex', justifyContent: 'center' }}>
                                                {renderedPageNumber !== null && renderedScale !== null && virtualItem.index + 1 === pageNumber  ? (
                                                    <PDFPageRenderer
                                                        pdfFile={pdfFile}
                                                        key={`${renderedPageNumber}@${renderedScale}`} // Unique key for "prevPage"
                                                        pageNumber={renderedPageNumber}
                                                        scale={renderedScale}
                                                        rotation={rotation}
                                                        width={containerWidth}
                                                        onRenderSuccess={() => {}}
                                                        searchQuery={searchQuery}
                                                        highlightColor="#FBB360"
                                                        setRenderedPageNumber={() => {}} // Dummy setters
                                                        setRenderedScale={() => {}}
                                                    />
                                                ) : null}
                                                <PDFPageRenderer
                                                    pdfFile={pdfFile}
                                                    pageNumber={virtualItem.index + 1}
                                                    scale={scale}
                                                    rotation={rotation}
                                                    width={containerWidth}
                                                    onRenderSuccess={() => {}}
                                                    searchQuery={searchQuery}
                                                    highlightColor="#FBB360"
                                                    setRenderedPageNumber={setRenderedPageNumber}
                                                    setRenderedScale={setRenderedScale}
                                                />
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </TransformComponent>
                    </TransformWrapper>
                )}
            </div>

            <SearchWindow
                isOpen={isSearchOpen}
                onClose={() => setIsSearchOpen(false)}
                searchQuery={searchQuery}
                onSearchChange={handleSearchInputChange}
                onSearch={handleSearch}
                searchResults={searchResults}
                currentSearchIndex={currentSearchIndex}
                onNavigateSearch={navigateSearch}
                isDarkTheme={isDarkTheme}
                totalOccurrences={totalOccurrences}
            />

            <GoToWindow
                isOpen={isGoToOpen}
                onClose={() => setIsGoToOpen(false)}
                onGoTo={handleGoTo}
                totalPages={numPages}
                isDarkTheme={isDarkTheme}
            />
        </div>
    );
};

export default PDFJsViewer;