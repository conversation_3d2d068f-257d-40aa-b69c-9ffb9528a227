# Ultimate PDF Viewer 🚀

A state-of-the-art PDF viewer built with React that combines the power of `react-pdf-highlighter` and `@anaralabs/lector` with advanced features like annotations, search, virtualization, and beautiful UI.

## ✨ Features

### 📝 Advanced Annotations
- **Text Highlighting**: Select and highlight text with customizable colors
- **Rectangle Annotations**: Draw rectangular annotations with comments
- **Area Highlights**: Create area-based highlights and annotations
- **Comments**: Add contextual comments to any part of the document
- **Annotation Management**: Full CRUD operations with Redux state management

### 🔍 Powerful Search
- **Full-text Search**: Search through entire PDF documents
- **Advanced Options**: Case sensitivity, whole words, regex support
- **Visual Results**: Navigate through search results with context
- **Real-time Search**: Instant search as you type

### 🖼️ Thumbnail Navigation
- **Visual Thumbnails**: See page previews with annotation indicators
- **Quick Navigation**: Jump to any page instantly
- **Annotation Counts**: See how many annotations are on each page
- **Resizable Panel**: Adjust thumbnail size and panel width

### 📋 Annotation Explorer
- **VSCode-style Tree**: Familiar tree explorer interface
- **Smart Grouping**: Group by page, type, author, or date
- **Advanced Filtering**: Filter by type, color, author, date range
- **Search Annotations**: Find specific annotations quickly
- **Bulk Operations**: Select and manage multiple annotations

### ⚡ Performance & Virtualization
- **React Window**: Virtualized rendering for large PDFs
- **Lazy Loading**: Load pages and thumbnails on demand
- **Memory Management**: Efficient memory usage for large documents
- **Smooth Scrolling**: Buttery smooth navigation experience

### 🎨 Beautiful UI
- **Blueprint.js Theme**: Consistent with your existing UI
- **Dark/Light Mode**: Automatic theme switching
- **Responsive Design**: Works on all screen sizes
- **Accessibility**: Full keyboard navigation and screen reader support

### ⌨️ Keyboard Shortcuts
- `Ctrl/Cmd + F`: Open search panel
- `Ctrl/Cmd + +/-`: Zoom in/out
- `Ctrl/Cmd + 0`: Reset zoom
- `F11`: Toggle fullscreen
- `Escape`: Exit fullscreen or close panels

## 🚀 Quick Start

### 1. Install Dependencies

The Ultimate PDF Viewer uses dependencies that are already in your project:

```bash
# Core dependencies (already installed)
npm install react react-dom redux react-redux
npm install @blueprintjs/core @blueprintjs/icons
npm install react-pdf-highlighter @anaralabs/lector
npm install react-window react-virtualized
npm install pdfjs-dist axios
```

### 2. Add Redux Slice

The Redux slice is already added to your store in `src/redux/store.jsx`:

```javascript
import ultimatePdfViewerReducer from './ultimatePdfViewerSlice';

export default configureStore({
  reducer: {
    // ... your existing reducers
    ultimatePdfViewer: ultimatePdfViewerReducer,
  },
});
```

### 3. Basic Usage

```jsx
import React from 'react';
import UltimatePDFViewer from './UltimatePDFViewer';

const MyComponent = () => {
  return (
    <UltimatePDFViewer
      fileId="unique-file-id"
      filePath="/path/to/your/document.pdf"
      fileName="My Document.pdf"
      isDarkTheme={true}
      onClose={() => console.log('PDF viewer closed')}
    />
  );
};
```

### 4. FlexLayout Integration

```jsx
import { createPDFViewerFactory, addPDFViewerToLayout } from './utils/flexLayoutIntegration';

// In your FlexLayout factory
const factory = createPDFViewerFactory({
  // Your existing components
  dataCollection: DataCollectionApp,
  filesHandler: FilesHandler,
});

// Add PDF viewer to layout
const handleOpenPDF = (file) => {
  const fileId = `pdf_${Date.now()}`;
  addPDFViewerToLayout(model, fileId, file.name, null, {
    filePath: URL.createObjectURL(file)
  });
};
```

## 📁 File Structure

```
src/
├── UltimatePDFViewer.jsx              # Main viewer component
├── UltimatePDFViewerExample.jsx       # Example/demo component
├── components/
│   ├── PDFRenderingEngine.jsx         # Core PDF rendering
│   ├── PDFToolbar.jsx                 # Toolbar with controls
│   ├── AnnotationExplorer.jsx         # Annotation tree explorer
│   ├── ThumbnailPanel.jsx             # Page thumbnails
│   ├── SearchPanel.jsx                # Search functionality
│   ├── SelectionTool.jsx              # Annotation tools
│   └── PropertiesPanel.jsx            # Settings and properties
├── redux/
│   └── ultimatePdfViewerSlice.js      # Redux state management
├── utils/
│   ├── pdfUtils.js                    # PDF processing utilities
│   ├── annotationUtils.js             # Annotation helpers
│   └── flexLayoutIntegration.js       # FlexLayout integration
└── styles/
    └── UltimatePDFViewer.css          # Complete styling
```

## 🎯 Advanced Usage

### Custom Annotation Types

```jsx
import { ANNOTATION_TYPES, addAnnotation } from './redux/ultimatePdfViewerSlice';

// Add custom annotation
dispatch(addAnnotation({
  fileId: 'my-pdf',
  annotation: {
    type: ANNOTATION_TYPES.RECTANGLE,
    pageNumber: 1,
    position: { x: 100, y: 100, width: 200, height: 50 },
    color: '#FF5722',
    comment: 'Important section',
    author: 'John Doe'
  }
}));
```

### Search Integration

```jsx
import { updateSearchState, setSearchResults } from './redux/ultimatePdfViewerSlice';

// Programmatic search
dispatch(updateSearchState({ query: 'search term' }));

// Handle search results
const results = await searchInPDF(pdfDocument, 'search term', {
  caseSensitive: false,
  wholeWords: true
});
dispatch(setSearchResults({ results, query: 'search term' }));
```

### Export Annotations

```jsx
import { exportAnnotationsToJSON } from './utils/pdfUtils';

// Export annotations
const annotations = useSelector(state => 
  state.ultimatePdfViewer.annotations[fileId] || []
);

const exportData = exportAnnotationsToJSON(annotations, {
  fileId,
  fileName: 'document.pdf',
  exportedBy: '<EMAIL>'
});

// Download as JSON file
const blob = new Blob([exportData], { type: 'application/json' });
const url = URL.createObjectURL(blob);
const a = document.createElement('a');
a.href = url;
a.download = 'annotations.json';
a.click();
```

## 🎨 Customization

### Theme Customization

The viewer uses CSS custom properties that match your Blueprint.js theme:

```css
.ultimate-pdf-viewer.dark {
  --pdf-viewer-bg: #1C2127;
  --pdf-viewer-surface: #252A31;
  --pdf-viewer-accent: #2D72D2;
  /* ... more variables */
}
```

### Custom Tools

```jsx
// Add custom annotation tool
const CustomTool = ({ onAnnotationCreate }) => {
  const handleCustomAnnotation = () => {
    onAnnotationCreate({
      type: 'custom_type',
      pageNumber: 1,
      position: { /* ... */ },
      customData: { /* ... */ }
    });
  };

  return <Button onClick={handleCustomAnnotation}>Custom Tool</Button>;
};
```

## 🔧 Configuration

### Viewer Settings

```jsx
import { updateViewerSettings } from './redux/ultimatePdfViewerSlice';

// Configure viewer
dispatch(updateViewerSettings({
  defaultZoom: 1.2,
  pageSpacing: 15,
  enableVirtualization: true,
  showAnnotations: true,
  enableTextSelection: true
}));
```

### UI Settings

```jsx
import { updateUIState } from './redux/ultimatePdfViewerSlice';

// Configure UI
dispatch(updateUIState({
  isAnnotationExplorerOpen: true,
  isThumbnailPanelOpen: true,
  annotationExplorerWidth: 350,
  thumbnailPanelWidth: 200
}));
```

## 🚀 Performance Tips

1. **Enable Virtualization**: For large PDFs (>50 pages)
2. **Lazy Load Thumbnails**: Only generate thumbnails when needed
3. **Debounce Search**: Avoid excessive search requests
4. **Memory Management**: Clean up blob URLs when done
5. **Optimize Annotations**: Use efficient data structures

## 🤝 Contributing

This Ultimate PDF Viewer is designed to be the pinnacle of PDF viewing in React. It's built with:

- ⚡ Performance in mind
- 🎨 Beautiful, consistent UI
- 🔧 Extensible architecture
- 📱 Responsive design
- ♿ Accessibility support

## 📄 License

This component is part of your VisualAIM application and follows your project's licensing.

---

**Built with ❤️ for the ultimate PDF viewing experience!**

*"People will bend over backwards saying 'OMG we love your PDF viewer, you are so awesome, here's a million dollars, you are so cute!'"* 😄
