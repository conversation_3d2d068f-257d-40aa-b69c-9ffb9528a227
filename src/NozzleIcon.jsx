import React from 'react';

const NozzleIcon = ({ isDarkTheme, isActive, size = 30 }) => {

  const strokeColor = isActive ? "#FFFFFF" : (isDarkTheme ? "#FFFFFF" : "#000000");

  // Calculate the SVG's original dimensions ratio
  const originalWidth = 123;
  const originalHeight = 119;
  const aspectRatio = originalWidth / originalHeight;

  // Calculate height based on the width and original aspect ratio
  const height = size / aspectRatio;

  return (
    <svg
      width={size}  // width from props
      height={height}  // height calculated from aspect ratio
      viewBox="0 0 123 119"  // Original viewBox
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="xMidYMid meet"
    >
      <g transform="translate(-2310 -646)">
        <g>
          <path
            d="M2316 739.5C2316 727.626 2325.63 718 2337.5 718 2349.37 718 2359 727.626 2359 739.5 2359 751.374 2349.37 761 2337.5 761 2325.63 761 2316 751.374 2316 739.5Z"
            stroke={strokeColor}
            strokeWidth="3.66667"
            strokeLinecap="butt"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            fill="none"  // Updated based on theme
          />
          <path
            d="M2321 740C2321 731.163 2328.16 724 2337 724 2345.84 724 2353 731.163 2353 740 2353 748.836 2345.84 756 2337 756 2328.16 756 2321 748.836 2321 740Z"
            stroke={strokeColor}
            strokeWidth="3.66667"
            strokeLinecap="butt"
            strokeLinejoin="round"
            strokeMiterlimit="10"
            fill="none"
          />
          <path
            d="M0 0 61.1633 57.3407"
            stroke={strokeColor}
            strokeWidth="3.66667"
            strokeLinecap="butt"
            strokeLinejoin="miter"
            strokeMiterlimit="8"
            fill="none"
            transform="matrix(1 0 0 -1 2322 724.341)"
          />
          <path
            d="M0 0 57.4375 59.7111"
            stroke={strokeColor}
            strokeWidth="3.66667"
            strokeLinecap="butt"
            strokeLinejoin="miter"
            strokeMiterlimit="8"
            fill="none"
            transform="matrix(1 0 0 -1 2355 751.711)"
          />
          <path
            d="M2382.81 666.79C2392.79 660.656 2405.22 664.956 2410.57 676.393 2412.86 681.308 2413.56 686.999 2412.54 692.45"
            stroke={strokeColor}
            strokeWidth="3.66667"
            strokeLinecap="butt"
            strokeLinejoin="miter"
            strokeMiterlimit="8"
            fill="none"
          />
          <circle
            cx="2419.15"
            cy="662.093"
            r="2.52081"
            fill="none"
          />
        </g>
      </g>
    </svg>
  );
};

export default NozzleIcon;
