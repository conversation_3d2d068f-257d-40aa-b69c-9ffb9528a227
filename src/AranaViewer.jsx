import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  Root,
  Pages,
  Page,
  CanvasLayer,
  TextLayer,
  HighlightLayer,
  Thumbnails,
  Thumbnail,
  ZoomIn,
  ZoomOut,
  CurrentZoom,
  CurrentPage,
  TotalPages,
  NextPage,
  PreviousPage
} from '@anaralabs/lector';
import * as pdfjsLib from 'pdfjs-dist/build/pdf';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';
import {
  Navbar,
  NavbarGroup,
  Button,
  ButtonGroup,
  Spinner,
  NonIdealState
} from "@blueprintjs/core";
import axios from 'axios';

// Set up the worker directly
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;
console.log("PDF.js worker set up with version:", pdfjsLib.version);

const AranaViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  // State
  const [pdfSource, setPdfSource] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showThumbnails, setShowThumbnails] = useState(true);
  const [documentInfo, setDocumentInfo] = useState({ numPages: 0, fileName: '' });

  // State for download progress
  const [downloadProgress, setDownloadProgress] = useState(0);

  // Fetch PDF if fileId is provided
  useEffect(() => {
    // We're not using direct filePath as requested
    // Only using the blob from the API

    // If fileId is provided, fetch from API
    if (fileId) {
      // Track if component is still mounted
      let isMounted = true;

      const fetchFile = async () => {
        if (!isMounted) return;

        setLoading(true);
        setError(null);
        setPdfSource(null);
        setDownloadProgress(0);

        try {
          console.log(`Fetching PDF for fileId: ${fileId}`);
          const sessionId = localStorage.getItem("session_id");

          // Using the fetch API with progress tracking
          const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
            headers: {
              'Accept-Encoding': 'gzip, deflate, br',
              'session-id': sessionId,
            },
          });

          if (!response.ok) {
            throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
          }

          // Get content length for progress tracking
          const contentLength = response.headers.get('Content-Length');
          const reader = response.body.getReader();
          let receivedLength = 0;
          const chunks = [];

          // Read the response in chunks to track progress
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;

            chunks.push(value);
            receivedLength += value.length;

            // Update progress if content length is available
            if (contentLength && isMounted) {
              setDownloadProgress(receivedLength / contentLength);
            }
          }

          if (!isMounted) return;

          // Create a blob from all chunks with the correct MIME type
          const blob = new Blob(chunks, { type: 'application/pdf' });

          // Validate PDF header
          const headerBuffer = await blob.slice(0, 5).arrayBuffer();
          const headerBytes = new Uint8Array(headerBuffer);
          const header = String.fromCharCode.apply(null, headerBytes);

          if (header.indexOf('%PDF-') !== 0) {
            console.error("Invalid PDF header:", header);
            throw new Error("The file does not appear to be a valid PDF. Missing PDF header signature.");
          }

          // Create a blob URL for the PDF
          const pdfUrl = URL.createObjectURL(blob);

          // Log success information
          console.log("PDF blob created successfully:");
          console.log("- Size:", blob.size, "bytes");
          console.log("- Type:", blob.type);
          console.log("- URL:", pdfUrl);

          if (isMounted) {
            setPdfSource(pdfUrl);
            setLoading(false);
            console.log('PDF Loaded:', {
              fileId,
              initialPage: 1,
              isVisible: true
            });
          }
        } catch (error) {
          console.error("Failed to fetch the PDF with fetch API:", error);

          // Try fallback approach with axios if fetch fails
          try {
            console.log("Trying fallback approach with axios...");

            if (!isMounted) return;

            const response = await axios.get(
              `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
              {
                headers: {
                  "session-id": localStorage.getItem("session_id"),
                },
                responseType: "blob",
                onDownloadProgress: (progressEvent) => {
                  if (progressEvent.total) {
                    const progress = progressEvent.loaded / progressEvent.total;
                    if (isMounted) {
                      setDownloadProgress(progress);
                    }
                  }
                }
              }
            );

            if (!isMounted) return;

            // Create a blob URL from the response
            const blob = new Blob([response.data], { type: 'application/pdf' });
            const pdfUrl = URL.createObjectURL(blob);

            console.log("Fallback approach successful:");
            console.log("- Size:", blob.size, "bytes");
            console.log("- Type:", blob.type);
            console.log("- URL:", pdfUrl);

            if (isMounted) {
              setPdfSource(pdfUrl);
              setLoading(false);
            }
          } catch (fallbackError) {
            console.error("Fallback approach also failed:", fallbackError);
            if (isMounted) {
              setError(`Failed to load PDF: ${error.message}. Fallback also failed: ${fallbackError.message}`);
              setLoading(false);
            }
          }
        }
      };

      fetchFile();

      // Cleanup function
      return () => {
        isMounted = false;
        if (pdfSource && typeof pdfSource === 'string' && pdfSource.startsWith('blob:')) {
          URL.revokeObjectURL(pdfSource);
        }
      };
    } else {
      setError("No PDF source provided");
      setLoading(false);
    }
  }, [fileId, pdfSource]);

  // Handle document loaded
  const handleDocumentLoaded = useCallback((totalPages) => {
    console.log(`PDF loaded successfully with ${totalPages} pages`);
    setDocumentInfo(prev => ({ ...prev, numPages: totalPages }));
    setLoading(false);
  }, []);

  // Handle document load error
  const handleDocumentError = useCallback((err) => {
    console.error("Error loading PDF:", err);
    setError(err.message || "Failed to load PDF");
    setLoading(false);
  }, []);

  // Styling
  const containerStyle = {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa',
    color: isDarkTheme ? '#f5f8fa' : '#182026',
    ...style
  };

  const navbarStyle = {
    padding: '0 10px',
    backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
  };

  const contentStyle = {
    display: 'grid',
    gridTemplateColumns: showThumbnails ? '180px 1fr' : '0px 1fr',
    flexGrow: 1,
    overflow: 'hidden',
    transition: 'grid-template-columns 0.3s ease'
  };

  const thumbnailsStyle = {
    borderRight: '1px solid #ccc',
    overflowY: 'auto',
    backgroundColor: isDarkTheme ? '#394b59' : '#f5f8fa',
    padding: '10px'
  };

  const pagesContainerStyle = {
    overflow: 'auto',
    height: '100%'
  };

  // If loading or error, show appropriate UI
  if (loading && !pdfSource) {
    return (
      <div style={containerStyle}>
        <div style={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center", height: "100%", gap: '10px' }}>
          <Spinner size={50} />
          <span>Loading PDF...</span>
          {downloadProgress > 0 && downloadProgress < 1 && (
            <div style={{ width: '300px', marginTop: '10px' }}>
              <div style={{
                height: '6px',
                width: `${Math.round(downloadProgress * 100)}%`,
                backgroundColor: '#2B95D6',
                borderRadius: '3px',
                transition: 'width 0.3s ease'
              }} />
              <div style={{ textAlign: 'center', marginTop: '5px', fontSize: '12px' }}>
                {Math.round(downloadProgress * 100)}%
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (error && !pdfSource) {
    return (
      <div style={containerStyle}>
        <NonIdealState
          icon="error"
          title="Error Loading PDF"
          description={error}
        />
      </div>
    );
  }

  return (
    <div style={containerStyle}>
      {pdfSource ? (
        <Root
          source={pdfSource}
          style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
          loader={
            <div style={{ display: "flex", alignItems: "center", justifyContent: "center", height: "100%", gap: '10px' }}>
              <Spinner size={30} /> <span>Processing PDF...</span>
            </div>
          }
          onDocumentLoaded={handleDocumentLoaded}
          onError={handleDocumentError}
          pdfLib={pdfjsLib}
        >
          {/* Control Bar */}
          <Navbar style={navbarStyle}>
            <NavbarGroup>
              <Button
                minimal
                small
                icon={showThumbnails ? "panel-table" : "th"}
                onClick={() => setShowThumbnails(!showThumbnails)}
                title={showThumbnails ? "Hide Thumbnails" : "Show Thumbnails"}
              />

              <ButtonGroup minimal={true} style={{ marginLeft: '10px' }}>
                <PreviousPage>
                  <Button icon="chevron-left" small />
                </PreviousPage>

                <div style={{ display: 'flex', alignItems: 'center', margin: '0 10px' }}>
                  <span>Page</span>
                  <CurrentPage style={{
                    margin: '0 5px',
                    padding: '2px 8px',
                    border: '1px solid #ccc',
                    borderRadius: '3px',
                    minWidth: '30px',
                    textAlign: 'center',
                    backgroundColor: isDarkTheme ? '#202b33' : '#fff'
                  }} />
                  <span>of</span>
                  <TotalPages style={{ margin: '0 5px' }} />
                </div>

                <NextPage>
                  <Button icon="chevron-right" small />
                </NextPage>
              </ButtonGroup>

              <span style={{ marginLeft: '20px' }}>Zoom</span>
              <ButtonGroup minimal={true} style={{ marginLeft: '5px' }}>
                <ZoomOut>
                  <Button icon="zoom-out" small />
                </ZoomOut>
                <CurrentZoom>
                  {(props) => (
                    <span style={{ padding: '0 8px', minWidth: '50px', textAlign: 'center' }}>
                      {`${Math.round(props.scale * 100)}%`}
                    </span>
                  )}
                </CurrentZoom>
                <ZoomIn>
                  <Button icon="zoom-in" small />
                </ZoomIn>
              </ButtonGroup>
            </NavbarGroup>
          </Navbar>

          {/* Main Content */}
          <div style={contentStyle}>
            {/* Thumbnails Panel */}
            <div style={thumbnailsStyle}>
              {showThumbnails && (
                <Thumbnails>
                  {(props) => (
                    <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '10px' }}>
                      {Array.from({ length: props.pagesCount || 0 }).map((_, index) => (
                        <Thumbnail
                          key={index}
                          pageIndex={index}
                          style={{
                            width: '120px',
                            border: '1px solid #aaa',
                            marginBottom: '10px',
                            cursor: 'pointer',
                            opacity: props.currentPage === index + 1 ? 1 : 0.7,
                            boxShadow: props.currentPage === index + 1 ? '0 0 5px blue' : 'none'
                          }}
                          onClick={() => props.scrollToPage(index + 1)}
                        />
                      ))}
                    </div>
                  )}
                </Thumbnails>
              )}
            </div>

            {/* PDF Viewer Panel */}
            <div style={pagesContainerStyle}>
              <Pages>
                <Page>
                  <CanvasLayer />
                  <TextLayer />
                  <HighlightLayer />
                </Page>
              </Pages>
            </div>
          </div>
        </Root>
      ) : (
        <NonIdealState
          icon="document"
          title="No PDF"
          description="No PDF source available."
        />
      )}
    </div>
  );
};

export default AranaViewer;
