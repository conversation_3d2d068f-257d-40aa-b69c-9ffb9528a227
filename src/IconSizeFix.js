import React, { useEffect } from 'react';

/**
 * This component provides a fix for Blueprint icon sizing issues.
 * It ensures that Blueprint's IconSize constant is available globally,
 * which is required for proper icon rendering in some Blueprint components.
 */
export const IconSizeFix = () => {
  useEffect(() => {
    // Define the icon size constants if they don't already exist
    if (typeof window !== 'undefined' && !window.IconSize) {
      window.IconSize = {
        STANDARD: 16,
        LARGE: 20,
      };
    }
  }, []);

  // This component doesn't render anything
  return null;
};

export default IconSizeFix;
