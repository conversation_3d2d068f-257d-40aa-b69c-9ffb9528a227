import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Position, NavbarDivider } from "@blueprintjs/core";

// This component will be used in the navbar to show AI loading status
const AILoadingIndicator = ({ isLoading }) => {
  if (!isLoading) return null;
  
  return (
    <>
      <Tooltip content="AI analysis in progress" position={Position.BOTTOM}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
          <Spinner size={16} intent="primary" />
          <span style={{ fontSize: '12px', color: '#BD6BBD' }}>AI analyzing...</span>
        </div>
      </Tooltip>
      <NavbarDivider />
    </>
  );
};

export default AILoadingIndicator;
