import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { useVirtualizer } from "@tanstack/react-virtual";
import { useIntersectionObserver } from "@wojtekmaj/react-hooks";
import { Card, Spinner, Button, ButtonGroup, HTMLSelect } from "@blueprintjs/core";
import { Input, Modal } from 'antd';
import { RotateCcw, RotateCw, MousePointer, Hand, ZoomIn, ZoomOut, Search, ChevronDown } from "lucide-react";
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const ZOOM_LEVELS = [0.1, 0.2, 0.3, 0.4, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3, 3.5, 4];
const PAGE_PADDING = 10;
const DEFAULT_HEIGHT = 600;

const PageWithObserver = React.memo(({
  pageNumber,
  scale,
  rotation,
  containerWidth,
  isPanMode,
  customTextRenderer,
  setPageVisibility
}) => {
  const [pageRef, setPageRef] = useState(null);

  const onIntersectionChange = useCallback(
    ([entry]) => {
      setPageVisibility(pageNumber, entry.isIntersecting);
    },
    [pageNumber, setPageVisibility]
  );

  useIntersectionObserver(pageRef, { threshold: [0.1] }, onIntersectionChange);

  return (
    <div 
      ref={setPageRef}
      style={{ 
        marginBottom: `${PAGE_PADDING}px`, 
        display: 'flex', 
        justifyContent: 'center',
        userSelect: isPanMode ? 'none' : 'text',
      }}
    >
      <Page
        pageNumber={pageNumber}
        scale={scale}
        rotate={rotation}
        width={containerWidth}
        renderAnnotationLayer={true}
        renderTextLayer={true}
        customTextRenderer={customTextRenderer}
      />
    </div>
  );
});

const PDFJsViewer = ({ fileId, isDarkTheme }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [containerWidth, setContainerWidth] = useState(0);
  const [isPanMode, setIsPanMode] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [startPanPosition, setStartPanPosition] = useState({ x: 0, y: 0 });
  const [pageVisibilities, setPageVisibilities] = useState({});

  const parentRef = useRef(null);
  const pdfContainerRef = useRef(null);
  const parentClassName = isDarkTheme ? "bp5-dark" : "";

  const setPageVisibility = useCallback((pageNum, isVisible) => {
    setPageVisibilities(prev => ({ ...prev, [pageNum]: isVisible }));
  }, []);

  useEffect(() => {
    const visiblePages = Object.entries(pageVisibilities)
      .filter(([_, isVisible]) => isVisible)
      .map(([pageNum, _]) => parseInt(pageNum));
    
    if (visiblePages.length > 0) {
      setPageNumber(Math.min(...visiblePages));
    }
  }, [pageVisibilities]);

  const estimateSize = useCallback(() => DEFAULT_HEIGHT * scale, [scale]);

  const virtualizer = useVirtualizer({
    count: numPages,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 5,
  });

  const scrollToPage = useCallback((page) => {
    virtualizer.scrollToIndex(page - 1, { align: 'start', behavior: 'smooth' });
  }, [virtualizer]);

  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();

    const fetchFile = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: {
              "session-id": localStorage.getItem("session_id"),
            },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        if (isMounted) {
          const pdfUrl = URL.createObjectURL(response.data);
          setPdfFile(pdfUrl);
          setLoading(false);
        }
      } catch (error) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(error)) {
            console.log("Request canceled:", error.message);
          } else {
            console.error("Failed to fetch the PDF:", error);
            setError("Failed to load the PDF. Please try again.");
          }
        }
      }
    };

    if (fileId) {
      fetchFile();
    }

    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId]);

  useEffect(() => {
    const updateContainerWidth = () => {
      if (pdfContainerRef.current) {
        const newWidth = pdfContainerRef.current.clientWidth;
        setContainerWidth(newWidth);
      }
    };

    const resizeObserver = new ResizeObserver(updateContainerWidth);

    if (pdfContainerRef.current) {
      resizeObserver.observe(pdfContainerRef.current);
    }

    updateContainerWidth();

    return () => resizeObserver.disconnect();
  }, []);

  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    setNumPages(numPages);
    setPageNumber(1);
    setLoading(false);
  }, []);

  const onDocumentLoadError = useCallback((error) => {
    console.error("Error loading PDF:", error);
    setError("Failed to load the PDF. Please check if the file is corrupted or try again.");
    setLoading(false);
  }, []);

  const updateZoom = useCallback((newZoomLevel) => {
    setZoomLevel(newZoomLevel);
    setScale(newZoomLevel);
  }, []);

  const handleZoomChange = useCallback((event) => {
    updateZoom(parseFloat(event.target.value));
  }, [updateZoom]);

  const zoomIn = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex < ZOOM_LEVELS.length - 1) {
      updateZoom(ZOOM_LEVELS[currentIndex + 1]);
    }
  }, [zoomLevel, updateZoom]);

  const zoomOut = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex > 0) {
      updateZoom(ZOOM_LEVELS[currentIndex - 1]);
    }
  }, [zoomLevel, updateZoom]);

  const rotateLeft = useCallback(() => {
    setRotation((prevRotation) => (prevRotation - 90 + 360) % 360);
  }, []);

  const rotateRight = useCallback(() => {
    setRotation((prevRotation) => (prevRotation + 90) % 360);
  }, []);

  const resetView = useCallback(() => {
    updateZoom(1.0);
    setRotation(0);
    setIsPanMode(false);
    setPanPosition({ x: 0, y: 0 });
  }, [updateZoom]);

  const togglePanMode = useCallback(() => {
    setIsPanMode((prev) => !prev);
  }, []);

  const handleSearch = useCallback(async () => {
    if (!pdfFile || !searchQuery) return;

    const pdf = await pdfjs.getDocument(pdfFile).promise;
    const results = [];

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const text = textContent.items.map((item) => item.str).join(' ');

      if (text.toLowerCase().includes(searchQuery.toLowerCase())) {
        results.push(i);
      }
    }

    setSearchResults(results);
    setCurrentSearchIndex(results.length > 0 ? 0 : -1);
    if (results.length > 0) {
      setPageNumber(results[0]);
      scrollToPage(results[0]);
    }
  }, [pdfFile, searchQuery, scrollToPage]);

  const navigateSearch = useCallback((direction) => {
    if (searchResults.length === 0) return;

    const newIndex = direction === 'next'
      ? (currentSearchIndex + 1) % searchResults.length
      : (currentSearchIndex - 1 + searchResults.length) % searchResults.length;

    setCurrentSearchIndex(newIndex);
    setPageNumber(searchResults[newIndex]);
    scrollToPage(searchResults[newIndex]);
  }, [searchResults, currentSearchIndex, scrollToPage]);

  const handleSearchInputChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleMouseDown = useCallback((e) => {
    if (isPanMode) {
      setIsDragging(true);
      setStartPanPosition({ x: e.clientX - panPosition.x, y: e.clientY - panPosition.y });
    }
  }, [isPanMode, panPosition]);

  const handleMouseMove = useCallback((e) => {
    if (isPanMode && isDragging) {
      const newX = e.clientX - startPanPosition.x;
      const newY = e.clientY - startPanPosition.y;
      setPanPosition({ x: newX, y: newY });
    }
  }, [isPanMode, isDragging, startPanPosition]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  const highlightPattern = useCallback((text, pattern) => {
    const splitText = text.split(new RegExp(`(${pattern})`, 'gi'));
    return splitText.map((part, i) => 
      part.toLowerCase() === pattern.toLowerCase() ? 
        <mark key={i} style={{ backgroundColor: '#FFD700' }}>{part}</mark> : part
    );
  }, []);

  if (loading) {
    return (
      <div style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <Spinner intent="primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
        <p>{error}</p>
        <Button onClick={() => window.location.reload()}>Retry</Button>
      </div>
    );
  }

  return (
    <div className={parentClassName} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      <Card
        style={{
          padding: "4px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          background: isDarkTheme ? "#1C2127" : "white",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "10px", flex: "1 1 25%" }}>
          <Button icon={<Search size={16} />} onClick={() => setIsSearchOpen(true)} className={isDarkTheme ? "bp5-dark" : ""} />
          <Input
            placeholder="Search..."
            value={searchQuery}
            onChange={handleSearchInputChange}
            onPressEnter={() => setIsSearchOpen(true)}
            style={{ width: "200px" }}
            className={isDarkTheme ? "bp5-dark" : ""}
          />
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "5px", flex: "1 1 50%", justifyContent: "center" }}>
          <ButtonGroup minimal={true}>
            <Button icon={<RotateCcw size={16} />} onClick={rotateLeft} className={isDarkTheme ? "bp5-dark" : ""} />
            <Button icon={<RotateCw size={16} />} onClick={rotateRight} className={isDarkTheme ? "bp5-dark" : ""} />
          </ButtonGroup>
          <ButtonGroup>
            <Button
              icon={<MousePointer size={16} />}
              onClick={() => setIsPanMode(false)}
              intent={!isPanMode ? "primary" : "none"}
              className={isDarkTheme ? "bp5-dark" : ""}
              minimal={isPanMode}
            />
            <Button
              icon={<Hand size={16} />}
              onClick={togglePanMode}
              intent={isPanMode ? "primary" : "none"}
              className={isDarkTheme ? "bp5-dark" : ""}
              minimal={!isPanMode}
            />
          </ButtonGroup>
          <ButtonGroup minimal={true}>
            <Button icon={<ZoomOut size={16} />} onClick={zoomOut} className={isDarkTheme ? "bp5-dark" : ""} />
            <Button icon={<ZoomIn size={16} />} onClick={zoomIn} className={isDarkTheme ? "bp5-dark" : ""} />
            <HTMLSelect
              options={ZOOM_LEVELS.map(level => ({ label: `${level * 100}%`, value: level }))}
              value={zoomLevel}
              onChange={handleZoomChange}
              style={{ width: '100px' }}
              minimal={true}
              icon={<ChevronDown size={14} />}
              className={isDarkTheme ? "bp5-dark" : ""}
            />
          </ButtonGroup>
          <Button text="Reset" onClick={resetView} minimal={true} small={true} className={isDarkTheme ? "bp5-dark" : ""} />
        </div>
        <div style={{ flex: "1 1 25%", textAlign: "right" }}>
          <span>
            Page {pageNumber} of {numPages}
          </span>
        </div>
      </Card>

      <div
        ref={pdfContainerRef}
        className="pdf-container"
        style={{
          flex: 1,
          overflow: "hidden",
          position: "relative",
        }}
      >
        {pdfFile && (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={<Spinner />}
          >
            <div
              ref={parentRef}
              style={{
                height: "100%",
                width: "100%",
                overflow: "auto",
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
              }}
              onMouseDown={handleMouseDown}
            >
              <div
                style={{
                  height: `${virtualizer.getTotalSize()}px`,
                  width: "100%",
                  position: "relative",
                  transform: `translate(${panPosition.x}px, ${panPosition.y}px)`,
                  cursor: isPanMode ? (isDragging ? 'grabbing' : 'grab') : 'default',
                }}
              >
                {virtualizer.getVirtualItems().map((virtualItem) => (
                  <div
                    key={virtualItem.key}
                    ref={virtualizer.measureElement}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                  >
                    <PageWithObserver
                      pageNumber={virtualItem.index + 1}
                      scale={scale}
                      rotation={rotation}
                      containerWidth={containerWidth}
                      isPanMode={isPanMode}
                      customTextRenderer={(props) => highlightPattern(props.str, searchQuery)}
                      setPageVisibility={setPageVisibility}
                    />
                  </div>
                ))}
              </div>
            </div>
          </Document>
        )}
      </div>

      <Modal
        title="Search PDF"
        open={isSearchOpen}
        onCancel={() => setIsSearchOpen(false)}
        footer={null}
      >
        <Input.Search
          placeholder="Enter search term"
          value={searchQuery}
          onChange={handleSearchInputChange}
          onSearch={handleSearch}
          style={{ marginBottom: "16px" }}
        />
        {searchResults.length > 0 && (
          <div>
            <p>
              Result {currentSearchIndex + 1} of {searchResults.length}
            </p>
            <Button onClick={() => navigateSearch('prev')} style={{ marginRight: "8px" }}>Previous</Button>
            <Button onClick={() => navigateSearch('next')}>Next</Button>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PDFJsViewer;