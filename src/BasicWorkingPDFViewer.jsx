import React, { useState, useEffect } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { Spinner } from '@blueprintjs/core';
import { useDispatch } from 'react-redux';
import { useInView } from 'react-intersection-observer';

// Import required CSS
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

const BasicWorkingPDFViewer = ({ fileId, filePath, isDarkTheme, style }) => {
  // Core state
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Redux and visibility tracking
  const dispatch = useDispatch();
  const { ref, inView } = useInView({ threshold: 0.1 });
  
  // Load PDF file using exact method
  useEffect(() => {
    async function fetchFile() {
      if (hasLoaded) return;
      
      setPdfFile(null);
      setDownloadProgress(0);
      setLoading(true);

      try {
        console.log('Fetching PDF file:', { fileId, filePath });
        
        // Using raw fetch with reader method
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'session-id': localStorage.getItem('session_id'),
          }
        });
        
        if (!response.ok) {
          throw new Error(`Server returned ${response.status} ${response.statusText}`);
        }
        
        // Get content length for progress tracking
        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          chunks.push(value);
          receivedLength += value.length;
          
          if (contentLength) {
            setDownloadProgress(receivedLength / parseInt(contentLength, 10));
          }
        }
        
        const blob = new Blob(chunks);
        const url = URL.createObjectURL(blob);
        setPdfFile(url);
        setHasLoaded(true);
        setLoading(false);
        
        // Update state if needed
        if (dispatch) {
          dispatch({
            type: 'UPDATE_PDF_VIEWER',
            payload: { fileId, isVisible: inView }
          });
        }
      } catch (err) {
        console.error("Error loading PDF:", err);
        setError(err.message);
        setLoading(false);
      }
    }
    
    fetchFile();
    
    return () => {
      if (pdfFile && pdfFile.startsWith('blob:')) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, filePath, dispatch, hasLoaded, inView, pdfFile]);
  
  // Document load handlers
  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    console.log(`PDF loaded with ${numPages} pages`);
  };
  
  // Render all pages in scrollable container
  const renderAllPages = () => {
    const pages = [];
    for (let i = 1; i <= numPages; i++) {
      pages.push(
        <div key={`page-${i}`} style={{
          margin: '10px 0',
          display: 'flex',
          justifyContent: 'center'
        }}>
          <Page 
            pageNumber={i} 
            renderTextLayer={true}
            renderAnnotationLayer={true}
          />
        </div>
      );
    }
    return pages;
  };
  
  // Loading indicator with progress
  const LoadingIndicator = () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column', 
      alignItems: 'center', 
      justifyContent: 'center',
      height: '100%',
      padding: '20px'
    }}>
      <Spinner size={50} />
      {downloadProgress > 0 && (
        <div style={{ width: '80%', marginTop: '20px' }}>
          <div 
            style={{ 
              height: '4px', 
              width: `${downloadProgress * 100}%`, 
              backgroundColor: '#2B95D6', 
              borderRadius: '2px' 
            }} 
          />
          <div style={{ marginTop: '8px', textAlign: 'center' }}>
            {Math.round(downloadProgress * 100)}% downloaded
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div 
      ref={ref}
      style={{ 
        height: '100%', 
        width: '100%', 
        display: 'flex', 
        flexDirection: 'column',
        ...style 
      }}
    >
      <div style={{ 
        flex: 1, 
        overflow: 'auto', 
        padding: '20px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center'
      }}>
        {loading ? (
          <LoadingIndicator />
        ) : error ? (
          <div style={{ 
            color: 'red', 
            padding: '20px', 
            textAlign: 'center' 
          }}>
            <h3>Error Loading PDF</h3>
            <p>{error}</p>
          </div>
        ) : (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={(err) => {
              console.error('Error rendering PDF:', err);
              setError(`Error rendering PDF: ${err.message}`);
            }}
            loading={<LoadingIndicator />}
            options={{
              cMapUrl: 'https://unpkg.com/pdfjs-dist@2.16.105/cmaps/',
              cMapPacked: true
            }}
          >
            {numPages ? renderAllPages() : null}
          </Document>
        )}
      </div>
    </div>
  );
};

export default BasicWorkingPDFViewer;
