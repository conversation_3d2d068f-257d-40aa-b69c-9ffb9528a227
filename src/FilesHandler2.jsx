import React, { useState, useEffect, useRef } from "react";
import { Layout, Model } from "flexlayout-react";
import {
  <PERSON><PERSON>,
  Card,
  Button,
  NonIdealState,
  Icon,
  Dialog,
  Intent,
  MenuItem,
  Spinner,
  Tag,
} from "@blueprintjs/core";
import { Select } from "@blueprintjs/select";
import PDF<PERSON>sViewer from "./PDFJsViewer2";
import UltimatePDFViewer from "./UltimatePDFViewer";
import { isPDFFile } from "./utils/pdfUtils";
import { useSelector, useDispatch } from "react-redux";
import "primereact/resources/themes/fluent-light/theme.css";
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";
import FilesTable from "./FilesTable/FilesTable";
import { updateInspectionData } from "./redux/inspectionDataSlice";
import { useUpdateCell } from "./hooks/useUpdateCell";
import { Divider } from "antd";
import axios from "axios";
import {
  setSelectedFile,
  setCurrentGeneralFile,
  addFileToInspection,
  removeFileFromInspection,
  setCurrentInspectionFilesPath,
} from "./redux/filesHandlerSlice";

const layoutModel = Model.fromJson({
  global: {tabContentClassName: "custom-tab"},
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        weight: 67,
        children: [
          {
            type: "tab",
            component: "mainContent",
            name: "Viewer",
            enableClose: false,
            enableRename: false,
            icons: ["popout", "maximize"],
          },
        ],
      },
      {
        type: "tabset",
        weight: 33,
        children: [
          {
            type: "tab",
            component: "sidebar",
            name: "Files List",
            enableClose: false,
            enableRename: false,
          },
        ],
      },
    ],
  },
});

function FilesHandler({ isDarkTheme, windowMode, ownerDocument, panZoomRef }) {
  const dispatch = useDispatch();
  const {
    selectedFile,
    currentInspectionFilesPath,
    activeModule,
    currentGeneralFile,
  } = useSelector((state) => state.filesHandler);
  const selectedRowData = useSelector(
    (state) => state.selectedRows["inspections-grid"]
  );

  const user = useSelector((state) => state.auth.user);
  const { updateCell, isLoading } = useUpdateCell();
  const [isLinkModalOpen, setIsLinkModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);

  const [isAlertOpen, setIsAlertOpen] = useState(false);

  const lookupValuesDict = useSelector(
    (state) => state.appConfiguration.lookupValuesDict
  );

  const activeTab = useSelector((state) => state.filesHandler.activeTab);
  const inspectionsGridApi = useSelector(
    (state) => state.filesHandler.inspectionsGridApi
  );

  const currentAssetFiles = useSelector(
    (state) => state.filesHandler.currentAssetFiles
  );

  const latestAssetClickParams = useSelector(
    (state) => state.appConfiguration.latestAssetClickParams
  );

  const selectedFileRef = useRef(null);

  useEffect(() => {
    if (activeTab !== "assetFiles") {
      dispatch(setCurrentGeneralFile([]));
    }
  }, [activeTab, dispatch]);

  const onPrimeButtonClick = async () => {
    if (!selectedFile || !selectedFile.path) {
      return;
    }

    if (!selectedRowData) {
      // No row selected on the inspectionsGrid
      return;
    }

    const currentFiles = selectedRowData.Files
      ? JSON.parse(selectedRowData.Files)
      : [];
    const isFilePresent = isFileLinked(selectedFile.path);

    if (isFilePresent) {
      // Remove the file from the selected row
      const updatedFiles = currentFiles.filter(
        (file) => file.path !== selectedFile.path
      );

      try {
        const success = await updateCell(
          "Files",
          JSON.stringify(updatedFiles),
          selectedRowData.rowIndex,
          "Previous Inspections"
        );
        if (success) {
          dispatch(
            updateInspectionData({
              rowIndex: selectedRowData.rowIndex,
              Files: JSON.stringify(updatedFiles),
            })
          );
          await logFileAction("unlink", "PI");
        }
      } catch (error) {
        console.error("Error updating cell:", error);
      }
    } else {
      // Check if the selected file is already present in currentAssetFiles
      const isFileInCurrentAssetFiles = currentAssetFiles.some(
        (file) => file === selectedFile.path
      );

      if (isFileInCurrentAssetFiles) {
        // Show an alert if the file is already associated with the asset
        setIsAlertOpen(true);
      } else {
        // Open the modal to select a category and link the file
        setIsLinkModalOpen(true);
      }
    }
  };

  const handleLinkModalClose = () => {
    setIsLinkModalOpen(false);
    setSelectedCategory(null);
  };

  const handleLinkModalSave = async () => {
    if (selectedFile && selectedRowData) {
      const category =
        selectedCategory ||
        (lookupValuesDict["File Category"] &&
          lookupValuesDict["File Category"][0]);
      if (category) {
        const currentFiles = selectedRowData.Files
          ? JSON.parse(selectedRowData.Files)
          : [];
        const isFilePresent = currentFiles.some(
          (file) => file.path === selectedFile.path
        );
        if (!isFilePresent) {
          const updatedFiles = JSON.stringify([
            ...currentFiles,
            { path: selectedFile.path, category },
          ]);

          setIsLinkModalOpen(false);
          setSelectedCategory(null);

          try {
            const success = await updateCell(
              "Files",
              updatedFiles,
              selectedRowData.rowIndex,
              "Previous Inspections"
            );
            if (success) {
              dispatch(
                updateInspectionData({
                  rowIndex: selectedRowData.rowIndex,
                  Files: updatedFiles,
                })
              );
              await logFileAction("link", "PI", category);
            }
          } catch (error) {
            console.error("Error updating cell:", error);
          }
        }
      }
    }
  };

  const handleFileClick = async (fileId, filename, path) => {
    if (!fileId || !filename || !path) {
      return;
    }

    selectedFileRef.current = { id: fileId, name: filename, path: path };
    dispatch(setSelectedFile({ id: fileId, name: filename, path: path }));
    setSelectedCategory(null); // Reset the selected category
    // Log the view action
    await logFileAction("view", "FT");
  };

  const isFileLinked = (filePath) => {
    if (selectedRowData && selectedRowData.Files) {
      const files = JSON.parse(selectedRowData.Files);
      return files.some((file) => file.path === filePath);
    }
    return false;
  };

  const logFileAction = async (action, module, category = "") => {
    try {
      const payload = {
        action,
        file_path: selectedFileRef.current.path,
        submitter: user.email,
        tag_name: latestAssetClickParams["Tag Name"],
        plant: latestAssetClickParams["Plant"],
        unit: latestAssetClickParams["Unit"],
        system: latestAssetClickParams["System"],
        asset_classification: latestAssetClickParams["Asset Classification"],
        equipment_type: latestAssetClickParams["Equipment Type"],
        module: module,
        category: category,
      };

      console.log("Logging file action:", payload);

      const response = await axios.post(
        `${process.env.REACT_APP_DATA_API}/file_action_tracker/`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            "session-id": localStorage.getItem("session-id"),
          },
        }
      );

      console.log("File action logged successfully:", response.data);
    } catch (error) {
      console.error("Error logging file action:", error);
    }
  };

  const factory = (node) => {
    const component = node.getComponent();
    if (component === "mainContent") {
      return (
        <div style={{ height: "calc(100vh-10px)", }}>
          {selectedFile && selectedFile.id ? (
            <>
              <Card
                style={{
                  height: "36px",
                  paddingTop: "0px",
                  paddingLeft: "10px",
                  paddingRight: "10px",
                  paddingBottom: "0px",
                  display: "flex",
                  alignItems: "center", // Center the items vertically
                  gap: "10px", // Space between items
                }}
              >
                <Tag
                  minimal
                  style={{
                    fontWeight: "bold",
                    flexShrink: 1,
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                  }}
                >
                  {selectedFile.path.split("/").pop()}
                </Tag>
                <Divider type="vertical" />
                <Button
                  icon={<Icon icon="link" />}
                  intent={
                    selectedRowData && isFileLinked(selectedFile.path)
                      ? "warning"
                      : "primary"
                  }
                  onClick={onPrimeButtonClick}
                  style={{
                    display:
                      activeTab !== "previousInspections"
                        ? "none"
                        : "inline-flex",
                  }}
                  className={`link-button ${
                    selectedRowData && isFileLinked(selectedFile.path)
                      ? "my-prime-button-break"
                      : ""
                  }`}
                  disabled={!selectedRowData}
                />
              </Card>
              <Card
                className="no-padding-card"
                style={{ height: "calc(100% - 100px)" }}
              >
                {isPDFFile(selectedFile.name || selectedFile.path) ? (
                  <UltimatePDFViewer
                    fileId={selectedFile.id}
                    filePath={selectedFile.path}
                    fileName={selectedFile.name || selectedFile.path.split("/").pop()}
                    isDarkTheme={isDarkTheme}
                    style={{ padding: "0", height: "100vh" }}
                  />
                ) : (
                  <PDFJsViewer
                    fileId={selectedFile.id}
                    isDarkTheme={isDarkTheme}
                    ownerDocument={ownerDocument}
                    panZoomRef={panZoomRef}
                    style={{ padding: "0", height: "100vh" }}
                  />
                )}
              </Card>
            </>
          ) : (
            <div className="centered-non-ideal" style={{ height: "100%" }}>
              <NonIdealState
                icon="document-open"
                title="No File Selected"
                description="Select a file from the table to be displayed"
              />
            </div>
          )}
        </div>
      );
    } else if (component === "sidebar") {
      return (
        <FilesTable onFileClick={handleFileClick} isDarkTheme={isDarkTheme} />
      );
    }
    return null; // For unrecognized components
  };

  return (
    <div
      className={isDarkTheme ? "bp5-dark" : ""}
      style={{
        position: "relative",
        height: "calc(100% - 5px)",
        width: "100%",
      }}
    >
      {isLoading && (
        <div className="loading-overlay">
          <Spinner />
        </div>
      )}
      <Layout model={layoutModel} factory={factory} />
      <Alert
        className={isDarkTheme ? "bp5-dark" : ""}
        isOpen={isAlertOpen}
        onClose={() => setIsAlertOpen(false)}
        canEscapeKeyCancel={false}
        canOutsideClickCancel={false}
        confirmButtonText="Close"
        icon="issue"
        intent="warning"
        style={{ width: "500px" }}
      >
        <p>
          File --{" "}
          <b>{selectedFile ? selectedFile.path.split("/").pop() : ""}</b> -- is
          already associated with this asset.
        </p>
      </Alert>

      <Dialog
        className={isDarkTheme ? "bp5-dark" : ""}
        isOpen={isLinkModalOpen}
        onClose={() => {
          handleLinkModalClose();
          setSelectedCategory(null);
        }}
        title="Link File"
        intent={Intent.PRIMARY}
      >
        <div className="bp5-dialog-body">
          <Select
            items={lookupValuesDict["File Category"] || []}
            onItemSelect={setSelectedCategory}
            itemRenderer={(item, { handleClick, modifiers }) => (
              <MenuItem
                key={item}
                onClick={handleClick}
                text={item}
                active={modifiers.active}
              />
            )}
            activeItem={
              selectedCategory ||
              (lookupValuesDict["File Category"] &&
                lookupValuesDict["File Category"][0])
            }
          >
            <Button
              text={
                selectedCategory ||
                (lookupValuesDict["File Category"] &&
                  lookupValuesDict["File Category"][0]) ||
                "Select Category"
              }
              rightIcon="caret-down"
            />
          </Select>
        </div>
        <div className="bp5-dialog-footer">
          <div className="bp5-dialog-footer-actions">
            <Button intent="danger" onClick={handleLinkModalClose}>
              Cancel
            </Button>
            <Button intent={Intent.PRIMARY} onClick={handleLinkModalSave}>
              Save
            </Button>
          </div>
        </div>
      </Dialog>
    </div>
  );
}

export default FilesHandler;
