import React, { useContext, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  Navbar,
  NavbarGroup,
  NavbarHeading,
  Alignment,
  Button,
  Icon,
  ProgressBar,
  NonIdealState,
  NavbarDivider,
  Position,
  Popover,
  Menu,
  MenuItem,
  H5,
  H6,
} from "@blueprintjs/core";
import NewWindow from "react-new-window";

import FilesHandler from "./FilesHandler";
import DataCollectionApp from "./DataCollectionApp";
import logo from "./images/VisualAIM_icon.png";
import userIcon from "./images/developer.png";
import "./Styles/App.css";
import { Badge } from "antd";
import {
  setIsFilesHandlerOpen,
  setLayoutModel,
} from "./redux/appConfigurationSlice";
import { Layout, Model } from "flexlayout-react";
import "./Styles/vaim-flexlayout-dark.css";
import "./Styles/vaim-flexlayout-light.css";
import NotificationsPanel from "./NotificationsPanel/NotificationsPanel";
import { AppContext } from "./AppContextProvider";
import { setIsLoggedIn, setUser, setPlant } from "./redux/authSlice";
import { Select } from "@blueprintjs/select";
import { RiDatabase2Line } from "react-icons/ri";

const initialLayoutModel = Model.fromJson({
  global: {},
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        weight: 30,
        children: [
          {
            type: "tab",
            component: "dataCollection",
            name: "Equipment Data",
            enableClose: false,
          },
        ],
      },
      {
        type: "tabset",
        weight: 70,
        children: [
          {
            type: "tab",
            component: "filesHandler",
            name: "Files",
            enableClose: false,
          },
        ],
      },
    ],
  },
});

const collapsedLayoutModel = Model.fromJson({
  global: {},
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        weight: 100,
        children: [
          {
            type: "tab",
            component: "dataCollection",
            name: "Data Collection",
            enableClose: false,
          },
        ],
      },
    ],
  },
});

function MainApp() {
  const dispatch = useDispatch();
  const { isDarkTheme, setIsDarkTheme } = useContext(AppContext);
  const {
    isFilesHandlerOpen,
    layoutModel,
    selectedAsset,
    selectedAssetClass,
    progressValue,
    selectedWord,
    notifications,
  } = useSelector((state) => state.appConfiguration);

  const { user } = useSelector((state) => state.auth);

  const { isProgressBarLoading } = useSelector(
    (state) => state.appConfiguration
  );
  const plant = useSelector((state) => state.auth.plant);

  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [popupRef, setPopupRef] = useState(null);

  const toggleOverlay = () => setIsOverlayOpen(!isOverlayOpen);

  const handleOpenFilesHandler = () => {
    setIsPopupOpen(true);
    dispatch(setIsFilesHandlerOpen(true));
    dispatch(setLayoutModel("collapsed"));
  };

  const onOpen = (nextPopupRef) => {
    setPopupRef(nextPopupRef);
  };

  const onUnload = () => {
    setIsPopupOpen(false);
    setPopupRef(null);
    dispatch(setIsFilesHandlerOpen(false));
  };

  function factory(node) {
    const component = node.getComponent();
    if (component === "dataCollection") {
      return (
        <div style={{ paddingTop: "0px", height: "100%" }}>
          <DataCollectionApp />
        </div>
      );
    } else if (component === "filesHandler") {
      return (
        <div style={{ paddingTop: "0px", height: "100%" }}>
          <FilesHandler
            selectedWord={selectedWord}
            isDarkTheme={isDarkTheme}
            windowMode={false}
          />
        </div>
      );
    }
  }

  const userMenu = (
    <Menu className={isDarkTheme ? "bp5-dark" : ""}>
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text={`User: ${user?.name || ""}`}
        icon="user"
      />
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text="Logout"
        icon="log-out"
        onClick={() => {
          localStorage.removeItem("session_id");
          dispatch(setIsLoggedIn(false));
          dispatch(setUser(null));
        }}
      />
    </Menu>
  );

  const ModeSelect = ({ options, onItemSelect }) => {
    const [selectedMode, setSelectedMode] = useState(options[0]);

    const renderOption = (option, { handleClick, modifiers }) => {
      if (!modifiers.matchesPredicate) {
        return null;
      }
      return (
        <MenuItem
          active={modifiers.active}
          key={option.value}
          onClick={handleClick}
          text={option.label}
        />
      );
    };

    const handleItemSelect = (item) => {
      setSelectedMode(item);
      onItemSelect(item);
    };

    return (
      <Select
        items={options}
        itemRenderer={renderOption}
        onItemSelect={handleItemSelect}
        filterable={false}
      >
        <Button
          minimal={true}
          rightIcon="caret-down"
          style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}
        >
          <H5 style={{ marginTop: '10px' }}>{selectedMode.label}</H5>
        </Button>
      </Select>
    );
  };

  const options = [
    { label: "Collection", value: "Collection" },
    { label: "Validation", value: "Validation" },
    { label: "Off", value: "Off" },
  ];

  const handleModeSelect = (item) => {
    console.log("Selected mode:", item);
    // Perform any additional actions based on the selected mode
  };

  return (
    <div className={`app-container ${isDarkTheme ? "bp5-dark" : ""}`}>
      <Navbar fixedToTop={true} className={`${isDarkTheme ? "bp5-dark" : ""}`}>
        <NavbarGroup align={Alignment.LEFT} style={{ flexGrow: 1 }}>
          <NavbarHeading>
            <div className="heading-container">
              <img src={logo} className="App-logo" alt="logo" width={45} />
            </div>
          </NavbarHeading>
        </NavbarGroup>
        <div
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {isProgressBarLoading ? (
            <div id="MainApp" className="custom-progress-bar">
              <ProgressBar value={100} />
            </div>
          ) : selectedAsset === null ? (
            <NonIdealState title="No Asset Selected" />
          ) : (
            <>
              <h2 style={{ margin: 0, marginRight: "10px", fontWeight: "bold" }}>
                {selectedAsset}
              </h2>
              <span
                className="bp5-tag bp5-intent-primary bp5-large bp5-minimal"
                style={{
                  minWidth: "70px",
                  fontWeight: "bold",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  fontSize: "14px",
                }}
              >
                {selectedAssetClass}
              </span>
            </>
          )}
        </div>
        <NavbarGroup align={Alignment.RIGHT} style={{ flexGrow: 1 }}>
          <H6 style={{ margin: 0, marginRight: "10px" }}>{plant}</H6>
          <Button
            icon={<Icon icon="applications" />}
            minimal={true}
            onClick={handleOpenFilesHandler}
            disabled={isFilesHandlerOpen}
          />
          <NavbarDivider />
          <Badge
            count={notifications?.errors?.length ?? 0}
            dot={true}
            color="#E76A6E"
            offset={[-6, 5]}
          >
            <Button
              role="button"
              icon="notifications"
              minimal={true}
              intent={
                (notifications?.errors?.length ?? 0) > 0 ? "danger" : "none"
              }
              onClick={toggleOverlay}
            />
          </Badge>
          <NavbarDivider />
          <Popover
            content={userMenu}
            position={Position.BOTTOM}
            interactionKind="hover"
            className={isDarkTheme ? "bp5-dark" : ""}
          >
            <div className="circle-image">
              <img src={user?.picture || userIcon} alt="User" />
            </div>
          </Popover>
          <NavbarDivider />
          <Button
            icon={
              <Icon
                icon={isDarkTheme ? "flash" : "moon"}
                style={{ color: isDarkTheme ? "#d39c43" : "#d39c43" }}
              />
            }
            minimal={true}
            onClick={() => setIsDarkTheme(!isDarkTheme)}
          />
          <NavbarDivider />
          <Button icon={<Icon icon="menu" />} minimal={true} />
        </NavbarGroup>
      </Navbar>

      <div className={`main-content ${isDarkTheme ? "" : "light-theme"} ${isDarkTheme ? "bp5-dark" : ""}`}>
        <div className="split-pane-container">
          <Layout
            className="flexlayout__layout-dark"
            model={
              layoutModel === "initial"
                ? initialLayoutModel
                : collapsedLayoutModel
            }
            factory={factory}
          />
        </div>
        <NotificationsPanel
          isOverlayOpen={isOverlayOpen}
          toggleOverlay={toggleOverlay}
          isDarkTheme={isDarkTheme}
          setIsOverlayOpen={setIsOverlayOpen}
          notifications={notifications}
        />
      </div>

      {isPopupOpen && (
        <NewWindow onOpen={onOpen} onUnload={onUnload}>
          <FilesHandler isDarkTheme={isDarkTheme} windowMode={true} />
        </NewWindow>
      )}
    </div>
  );
}

export default MainApp;
