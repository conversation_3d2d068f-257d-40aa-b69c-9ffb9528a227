import React, { useEffect, useState, useRef } from 'react';
import { Spin<PERSON>, SpinnerSize } from "@blueprintjs/core";
import { useDispatch } from 'react-redux';
import { useInView } from 'react-intersection-observer';
import "@blueprintjs/core/lib/css/blueprint.css";
import { updatePdfViewer, setViewerVisibility, setFocusedViewer } from './redux/currentPdfSlice';

/**
 * AnnotatablePDFViewer - A PDF viewer component with advanced annotation capabilities.
 * This component integrates the PDF.js viewer with the pdfjs-annotation-extension to 
 * provide comprehensive annotation tools.
 * 
 * Features:
 * - Rectangle, Circle, Free Hand drawing
 * - Free Highlight with auto-correction
 * - FreeText, Signature, and custom Stamp support
 * - Text Highlight, Strikeout, and Underline
 * - Annotation selection and editing
 * - Mobile support for annotations
 * - Loading and saving annotations
 */
export default function AnnotatablePDFViewer({ 
  fileId, 
  filePath, 
  isDarkTheme, 
  username = "user",
  getAnnotationsUrl = "",
  saveAnnotationsUrl = "",
  enableEditing = true
}) {
  const dispatch = useDispatch();
  const [pdfFile, setPdfFile] = useState(null);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [hasLoaded, setHasLoaded] = useState(false);
  const iframeRef = useRef(null);
  const [viewerReady, setViewerReady] = useState(false);
  const pendingPageNavigation = useRef(null);
  
  const { ref, inView } = useInView({
    threshold: 0.1,
  });

  // Function to navigate to a specific page
  const navigateToPage = (pageNumber) => {
    if (iframeRef.current && iframeRef.current.contentWindow) {
      try {
        console.log(`Attempting to navigate PDF to page ${pageNumber}`);
        
        // Send a message to the iframe
        iframeRef.current.contentWindow.postMessage({
          type: 'setPage',
          pageNumber: parseInt(pageNumber, 10),
          timestamp: Date.now(),
          action: 'forceNavigate'
        }, '*');
        
        // Update Redux state
        dispatch(updatePdfViewer({
          fileId,
          page: parseInt(pageNumber, 10),
          filePath,
          isVisible: inView
        }));
        
        console.log(`Successfully sent page navigation message for page ${pageNumber}`);
      } catch (err) {
        console.error('Error sending page navigation to iframe:', err);
      }
    } else {
      console.warn('PDF iframe not ready for navigation');
    }
  };

  // Listen for custom events for page navigation
  useEffect(() => {
    const handlePageNavigation = (event) => {
      if (event.detail && event.detail.pageNumber) {
        console.log('Received page navigation event:', event.detail);
        const pageNumber = parseInt(event.detail.pageNumber, 10);
        
        if (viewerReady) {
          navigateToPage(pageNumber);
        } else {
          console.log('Viewer not ready, storing pending navigation to page:', pageNumber);
          pendingPageNavigation.current = pageNumber;
        }
      }
    };
    
    window.addEventListener('pdf-page-navigation', handlePageNavigation);
    
    // Also listen for window messages for cross-frame communication
    const handleWindowMessage = (event) => {
      if (event.data && event.data.type === 'setPage') {
        console.log('Received postMessage for page navigation:', event.data);
        const pageNumber = parseInt(event.data.pageNumber, 10);
        
        if (viewerReady) {
          navigateToPage(pageNumber);
        } else {
          console.log('Viewer not ready, storing pending navigation to page:', pageNumber);
          pendingPageNavigation.current = pageNumber;
        }
      }
    };
    
    window.addEventListener('message', handleWindowMessage);
    
    return () => {
      window.removeEventListener('pdf-page-navigation', handlePageNavigation);
      window.removeEventListener('message', handleWindowMessage);
    };
  }, [viewerReady]);

  // Fetch the PDF file
  useEffect(() => {
    async function fetchFile() {
      if (hasLoaded) return;
      
      setPdfFile(null);
      setDownloadProgress(0);

      try {
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          if (contentLength) {
            setDownloadProgress(receivedLength / contentLength);
          }
        }

        const blob = new Blob(chunks);
        const pdfUrl = URL.createObjectURL(blob);
        setPdfFile(pdfUrl);
        setHasLoaded(true);

        dispatch(updatePdfViewer({
          fileId,
          filePath,
          page: 1,
          isVisible: inView
        }));

        console.log('PDF Loaded:', {
          fileId,
          filePath,
          initialPage: 1,
          isVisible: inView
        });
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
      }
    }

    fetchFile();

    return () => {
      if (pdfFile && !hasLoaded) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, filePath, dispatch, hasLoaded, inView, pdfFile]);

  // Listen for messages from the PDF viewer
  useEffect(() => {
    const handleMessage = (event) => {
      if (event.data.type === 'pdfState' && inView) {
        const { action, data } = event.data;
        
        switch (action) {
          case 'pagesloaded':
            dispatch(updatePdfViewer({
              fileId,
              page: data.page,
              filePath,
              isVisible: inView
            }));
            break;

          case 'pagechanging':
            dispatch(updatePdfViewer({
              fileId,
              page: data.page,
              scale: data.scale,
              scrollTop: data.scrollTop,
              filePath,
              isVisible: inView
            }));
            break;
            
          case 'scalechanging':
            dispatch(updatePdfViewer({
              fileId,
              scale: data.scale,
              scrollTop: data.scrollTop,
              filePath,
              isVisible: inView
            }));
            break;
            
          default:
            console.log('Unhandled PDF.js action:', action);
            break;
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [dispatch, fileId, filePath, inView]);

  // Update visibility state
  useEffect(() => {
    dispatch(setViewerVisibility({ fileId, isVisible: inView }));
    
    if (inView) {
      dispatch(setFocusedViewer(fileId));
    }
  }, [inView, fileId, dispatch]);

  // Listen for iframe ready message
  useEffect(() => {
    const handleIframeMessage = (event) => {
      if (event.data && event.data.source === 'pdfjs') {
        if (event.data.type === 'viewerReady') {
          console.log('PDF.js viewer is now ready');
          setViewerReady(true);
          
          // Execute pending page navigation if any
          if (pendingPageNavigation.current !== null) {
            const pageNumber = pendingPageNavigation.current;
            console.log('Executing pending navigation to page:', pageNumber);
            navigateToPage(pageNumber);
            pendingPageNavigation.current = null;
          }
        }
      }
    };
    
    window.addEventListener('message', handleIframeMessage);
    return () => {
      window.removeEventListener('message', handleIframeMessage);
    };
  }, []);

  // Build URL parameters for annotation extension
  const buildUrlParams = () => {
    const params = [];
    
    // Add username parameter
    if (username) {
      params.push(`ae_username=${encodeURIComponent(username)}`);
    }
    
    // Add annotation get URL if provided
    if (getAnnotationsUrl) {
      params.push(`ae_get_url=${encodeURIComponent(getAnnotationsUrl)}`);
    }
    
    // Add annotation save URL if provided
    if (saveAnnotationsUrl) {
      params.push(`ae_post_url=${encodeURIComponent(saveAnnotationsUrl)}`);
    }
    
    return params.length > 0 ? '#' + params.join('&') : '';
  };

  if (!pdfFile) {
    return (
      <div className="spinner-container">
        <Spinner
          className="spinner-center"
          intent="primary"
          size={SpinnerSize.STANDARD}
          value={downloadProgress}
        />
      </div>
    );
  }

  // Build the viewer URL with annotation parameters
  const viewerUrl = `${process.env.PUBLIC_URL}/pdfjs-dist/web/viewer.html?file=${encodeURIComponent(pdfFile)}${buildUrlParams()}`;

  return (
    <div ref={ref} id={`pdf-viewer-${fileId}`} style={{height: "100%", display: "flex", flexDirection: "column"}}>
      <div style={{flex: 1, position: "relative"}}>
        <iframe
          ref={iframeRef}
          title={`PDF Viewer for ${filePath}`}
          width="100%"
          height="100%"
          src={viewerUrl}
          className={isDarkTheme ? "dark-theme" : "light-theme"}
          style={{ border: "none" }}
          onLoad={(event) => {
            const iframeDocument = event.target.contentDocument || event.target.contentWindow.document;
            
            // Add custom CSS for better viewer appearance
            const customStyle = iframeDocument.createElement('style');
            customStyle.innerHTML = `
              #viewerContainer {
                overflow-y: scroll !important;
                padding: 40px 0 !important;
                position: absolute !important;
                top: 32px !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
              }
              #viewer {
                margin: 0 !important;
                padding: 0 !important;
                position: relative !important;
              }
              .pdfViewer .page {
                margin: 1px auto !important;
                border: none !important;
              }
              .pdfViewer .page:first-child {
                margin-top: 0 !important;
              }
              .pdfViewer .page:last-child {
                margin-bottom: 40px !important;
              }
              #mainContainer {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
                overflow: hidden !important;
              }
              #toolbarContainer {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                height: 32px !important;
                z-index: 2 !important;
                background: var(--background-color) !important;
              }
              #outerContainer {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                bottom: 0 !important;
              }
              
              /* Annotation extension styles */
              .annotation-toolbar {
                display: flex;
                align-items: center;
                background-color: var(--toolbar-bg-color) !important;
                color: var(--main-color) !important;
              }
              .annotation-toolbar button {
                color: var(--main-color) !important;
              }
              .annotation-toolbar button.active {
                background-color: var(--button-hover-color) !important;
              }
            `;
            iframeDocument.head.appendChild(customStyle);
            
            // Set theme
            setThemeInIframe(iframeDocument, isDarkTheme ? 'Dark' : 'Light');
            
            // Add event listeners for page changes
            setupPageChangeListener(iframeDocument, filePath);

            // Initialize annotation extension if needed
            initializeAnnotationExtension(iframeDocument);
          }}
        />
      </div>
    </div>
  );
}

// Function to set the theme in the iframe
function setThemeInIframe(iframeDocument, theme) {
  const setThemeFunctionString = `
    function setTheme(theme) {
      if (theme == 'Dark') {
        PDFViewerApplicationOptions.set('viewerCssTheme', 1);
      }
      else {
        PDFViewerApplicationOptions.set('viewerCssTheme', 2);
      }
      PDFViewerApplication._forceCssTheme();
    }
  `;

  const script = iframeDocument.createElement('script');
  script.textContent = setThemeFunctionString;
  iframeDocument.head.appendChild(script);

  iframeDocument.defaultView.setTheme(theme);
}

// Function to set up page change listener
function setupPageChangeListener(iframeDocument, filePath) {
  const script = iframeDocument.createElement('script');
  script.textContent = `
    function setupPageChangeTracking() {
      if (typeof PDFViewerApplication !== 'undefined') {
        PDFViewerApplication.eventBus.on('pagesloaded', function(evt) {
          console.log('PDF Pages Loaded:', {
            filePath: "${filePath}",
            totalPages: PDFViewerApplication.pagesCount,
            currentPage: PDFViewerApplication.page
          });

          window.parent.postMessage({
            type: 'pdfState',
            action: 'pagesloaded',
            data: {
              page: PDFViewerApplication.page,
              totalPages: PDFViewerApplication.pagesCount
            }
          }, '*');
        });

        PDFViewerApplication.eventBus.on('pagechanging', function(evt) {
          const pageNumber = evt.pageNumber;
          console.log('Page Changing:', {
            filePath: "${filePath}",
            previousPage: PDFViewerApplication.page,
            newPage: pageNumber,
            totalPages: PDFViewerApplication.pagesCount
          });
          
          window.parent.postMessage({
            type: 'pdfState',
            action: 'pagechanging',
            data: {
              page: pageNumber,
              scale: PDFViewerApplication.pdfViewer.currentScale,
              scrollTop: document.getElementById('viewerContainer').scrollTop
            }
          }, '*');
        });

        PDFViewerApplication.eventBus.on('scalechanging', function(evt) {
          window.parent.postMessage({
            type: 'pdfState',
            action: 'scalechanging',
            data: {
              scale: evt.scale,
              scrollTop: document.getElementById('viewerContainer').scrollTop
            }
          }, '*');
        });
      }
    }

    if (typeof PDFViewerApplication !== 'undefined') {
      PDFViewerApplication.initializedPromise.then(setupPageChangeTracking);
    } else {
      document.addEventListener('webviewerloaded', function() {
        PDFViewerApplication.initializedPromise.then(setupPageChangeTracking);
      });
    }
  `;
  iframeDocument.head.appendChild(script);
}

// Initialize the annotation extension features
function initializeAnnotationExtension(iframeDocument) {
  const script = iframeDocument.createElement('script');
  script.textContent = `
    function initAnnotationExtension() {
      if (typeof PDFViewerApplication !== 'undefined') {
        // Set annotationMode to 0 to enable editing PDF annotations
        if (PDFViewerApplicationOptions && PDFViewerApplicationOptions.set) {
          PDFViewerApplicationOptions.set('annotationMode', 0);
        }
        
        // Let annotation extension know the PDF is ready
        if (typeof window.PDFJSAnnotationExtension !== 'undefined') {
          console.log('Notifying annotation extension that PDF is ready');
          const readyEvent = new CustomEvent('pdf-viewer-ready', {
            detail: { pdfViewerApplication: PDFViewerApplication }
          });
          document.dispatchEvent(readyEvent);
        } else {
          console.log('Annotation extension not found, waiting for it to load...');
          // Wait for annotation extension to load
          const checkInterval = setInterval(() => {
            if (typeof window.PDFJSAnnotationExtension !== 'undefined') {
              clearInterval(checkInterval);
              console.log('Annotation extension loaded, notifying that PDF is ready');
              const readyEvent = new CustomEvent('pdf-viewer-ready', {
                detail: { pdfViewerApplication: PDFViewerApplication }
              });
              document.dispatchEvent(readyEvent);
            }
          }, 200);
          
          // Stop checking after 10 seconds
          setTimeout(() => clearInterval(checkInterval), 10000);
        }
      }
    }

    // Initialize when PDF viewer is ready
    if (typeof PDFViewerApplication !== 'undefined') {
      PDFViewerApplication.initializedPromise.then(initAnnotationExtension);
    } else {
      document.addEventListener('webviewerloaded', function() {
        PDFViewerApplication.initializedPromise.then(initAnnotationExtension);
      });
    }
  `;
  iframeDocument.head.appendChild(script);
}
