import { configureStore } from "@reduxjs/toolkit";
import filesHandlerReducer from "./filesHandlerSlice";
import appConfigurationsReducer from "./appConfigurationSlice";
import authReducer from "./authSlice";
import postsReducer from "./postsSlice";
import inspectionDataReducer from './inspectionDataSlice';
import selectedRowsReducer from './selectedRowsSlice';
import currentPdfReducer from './currentPdfSlice';
import pdfViewerReducer from './pdfViewerSlice';
import selectedAssetPropertiesReducer from './selectedAssetPropertiesSlice';
import componentsGridReducer from './componentsGridSlice';
import actionHistoryReducer from './actionHistorySlice';
import ultimatePdfViewerReducer from './ultimatePdfViewerSlice';

export default configureStore({
  reducer: {
    auth: authReducer,
    filesHandler: filesHandlerReducer,
    appConfiguration: appConfigurationsReducer,
    posts: postsReducer,
    inspectionData: inspectionDataReducer,
    selectedRows: selectedRowsReducer,
    currentPdf: currentPdfReducer,
    pdfViewer: pdfViewerReducer,
    selectedAssetProperties: selectedAssetPropertiesReducer,
    componentsGrid: componentsGridReducer,
    actionHistory: actionHistoryReducer,
    ultimatePdfViewer: ultimatePdfViewerReducer,
  },
});