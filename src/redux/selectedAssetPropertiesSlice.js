import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  rowIndices: [],
  rows: [],

};

const selectedAssetPropertiesSlice = createSlice({
  name: 'selectedAssetProperties',
  initialState,
  reducers: {
    setDataRows: (state, action) => {
      state.rowIndices = action.payload.rowIndices;
      state.rows = action.payload.rows;
    },
    addDataRow: (state, action) => {
      state.rowIndices.push(action.payload.index);
      state.rows.push(action.payload.row);
    },
    removeDataRow: (state, action) => {
      const indexToRemove = state.rowIndices.indexOf(action.payload);
      if (indexToRemove !== -1) {
        state.rowIndices.splice(indexToRemove, 1);
        state.rows.splice(indexToRemove, 1);
      }
    },
    clearDataRows: (state) => {
      state.rowIndices = [];
      state.rows = [];
    },
    setQCVOwner: (state, action) => {
      state.QCV_Owner = action.payload;
    },
  },
});

export const { setDataRows, addDataRow, removeDataRow, clearDataRows, setQCVOwner } = selectedAssetPropertiesSlice.actions;

export default selectedAssetPropertiesSlice.reducer;