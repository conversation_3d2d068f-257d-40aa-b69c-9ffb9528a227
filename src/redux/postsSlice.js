import { createSlice } from "@reduxjs/toolkit";

const postsSlice = createSlice({
  name: "posts",
  initialState: {
    postIds: [],
    posts: [],
    postsIndices: [],
    isLoading: false,
    error: null,
    shouldRefetch: false,
  },
  reducers: {
    setPostIds: (state, action) => {
      state.postIds = action.payload;
      state.shouldRefetch = true;
    },
    setPostsIndices: (state, action) => {
      state.postsIndices = action.payload
    },
    setPostsLoading: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    setPostsSuccess: (state, action) => {
      state.posts = action.payload;
      state.isLoading = false;
      state.error = null;
      state.shouldRefetch = false;
    },
    setPostsError: (state, action) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    addPost: (state, action) => {
      const newPost = action.payload;
      state.posts.unshift({ ...newPost, replies: [] });
      state.postIds.unshift(newPost.post_id);
    },
    removePost: (state, action) => {
      const postId = action.payload;
      state.posts = state.posts.filter((post) => post.id !== postId);
      state.postIds = state.postIds.filter((id) => id !== postId);
    },
    clearPosts: (state) => {
      state.posts = [];
    },
    addReply: (state, action) => {
      const { postId, reply } = action.payload;
      const post = state.posts.find((post) => post.id === postId);
      if (post) {
        post.replies.push(reply);
      }
    },
    removeReply: (state, action) => {
      const { postId, replyId } = action.payload;
      const post = state.posts.find((post) => post.id === postId);
      if (post) {
        post.replies = post.replies.filter((reply) => reply.id !== replyId);
      }
    },
    updatePostStatus: (state, action) => {
      const { postId, status } = action.payload;
      const post = state.posts.find((post) => post.id === postId);
      if (post) {
        post.status = status;
      }
    },
  },
});

export const {
  setPostIds,
  setPostsIndices,
  setPostsLoading,
  setPostsSuccess,
  setPostsError,
  addPost,
  removePost,
  clearPosts,
  addReply,
  removeReply,
  updatePostStatus,
} = postsSlice.actions;

export default postsSlice.reducer;