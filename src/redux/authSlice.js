import { createSlice } from "@reduxjs/toolkit";

const authSlice = createSlice({
  name: "auth",
  initialState: {
    isLoggedIn: false,
    user: null,
    plant: null,
  },
  reducers: {
    setIsLoggedIn: (state, action) => {
      state.isLoggedIn = action.payload;
    },
    setUser: (state, action) => {
      state.user = action.payload;
    },
    setUserPicture: (state, action) => {
      state.user.picture = action.payload;
    },
    setUserName: (state, action) => {
      state.user.name = action.payload;
    },
    setUserEmail: (state, action) => {
      state.user.email = action.payload;
    },
    setPlant: (state, action) => {
      state.plant = action.payload;
    }
  },
});

export const {
  setIsLoggedIn,
  setUser,
  setUserPicture,
  setUserName,
  setUserEmail,
  setPlant
} = authSlice.actions;

export default authSlice.reducer;