import { createSlice } from '@reduxjs/toolkit';

export const selectedRowsSlice = createSlice({
  name: 'selectedRows',
  initialState: {
    'inspections-grid': null,
  },
  reducers: {
    setSelectedRow: (state, action) => {
      const { gridId, rowData } = action.payload;
      state[gridId] = rowData;
    },
    clearSelectedRow: (state, action) => {
      const { gridId } = action.payload;
      state[gridId] = null;
    },
  },
});

export const { setSelectedRow, clearSelectedRow } = selectedRowsSlice.actions;
export default selectedRowsSlice.reducer;