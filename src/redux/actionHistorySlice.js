import { createSlice } from '@reduxjs/toolkit';

const initialState = {
    actions: [], // Will store the last 20 actions
    isHistoryOpen: false // Controls modal visibility
};

export const actionHistorySlice = createSlice({
    name: 'actionHistory',
    initialState,
    reducers: {
        addAction: (state, action) => {
            const newAction = {
                id: Date.now(), // Unique identifier
                timestamp: new Date().toLocaleString(),
                type: action.payload.type, // 'SUCCESS', 'WARNING', 'DANGER'
                message: action.payload.message,
                source: action.payload.source // Component/file that generated the action
            };
            
            state.actions.unshift(newAction);
            
            // Keep only last 20 actions
            if (state.actions.length > 20) {
                state.actions.pop();
            }
        },
        clearHistory: (state) => {
            state.actions = [];
        },
        setHistoryOpen: (state, action) => {
            state.isHistoryOpen = action.payload;
        }
    }
});

export const { addAction, clearHistory, setHistoryOpen } = actionHistorySlice.actions;

// Selectors
export const selectActions = (state) => state.actionHistory.actions;
export const selectIsHistoryOpen = (state) => state.actionHistory.isHistoryOpen;

export default actionHistorySlice.reducer; 