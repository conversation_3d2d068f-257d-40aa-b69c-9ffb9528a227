import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  pdfFile: null,
  numPages: 0,
  pageNumber: 1,
  scale: 1,
  rotation: 0,
  searchQuery: '',
  searchResults: [],
  currentSearchIndex: -1,
};

const pdfViewerSlice = createSlice({
  name: 'pdfViewer',
  initialState,
  reducers: {
    setPdfFile: (state, action) => {
      state.pdfFile = action.payload;
    },
    setNumPages: (state, action) => {
      state.numPages = action.payload;
    },
    setPageNumber: (state, action) => {
      state.pageNumber = action.payload;
    },
    setScale: (state, action) => {
      state.scale = action.payload;
    },
    setRotation: (state, action) => {
      state.rotation = action.payload;
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    setSearchResults: (state, action) => {
      state.searchResults = action.payload;
    },
    setCurrentSearchIndex: (state, action) => {
      state.currentSearchIndex = action.payload;
    },
  },
});

export const {
  setPdfFile,
  setNumPages,
  setPageNumber,
  setScale,
  setRotation,
  setSearchQuery,
  setSearchResults,
  setCurrentSearchIndex,
} = pdfViewerSlice.actions;

export default pdfViewerSlice.reducer;