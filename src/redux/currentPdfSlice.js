import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  viewers: {},
  focusedViewer: null,
  visibleViewers: [],
};

const currentPdfSlice = createSlice({
  name: "currentPdf",
  initialState,
  reducers: {
    updatePdfViewer: (state, action) => {
      const { fileId, ...updates } = action.payload;
      state.viewers[fileId] = { ...state.viewers[fileId], ...updates };
    },
    setViewerVisibility: (state, action) => {
      const { fileId, isVisible } = action.payload;
      if (state.viewers[fileId]) {
        state.viewers[fileId].isVisible = isVisible;
        if (isVisible) {
          if (!state.visibleViewers.includes(fileId)) {
            state.visibleViewers.push(fileId);
          }
        } else {
          state.visibleViewers = state.visibleViewers.filter(id => id !== fileId);
        }
      }
    },
    removePdfViewer: (state, action) => {
      const { fileId } = action.payload;
      delete state.viewers[fileId];
      state.visibleViewers = state.visibleViewers.filter(id => id !== fileId);
      if (state.focusedViewer === fileId) {
        state.focusedViewer = null;
      }
    },
    setFocusedViewer: (state, action) => {
      state.focusedViewer = action.payload;
    },
    clearAllViewers: (state) => {
      state.viewers = {};
      state.visibleViewers = [];
      state.focusedViewer = null;
    },
  },
});

export const {
  updatePdfViewer,
  setViewerVisibility,
  removePdfViewer,
  setFocusedViewer,
  clearAllViewers,
} = currentPdfSlice.actions;

export default currentPdfSlice.reducer;

// Selector to get information about visible PDFs
export const selectVisiblePDFs = (state) => {
  return state.currentPdf.visibleViewers.map(fileId => ({
    fileId,
    filePath: state.currentPdf.viewers[fileId].filePath,
    page: state.currentPdf.viewers[fileId].page,
  }));
};