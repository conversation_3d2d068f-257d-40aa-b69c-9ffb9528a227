import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  inspectionData: [],
};

export const inspectionDataSlice = createSlice({
  name: 'inspectionData',
  initialState,
  reducers: {
    setInspectionData: (state, action) => {
      state.inspectionData = action.payload;
    },
    updateInspectionData: (state, action) => {
      const updatedData = action.payload;
      const index = state.inspectionData.findIndex(
        (item) => item.rowIndex === updatedData.rowIndex
      );
      if (index !== -1) {
        state.inspectionData[index] = {
          ...state.inspectionData[index],
          ...updatedData,
        };
      } else {
        state.inspectionData.push(updatedData);
      }
    },
  },
});

export const { setInspectionData, updateInspectionData } = inspectionDataSlice.actions;
export default inspectionDataSlice.reducer;