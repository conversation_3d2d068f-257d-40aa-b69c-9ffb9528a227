import { createSlice } from "@reduxjs/toolkit";

const appConfigurationSlice = createSlice({
  name: "appConfiguration",
  initialState: {
    isFilesHandlerOpen: false,
    layoutModel: "initial",
    lookupValuesDict: {},
    appUsers: [],
    selectedAsset: null,
    selectedAssetSearchKey:'',
    selectedAssetClass: '',
    isProgressBarLoading: false,
    progressValue: 0,
    selectedWord: null,
    notifications: [],
    latestAssetClickParams: {},
    QCV_Owner: '',
    // Add the ownerDict to hold OpenAssetOwner, OpenAssetQCOwner, and OpenAssetQCVOwner
    ownerDict: {
      collector: null, // OpenAssetOwner
      qc: null,        // OpenAssetQCOwner
      qcv: null        // OpenAssetQCVOwner
    },
    isGenAILoading: false, // Add this new state for GenAI loading
  },
  reducers: {
    setIsFilesHandlerOpen: (state, action) => {
      state.isFilesHandlerOpen = action.payload;
    },
    setLayoutModel: (state, action) => {
      state.layoutModel = action.payload;
    },
    setLookupValuesDict: (state, action) => {
      state.lookupValuesDict = action.payload;
    },
    setAppUsers: (state, action) => {
      state.appUsers = action.payload;
    },
    setSelectedAsset: (state, action) => {
      state.selectedAsset = action.payload;
    },
    setSelectedAssetSearchKey: (state, action) => {
      state.selectedAssetSearchKey = action.payload;
    },
    setSelectedAssetClass: (state, action) => {
      state.selectedAssetClass = action.payload;
    },
    setIsProgressBarLoading: (state, action) => {
      state.isProgressBarLoading = action.payload;
    },
    setProgressValue: (state, action) => {
      state.progressValue = action.payload;
    },
    setSelectedWord: (state, action) => {
      state.selectedWord = action.payload;
    },
    setNotifications: (state, action) => {
      state.notifications = action.payload;
    },
    setLatestAssetClickParams: (state, action) => {
      state.latestAssetClickParams = {
        ...state.latestAssetClickParams,
        ...action.payload,
      };
    },
    setQCVOwner: (state, action) => {
      state.QCV_Owner = action.payload;
    },
    // Reducer to set the ownerDict values
    setOwnerDict: (state, action) => {
      state.ownerDict = {
        ...state.ownerDict,
        ...action.payload
      };
    },
    setIsGenAILoading: (state, action) => {
      state.isGenAILoading = action.payload;
    },
  },
});

export const {
  setIsFilesHandlerOpen,
  setLayoutModel,
  setLookupValuesDict,
  setAppUsers,
  setSelectedAsset,
  setSelectedAssetSearchKey,
  setSelectedAssetClass,
  setIsProgressBarLoading,
  setProgressValue,
  setSelectedWord,
  setNotifications,
  setLatestAssetClickParams,
  setOwnerDict,
  setQCVOwner,
  setIsGenAILoading, // Export the new setOwnerDict action
} = appConfigurationSlice.actions;

export default appConfigurationSlice.reducer;
