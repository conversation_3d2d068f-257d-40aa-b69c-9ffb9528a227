import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  columnState: null,
  filterState: null,
  sortState: null,
};

const componentsGridSlice = createSlice({
  name: 'componentsGrid',
  initialState,
  reducers: {
    updateGridState: (state, action) => {
      return {
        ...state,
        ...action.payload
      };
    },
  }
});

export const { updateGridState } = componentsGridSlice.actions;
export default componentsGridSlice.reducer;