import { createSlice, createSelector } from '@reduxjs/toolkit';

// Annotation types
export const ANNOTATION_TYPES = {
  TEXT_HIGHLIGHT: 'text_highlight',
  RECTANGLE: 'rectangle',
  COMMENT: 'comment',
  AREA_HIGHLIGHT: 'area_highlight',
  DRAWING: 'drawing'
};

// Annotation colors
export const ANNOTATION_COLORS = {
  YELLOW: '#FFEB3B',
  GREEN: '#4CAF50',
  BLUE: '#2196F3',
  RED: '#F44336',
  PURPLE: '#9C27B0',
  ORANGE: '#FF9800'
};

const initialState = {
  // PDF Document Management
  documents: {}, // fileId -> document state
  activeDocumentId: null,
  
  // Viewer State
  viewerSettings: {
    theme: 'dark',
    defaultZoom: 1.0,
    fitToWidth: true,
    showThumbnails: true,
    showAnnotations: true,
    enableVirtualization: true,
    pageSpacing: 10,
    enableSearch: true,
    enableTextSelection: true
  },
  
  // Annotations Management
  annotations: {}, // fileId -> annotations array
  selectedAnnotations: [], // array of annotation IDs
  annotationFilters: {
    types: Object.values(ANNOTATION_TYPES),
    colors: Object.values(ANNOTATION_COLORS),
    authors: [],
    dateRange: null,
    searchQuery: ''
  },
  
  // UI State
  ui: {
    isAnnotationExplorerOpen: true,
    isThumbnailPanelOpen: true,
    isSearchPanelOpen: false,
    isPropertiesPanelOpen: false,
    annotationExplorerWidth: 300,
    thumbnailPanelWidth: 200,
    selectedTool: 'select', // select, highlight, rectangle, comment, drawing
    isFullscreen: false,
    showGrid: false,
    showRulers: false
  },
  
  // Performance & Loading
  loading: {}, // fileId -> boolean
  errors: {}, // fileId -> error message
  
  // Search State
  search: {
    query: '',
    results: [], // array of search results with page numbers and positions
    currentResultIndex: -1,
    isSearching: false,
    caseSensitive: false,
    wholeWords: false
  },
  
  // Collaboration (future feature)
  collaboration: {
    isEnabled: false,
    connectedUsers: [],
    realtimeAnnotations: {}
  }
};

const ultimatePdfViewerSlice = createSlice({
  name: 'ultimatePdfViewer',
  initialState,
  reducers: {
    // Document Management
    setActiveDocument: (state, action) => {
      state.activeDocumentId = action.payload;
    },
    
    addDocument: (state, action) => {
      const { fileId, filePath, fileName, numPages } = action.payload;
      state.documents[fileId] = {
        fileId,
        filePath,
        fileName,
        numPages,
        currentPage: 1,
        zoom: state.viewerSettings.defaultZoom,
        rotation: 0,
        scrollPosition: { x: 0, y: 0 },
        lastViewed: Date.now(),
        bookmarks: [],
        metadata: {}
      };
      
      // Initialize annotations array for this document
      if (!state.annotations[fileId]) {
        state.annotations[fileId] = [];
      }
    },
    
    removeDocument: (state, action) => {
      const fileId = action.payload;
      delete state.documents[fileId];
      delete state.annotations[fileId];
      delete state.loading[fileId];
      delete state.errors[fileId];
      
      if (state.activeDocumentId === fileId) {
        state.activeDocumentId = null;
      }
    },
    
    updateDocumentState: (state, action) => {
      const { fileId, updates } = action.payload;
      if (state.documents[fileId]) {
        state.documents[fileId] = { ...state.documents[fileId], ...updates };
      }
    },
    
    // Viewer Settings
    updateViewerSettings: (state, action) => {
      state.viewerSettings = { ...state.viewerSettings, ...action.payload };
    },
    
    // Annotations Management
    addAnnotation: (state, action) => {
      const { fileId, annotation } = action.payload;
      const newAnnotation = {
        id: `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: annotation.type,
        pageNumber: annotation.pageNumber,
        position: annotation.position,
        content: annotation.content,
        color: annotation.color || ANNOTATION_COLORS.YELLOW,
        author: annotation.author || 'Anonymous',
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isVisible: true,
        ...annotation
      };
      
      if (!state.annotations[fileId]) {
        state.annotations[fileId] = [];
      }
      state.annotations[fileId].push(newAnnotation);
    },
    
    updateAnnotation: (state, action) => {
      const { fileId, annotationId, updates } = action.payload;
      if (state.annotations[fileId]) {
        const index = state.annotations[fileId].findIndex(a => a.id === annotationId);
        if (index !== -1) {
          state.annotations[fileId][index] = {
            ...state.annotations[fileId][index],
            ...updates,
            updatedAt: Date.now()
          };
        }
      }
    },
    
    removeAnnotation: (state, action) => {
      const { fileId, annotationId } = action.payload;
      if (state.annotations[fileId]) {
        state.annotations[fileId] = state.annotations[fileId].filter(a => a.id !== annotationId);
      }
      state.selectedAnnotations = state.selectedAnnotations.filter(id => id !== annotationId);
    },
    
    selectAnnotations: (state, action) => {
      state.selectedAnnotations = action.payload;
    },
    
    toggleAnnotationSelection: (state, action) => {
      const annotationId = action.payload;
      const index = state.selectedAnnotations.indexOf(annotationId);
      if (index === -1) {
        state.selectedAnnotations.push(annotationId);
      } else {
        state.selectedAnnotations.splice(index, 1);
      }
    },
    
    updateAnnotationFilters: (state, action) => {
      state.annotationFilters = { ...state.annotationFilters, ...action.payload };
    },
    
    // UI State Management
    updateUIState: (state, action) => {
      state.ui = { ...state.ui, ...action.payload };
    },
    
    setSelectedTool: (state, action) => {
      state.ui.selectedTool = action.payload;
    },
    
    togglePanel: (state, action) => {
      const { panel, isOpen } = action.payload;
      state.ui[panel] = isOpen !== undefined ? isOpen : !state.ui[panel];
    },
    
    // Loading and Error States
    setLoading: (state, action) => {
      const { fileId, isLoading } = action.payload;
      state.loading[fileId] = isLoading;
    },
    
    setError: (state, action) => {
      const { fileId, error } = action.payload;
      state.errors[fileId] = error;
    },
    
    clearError: (state, action) => {
      const fileId = action.payload;
      delete state.errors[fileId];
    },
    
    // Search Management
    updateSearchState: (state, action) => {
      state.search = { ...state.search, ...action.payload };
    },
    
    setSearchResults: (state, action) => {
      const { results, query } = action.payload;
      state.search.results = results;
      state.search.query = query;
      state.search.currentResultIndex = results.length > 0 ? 0 : -1;
    },
    
    navigateSearchResult: (state, action) => {
      const direction = action.payload; // 'next' or 'previous'
      const { results, currentResultIndex } = state.search;
      
      if (results.length === 0) return;
      
      if (direction === 'next') {
        state.search.currentResultIndex = (currentResultIndex + 1) % results.length;
      } else {
        state.search.currentResultIndex = currentResultIndex <= 0 ? results.length - 1 : currentResultIndex - 1;
      }
    },
    
    // Bulk Operations
    clearAllAnnotations: (state, action) => {
      const fileId = action.payload;
      if (state.annotations[fileId]) {
        state.annotations[fileId] = [];
      }
      state.selectedAnnotations = [];
    },
    
    importAnnotations: (state, action) => {
      const { fileId, annotations } = action.payload;
      state.annotations[fileId] = annotations.map(annotation => ({
        ...annotation,
        id: annotation.id || `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: annotation.createdAt || Date.now(),
        updatedAt: annotation.updatedAt || Date.now()
      }));
    }
  }
});

// Selectors
export const selectActiveDocument = createSelector(
  [(state) => state.ultimatePdfViewer.documents, (state) => state.ultimatePdfViewer.activeDocumentId],
  (documents, activeDocumentId) => activeDocumentId ? documents[activeDocumentId] : null
);

export const selectDocumentAnnotations = createSelector(
  [(state) => state.ultimatePdfViewer.annotations, (_, fileId) => fileId],
  (annotations, fileId) => annotations[fileId] || []
);

export const selectFilteredAnnotations = createSelector(
  [selectDocumentAnnotations, (state) => state.ultimatePdfViewer.annotationFilters],
  (annotations, filters) => {
    return annotations.filter(annotation => {
      // Type filter
      if (!filters.types.includes(annotation.type)) return false;
      
      // Color filter
      if (!filters.colors.includes(annotation.color)) return false;
      
      // Author filter
      if (filters.authors.length > 0 && !filters.authors.includes(annotation.author)) return false;
      
      // Search query filter
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        const content = (annotation.content?.text || annotation.content?.comment || '').toLowerCase();
        if (!content.includes(query)) return false;
      }
      
      // Date range filter
      if (filters.dateRange) {
        const { start, end } = filters.dateRange;
        if (annotation.createdAt < start || annotation.createdAt > end) return false;
      }
      
      return true;
    });
  }
);

export const selectAnnotationsByPage = createSelector(
  [selectFilteredAnnotations],
  (annotations) => {
    return annotations.reduce((acc, annotation) => {
      const page = annotation.pageNumber;
      if (!acc[page]) acc[page] = [];
      acc[page].push(annotation);
      return acc;
    }, {});
  }
);

export const {
  setActiveDocument,
  addDocument,
  removeDocument,
  updateDocumentState,
  updateViewerSettings,
  addAnnotation,
  updateAnnotation,
  removeAnnotation,
  selectAnnotations,
  toggleAnnotationSelection,
  updateAnnotationFilters,
  updateUIState,
  setSelectedTool,
  togglePanel,
  setLoading,
  setError,
  clearError,
  updateSearchState,
  setSearchResults,
  navigateSearchResult,
  clearAllAnnotations,
  importAnnotations
} = ultimatePdfViewerSlice.actions;

export default ultimatePdfViewerSlice.reducer;
