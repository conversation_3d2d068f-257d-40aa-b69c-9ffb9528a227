import React from 'react';
import PDFViewerNew from './PDFViewerNew';

export default function PDFViewerDemo() {
  // Example file ID and path - replace with your actual file data
  const exampleFileId = "your-file-id";
  const exampleFilePath = "path/to/your/file.pdf";
  
  // Get the current theme from your app's theme context or state
  const isDarkTheme = false; // Set to true for dark theme
  
  return (
    <div style={{ width: '100%', height: '800px' }}>
      <h2>New PDF Viewer Implementation</h2>
      <div style={{ width: '100%', height: '700px', border: '1px solid #ccc' }}>
        <PDFViewerNew 
          fileId={exampleFileId}
          filePath={exampleFilePath}
          isDarkTheme={isDarkTheme}
        />
      </div>
    </div>
  );
}
