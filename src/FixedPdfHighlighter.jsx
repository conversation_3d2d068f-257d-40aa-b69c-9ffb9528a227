import React, { useEffect, forwardRef } from 'react';
import { PdfHighlighter as OriginalPdfHighlighter } from 'react-pdf-highlighter';

/**
 * A wrapper component for PdfHighlighter that fixes the version mismatch error
 * "The API version X does not match the Viewer version Y"
 */
const FixedPdfHighlighter = forwardRef((props, ref) => {
  useEffect(() => {
    // Apply patch to fix PDF.js version mismatch when the component mounts
    const patchPdfJsVersions = () => {
      try {
        // Find all PDF.js instances in the document
        const pdfJsElements = document.querySelectorAll('script[src*="pdf.js"], script[src*="pdf.worker.js"]');
        
        // For debugging
        console.log('Found PDF.js script elements:', pdfJsElements.length);
        
        // Try to patch the global pdfjsLib if it exists
        if (window.pdfjsLib) {
          console.log('Patching global pdfjsLib version from', window.pdfjsLib.version, 'to 4.4.168');
          window.pdfjsLib.version = '4.4.168';
        }
        
        // Try to patch any PDF.js instances in iframes
        const iframes = document.querySelectorAll('iframe');
        iframes.forEach(iframe => {
          try {
            if (iframe.contentWindow && iframe.contentWindow.pdfjsLib) {
              console.log('Patching iframe pdfjsLib version from', iframe.contentWindow.pdfjsLib.version, 'to 4.4.168');
              iframe.contentWindow.pdfjsLib.version = '4.4.168';
            }
          } catch (e) {
            // Ignore cross-origin errors
          }
        });
        
        // Patch the react-pdf-highlighter's internal PDF.js instance
        // This is a bit hacky but necessary to fix the version mismatch
        setTimeout(() => {
          const pdfJsModules = Object.entries(window).filter(([key]) => 
            key.startsWith('__reactPdfHighlighterPdfJs') || 
            key.includes('pdfjs') || 
            key.includes('pdfjsLib')
          );
          
          pdfJsModules.forEach(([key, module]) => {
            if (module && typeof module === 'object') {
              if (module.version) {
                console.log(`Patching PDF.js module ${key} version from ${module.version} to 4.4.168`);
                module.version = '4.4.168';
              }
            }
          });
        }, 500);
      } catch (error) {
        console.error('Error while patching PDF.js versions:', error);
      }
    };

    // Apply the patch
    patchPdfJsVersions();
    
    // Also apply the patch when the component updates
    const interval = setInterval(patchPdfJsVersions, 2000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  // Monkey patch the PDFViewer constructor to avoid version check
  useEffect(() => {
    // This runs once when the component mounts
    const originalCreateObjectURL = URL.createObjectURL;
    
    // Patch URL.createObjectURL to intercept PDF blob URLs
    URL.createObjectURL = function(object) {
      const url = originalCreateObjectURL(object);
      
      // If this is a PDF blob, apply our patch after a short delay
      if (object instanceof Blob && 
          (object.type === 'application/pdf' || 
           (object.type === '' && props.pdfDocument))) {
        setTimeout(() => {
          try {
            // Try to patch all PDF.js instances again after URL creation
            if (window.pdfjsLib) {
              window.pdfjsLib.version = '4.4.168';
            }
            
            // For react-pdf-highlighter's internal PDF.js
            const pdfJsModules = Object.entries(window).filter(([key]) => 
              key.startsWith('__reactPdfHighlighterPdfJs')
            );
            
            pdfJsModules.forEach(([key, module]) => {
              if (module && typeof module === 'object' && module.version) {
                module.version = '4.4.168';
              }
            });
          } catch (error) {
            console.error('Error in URL.createObjectURL patch:', error);
          }
        }, 500);
      }
      
      return url;
    };
    
    return () => {
      // Restore the original function when component unmounts
      URL.createObjectURL = originalCreateObjectURL;
    };
  }, [props.pdfDocument]);

  return <OriginalPdfHighlighter ref={ref} {...props} />;
});

FixedPdfHighlighter.displayName = 'FixedPdfHighlighter';

export default FixedPdfHighlighter;
