// OwnerDocumentContext.js
import React, { createContext, useContext, useState } from 'react';

const OwnerDocumentContext = createContext();

export const OwnerDocumentProvider = ({ children }) => {
  const [ownerDocument, setOwnerDocument] = useState(document);

  return (
    <OwnerDocumentContext.Provider value={{ ownerDocument, setOwnerDocument }}>
      {children}
    </OwnerDocumentContext.Provider>
  );
};

export const useOwnerDocument = () => {
  return useContext(OwnerDocumentContext);
};
