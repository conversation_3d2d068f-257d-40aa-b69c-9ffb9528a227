import "@blueprintjs/core/lib/css/blueprint.css";
import React, { useState, useEffect, useContext } from "react";
import { Provider, useSelector } from "react-redux";
import store from "./redux/store";
import {
  BlueprintProvider,
  Navbar,
  NavbarGroup,
  NavbarHeading,
  Alignment,
  Button,
  ButtonGroup,
  Icon,
  ProgressBar,
  NonIdealState,
  Dialog,
  NavbarDivider,
  Position,
  Popover,
  Menu,
  MenuItem,
  ContextMenu,
  Spinner,
  DialogBody,
  DialogFooter,
} from "@blueprintjs/core";
import FilesHandler from "./FilesHandler";
import DataCollectionApp from "./DataCollectionApp";

import logo from "./images/VisualAIM_icon.png";
import userIcon from "./images/developer.png";
import logoApp from "./images/VisualAIM_icon.png";
import { AppContext, AppContextProvider } from "./AppContextProvider";
import jwt_decode from "jwt-decode";
import axios from "axios"; // Import Axios library
import "./Styles/App.css";
import { Badge } from "antd";
import NotificationsPanel from "./NotificationsPanel/NotificationsPanel";

import { Layout, Model } from "flexlayout-react";
import "./Styles/vaim-flexlayout-dark.css";
import "./Styles/vaim-flexlayout-light.css";
import useNewWindow from "./useNewWindow";
import { useDispatch } from "react-redux";
import { setIsFilesHandlerOpen, setLayoutModel } from "./redux/appConfigurationSlice";

const initialLayoutModel = Model.fromJson({
  global: {},
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        weight: 30,
        children: [
          {
            type: "tab",
            component: "dataCollection",
            name: "Equipment Data",
            enableClose: false,
          },
        ],
      },
      {
        type: "tabset",
        weight: 70,
        children: [
          {
            type: "tab",
            component: "filesHandler",
            name: "Files",
            enableClose: false,
          },
        ],
      },
    ],
  },
});

const collapsedLayoutModel = Model.fromJson({
  global: {},
  layout: {
    type: "row",
    children: [
      {
        type: "tabset",
        weight: 100,
        children: [
          {
            type: "tab",
            component: "dataCollection",
            name: "Data Collection",
            enableClose: false,
          },
        ],
      },
    ],
  },
});

function GoogleSignInButton({ handleCredentialResponse }) {
  useEffect(() => {
    function checkGoogleLibrary() {
      if (
        window.google &&
        window.google.accounts &&
        window.google.accounts.id
      ) {
        window.google.accounts.id.initialize({
          client_id: `${process.env.REACT_APP_GOOGLE_CLIENT_ID}.apps.googleusercontent.com`,
          callback: handleCredentialResponse,
        });
        window.google.accounts.id.renderButton(
          document.getElementById("signInDiv"),
          {
            theme: "neutral",
            size: "large",
            text: "Sign in with Google",
            width: "250px",
            shape: "squared",
            logo_alignment: "center",
          },
        );
        window.google.accounts.id.prompt();
      } else {
        setTimeout(checkGoogleLibrary, 100);
      }
    }

    checkGoogleLibrary();
  }, []);

  useEffect(() => {
    const elements = document.querySelectorAll(".customClass");
    elements.forEach((el) => (el.style.backgroundColor = "black"));
  });

  return (
    <div
      id="signInDiv"
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        colorScheme: "light",
      }}
    ></div>
  );
}

function App() {
  const dispatch = useDispatch();
  const { isFilesHandlerOpen, layoutModel } = useSelector(
    (state) => state.appConfiguration,
  );
  const [isLoggedIn, setIsLoggedIn] = useState(true);
  const [user, setUser] = useState({});
  const [view, setView] = useState("login");

  const { isDarkTheme, setIsDarkTheme } = useContext(AppContext);
  const { setUserEmail } = useContext(AppContext);
  const { setUserName } = useContext(AppContext);

  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const toggleOverlay = () => setIsOverlayOpen(!isOverlayOpen);

  const [selectedAsset, setSelectedAsset] = useState(null);

  const [selectedAssetClass, setSelectedAssetClass] = useState(null);


 
  const [isProgressBarLoading, setIsProgressBarLoading] = useState(false);
  const [progressValue, setProgressValue] = useState(0);
  const [selectedWord, setSelectedWord] = useState("");
  const [rightPaneWidth, setRightPaneWidth] = useState("67%");
  const { currentInspectionFiles, setCurrentInspectionFiles } =
    useContext(AppContext);
  const [isLoginLoading, setIsLoginLoading] = useState(false);

  const { notifications } = useContext(AppContext);

  const handleToggleLayout = () => {
    setLayoutModel(
      layoutModel === initialLayoutModel
        ? collapsedLayoutModel
        : initialLayoutModel,
    );
  };

  const { openNewWindow } = useNewWindow(
    <FilesHandler isDarkTheme={isDarkTheme} windowMode={true} />,
    () => {
      setTimeout(() => {
        dispatch(setIsFilesHandlerOpen(false));
      }, 0);
    },
  );

  const handleOpenFilesHandler = () => {
    dispatch(setIsFilesHandlerOpen(true));
    dispatch(setLayoutModel("collapsed"));
    openNewWindow();
  };

  //use effect to refresh notifications is it changes
  useEffect(() => {
    console.log("notifications", notifications);
  }, [notifications]);

  useEffect(() => {
    const elements = document.querySelectorAll(".customClass");
    elements.forEach((el) => (el.style.backgroundColor = "black"));
  });

  useEffect(() => {
    let interval;
    if (isProgressBarLoading) {
      interval = setInterval(() => {
        setProgressValue((prevValue) => {
          if (prevValue < 1) {
            return prevValue + 0.15;
          } else {
            clearInterval(interval);
            return 1;
          }
        });
      }, 15);
    }
    return () => clearInterval(interval);
  }, [isProgressBarLoading]);

  function showContextMenu(e) {
    // Prevent the default context menu from showing
    e.preventDefault();

    // Show the context menu with the userMenu and dark theme based on isDarkTheme state
    ContextMenu.show(
      userMenu,
      { left: e.clientX, top: e.clientY },
      () => {},
      isDarkTheme,
    );
  }

  // Then attach this function to an event, for example, on right-clicking a specific element
  <div onContextMenu={showContextMenu}>Right Click on Me</div>;

  function handleCredentialResponse(response) {
    localStorage.setItem("token", response.credential);

    setIsLoginLoading(true);

    // Validate the token here
    async function validateToken() {
      const token = response.credential;
      if (token) {
        try {
          const responseBackEndCheck = await axios.get(
            `${process.env.REACT_APP_VALIDATION_API}/validate_token?token=${token}`,
            {
              headers: {
                "Content-Type": "application/json",
              },
            },
          );
          const data = responseBackEndCheck.data;
          console.log("Token validation response:", data);
          if (data.isValid) {
            var userObject = jwt_decode(response.credential);

            setUserEmail(userObject.email);
            setUserName(userObject.name);
            setUser(userObject);
            setIsLoggedIn(true);
            setIsLoginLoading(false);

            setView("mainApp");
          } else {
            handleLogout();
          }
        } catch (error) {
          console.error("Token validation error:", error);
          handleLogout();
        }
      }
    }

    validateToken();
  }

  function handleLogout() {
    localStorage.removeItem("token");
    // Perform any cleanup or state updates needed for logout
    setIsLoggedIn(false);
    setUser({});
    setView("login");
    // Optionally, clear any relevant data in local storage or context
  }

  const userMenu = (
    <Menu className={isDarkTheme ? "bp5-dark" : ""}>
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text={`User: ${user.name}`}
        icon="user"
      />
      <MenuItem
        className={isDarkTheme ? "bp5-dark" : ""}
        text="Logout"
        icon="log-out"
        onClick={handleLogout}
      />
    </Menu>
  );

  function factory(node) {
    const component = node.getComponent();
    if (component === "dataCollection") {
      return (
        <div style={{ paddingTop: "0px", height: "100%" }}>
          <DataCollectionApp
            setSelectedAsset={setSelectedAsset}
            setSelectedAssetClass={setSelectedAssetClass}
            isProgressBarLoading={isProgressBarLoading}
            setIsProgressBarLoading={setIsProgressBarLoading}
            selectedWord={selectedWord}
            setSelectedWord={setSelectedWord}
            setCurrentInspectionFiles={setCurrentInspectionFiles}
            isDarkTheme={isDarkTheme}
          />
        </div>
      );
    } else if (component === "filesHandler") {
      return (
        <div style={{ paddingTop: "0px", height: "100%" }}>
          <FilesHandler
            selectedWord={selectedWord}
            setSelectedWord={setSelectedWord}
            currentInspectionFiles={currentInspectionFiles}
            isDarkTheme={isDarkTheme}
            windowMode={false}
          />
        </div>
      );
    }
  }

  if (view === "login") {
    return (
      <div className="app-container login-background">
        <div style={{ textAlign: "center", marginBottom: "0px" }}></div>
        <div
          style={{
            display: "flex",
            height: "100vh",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {isLoginLoading && (
            <div className="loading-overlay">
              <Spinner></Spinner>
            </div>
          )}

          <Dialog
            usePortal={true}
            className="bp5-dark"
            isOpen={true}
            title={"Login Screen"}
            canEscapeKeyClose={false}
            canOutsideClickClose={false}
            isCloseButtonShown={false}
            icon="log-in"
            style={{ width: "400px", height: "400px" }}
          >
            <DialogBody>
              <br />
              <div
                style={{
                  width: "100px",
                  overflow: "hidden",
                  justifyContent: "center",
                  alignItems: "center",
                  margin: "0 auto",
                }}
              >
                <img
                  src={logoApp}
                  style={{ width: "100%", height: "auto" }}
                  alt="App"
                />
              </div>
              <br />
              <br />
              <br />

              <h2 style={{ textAlign: "center", fontWeight: "bold" }}>
                Data Collection App
              </h2>

              <div
                style={{
                  marginBottom: "10px",
                  display: "flex",
                  justifyContent: "center",
                }}
              ></div>
              <br />
              <br />

              <GoogleSignInButton
                handleCredentialResponse={handleCredentialResponse}
              />
            </DialogBody>
            <DialogFooter>
              <div
                style={{
                  display: "flex",
                  justifyContent: "right",
                  alignItems: "left",
                  fontWeight: "bold",
                  fontSize: "14px",
                }}
              >
                <br></br>
              </div>
            </DialogFooter>
          </Dialog>
        </div>
      </div>
    );
  }

  if (view === "mainApp") {
    return (
      <div className={`app-container ${isDarkTheme ? "bp5-dark" : ""}`}>
        <Navbar
          fixedToTop={true}
          className={`${isDarkTheme ? "bp5-dark" : ""}`}
        >
          <NavbarGroup align={Alignment.LEFT} style={{ flexGrow: 1 }} >
            <NavbarHeading >
              <div className="heading-container" >
                <img src={logo} className="App-logo" alt="logo" width={45}  />
              </div>
            </NavbarHeading>
            <NavbarDivider />

            {/* Include any other left-aligned items here */}
          </NavbarGroup>

          <div
            style={{
              position: "absolute",
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              display: "flex",
              justifyContent: "center", // Centers horizontally
              alignItems: "center", // Centers vertically (along the cross axis)
            }}
          >
            {isProgressBarLoading ? (
              <div className="custom-progress-bar">
                <ProgressBar value={progressValue} />
              </div>
            ) : selectedAsset === null ? (
              <>
                <NonIdealState title="No Asset Selected" />
              </>
            ) : (
              <>
                <h2
                  style={{
                    margin: 0,
                    marginRight: "10px",
                    fontWeight: "bold",
                  }}
                >
                  {selectedAsset}
                </h2>
                <span
                  className="bp5-tag bp5-intent-primary bp5-large bp5-minimal"
                  style={{
                    minWidth: "70px",
                    fontWeight: "bold",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    fontSize: "14px",
                  }}
                >
                  {selectedAssetClass}
                </span>
              </>
            )}
          </div>

          <NavbarGroup align={Alignment.RIGHT} style={{ flexGrow: 1 }}>
            <Button
              icon={<Icon icon="applications" />}
              minimal={true}
              onClick={handleOpenFilesHandler}
              disabled={isFilesHandlerOpen}
            />
            <NavbarDivider />
            <Badge
              count={notifications.errors?.length ?? 0}
              dot={true}
              color="#E76A6E"
              offset={[-6, 5]}
            >
              <Button
                role="button"
                icon="notifications"
                minimal={true}
                intent={
                  (notifications.errors?.length ?? 0) > 0 ? "danger" : "none"
                }
                onClick={toggleOverlay}
              />
            </Badge>
            <NavbarDivider />
            <Popover
              content={userMenu}
              position={Position.BOTTOM}
              interactionKind="hover"
              className={isDarkTheme ? "bp5-dark" : ""}
            >
              <div className="circle-image">
                <img src={user.picture || userIcon} alt="User" />
              </div>
            </Popover>
            <NavbarDivider />
            <Button
              icon={
                <Icon
                  icon={isDarkTheme ? "flash" : "moon"}
                  style={{ color: isDarkTheme ? "#d39c43" : "#d39c43" }}
                />
              }
              minimal={true}
              onClick={() => setIsDarkTheme(!isDarkTheme)}
            />
            <NavbarDivider />
            <Button icon={<Icon icon="menu" />} minimal={true} />
          </NavbarGroup>
        </Navbar>

        <div
          className={`main-content ${isDarkTheme ? "" : "light-theme"} ${isDarkTheme ? "bp5-dark" : ""}`}
        >
          <div className="split-pane-container">
            <Layout
              className="flexlayout__layout-dark"
              model={
                layoutModel === "initial"
                  ? initialLayoutModel
                  : collapsedLayoutModel
              }
              factory={factory}
            />
          </div>
          <NotificationsPanel
            isOverlayOpen={isOverlayOpen}
            toggleOverlay={toggleOverlay}
            isDarkTheme={isDarkTheme}
            setIsOverlayOpen={setIsOverlayOpen}
            notifications={notifications}
          />
        </div>
      </div>
    );
  }
}

export default function WrappedApp() {
  return (
    <BlueprintProvider>
      <Provider store={store}>
        <AppContextProvider>
          <App />
        </AppContextProvider>
      </Provider>
    </BlueprintProvider>
  );
}
