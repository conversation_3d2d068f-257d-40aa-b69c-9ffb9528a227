import React, { useState } from 'react';
import AppPdfViewer from './AppPdfViewer';
import { Button, Switch, Card, Elevation } from '@blueprintjs/core';

/**
 * Test component for AppPdfViewer
 */
const TestPdfViewer = () => {
  const [isDarkTheme, setIsDarkTheme] = useState(false);
  const [showCustomTools, setShowCustomTools] = useState(true);
  
  // Sample PDF URLs for testing
  const pdfUrls = [
    "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf",
    "https://www.africau.edu/images/default/sample.pdf",
    "https://cdn.codewithmosh.com/image/upload/v1721763853/guides/web-roadmap.pdf"
  ];
  
  const [currentPdfUrl, setCurrentPdfUrl] = useState(pdfUrls[0]);
  
  return (
    <div style={{ padding: '20px' }}>
      <Card elevation={Elevation.TWO} style={{ marginBottom: '20px', padding: '15px' }}>
        <h2>PDF Viewer Test</h2>
        
        <div style={{ display: 'flex', gap: '10px', marginBottom: '15px' }}>
          <Switch 
            checked={isDarkTheme} 
            onChange={() => setIsDarkTheme(!isDarkTheme)} 
            label="Dark Theme" 
          />
          <Switch 
            checked={showCustomTools} 
            onChange={() => setShowCustomTools(!showCustomTools)} 
            label="Show Custom Tools" 
          />
        </div>
        
        <div style={{ display: 'flex', gap: '10px', marginBottom: '15px' }}>
          {pdfUrls.map((url, index) => (
            <Button 
              key={index} 
              onClick={() => setCurrentPdfUrl(url)}
              active={currentPdfUrl === url}
            >
              PDF Sample {index + 1}
            </Button>
          ))}
        </div>
        
        <div>
          <strong>Current PDF URL:</strong> {currentPdfUrl}
        </div>
      </Card>
      
      <div style={{ border: '1px solid #ccc', borderRadius: '3px' }}>
        <AppPdfViewer 
          src={currentPdfUrl}
          isDarkTheme={isDarkTheme}
          showCustomTools={showCustomTools}
        />
      </div>
    </div>
  );
};

export default TestPdfViewer;
