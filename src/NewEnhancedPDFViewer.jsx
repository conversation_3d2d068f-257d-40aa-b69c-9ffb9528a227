import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { Spinner, Button, ButtonGroup, Classes, Icon, InputGroup, Divider } from "@blueprintjs/core";
import { Virtuoso } from 'react-virtuoso';
// Import required BlueprintJS CSS
import '@blueprintjs/core/lib/css/blueprint.css';
import { ZoomIn, ZoomOut, HandIcon, TextSelect, Maximize, Search, ChevronLeft, ChevronRight, Grid, List, Download } from "lucide-react";
import 'react-pdf/dist/esm/Page/TextLayer.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

/**
 * EnhancedPDFViewer - A feature-rich PDF viewer component
 * Features:
 * - BlueprintJS theming for dark/light mode
 * - Virtualized page rendering for smooth scrolling
 * - Toggle between pan and text selection modes
 * - Flicker-free zooming with debouncing
 * - Responsive design
 * - Thumbnail navigation panel
 * - Search functionality with result highlighting
 * - Improved UI controls and layout
 * - Page download capability
 */
const NewEnhancedPDFViewer = ({ fileId, filePath, isDarkTheme }) => {
  // Core state
  const [numPages, setNumPages] = useState(null);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Viewer settings
  const [scale, setScale] = useState(1.0);
  const [isPanMode, setIsPanMode] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [visiblePageNumbers, setVisiblePageNumbers] = useState([1]);
  const [showThumbnails, setShowThumbnails] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [isSearching, setIsSearching] = useState(false);
  
  // Refs
  const containerRef = useRef(null);
  const virtuosoRef = useRef(null);
  const thumbnailsVirtuosoRef = useRef(null);
  const documentRef = useRef(null);
  const pagesRefs = useRef({});
  const thumbnailsRefs = useRef({});
  const scaleTimeoutRef = useRef(null);
  const searchRef = useRef(null);

  // Fetch the PDF file when component mounts
  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();
    let objectUrl = null;

    // Reset state
    setPdfUrl(null);
    setLoading(true);
    setError(null);

    const fetchFile = async () => {
      try {
        console.log('Fetching PDF file:', { fileId, filePath });
        
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: { "session-id": localStorage.getItem("session_id") },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        if (isMounted) {
          objectUrl = URL.createObjectURL(response.data);
          console.log('Created blob URL for PDF:', objectUrl);
          setPdfUrl(objectUrl);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          console.error("Error fetching PDF file:", err);
          setError(`Failed to load PDF: ${err.message}`);
          setLoading(false);
        }
      }
    };

    if (fileId) {
      fetchFile();
    } else if (filePath && filePath.startsWith('blob:')) {
      // If it's already a blob URL, use it directly
      setPdfUrl(filePath);
      setLoading(false);
    } else if (filePath) {
      // If it's a local path or URL
      setPdfUrl(filePath);
      setLoading(false);
    } else {
      setError('No file ID or path provided');
      setLoading(false);
    }

    // Cleanup function
    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled");
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    };
  }, [fileId, filePath]);

  // Handle fullscreen mode
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Add global styles for PDF rendering
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .pdf-main-content {
        overflow: auto !important;
      }
      .virtuoso-scroller {
        overflow: auto !important;
      }
      .pdf-page-container {
        margin-bottom: 12px;
      }
      .react-pdf__Document {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .react-pdf__Page {
        margin: 0 auto;
        position: relative;
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Prevent handling if inside input element
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
        return;
      }

      switch (e.key) {
        case '+':
        case '=':
          zoomIn();
          e.preventDefault();
          break;
        case '-':
          zoomOut();
          e.preventDefault();
          break;
        case 'h':
          togglePanMode();
          e.preventDefault();
          break;
        case 'f':
          toggleFullscreen();
          e.preventDefault();
          break;
        case 'ArrowLeft':
          if (visiblePageNumbers[0] > 1) {
            scrollToPage(visiblePageNumbers[0] - 1);
          }
          e.preventDefault();
          break;
        case 'ArrowRight':
          if (visiblePageNumbers[0] < numPages) {
            scrollToPage(visiblePageNumbers[0] + 1);
          }
          e.preventDefault();
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [visiblePageNumbers, numPages]);

  // Update cursor style based on pan mode
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.style.cursor = isPanMode ? 'grab' : 'auto';
    }
  }, [isPanMode]);

  // Debounced scale change to prevent flickering
  const handleScaleChange = useCallback((newScale) => {
    // Clear any existing timeout
    if (scaleTimeoutRef.current) {
      clearTimeout(scaleTimeoutRef.current);
    }

    // Set a new timeout for 100ms
    scaleTimeoutRef.current = setTimeout(() => {
      setScale(newScale);
    }, 100);
  }, []);

  // Handle document load success
  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setLoading(false);
    
    console.log(`PDF loaded with ${numPages} pages`);
    
    // Make the component instance available to parent components
    if (typeof window !== 'undefined') {
      window.pdfViewerInstance = {
        goToPage: (pageNum) => scrollToPage(parseInt(pageNum, 10)),
        getPageCount: () => numPages,
        getCurrentPage: () => visiblePageNumbers[0] || 1,
        zoomIn: () => zoomIn(),
        zoomOut: () => zoomOut(),
        setScale: (newScale) => handleScaleChange(newScale),
        getScale: () => scale,
        togglePanMode: () => togglePanMode(),
        isPanMode: () => isPanMode
      };
    }
  };

  // Handle page visibility changes from virtualization
  const handlePageVisible = (pageNumber) => {
    if (!visiblePageNumbers.includes(pageNumber)) {
      setVisiblePageNumbers(prev => [...prev, pageNumber].sort((a, b) => a - b));
    }
  };

  // Scroll to specific page
  const scrollToPage = useCallback((pageNumber) => {
    if (virtuosoRef.current && pageNumber >= 1 && pageNumber <= numPages) {
      virtuosoRef.current.scrollToIndex({
        index: pageNumber - 1,
        align: 'start',
        behavior: 'smooth'
      });
      return true;
    }
    return false;
  }, [numPages]);

  // Zoom functions with smooth transitions
  const zoomIn = useCallback(() => {
    const newScale = Math.min(scale + 0.2, 3);
    handleScaleChange(newScale);
  }, [scale, handleScaleChange]);

  const zoomOut = useCallback(() => {
    const newScale = Math.max(scale - 0.2, 0.5);
    handleScaleChange(newScale);
  }, [scale, handleScaleChange]);
  
  // Toggle fullscreen mode
  const toggleFullscreen = useCallback(() => {
    if (!isFullscreen) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, [isFullscreen]);
  
  // Toggle between pan and text selection modes
  const togglePanMode = useCallback(() => {
    setIsPanMode(prev => !prev);
  }, []);

  // Handle document search
  const handleSearch = useCallback(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setCurrentSearchIndex(-1);
      return;
    }

    setIsSearching(true);

    // Clear previous results
    setSearchResults([]);
    setCurrentSearchIndex(-1);

    const results = [];

    // Search through all pages
    if (documentRef.current && documentRef.current.textContent) {
      // Process search in chunks to prevent UI blocking
      const processPage = (pageNumber) => {
        if (pageNumber > numPages) {
          setIsSearching(false);
          if (results.length > 0) {
            setSearchResults(results);
            setCurrentSearchIndex(0);
            // Navigate to first result
            const firstResult = results[0];
            scrollToPage(firstResult.page);
          }
          return;
        }

        const textContent = documentRef.current.textContent[pageNumber];
        if (!textContent) {
          // Skip to next page if text content not available
          setTimeout(() => processPage(pageNumber + 1), 0);
          return;
        }

        const textItems = textContent.items;
        const query = searchQuery.toLowerCase();

        // Simple search algorithm - can be improved
        for (let i = 0; i < textItems.length; i++) {
          const text = textItems[i].str.toLowerCase();
          if (text.includes(query)) {
            results.push({
              page: pageNumber,
              text: textItems[i].str,
              left: textItems[i].transform[4],
              top: textItems[i].transform[5],
              width: textItems[i].width,
              height: textItems[i].height,
              index: i
            });
          }
        }

        // Process next page
        setTimeout(() => processPage(pageNumber + 1), 0);
      };

      // Start processing from page 1
      processPage(1);
    }
  }, [searchQuery, numPages, scrollToPage]);

  // Navigate through search results
  const navigateSearchResult = useCallback((direction) => {
    if (searchResults.length === 0) return;

    let newIndex;
    if (direction === 'next') {
      newIndex = Math.min(currentSearchIndex + 1, searchResults.length - 1);
    } else {
      newIndex = Math.max(currentSearchIndex - 1, 0);
    }

    setCurrentSearchIndex(newIndex);
    const result = searchResults[newIndex];
    scrollToPage(result.page);
  }, [currentSearchIndex, searchResults, scrollToPage]);

  // Handle PDF download
  const handleDownload = useCallback(() => {
    if (pdfUrl) {
      const link = document.createElement('a');
      link.href = pdfUrl;
      link.download = filePath.split('/').pop() || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [pdfUrl, filePath]);

  // Add custom CSS for PDF viewer
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .pdf-pan-mode .react-pdf__Page {
        cursor: grab;
      }
      .pdf-pan-mode .react-pdf__Page:active {
        cursor: grabbing;
      }
      .pdf-page-container {
        margin-bottom: 10px;
      }
      .pdf-thumbnail:hover {
        opacity: 0.8;
      }
      .pdf-viewer-container {
        overflow-y: auto;
      }
      .pdf-viewer-container.fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1000;
      }
    `;
    document.head.appendChild(style);
    return () => document.head.removeChild(style);
  }, []);
  
  return (
    <div 
      ref={containerRef} 
      className={`pdf-viewer-container ${isFullscreen ? 'fullscreen' : ''} ${isDarkTheme ? 'bp4-dark' : ''}`} 
      style={{ 
        height: '100%', 
        width: '100%', 
        display: 'flex', 
        flexDirection: 'column', 
        position: 'relative',
        overflow: 'hidden' 
      }}
      tabIndex={0}
    >
      <div className={`${Classes.NAVBAR} ${isDarkTheme ? Classes.DARK : ''}`} style={{ padding: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          {/* Thumbnail Toggle */}
          <div title={showThumbnails ? "Hide Thumbnails" : "Show Thumbnails"}>
            <Button
              icon={showThumbnails ? <List size={16} /> : <Grid size={16} />}
              onClick={() => setShowThumbnails(!showThumbnails)}
              minimal
              active={showThumbnails}
            />
          </div>

          <Divider style={{ height: '20px' }} />

          {/* Page navigation controls */}
          <ButtonGroup>
            <Button 
              icon={<ChevronLeft size={16} />} 
              onClick={() => {
                const prevPage = Math.max(1, (visiblePageNumbers[0] || 1) - 1);
                scrollToPage(prevPage);
              }} 
              disabled={visiblePageNumbers[0] <= 1}
              minimal
            />
            <span style={{ display: 'inline-flex', alignItems: 'center', padding: '0 8px', minWidth: '64px', justifyContent: 'center' }}>
              {visiblePageNumbers[0] || 1} / {numPages || '?'}
            </span>
            <Button 
              icon={<ChevronRight size={16} />} 
              onClick={() => {
                const nextPage = Math.min(numPages, (visiblePageNumbers[0] || 1) + 1);
                scrollToPage(nextPage);
              }} 
              disabled={!numPages || visiblePageNumbers[0] >= numPages}
              minimal
            />
          </ButtonGroup>
        </div>

        {/* Search controls */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '5px', flexGrow: 1, maxWidth: '400px', margin: '0 15px' }}>
          <InputGroup
            leftIcon={<Search size={16} />}
            placeholder="Search in document"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleSearch();
              }
            }}
            rightElement={
              <ButtonGroup>
                <Button
                  minimal
                  icon={<ChevronLeft size={16} />}
                  disabled={searchResults.length === 0 || currentSearchIndex <= 0}
                  onClick={() => navigateSearchResult('prev')}
                />
                <Button
                  minimal
                  icon={<ChevronRight size={16} />}
                  disabled={searchResults.length === 0 || currentSearchIndex >= searchResults.length - 1}
                  onClick={() => navigateSearchResult('next')}
                />
              </ButtonGroup>
            }
          />
          <Button
            text={isSearching ? "Cancel" : "Search"}
            intent={isSearching ? "danger" : "primary"}
            small
            onClick={handleSearch}
          />
          {searchResults.length > 0 && (
            <span className={Classes.TEXT_MUTED}>
              {currentSearchIndex + 1}/{searchResults.length}
            </span>
          )}
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          {/* Pan/Text Selection Toggle */}
          <div title={`Toggle pan mode (${isPanMode ? 'Pan' : 'Text selection'})`}>
            <Button
              icon={isPanMode ? <TextSelect size={16} /> : <HandIcon size={16} />}
              onClick={togglePanMode}
              minimal
              active={isPanMode}
            />
          </div>
          
          {/* Zoom Controls */}
          <ButtonGroup>
            <div title="Zoom Out">
              <Button icon={<ZoomOut size={16} />} onClick={zoomOut} minimal />
            </div>
            <span style={{ display: 'inline-flex', alignItems: 'center', padding: '0 8px', minWidth: '64px', justifyContent: 'center' }}>
              {Math.round(scale * 100)}%
            </span>
            <div title="Zoom In">
              <Button icon={<ZoomIn size={16} />} onClick={zoomIn} minimal />
            </div>
          </ButtonGroup>
          
          {/* Fullscreen Toggle */}
          <div title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"}>
            <Button
              icon={<Maximize size={16} />}
              onClick={toggleFullscreen}
              minimal
              active={isFullscreen}
            />
          </div>

          {/* Download PDF */}
          <div title="Download PDF">
            <Button
              icon={<Download size={16} />}
              onClick={handleDownload}
              minimal
            />
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div style={{ display: 'flex', flex: 1, overflow: 'hidden' }}>        
        {/* Thumbnails Panel */}
        <div 
          style={{ 
            width: showThumbnails ? '200px' : '0px', 
            transition: 'width 0.3s ease',
            overflow: 'hidden',
            borderRight: showThumbnails ? `1px solid ${isDarkTheme ? '#293742' : '#D8E1E8'}` : 'none',
            backgroundColor: isDarkTheme ? '#394B59' : '#EBF1F5'
          }}
        >
          {showThumbnails && (
            <Virtuoso
              ref={thumbnailsVirtuosoRef}
              style={{ height: '100%', width: '100%' }}
              totalCount={numPages || 0}
              itemContent={index => {
                const pageNumber = index + 1;
                return (
                  <div 
                    key={`thumbnail-${pageNumber}`}
                    onClick={() => scrollToPage(pageNumber)}
                    style={{ 
                      padding: '10px', 
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      cursor: 'pointer',
                      backgroundColor: visiblePageNumbers.includes(pageNumber) ? (isDarkTheme ? '#30404D' : '#E1E8ED') : 'transparent',
                      transition: 'all 0.2s ease'
                    }}
                    ref={el => thumbnailsRefs.current[pageNumber] = el}
                  >
                    <div style={{ 
                      boxShadow: '0 2px 5px rgba(0, 0, 0, 0.2)', 
                      border: visiblePageNumbers.includes(pageNumber) ? '2px solid #137CBD' : `1px solid ${isDarkTheme ? '#293742' : '#D8E1E8'}`,
                      borderRadius: '2px',
                      overflow: 'hidden',
                      backgroundColor: isDarkTheme ? '#10161A' : 'white',
                      width: '80%',
                      margin: '0 auto'
                    }}>
                      <Document
                        file={pdfUrl}
                        loading={null} // Don't show loading indicator for thumbnails
                      >
                        <Page
                          pageNumber={pageNumber}
                          scale={0.2}
                          width={150}
                          renderTextLayer={false}
                          renderAnnotationLayer={false}
                          className="pdf-thumbnail"
                        />
                      </Document>
                    </div>
                    <div style={{ 
                      marginTop: '5px', 
                      fontSize: '12px',
                      color: isDarkTheme ? '#F5F8FA' : '#394B59',
                      textAlign: 'center'
                    }}>
                      {pageNumber}
                    </div>
                  </div>
                );
              }}
            />
          )}
        </div>
        
        {/* PDF Viewer Container */}
        <div 
          style={{ 
            flex: 1, 
            position: 'relative',
            overflow: 'auto',
            backgroundColor: isDarkTheme ? '#30404D' : '#F5F8FA',
            transition: 'background-color 0.3s ease'
          }}
          className={`${isPanMode ? 'pdf-pan-mode' : ''} pdf-main-content`}
        >
          {loading ? (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', flexDirection: 'column' }}>
              <Spinner size={50} />
              <div style={{ marginTop: '20px' }} className={Classes.TEXT_MUTED}>Loading PDF...</div>
            </div>
          ) : error ? (
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <div style={{ color: 'red', marginBottom: '10px' }}>Error loading PDF</div>
              <div>{error}</div>
            </div>
          ) : (
            <Document
              inputRef={documentRef}
              file={pdfUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              onLoadError={(error) => setError(`Failed to load PDF: ${error.message}`)}
              loading={<Spinner size={50} />}
              className="pdf-document"
            >
              {numPages && (
                <Virtuoso
                  ref={virtuosoRef}
                  style={{ height: '100%', width: '100%', overflow: 'auto' }}
                  totalCount={numPages}
                  overscan={2}
                  initialTopMostItemIndex={0}
                  itemContent={index => {
                    const pageNumber = index + 1;
                    return (
                      <div 
                        key={`page-${pageNumber}`}
                        className={`pdf-page-container ${visiblePageNumbers.includes(pageNumber) ? 'pdf-page-visible' : ''}`}
                        style={{ 
                          display: 'flex', 
                          justifyContent: 'center',
                          padding: '20px 0',
                          position: 'relative',
                        }}
                        ref={el => pagesRefs.current[pageNumber] = el}
                      >
                        <div style={{ 
                          boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)', 
                          transition: 'transform 0.2s',
                          position: 'relative'
                        }}>
                          <Page
                            pageNumber={pageNumber}
                            scale={scale}
                            width={800} // Maximum width
                            renderTextLayer={true}
                            renderAnnotationLayer={true}
                            loading={pageNumber === 1 ? <Spinner size={30} /> : null}
                            onRenderSuccess={() => {
                              // Mark this page as ready
                              if (pageNumber === 1 && loading) {
                                setLoading(false);
                              }
                            }}
                            className="pdf-page"
                            onGetTextSuccess={(textContent) => {
                              // Store text content for search functionality
                              if (!documentRef.current) documentRef.current = {};
                              if (!documentRef.current.textContent) documentRef.current.textContent = {};
                              documentRef.current.textContent[pageNumber] = textContent;
                            }}
                          />
                          
                          {/* Render search result highlights */}
                          {searchResults.filter(result => result.page === pageNumber).map((result, idx) => (
                            <div 
                              key={`highlight-${pageNumber}-${idx}`}
                              style={{
                                position: 'absolute',
                                left: `${result.left}px`,
                                top: `${result.top}px`,
                                width: `${result.width}px`,
                                height: `${result.height}px`,
                                backgroundColor: currentSearchIndex === searchResults.indexOf(result) ? 'rgba(255, 165, 0, 0.5)' : 'rgba(255, 255, 0, 0.3)',
                                pointerEvents: 'none',
                                zIndex: 1,
                                border: currentSearchIndex === searchResults.indexOf(result) ? '2px solid orange' : 'none',
                                borderRadius: '2px'
                              }}
                            />
                          ))}
                        </div>
                      </div>
                    );
                  }}
                  rangeChanged={range => {
                    // Update visible page numbers
                    const visiblePages = [];
                    for (let i = range.startIndex; i <= range.endIndex; i++) {
                      visiblePages.push(i + 1); // Convert to 1-based page numbers
                    }
                    setVisiblePageNumbers(visiblePages);
                    
                    // Sync thumbnail list when page changes
                    if (thumbnailsVirtuosoRef.current && visiblePages.length > 0) {
                      thumbnailsVirtuosoRef.current.scrollToIndex({
                        index: visiblePages[0] - 1,
                        align: 'center',
                        behavior: 'smooth'
                      });
                    }
                  }}
                />
              )}
            </Document>
          )}
        </div>
      </div>
    </div>
  );
};

export default NewEnhancedPDFViewer;
