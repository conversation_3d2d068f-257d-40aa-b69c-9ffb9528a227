import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Card, Spinner, NonIdealState } from '@blueprintjs/core';

// Import our components (to be created)
import PDFRenderingEngine from './components/PDFRenderingEngine';
import AnnotationExplorer from './components/AnnotationExplorer';
import ThumbnailPanel from './components/ThumbnailPanel';
import PDFToolbar from './components/PDFToolbar';
import SearchPanel from './components/SearchPanel';
import PropertiesPanel from './components/PropertiesPanel';

// Import Redux actions
import {
  setActiveDocument,
  addDocument,
  updateDocumentState,
  updateUIState,
  setLoading,
  setError,
  clearError,
  selectActiveDocument
} from './redux/ultimatePdfViewerSlice';

// Import utilities
import { loadPDFDocument, createPDFUrl } from './utils/pdfUtils';

// Styles - temporarily commented out due to import issue
// import './styles/UltimatePDFViewer.css';

const UltimatePDFViewer = ({ 
  fileId, 
  filePath, 
  fileName,
  isDarkTheme = true,
  onClose,
  className = '',
  style = {}
}) => {
  const dispatch = useDispatch();
  const containerRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  
  // Redux selectors
  const activeDocument = useSelector(selectActiveDocument);
  const ui = useSelector(state => state.ultimatePdfViewer.ui);
  const loading = useSelector(state => state.ultimatePdfViewer.loading[fileId]);
  const error = useSelector(state => state.ultimatePdfViewer.errors[fileId]);
  const viewerSettings = useSelector(state => state.ultimatePdfViewer.viewerSettings);

  // Initialize document when component mounts
  useEffect(() => {
    if (fileId && filePath && !isInitialized) {
      initializeDocument();
    }
  }, [fileId, filePath, isInitialized]);

  // Set as active document when this viewer is focused
  useEffect(() => {
    if (fileId && isInitialized) {
      dispatch(setActiveDocument(fileId));
    }
  }, [fileId, isInitialized, dispatch]);

  const initializeDocument = useCallback(async () => {
    if (!fileId || !filePath) return;

    dispatch(setLoading({ fileId, isLoading: true }));
    dispatch(clearError(fileId));

    try {
      // Load PDF document to get metadata
      const pdfDocument = await loadPDFDocument(filePath);
      const numPages = pdfDocument.numPages;

      // Add document to Redux store
      dispatch(addDocument({
        fileId,
        filePath,
        fileName: fileName || filePath.split('/').pop(),
        numPages
      }));

      setIsInitialized(true);
    } catch (err) {
      console.error('Failed to initialize PDF document:', err);
      dispatch(setError({ 
        fileId, 
        error: `Failed to load PDF: ${err.message}` 
      }));
    } finally {
      dispatch(setLoading({ fileId, isLoading: false }));
    }
  }, [fileId, filePath, fileName, dispatch]);

  // Keyboard shortcuts
  const handleKeyDown = useCallback((event) => {
    if (!activeDocument) return;

    const { key, ctrlKey, metaKey, shiftKey } = event;
    const isModifierPressed = ctrlKey || metaKey;

    switch (key) {
      case 'f':
        if (isModifierPressed) {
          event.preventDefault();
          dispatch(updateUIState({ isSearchPanelOpen: !ui.isSearchPanelOpen }));
        }
        break;
      case 'Escape':
        if (ui.isFullscreen) {
          dispatch(updateUIState({ isFullscreen: false }));
        }
        break;
      case 'F11':
        event.preventDefault();
        dispatch(updateUIState({ isFullscreen: !ui.isFullscreen }));
        break;
      case '+':
      case '=':
        if (isModifierPressed) {
          event.preventDefault();
          const newZoom = Math.min(activeDocument.zoom * 1.2, 5.0);
          dispatch(updateDocumentState({ 
            fileId, 
            updates: { zoom: newZoom } 
          }));
        }
        break;
      case '-':
        if (isModifierPressed) {
          event.preventDefault();
          const newZoom = Math.max(activeDocument.zoom / 1.2, 0.1);
          dispatch(updateDocumentState({ 
            fileId, 
            updates: { zoom: newZoom } 
          }));
        }
        break;
      case '0':
        if (isModifierPressed) {
          event.preventDefault();
          dispatch(updateDocumentState({ 
            fileId, 
            updates: { zoom: 1.0 } 
          }));
        }
        break;
    }
  }, [activeDocument, ui, fileId, dispatch]);

  // Attach keyboard event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('keydown', handleKeyDown);
      container.focus(); // Ensure container can receive keyboard events
      
      return () => {
        container.removeEventListener('keydown', handleKeyDown);
      };
    }
  }, [handleKeyDown]);

  // Simplified panel handlers (no resizing for now)
  const handleAnnotationExplorerToggle = useCallback(() => {
    dispatch(updateUIState({ isAnnotationExplorerOpen: !ui.isAnnotationExplorerOpen }));
  }, [dispatch, ui.isAnnotationExplorerOpen]);

  const handleThumbnailPanelToggle = useCallback(() => {
    dispatch(updateUIState({ isThumbnailPanelOpen: !ui.isThumbnailPanelOpen }));
  }, [dispatch, ui.isThumbnailPanelOpen]);

  // Calculate layout dimensions
  const layoutDimensions = useMemo(() => {
    const annotationWidth = ui.isAnnotationExplorerOpen ? ui.annotationExplorerWidth : 0;
    const thumbnailWidth = ui.isThumbnailPanelOpen ? ui.thumbnailPanelWidth : 0;
    const toolbarHeight = 50;
    
    return {
      annotationWidth,
      thumbnailWidth,
      toolbarHeight,
      mainViewerWidth: `calc(100% - ${annotationWidth + thumbnailWidth}px)`,
      mainViewerHeight: `calc(100% - ${toolbarHeight}px)`
    };
  }, [ui.isAnnotationExplorerOpen, ui.isThumbnailPanelOpen, ui.annotationExplorerWidth, ui.thumbnailPanelWidth]);

  // Render loading state
  if (loading) {
    return (
      <div className={`ultimate-pdf-viewer ${isDarkTheme ? 'dark' : 'light'} ${className}`} style={style}>
        <Card className="loading-container">
          <Spinner size={50} />
          <p>Loading PDF document...</p>
        </Card>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`ultimate-pdf-viewer ${isDarkTheme ? 'dark' : 'light'} ${className}`} style={style}>
        <Card className="error-container">
          <NonIdealState
            icon="error"
            title="Failed to Load PDF"
            description={error}
            action={
              <button onClick={() => initializeDocument()}>
                Try Again
              </button>
            }
          />
        </Card>
      </div>
    );
  }

  // Render main viewer
  const viewerStyle = {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    width: '100%',
    position: 'relative',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: '14px',
    overflow: 'hidden',
    backgroundColor: isDarkTheme ? '#1C2127' : '#FFFFFF',
    color: isDarkTheme ? '#F5F8FA' : '#182026',
    ...style
  };

  return (
    <div
      ref={containerRef}
      className={`ultimate-pdf-viewer ${isDarkTheme ? 'dark' : 'light'} ${className} ${ui.isFullscreen ? 'fullscreen' : ''}`}
      style={viewerStyle}
      tabIndex={0} // Make container focusable for keyboard events
    >
      {/* Toolbar */}
      <div
        className="pdf-toolbar"
        style={{
          height: layoutDimensions.toolbarHeight,
          backgroundColor: isDarkTheme ? '#252A31' : '#F5F8FA',
          borderBottom: `1px solid ${isDarkTheme ? '#383E47' : '#D8E1E8'}`,
          display: 'flex',
          alignItems: 'center',
          padding: '0 16px',
          flexShrink: 0,
          zIndex: 100
        }}
      >
        <PDFToolbar
          fileId={fileId}
          onClose={onClose}
          isDarkTheme={isDarkTheme}
        />
      </div>

      {/* Main Content Area */}
      <div
        className="pdf-content"
        style={{
          height: layoutDimensions.mainViewerHeight,
          display: 'flex',
          flex: 1,
          overflow: 'hidden',
          backgroundColor: isDarkTheme ? '#1C2127' : '#FFFFFF'
        }}
      >
        
        {/* Annotation Explorer */}
        {ui.isAnnotationExplorerOpen && (
          <div
            className="annotation-explorer-container"
            style={{
              width: ui.annotationExplorerWidth,
              backgroundColor: isDarkTheme ? '#252A31' : '#F5F8FA',
              borderRight: `1px solid ${isDarkTheme ? '#383E47' : '#D8E1E8'}`,
              overflow: 'hidden',
              position: 'relative'
            }}
          >
            <AnnotationExplorer
              fileId={fileId}
              isDarkTheme={isDarkTheme}
            />
          </div>
        )}

        {/* Main PDF Viewer */}
        <div
          className="main-pdf-viewer"
          style={{
            width: layoutDimensions.mainViewerWidth,
            height: '100%',
            flex: 1,
            position: 'relative',
            overflow: 'hidden',
            backgroundColor: isDarkTheme ? '#1C2127' : '#FFFFFF'
          }}
        >
          {activeDocument && (
            <PDFRenderingEngine
              fileId={fileId}
              document={activeDocument}
              isDarkTheme={isDarkTheme}
            />
          )}

          {/* Search Panel Overlay */}
          {ui.isSearchPanelOpen && (
            <SearchPanel
              fileId={fileId}
              isDarkTheme={isDarkTheme}
            />
          )}
        </div>

        {/* Thumbnail Panel */}
        {ui.isThumbnailPanelOpen && (
          <div
            className="thumbnail-panel-container"
            style={{
              width: ui.thumbnailPanelWidth,
              backgroundColor: isDarkTheme ? '#252A31' : '#F5F8FA',
              borderLeft: `1px solid ${isDarkTheme ? '#383E47' : '#D8E1E8'}`,
              overflow: 'hidden',
              position: 'relative'
            }}
          >
            <ThumbnailPanel
              fileId={fileId}
              isDarkTheme={isDarkTheme}
            />
          </div>
        )}

      </div>

      {/* Properties Panel (floating) */}
      {ui.isPropertiesPanelOpen && (
        <PropertiesPanel 
          fileId={fileId}
          isDarkTheme={isDarkTheme}
        />
      )}
    </div>
  );
};

export default UltimatePDFViewer;
