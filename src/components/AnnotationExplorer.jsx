import React, { useState, useMemo, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FixedSizeList as List } from 'react-window';
import {
  Card,
  InputGroup,
  Button,
  ButtonGroup,
  Tag,
  Tree,
  Icon,
  Collapse,
  Menu,
  MenuItem,
  Popover,
  NonIdealState,
  Tooltip
} from '@blueprintjs/core';
import {
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
  FileText,
  Square,
  Highlighter,
  MessageSquare,
  Calendar,
  User,
  MoreVertical,
  Eye,
  EyeOff,
  Trash2,
  Edit
} from 'lucide-react';

// Redux actions
import {
  selectDocumentAnnotations,
  selectFilteredAnnotations,
  updateAnnotationFilters,
  selectAnnotations,
  toggleAnnotationSelection,
  removeAnnotation,
  updateAnnotation,
  ANNOTATION_TYPES,
  ANNOTATION_COLORS
} from '../redux/ultimatePdfViewerSlice';

// Utilities
import { formatDate, groupAnnotationsByPage, groupAnnotationsByType } from '../utils/annotationUtils';

const AnnotationExplorer = ({ fileId, isDarkTheme }) => {
  const dispatch = useDispatch();
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedNodes, setExpandedNodes] = useState(new Set(['pages', 'types']));
  const [groupBy, setGroupBy] = useState('page'); // 'page', 'type', 'author', 'date'
  const [sortBy, setSortBy] = useState('created'); // 'created', 'updated', 'page', 'type'
  const [sortOrder, setSortOrder] = useState('desc'); // 'asc', 'desc'

  // Redux selectors
  const allAnnotations = useSelector(state => selectDocumentAnnotations(state, fileId));
  const filteredAnnotations = useSelector(state => selectFilteredAnnotations(state, fileId));
  const selectedAnnotations = useSelector(state => state.ultimatePdfViewer.selectedAnnotations);
  const annotationFilters = useSelector(state => state.ultimatePdfViewer.annotationFilters);

  // Filter annotations based on search query
  const searchFilteredAnnotations = useMemo(() => {
    if (!searchQuery.trim()) return filteredAnnotations;
    
    const query = searchQuery.toLowerCase();
    return filteredAnnotations.filter(annotation => {
      const content = (annotation.content?.text || annotation.content?.comment || annotation.comment || '').toLowerCase();
      const author = (annotation.author || '').toLowerCase();
      const type = annotation.type.toLowerCase();
      
      return content.includes(query) || author.includes(query) || type.includes(query);
    });
  }, [filteredAnnotations, searchQuery]);

  // Group and sort annotations
  const groupedAnnotations = useMemo(() => {
    let sorted = [...searchFilteredAnnotations];
    
    // Sort annotations
    sorted.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'created':
          comparison = a.createdAt - b.createdAt;
          break;
        case 'updated':
          comparison = a.updatedAt - b.updatedAt;
          break;
        case 'page':
          comparison = a.pageNumber - b.pageNumber;
          break;
        case 'type':
          comparison = a.type.localeCompare(b.type);
          break;
        default:
          comparison = 0;
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });

    // Group annotations
    switch (groupBy) {
      case 'page':
        return groupAnnotationsByPage(sorted);
      case 'type':
        return groupAnnotationsByType(sorted);
      case 'author':
        return groupAnnotationsByAuthor(sorted);
      case 'date':
        return groupAnnotationsByDate(sorted);
      default:
        return { 'All Annotations': sorted };
    }
  }, [searchFilteredAnnotations, groupBy, sortBy, sortOrder]);

  // Handle search
  const handleSearchChange = useCallback((event) => {
    setSearchQuery(event.target.value);
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((filterType, value) => {
    dispatch(updateAnnotationFilters({ [filterType]: value }));
  }, [dispatch]);

  // Handle annotation selection
  const handleAnnotationClick = useCallback((annotation, event) => {
    if (event.ctrlKey || event.metaKey) {
      dispatch(toggleAnnotationSelection(annotation.id));
    } else {
      dispatch(selectAnnotations([annotation.id]));
    }
    
    // Scroll to annotation in PDF
    if (window.pdfScrollTo) {
      window.pdfScrollTo(annotation.pageNumber);
    }
  }, [dispatch]);

  // Handle annotation actions
  const handleDeleteAnnotation = useCallback((annotationId) => {
    dispatch(removeAnnotation({ fileId, annotationId }));
  }, [fileId, dispatch]);

  const handleToggleAnnotationVisibility = useCallback((annotationId) => {
    const annotation = allAnnotations.find(a => a.id === annotationId);
    if (annotation) {
      dispatch(updateAnnotation({
        fileId,
        annotationId,
        updates: { isVisible: !annotation.isVisible }
      }));
    }
  }, [allAnnotations, fileId, dispatch]);

  // Handle node expansion
  const handleNodeToggle = useCallback((nodeId) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // Get annotation icon
  const getAnnotationIcon = (type) => {
    switch (type) {
      case ANNOTATION_TYPES.TEXT_HIGHLIGHT:
        return <Highlighter size={14} />;
      case ANNOTATION_TYPES.RECTANGLE:
      case ANNOTATION_TYPES.AREA_HIGHLIGHT:
        return <Square size={14} />;
      case ANNOTATION_TYPES.COMMENT:
        return <MessageSquare size={14} />;
      default:
        return <FileText size={14} />;
    }
  };

  // Render annotation item
  const renderAnnotationItem = useCallback((annotation) => {
    const isSelected = selectedAnnotations.includes(annotation.id);
    
    return (
      <div
        key={annotation.id}
        className={`annotation-item ${isSelected ? 'selected' : ''} ${!annotation.isVisible ? 'hidden' : ''}`}
        onClick={(e) => handleAnnotationClick(annotation, e)}
      >
        <div className="annotation-content">
          <div className="annotation-header">
            <span className="annotation-icon">
              {getAnnotationIcon(annotation.type)}
            </span>
            <span className="annotation-type-label">
              {annotation.type.replace('_', ' ').toUpperCase()}
            </span>
            <Tag
              minimal
              style={{ backgroundColor: annotation.color, color: '#000' }}
              className="annotation-color-tag"
            />
          </div>
          
          <div className="annotation-text">
            {annotation.content?.text || annotation.comment || 'No content'}
          </div>
          
          <div className="annotation-meta">
            <span className="annotation-page">Page {annotation.pageNumber}</span>
            <span className="annotation-author">{annotation.author}</span>
            <span className="annotation-date">
              {formatDate(annotation.createdAt)}
            </span>
          </div>
        </div>
        
        <div className="annotation-actions">
          <Popover
            content={
              <Menu>
                <MenuItem
                  icon={annotation.isVisible ? <EyeOff size={14} /> : <Eye size={14} />}
                  text={annotation.isVisible ? 'Hide' : 'Show'}
                  onClick={() => handleToggleAnnotationVisibility(annotation.id)}
                />
                <MenuItem
                  icon={<Edit size={14} />}
                  text="Edit"
                  onClick={() => {/* TODO: Implement edit */}}
                />
                <MenuItem
                  icon={<Trash2 size={14} />}
                  text="Delete"
                  intent="danger"
                  onClick={() => handleDeleteAnnotation(annotation.id)}
                />
              </Menu>
            }
            position="bottom-right"
          >
            <Button
              minimal
              small
              icon={<MoreVertical size={14} />}
              onClick={(e) => e.stopPropagation()}
            />
          </Popover>
        </div>
      </div>
    );
  }, [selectedAnnotations, handleAnnotationClick, handleToggleAnnotationVisibility, handleDeleteAnnotation]);

  // Render group header
  const renderGroupHeader = useCallback((groupName, annotations) => {
    const nodeId = `group-${groupName}`;
    const isExpanded = expandedNodes.has(nodeId);
    
    return (
      <div
        className="group-header"
        onClick={() => handleNodeToggle(nodeId)}
      >
        <Icon icon={isExpanded ? "chevron-down" : "chevron-right"} />
        <span className="group-name">{groupName}</span>
        <Tag minimal round className="group-count">
          {annotations.length}
        </Tag>
      </div>
    );
  }, [expandedNodes, handleNodeToggle]);

  // Render filter controls
  const renderFilterControls = () => (
    <div className="filter-controls">
      <Popover
        content={
          <div className="filter-menu">
            <div className="filter-section">
              <h4>Annotation Types</h4>
              {Object.values(ANNOTATION_TYPES).map(type => (
                <label key={type} className="filter-checkbox">
                  <input
                    type="checkbox"
                    checked={annotationFilters.types.includes(type)}
                    onChange={(e) => {
                      const newTypes = e.target.checked
                        ? [...annotationFilters.types, type]
                        : annotationFilters.types.filter(t => t !== type);
                      handleFilterChange('types', newTypes);
                    }}
                  />
                  {type.replace('_', ' ').toUpperCase()}
                </label>
              ))}
            </div>
            
            <div className="filter-section">
              <h4>Colors</h4>
              <div className="color-filters">
                {Object.entries(ANNOTATION_COLORS).map(([name, color]) => (
                  <button
                    key={name}
                    className={`color-filter ${annotationFilters.colors.includes(color) ? 'active' : ''}`}
                    style={{ backgroundColor: color }}
                    onClick={() => {
                      const newColors = annotationFilters.colors.includes(color)
                        ? annotationFilters.colors.filter(c => c !== color)
                        : [...annotationFilters.colors, color];
                      handleFilterChange('colors', newColors);
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        }
        position="bottom-left"
      >
        <Button
          minimal
          small
          icon={<Filter size={14} />}
          text="Filter"
        />
      </Popover>
      
      <ButtonGroup minimal>
        <Button
          small
          text="Group"
          rightIcon="caret-down"
        />
        <Button
          small
          text="Sort"
          rightIcon="caret-down"
        />
      </ButtonGroup>
    </div>
  );

  return (
    <Card className={`annotation-explorer ${isDarkTheme ? 'dark' : 'light'}`}>
      {/* Header */}
      <div className="explorer-header">
        <div className="header-title">
          <Icon icon="annotation" />
          <span>Annotations</span>
          <Tag minimal round>
            {allAnnotations.length}
          </Tag>
        </div>
        
        {renderFilterControls()}
      </div>

      {/* Search */}
      <div className="search-container">
        <InputGroup
          leftIcon={<Search size={14} />}
          placeholder="Search annotations..."
          value={searchQuery}
          onChange={handleSearchChange}
          small
        />
      </div>

      {/* Annotations List */}
      <div className="annotations-list">
        {Object.keys(groupedAnnotations).length === 0 ? (
          <NonIdealState
            icon="annotation"
            title="No annotations"
            description="Start annotating the PDF to see them here"
          />
        ) : (
          Object.entries(groupedAnnotations).map(([groupName, annotations]) => {
            const nodeId = `group-${groupName}`;
            const isExpanded = expandedNodes.has(nodeId);
            
            return (
              <div key={groupName} className="annotation-group">
                {renderGroupHeader(groupName, annotations)}
                <Collapse isOpen={isExpanded}>
                  <div className="group-content">
                    {annotations.map(renderAnnotationItem)}
                  </div>
                </Collapse>
              </div>
            );
          })
        )}
      </div>
    </Card>
  );
};

// Helper functions for grouping
const groupAnnotationsByAuthor = (annotations) => {
  return annotations.reduce((groups, annotation) => {
    const author = annotation.author || 'Unknown';
    if (!groups[author]) groups[author] = [];
    groups[author].push(annotation);
    return groups;
  }, {});
};

const groupAnnotationsByDate = (annotations) => {
  return annotations.reduce((groups, annotation) => {
    const date = new Date(annotation.createdAt).toDateString();
    if (!groups[date]) groups[date] = [];
    groups[date].push(annotation);
    return groups;
  }, {});
};

export default AnnotationExplorer;
