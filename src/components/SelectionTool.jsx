import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { Button, InputGroup } from '@blueprintjs/core';
import { ANNOTATION_TYPES, ANNOTATION_COLORS } from '../redux/ultimatePdfViewerSlice';

const SelectionTool = ({ tool, onAnnotationCreate, containerRef }) => {
  const [isDrawing, setIsDrawing] = useState(false);
  const [startPoint, setStartPoint] = useState(null);
  const [currentPoint, setCurrentPoint] = useState(null);
  const [selectedColor, setSelectedColor] = useState(ANNOTATION_COLORS.YELLOW);
  const [comment, setComment] = useState('');
  const [showCommentInput, setShowCommentInput] = useState(false);
  const overlayRef = useRef(null);

  // Redux selectors
  const viewerSettings = useSelector(state => state.ultimatePdfViewer.viewerSettings);

  // Handle mouse down
  const handleMouseDown = useCallback((event) => {
    if (tool === 'select' || tool === 'hand') return;

    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setStartPoint({ x, y });
    setIsDrawing(true);
    setCurrentPoint({ x, y });

    // Prevent default behavior
    event.preventDefault();
    event.stopPropagation();
  }, [tool, containerRef]);

  // Handle mouse move
  const handleMouseMove = useCallback((event) => {
    if (!isDrawing || !startPoint) return;

    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    setCurrentPoint({ x, y });
  }, [isDrawing, startPoint, containerRef]);

  // Handle mouse up
  const handleMouseUp = useCallback((event) => {
    if (!isDrawing || !startPoint || !currentPoint) return;

    const rect = containerRef.current?.getBoundingClientRect();
    if (!rect) return;

    // Calculate selection bounds
    const minX = Math.min(startPoint.x, currentPoint.x);
    const minY = Math.min(startPoint.y, currentPoint.y);
    const maxX = Math.max(startPoint.x, currentPoint.x);
    const maxY = Math.max(startPoint.y, currentPoint.y);

    const width = maxX - minX;
    const height = maxY - minY;

    // Only create annotation if selection is large enough
    if (width > 10 && height > 10) {
      const position = {
        x: minX,
        y: minY,
        width,
        height
      };

      // Determine page number (simplified - would need proper page detection)
      const pageNumber = 1; // TODO: Calculate actual page number based on scroll position

      const annotation = {
        type: tool === 'rectangle' ? ANNOTATION_TYPES.RECTANGLE : ANNOTATION_TYPES.AREA_HIGHLIGHT,
        pageNumber,
        position,
        color: selectedColor,
        comment: comment || undefined,
        content: {
          type: 'area',
          bounds: position
        }
      };

      onAnnotationCreate(annotation);

      // Show comment input for certain tools
      if (tool === 'comment' || tool === 'rectangle') {
        setShowCommentInput(true);
      }
    }

    // Reset drawing state
    setIsDrawing(false);
    setStartPoint(null);
    setCurrentPoint(null);
  }, [isDrawing, startPoint, currentPoint, tool, selectedColor, comment, onAnnotationCreate, containerRef]);

  // Handle comment submission
  const handleCommentSubmit = useCallback(() => {
    setShowCommentInput(false);
    setComment('');
  }, []);

  // Attach event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container || tool === 'select' || tool === 'hand') return;

    container.addEventListener('mousedown', handleMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      container.removeEventListener('mousedown', handleMouseDown);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseDown, handleMouseMove, handleMouseUp, tool, containerRef]);

  // Calculate selection rectangle
  const selectionRect = startPoint && currentPoint && isDrawing ? {
    left: Math.min(startPoint.x, currentPoint.x),
    top: Math.min(startPoint.y, currentPoint.y),
    width: Math.abs(currentPoint.x - startPoint.x),
    height: Math.abs(currentPoint.y - startPoint.y)
  } : null;

  // Get cursor style based on tool
  const getCursorStyle = () => {
    switch (tool) {
      case 'highlight':
        return 'text';
      case 'rectangle':
        return 'crosshair';
      case 'comment':
        return 'crosshair';
      case 'hand':
        return 'grab';
      default:
        return 'default';
    }
  };

  if (tool === 'select' || tool === 'hand') {
    return null;
  }

  return (
    <>
      {/* Selection overlay */}
      <div
        ref={overlayRef}
        className="selection-tool-overlay"
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          cursor: getCursorStyle(),
          pointerEvents: 'auto',
          zIndex: 1000
        }}
      >
        {/* Active selection rectangle */}
        {selectionRect && (
          <div
            className="selection-rectangle"
            style={{
              position: 'absolute',
              left: selectionRect.left,
              top: selectionRect.top,
              width: selectionRect.width,
              height: selectionRect.height,
              border: `2px solid ${selectedColor}`,
              backgroundColor: `${selectedColor}20`,
              pointerEvents: 'none'
            }}
          />
        )}
      </div>

      {/* Tool controls */}
      <div className="selection-tool-controls">
        <div className="tool-panel">
          <div className="color-selector">
            <span>Color:</span>
            <div className="color-options">
              {Object.entries(ANNOTATION_COLORS).map(([name, color]) => (
                <button
                  key={name}
                  className={`color-option ${selectedColor === color ? 'active' : ''}`}
                  style={{ backgroundColor: color }}
                  onClick={() => setSelectedColor(color)}
                  title={name}
                />
              ))}
            </div>
          </div>

          {(tool === 'comment' || tool === 'rectangle') && (
            <div className="comment-input">
              <InputGroup
                placeholder="Add a comment..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                small
              />
            </div>
          )}
        </div>
      </div>

      {/* Comment input dialog */}
      {showCommentInput && (
        <div className="comment-dialog">
          <div className="comment-dialog-content">
            <h4>Add Comment</h4>
            <InputGroup
              placeholder="Enter your comment..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              autoFocus
            />
            <div className="comment-dialog-actions">
              <Button
                text="Cancel"
                onClick={() => setShowCommentInput(false)}
              />
              <Button
                text="Add"
                intent="primary"
                onClick={handleCommentSubmit}
              />
            </div>
          </div>
        </div>
      )}

      {/* Tool instructions */}
      <div className="tool-instructions">
        {tool === 'rectangle' && (
          <div className="instruction">
            Click and drag to create a rectangle annotation
          </div>
        )}
        {tool === 'highlight' && (
          <div className="instruction">
            Select text to highlight it
          </div>
        )}
        {tool === 'comment' && (
          <div className="instruction">
            Click and drag to add a comment area
          </div>
        )}
      </div>
    </>
  );
};

export default SelectionTool;
