import React, { useState, useEffect, useRef } from 'react';
import { InputNumber, Button } from 'antd';
import { ArrowRight, X } from "lucide-react";
import { Button as BpButton } from "@blueprintjs/core";
import Draggable from 'react-draggable';

/**
 * GoToWindow component for navigating to a specific page in a PDF viewer
 */
export const GoToWindow = ({ isOpen, onClose, onGoTo, totalPages, isDarkTheme }) => {
    const [pageNumber, setPageNumber] = useState(1);
    const inputRef = useRef(null);
    
    // Focus input when window opens
    useEffect(() => {
        if (isOpen && inputRef.current) {
            setTimeout(() => {
                inputRef.current.focus();
            }, 100);
        }
    }, [isOpen]);
    
    if (!isOpen) return null;

    const handleGoTo = () => {
        if (pageNumber >= 1 && pageNumber <= totalPages) {
            onGoTo(pageNumber);
            onClose();
        }
    };
    
    const handleKeyPress = (e) => {
        if (e.key === 'Enter') {
            handleGoTo();
        }
    };

    return (
        <Draggable handle=".goto-window-header">
            <div className="goto-window" style={{
                position: 'absolute',
                top: '50px',
                left: '20px',
                width: '250px',
                backgroundColor: isDarkTheme ? '#2F343C' : '#FFFFFF',
                border: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
                borderRadius: '4px',
                boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                zIndex: 1000,
                color: isDarkTheme ? '#F5F8FA' : '#182026',
            }}>
                <div className="goto-window-header" style={{
                    padding: '5px 10px',
                    backgroundColor: isDarkTheme ? '#2F343C' : '#F5F8FA',
                    borderBottom: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
                    cursor: 'move',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    height: '32px',
                }}>
                    <span style={{ fontSize: '14px', fontWeight: 'bold' }}>Go to Page</span>
                    <BpButton 
                        icon={<X size={14} />} 
                        minimal={true} 
                        small={true} 
                        onClick={onClose} 
                    />
                </div>
                <div style={{ padding: '15px' }}>
                    <div style={{ marginBottom: '10px' }}>
                        <label style={{ 
                            display: 'block', 
                            marginBottom: '5px',
                            fontSize: '13px',
                            color: isDarkTheme ? '#E1E8ED' : '#5C7080'
                        }}>
                            Enter page number (1 - {totalPages}):
                        </label>
                        <InputNumber
                            ref={inputRef}
                            min={1}
                            max={totalPages}
                            value={pageNumber}
                            onChange={(value) => setPageNumber(value)}
                            onKeyPress={handleKeyPress}
                            style={{ 
                                width: '100%',
                                marginBottom: '15px'
                            }}
                        />
                    </div>
                    <Button 
                        type="primary"
                        icon={<ArrowRight size={16} />}
                        onClick={handleGoTo}
                        style={{ width: '100%' }}
                    >
                        Go to Page
                    </Button>
                </div>
            </div>
        </Draggable>
    );
};

export default GoToWindow;
