import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// PDF.js imports
import * as pdfjsLib from 'pdfjs-dist/build/pdf';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';

// React PDF Highlighter imports
import {
  PdfLoader,
  PdfHighlighter,
  Highlight,
  AreaHighlight,
  Popup,
  Tip
} from "react-pdf-highlighter";

// Redux actions
import {
  updateDocumentState,
  addAnnotation,
  updateAnnotation,
  removeAnnotation,
  selectDocumentAnnotations,
  selectAnnotationsByPage,
  setSelectedTool
} from '../redux/ultimatePdfViewerSlice';

// Utilities
import { 
  loadPDFDocument, 
  createPDFUrl, 
  pdfToScreenCoords, 
  screenToPdfCoords,
  validateAnnotationPosition,
  generateAnnotationId,
  throttle,
  debounce
} from '../utils/pdfUtils';

// Components
import PDFPage from './PDFPage';
import AnnotationOverlay from './AnnotationOverlay';
import SelectionTool from './SelectionTool';

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

const PDFRenderingEngine = ({ fileId, document, isDarkTheme }) => {
  const dispatch = useDispatch();
  const containerRef = useRef(null);
  const highlighterRef = useRef(null);
  const [pdfDocument, setPdfDocument] = useState(null);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [pageHeights, setPageHeights] = useState({});
  const [containerDimensions, setContainerDimensions] = useState({ width: 0, height: 0 });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Redux selectors
  const annotations = useSelector(state => selectDocumentAnnotations(state, fileId));
  const annotationsByPage = useSelector(state => selectAnnotationsByPage(state, fileId));
  const selectedTool = useSelector(state => state.ultimatePdfViewer.ui.selectedTool);
  const viewerSettings = useSelector(state => state.ultimatePdfViewer.viewerSettings);

  // Initialize PDF document
  useEffect(() => {
    if (document && fileId) {
      initializePDF();
    }
  }, [document, fileId]);

  const initializePDF = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Create PDF URL using fileId
      const url = await createPDFUrl(fileId);
      setPdfUrl(url);

      // Load PDF document using fileId
      const pdfDoc = await loadPDFDocument(fileId);
      setPdfDocument(pdfDoc);

      // Initialize page heights for virtualization
      const heights = {};
      for (let i = 1; i <= pdfDoc.numPages; i++) {
        const page = await pdfDoc.getPage(i);
        const viewport = page.getViewport({ scale: document.zoom || 1.0 });
        heights[i] = viewport.height;
      }
      setPageHeights(heights);

    } catch (err) {
      console.error('Failed to initialize PDF:', err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [fileId, document?.zoom]);

  // Handle container resize
  const updateContainerDimensions = useCallback(() => {
    if (containerRef.current) {
      const { clientWidth, clientHeight } = containerRef.current;
      setContainerDimensions({ width: clientWidth, height: clientHeight });
    }
  }, []);

  useEffect(() => {
    updateContainerDimensions();
    
    const resizeObserver = new ResizeObserver(throttle(updateContainerDimensions, 100));
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [updateContainerDimensions]);

  // Simplified page height calculation
  const estimatePageHeight = useCallback((index) => {
    const pageNumber = index + 1;
    const baseHeight = pageHeights[pageNumber] || 800;
    const scaledHeight = baseHeight * (document.zoom || 1.0);
    return scaledHeight + (viewerSettings.pageSpacing || 10);
  }, [pageHeights, document.zoom, viewerSettings.pageSpacing]);

  // Annotation handlers
  const handleAddAnnotation = useCallback((annotation) => {
    const newAnnotation = {
      ...annotation,
      id: generateAnnotationId(),
      fileId,
      author: 'Current User', // TODO: Get from auth state
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    dispatch(addAnnotation({ fileId, annotation: newAnnotation }));
  }, [fileId, dispatch]);

  const handleUpdateAnnotation = useCallback((annotationId, updates) => {
    dispatch(updateAnnotation({ fileId, annotationId, updates }));
  }, [fileId, dispatch]);

  const handleRemoveAnnotation = useCallback((annotationId) => {
    dispatch(removeAnnotation({ fileId, annotationId }));
  }, [fileId, dispatch]);

  // Text selection handler
  const handleTextSelection = useCallback((position, content, hideTipAndSelection, transformSelection) => {
    if (selectedTool !== 'highlight') return null;

    return (
      <Tip
        onOpen={transformSelection}
        onConfirm={(comment) => {
          handleAddAnnotation({
            type: 'text_highlight',
            pageNumber: position.pageNumber,
            position,
            content,
            comment,
            color: '#FFEB3B'
          });
          hideTipAndSelection();
        }}
      />
    );
  }, [selectedTool, handleAddAnnotation]);

  // Area selection handler
  const handleAreaSelection = useCallback((event) => {
    return selectedTool === 'rectangle' && event.altKey;
  }, [selectedTool]);

  // Highlight transform function
  const highlightTransform = useCallback((
    highlight,
    index,
    setTip,
    hideTip,
    viewportToScaled,
    screenshot,
    isScrolledTo
  ) => {
    const isTextHighlight = highlight.type === 'text_highlight';
    const isAreaHighlight = highlight.type === 'area_highlight' || highlight.type === 'rectangle';

    let component;

    if (isTextHighlight) {
      component = (
        <Highlight
          isScrolledTo={isScrolledTo}
          position={highlight.position}
          comment={highlight.comment}
          style={{
            backgroundColor: highlight.color,
            opacity: 0.6
          }}
        />
      );
    } else if (isAreaHighlight) {
      component = (
        <AreaHighlight
          isScrolledTo={isScrolledTo}
          highlight={highlight}
          onChange={(boundingRect) => {
            handleUpdateAnnotation(highlight.id, {
              position: { boundingRect: viewportToScaled(boundingRect) },
              content: { image: screenshot(boundingRect) }
            });
          }}
          style={{
            border: `2px solid ${highlight.color}`,
            backgroundColor: `${highlight.color}20`
          }}
        />
      );
    }

    return (
      <Popup
        popupContent={
          <div className="annotation-popup">
            <div className="annotation-header">
              <span className="annotation-type">{highlight.type}</span>
              <span className="annotation-author">{highlight.author}</span>
            </div>
            {highlight.comment && (
              <div className="annotation-comment">{highlight.comment}</div>
            )}
            <div className="annotation-actions">
              <button 
                onClick={() => handleRemoveAnnotation(highlight.id)}
                className="delete-annotation-btn"
              >
                Delete
              </button>
            </div>
          </div>
        }
        onMouseOver={(popupContent) => setTip(highlight, () => popupContent)}
        onMouseOut={hideTip}
        key={index}
      >
        {component}
      </Popup>
    );
  }, [handleUpdateAnnotation, handleRemoveAnnotation]);

  // Scroll to page
  const scrollToPage = useCallback((pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= document.numPages) {
      dispatch(updateDocumentState({
        fileId,
        updates: { currentPage: pageNumber }
      }));
    }
  }, [document.numPages, fileId, dispatch]);

  // Handle zoom changes
  const handleZoomChange = useCallback((newZoom) => {
    dispatch(updateDocumentState({ 
      fileId, 
      updates: { zoom: newZoom } 
    }));
  }, [fileId, dispatch]);

  // Handle page change
  const handlePageChange = useCallback(
    debounce((pageNumber) => {
      dispatch(updateDocumentState({ 
        fileId, 
        updates: { currentPage: pageNumber } 
      }));
    }, 300),
    [fileId, dispatch]
  );

  // Simplified page tracking
  useEffect(() => {
    // Simple page tracking without virtualization
    if (document.currentPage) {
      handlePageChange(document.currentPage);
    }
  }, [document.currentPage, handlePageChange]);

  // Render loading state
  if (isLoading) {
    return (
      <div className="pdf-rendering-engine loading">
        <div className="loading-spinner">Loading PDF...</div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="pdf-rendering-engine error">
        <div className="error-message">
          <h3>Failed to load PDF</h3>
          <p>{error}</p>
          <button onClick={initializePDF}>Retry</button>
        </div>
      </div>
    );
  }

  // Render PDF using iframe (similar to existing PDF viewers)
  return (
    <div
      ref={containerRef}
      className={`pdf-rendering-engine ${isDarkTheme ? 'dark' : 'light'}`}
      style={{
        height: '100%',
        width: '100%',
        background: isDarkTheme ? '#1C2127' : '#FFFFFF',
        color: isDarkTheme ? '#F5F8FA' : '#182026',
        position: 'relative'
      }}
    >
      {pdfUrl ? (
        <iframe
          title="Ultimate PDF Viewer"
          width="100%"
          height="100%"
          src={`${process.env.PUBLIC_URL}/pdfjs-3.11.174-dist/web/viewer.html?file=${encodeURIComponent(pdfUrl)}`}
          style={{
            border: 'none',
            width: '100%',
            height: '100%'
          }}
          onLoad={(event) => {
            const iframeDocument = event.target.contentDocument || event.target.contentWindow.document;

            // Apply dark theme
            const setThemeFunctionString = `
              function setTheme(theme) {
                if (theme == 'Dark') {
                  PDFViewerApplicationOptions.set('viewerCssTheme', 1);
                } else {
                  PDFViewerApplicationOptions.set('viewerCssTheme', 2);
                }
                PDFViewerApplication._forceCssTheme();
              }
            `;

            const script = iframeDocument.createElement('script');
            script.textContent = setThemeFunctionString;
            iframeDocument.head.appendChild(script);

            // Set theme
            iframeDocument.defaultView.setTheme(isDarkTheme ? 'Dark' : 'Light');

            // Store scroll function for external use
            window.pdfScrollTo = (pageNumber) => {
              if (iframeDocument.defaultView.PDFViewerApplication) {
                iframeDocument.defaultView.PDFViewerApplication.page = pageNumber;
              }
            };
          }}
        />
      ) : (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%'
        }}>
          <div style={{ textAlign: 'center' }}>
            <h3>Ultimate PDF Viewer</h3>
            <p>PDF: {document?.fileName}</p>
            <p>Pages: {document?.numPages || 'Loading...'}</p>
            <p>Current Page: {document?.currentPage || 1}</p>
            <p>Zoom: {Math.round((document?.zoom || 1) * 100)}%</p>
            <div style={{ marginTop: '20px', fontSize: '14px', opacity: 0.7 }}>
              <p>🚀 Loading PDF content...</p>
            </div>
          </div>
        </div>
      )}

      {/* Selection tools overlay */}
      {selectedTool !== 'select' && (
        <SelectionTool
          tool={selectedTool}
          onAnnotationCreate={handleAddAnnotation}
          containerRef={containerRef}
        />
      )}
    </div>
  );
};

export default PDFRenderingEngine;
