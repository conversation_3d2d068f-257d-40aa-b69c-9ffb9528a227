import React, { useState, useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  InputGroup,
  Button,
  ButtonGroup,
  Checkbox,
  Tag,
  Spinner,
  NonIdealState,
  Collapse,
  Icon
} from '@blueprintjs/core';
import {
  Search,
  X,
  ChevronUp,
  ChevronDown,
  RotateCcw,
  Settings
} from 'lucide-react';

// Redux actions
import {
  updateSearchState,
  setSearchResults,
  navigateSearchResult,
  updateDocumentState,
  selectActiveDocument
} from '../redux/ultimatePdfViewerSlice';

// Utilities
import { loadPDFDocument, searchInPDF } from '../utils/pdfUtils';

const SearchPanel = ({ fileId, isDarkTheme }) => {
  const dispatch = useDispatch();
  const searchInputRef = useRef(null);
  const [isSearching, setIsSearching] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Redux selectors
  const document = useSelector(selectActiveDocument);
  const searchState = useSelector(state => state.ultimatePdfViewer.search);

  // Focus search input when panel opens
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Perform search
  const performSearch = useCallback(async (query, options = {}) => {
    if (!query.trim() || !document) return;

    setIsSearching(true);
    dispatch(updateSearchState({ isSearching: true }));

    try {
      const pdfDocument = await loadPDFDocument(document.filePath);
      const results = await searchInPDF(pdfDocument, query, {
        caseSensitive: options.caseSensitive || searchState.caseSensitive,
        wholeWords: options.wholeWords || searchState.wholeWords,
        maxResults: 100
      });

      dispatch(setSearchResults({ results, query }));
    } catch (error) {
      console.error('Search failed:', error);
      dispatch(setSearchResults({ results: [], query }));
    } finally {
      setIsSearching(false);
      dispatch(updateSearchState({ isSearching: false }));
    }
  }, [document, searchState.caseSensitive, searchState.wholeWords, dispatch]);

  // Handle search input change
  const handleSearchChange = useCallback((event) => {
    const query = event.target.value;
    dispatch(updateSearchState({ query }));
    
    // Debounced search
    if (query.trim()) {
      const timeoutId = setTimeout(() => {
        performSearch(query);
      }, 300);
      
      return () => clearTimeout(timeoutId);
    } else {
      dispatch(setSearchResults({ results: [], query: '' }));
    }
  }, [dispatch, performSearch]);

  // Handle search submit
  const handleSearchSubmit = useCallback((event) => {
    event.preventDefault();
    if (searchState.query.trim()) {
      performSearch(searchState.query);
    }
  }, [searchState.query, performSearch]);

  // Handle search navigation
  const handleNavigateResult = useCallback((direction) => {
    dispatch(navigateSearchResult(direction));
    
    // Scroll to the current result
    const currentResult = searchState.results[searchState.currentResultIndex];
    if (currentResult) {
      // Navigate to the page
      dispatch(updateDocumentState({
        fileId,
        updates: { currentPage: currentResult.pageNumber }
      }));
      
      // Scroll to page in main viewer
      if (window.pdfScrollTo) {
        window.pdfScrollTo(currentResult.pageNumber);
      }
    }
  }, [dispatch, searchState.results, searchState.currentResultIndex, fileId]);

  // Handle option changes
  const handleOptionChange = useCallback((option, value) => {
    dispatch(updateSearchState({ [option]: value }));
    
    // Re-search if there's a query
    if (searchState.query.trim()) {
      performSearch(searchState.query, { [option]: value });
    }
  }, [dispatch, searchState.query, performSearch]);

  // Clear search
  const handleClearSearch = useCallback(() => {
    dispatch(updateSearchState({ query: '' }));
    dispatch(setSearchResults({ results: [], query: '' }));
  }, [dispatch]);

  // Render search result item
  const renderSearchResult = useCallback((result, index) => {
    const isActive = index === searchState.currentResultIndex;
    
    return (
      <div
        key={`${result.pageNumber}-${result.index}`}
        className={`search-result-item ${isActive ? 'active' : ''}`}
        onClick={() => {
          dispatch(updateSearchState({ currentResultIndex: index }));
          dispatch(updateDocumentState({
            fileId,
            updates: { currentPage: result.pageNumber }
          }));
          if (window.pdfScrollTo) {
            window.pdfScrollTo(result.pageNumber);
          }
        }}
      >
        <div className="result-header">
          <span className="result-page">Page {result.pageNumber}</span>
          <span className="result-position">#{index + 1}</span>
        </div>
        <div className="result-context">
          {result.context}
        </div>
      </div>
    );
  }, [searchState.currentResultIndex, dispatch, fileId]);

  const hasResults = searchState.results.length > 0;
  const currentResultNumber = searchState.currentResultIndex + 1;

  return (
    <Card className={`search-panel ${isDarkTheme ? 'dark' : 'light'}`}>
      {/* Header */}
      <div className="search-header">
        <div className="header-title">
          <Search size={16} />
          <span>Search</span>
        </div>
      </div>

      {/* Search Input */}
      <form onSubmit={handleSearchSubmit} className="search-form">
        <InputGroup
          inputRef={searchInputRef}
          leftIcon={<Search size={14} />}
          placeholder="Search in document..."
          value={searchState.query}
          onChange={handleSearchChange}
          rightElement={
            <div className="search-input-actions">
              {isSearching && <Spinner size={16} />}
              {searchState.query && (
                <Button
                  minimal
                  small
                  icon={<X size={14} />}
                  onClick={handleClearSearch}
                />
              )}
            </div>
          }
        />
      </form>

      {/* Search Options */}
      <div className="search-options">
        <Button
          minimal
          small
          text="Advanced"
          rightIcon={showAdvanced ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
          onClick={() => setShowAdvanced(!showAdvanced)}
        />
        
        <Collapse isOpen={showAdvanced}>
          <div className="advanced-options">
            <Checkbox
              checked={searchState.caseSensitive}
              onChange={(e) => handleOptionChange('caseSensitive', e.target.checked)}
              label="Case sensitive"
            />
            <Checkbox
              checked={searchState.wholeWords}
              onChange={(e) => handleOptionChange('wholeWords', e.target.checked)}
              label="Whole words only"
            />
          </div>
        </Collapse>
      </div>

      {/* Search Results Summary */}
      {searchState.query && (
        <div className="search-summary">
          {isSearching ? (
            <div className="searching-indicator">
              <Spinner size={16} />
              <span>Searching...</span>
            </div>
          ) : hasResults ? (
            <div className="results-summary">
              <span className="results-count">
                {searchState.results.length} result{searchState.results.length !== 1 ? 's' : ''}
              </span>
              {hasResults && (
                <div className="navigation-controls">
                  <span className="current-result">
                    {currentResultNumber} of {searchState.results.length}
                  </span>
                  <ButtonGroup minimal>
                    <Button
                      small
                      icon={<ChevronUp size={14} />}
                      onClick={() => handleNavigateResult('previous')}
                      disabled={searchState.currentResultIndex <= 0}
                    />
                    <Button
                      small
                      icon={<ChevronDown size={14} />}
                      onClick={() => handleNavigateResult('next')}
                      disabled={searchState.currentResultIndex >= searchState.results.length - 1}
                    />
                  </ButtonGroup>
                </div>
              )}
            </div>
          ) : (
            <div className="no-results">
              No results found for "{searchState.query}"
            </div>
          )}
        </div>
      )}

      {/* Search Results List */}
      <div className="search-results">
        {!searchState.query ? (
          <NonIdealState
            icon={<Search size={32} />}
            title="Search Document"
            description="Enter text to search for in the PDF document"
          />
        ) : hasResults ? (
          <div className="results-list">
            {searchState.results.map(renderSearchResult)}
          </div>
        ) : searchState.query && !isSearching ? (
          <NonIdealState
            icon="search"
            title="No Results"
            description={`No matches found for "${searchState.query}"`}
            action={
              <Button
                icon={<RotateCcw size={14} />}
                text="Clear Search"
                onClick={handleClearSearch}
              />
            }
          />
        ) : null}
      </div>

      {/* Search Statistics */}
      {hasResults && (
        <div className="search-stats">
          <div className="stats-item">
            <span className="stats-label">Pages with results:</span>
            <span className="stats-value">
              {new Set(searchState.results.map(r => r.pageNumber)).size}
            </span>
          </div>
          <div className="stats-item">
            <span className="stats-label">Total matches:</span>
            <span className="stats-value">{searchState.results.length}</span>
          </div>
        </div>
      )}
    </Card>
  );
};

export default SearchPanel;
