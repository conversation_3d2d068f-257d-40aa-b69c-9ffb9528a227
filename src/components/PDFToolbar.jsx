import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Navbar,
  NavbarGroup,
  NavbarDivider,
  Button,
  ButtonGroup,
  InputGroup,
  HTMLSelect,
  Slider,
  Popover,
  Menu,
  MenuItem,
  MenuDivider,
  Icon,
  Tooltip
} from '@blueprintjs/core';
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  RotateCw,
  Search,
  Download,
  Printer,
  Settings,
  Maximize,
  Minimize,
  X,
  FileText,
  Square,
  Highlighter,
  MessageSquare,
  Hand,
  MousePointer
} from 'lucide-react';

// Redux actions
import {
  updateDocumentState,
  updateUIState,
  setSelectedTool,
  togglePanel,
  updateViewerSettings,
  selectActiveDocument
} from '../redux/ultimatePdfViewerSlice';

// Utilities
import { exportAnnotationsToJSON } from '../utils/pdfUtils';

const PDFToolbar = ({ fileId, onClose, isDarkTheme }) => {
  const dispatch = useDispatch();
  const [pageInput, setPageInput] = useState('');

  // Redux selectors
  const document = useSelector(selectActiveDocument);
  const annotations = useSelector(state => state.ultimatePdfViewer.annotations[fileId] || []);
  const selectedTool = useSelector(state => state.ultimatePdfViewer.ui.selectedTool);
  const ui = useSelector(state => state.ultimatePdfViewer.ui);
  const viewerSettings = useSelector(state => state.ultimatePdfViewer.viewerSettings);

  // Zoom controls
  const handleZoomIn = useCallback(() => {
    if (document) {
      const newZoom = Math.min(document.zoom * 1.2, 5.0);
      dispatch(updateDocumentState({ fileId, updates: { zoom: newZoom } }));
    }
  }, [document, fileId, dispatch]);

  const handleZoomOut = useCallback(() => {
    if (document) {
      const newZoom = Math.max(document.zoom / 1.2, 0.1);
      dispatch(updateDocumentState({ fileId, updates: { zoom: newZoom } }));
    }
  }, [document, fileId, dispatch]);

  const handleZoomChange = useCallback((value) => {
    dispatch(updateDocumentState({ fileId, updates: { zoom: value } }));
  }, [fileId, dispatch]);

  const handleZoomPreset = useCallback((zoom) => {
    dispatch(updateDocumentState({ fileId, updates: { zoom } }));
  }, [fileId, dispatch]);

  // Rotation controls
  const handleRotateLeft = useCallback(() => {
    if (document) {
      const newRotation = (document.rotation - 90 + 360) % 360;
      dispatch(updateDocumentState({ fileId, updates: { rotation: newRotation } }));
    }
  }, [document, fileId, dispatch]);

  const handleRotateRight = useCallback(() => {
    if (document) {
      const newRotation = (document.rotation + 90) % 360;
      dispatch(updateDocumentState({ fileId, updates: { rotation: newRotation } }));
    }
  }, [document, fileId, dispatch]);

  // Page navigation
  const handlePageChange = useCallback((event) => {
    const value = event.target.value;
    setPageInput(value);
    
    const pageNumber = parseInt(value, 10);
    if (pageNumber >= 1 && pageNumber <= document?.numPages) {
      dispatch(updateDocumentState({ fileId, updates: { currentPage: pageNumber } }));
      // Trigger scroll to page
      if (window.pdfScrollTo) {
        window.pdfScrollTo(pageNumber);
      }
    }
  }, [document?.numPages, fileId, dispatch]);

  const handlePageInputKeyPress = useCallback((event) => {
    if (event.key === 'Enter') {
      handlePageChange(event);
    }
  }, [handlePageChange]);

  // Tool selection
  const handleToolSelect = useCallback((tool) => {
    dispatch(setSelectedTool(tool));
  }, [dispatch]);

  // Panel toggles
  const handleTogglePanel = useCallback((panel) => {
    dispatch(togglePanel({ panel }));
  }, [dispatch]);

  // Export annotations
  const handleExportAnnotations = useCallback(() => {
    const exportData = exportAnnotationsToJSON(annotations, {
      fileId,
      fileName: document?.fileName,
      filePath: document?.filePath,
      numPages: document?.numPages
    });
    
    const blob = new Blob([exportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${document?.fileName || 'annotations'}_annotations.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [annotations, document, fileId]);

  // Print PDF
  const handlePrint = useCallback(() => {
    window.print();
  }, []);

  // Fullscreen toggle
  const handleToggleFullscreen = useCallback(() => {
    dispatch(updateUIState({ isFullscreen: !ui.isFullscreen }));
  }, [ui.isFullscreen, dispatch]);

  if (!document) return null;

  const zoomPercentage = Math.round(document.zoom * 100);

  return (
    <Navbar className="pdf-toolbar">
      {/* Left Section - File Info and Navigation */}
      <NavbarGroup align="left">
        <div className="file-info">
          <Icon icon="document" />
          <span className="file-name">{document.fileName}</span>
        </div>
        
        <NavbarDivider />
        
        {/* Page Navigation */}
        <ButtonGroup minimal>
          <Button
            icon="chevron-left"
            disabled={document.currentPage <= 1}
            onClick={() => handlePageChange({ target: { value: document.currentPage - 1 } })}
          />
          <InputGroup
            value={pageInput || document.currentPage}
            onChange={(e) => setPageInput(e.target.value)}
            onBlur={handlePageChange}
            onKeyPress={handlePageInputKeyPress}
            style={{ width: '60px' }}
            small
          />
          <span className="page-info">of {document.numPages}</span>
          <Button
            icon="chevron-right"
            disabled={document.currentPage >= document.numPages}
            onClick={() => handlePageChange({ target: { value: document.currentPage + 1 } })}
          />
        </ButtonGroup>
      </NavbarGroup>

      {/* Center Section - Tools */}
      <NavbarGroup align="center">
        <ButtonGroup>
          <Tooltip content="Select Tool">
            <Button
              icon={<MousePointer size={16} />}
              active={selectedTool === 'select'}
              onClick={() => handleToolSelect('select')}
            />
          </Tooltip>
          <Tooltip content="Hand Tool">
            <Button
              icon={<Hand size={16} />}
              active={selectedTool === 'hand'}
              onClick={() => handleToolSelect('hand')}
            />
          </Tooltip>
          <Tooltip content="Text Highlight">
            <Button
              icon={<Highlighter size={16} />}
              active={selectedTool === 'highlight'}
              onClick={() => handleToolSelect('highlight')}
            />
          </Tooltip>
          <Tooltip content="Rectangle Annotation">
            <Button
              icon={<Square size={16} />}
              active={selectedTool === 'rectangle'}
              onClick={() => handleToolSelect('rectangle')}
            />
          </Tooltip>
          <Tooltip content="Comment">
            <Button
              icon={<MessageSquare size={16} />}
              active={selectedTool === 'comment'}
              onClick={() => handleToolSelect('comment')}
            />
          </Tooltip>
        </ButtonGroup>

        <NavbarDivider />

        {/* Zoom Controls */}
        <ButtonGroup>
          <Tooltip content="Zoom Out">
            <Button
              icon={<ZoomOut size={16} />}
              onClick={handleZoomOut}
              disabled={document.zoom <= 0.1}
            />
          </Tooltip>
          
          <Popover
            content={
              <div style={{ padding: '10px', width: '200px' }}>
                <Slider
                  min={0.1}
                  max={5.0}
                  stepSize={0.1}
                  value={document.zoom}
                  onChange={handleZoomChange}
                  labelRenderer={(value) => `${Math.round(value * 100)}%`}
                />
                <div style={{ marginTop: '10px' }}>
                  <Button small onClick={() => handleZoomPreset(0.5)}>50%</Button>
                  <Button small onClick={() => handleZoomPreset(1.0)}>100%</Button>
                  <Button small onClick={() => handleZoomPreset(1.5)}>150%</Button>
                  <Button small onClick={() => handleZoomPreset(2.0)}>200%</Button>
                </div>
              </div>
            }
            position="bottom"
          >
            <Button text={`${zoomPercentage}%`} rightIcon="caret-down" />
          </Popover>
          
          <Tooltip content="Zoom In">
            <Button
              icon={<ZoomIn size={16} />}
              onClick={handleZoomIn}
              disabled={document.zoom >= 5.0}
            />
          </Tooltip>
        </ButtonGroup>

        <NavbarDivider />

        {/* Rotation Controls */}
        <ButtonGroup>
          <Tooltip content="Rotate Left">
            <Button
              icon={<RotateCcw size={16} />}
              onClick={handleRotateLeft}
            />
          </Tooltip>
          <Tooltip content="Rotate Right">
            <Button
              icon={<RotateCw size={16} />}
              onClick={handleRotateRight}
            />
          </Tooltip>
        </ButtonGroup>
      </NavbarGroup>

      {/* Right Section - Actions and Panels */}
      <NavbarGroup align="right">
        <ButtonGroup>
          <Tooltip content="Search">
            <Button
              icon={<Search size={16} />}
              active={ui.isSearchPanelOpen}
              onClick={() => handleTogglePanel('isSearchPanelOpen')}
            />
          </Tooltip>
          <Tooltip content="Annotations">
            <Button
              icon={<FileText size={16} />}
              active={ui.isAnnotationExplorerOpen}
              onClick={() => handleTogglePanel('isAnnotationExplorerOpen')}
            />
          </Tooltip>
          <Tooltip content="Thumbnails">
            <Button
              icon="grid-view"
              active={ui.isThumbnailPanelOpen}
              onClick={() => handleTogglePanel('isThumbnailPanelOpen')}
            />
          </Tooltip>
        </ButtonGroup>

        <NavbarDivider />

        <ButtonGroup>
          <Tooltip content="Export Annotations">
            <Button
              icon={<Download size={16} />}
              onClick={handleExportAnnotations}
              disabled={annotations.length === 0}
            />
          </Tooltip>
          <Tooltip content="Print">
            <Button
              icon={<Printer size={16} />}
              onClick={handlePrint}
            />
          </Tooltip>
          
          <Popover
            content={
              <Menu>
                <MenuItem
                  icon="eye-open"
                  text="Show Annotations"
                  onClick={() => dispatch(updateViewerSettings({ showAnnotations: !viewerSettings.showAnnotations }))}
                />
                <MenuItem
                  icon="grid"
                  text="Show Grid"
                  onClick={() => dispatch(updateUIState({ showGrid: !ui.showGrid }))}
                />
                <MenuItem
                  icon="ruler"
                  text="Show Rulers"
                  onClick={() => dispatch(updateUIState({ showRulers: !ui.showRulers }))}
                />
                <MenuDivider />
                <MenuItem
                  icon="cog"
                  text="Settings"
                  onClick={() => dispatch(updateUIState({ isPropertiesPanelOpen: !ui.isPropertiesPanelOpen }))}
                />
              </Menu>
            }
            position="bottom-right"
          >
            <Button icon={<Settings size={16} />} />
          </Popover>
        </ButtonGroup>

        <NavbarDivider />

        <ButtonGroup>
          <Tooltip content={ui.isFullscreen ? "Exit Fullscreen" : "Fullscreen"}>
            <Button
              icon={ui.isFullscreen ? <Minimize size={16} /> : <Maximize size={16} />}
              onClick={handleToggleFullscreen}
            />
          </Tooltip>
          {onClose && (
            <Tooltip content="Close">
              <Button
                icon={<X size={16} />}
                onClick={onClose}
              />
            </Tooltip>
          )}
        </ButtonGroup>
      </NavbarGroup>
    </Navbar>
  );
};

export default PDFToolbar;
