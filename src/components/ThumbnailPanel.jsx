import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  <PERSON>,
  Spinner,
  Button,
  ButtonGroup,
  Slider,
  Tooltip,
  NonIdealState
} from '@blueprintjs/core';
import { Grid, List as ListIcon, ZoomIn, ZoomOut } from 'lucide-react';

// Redux actions
import {
  updateDocumentState,
  selectActiveDocument,
  selectAnnotationsByPage
} from '../redux/ultimatePdfViewerSlice';

// Utilities
import { loadPDFDocument, getPageThumbnail } from '../utils/pdfUtils';

const ThumbnailPanel = ({ fileId, isDarkTheme }) => {
  const dispatch = useDispatch();
  const listRef = useRef(null);
  const [thumbnails, setThumbnails] = useState({});
  const [thumbnailSize, setThumbnailSize] = useState(150);
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPages, setLoadingPages] = useState(new Set());

  // Redux selectors
  const document = useSelector(selectActiveDocument);
  const annotationsByPage = useSelector(state => selectAnnotationsByPage(state, fileId));

  // Calculate thumbnail scale based on size
  const thumbnailScale = useMemo(() => {
    return thumbnailSize / 600; // Base scale for 600px width
  }, [thumbnailSize]);

  // Load thumbnails for visible pages
  const loadThumbnail = useCallback(async (pageNumber) => {
    if (thumbnails[pageNumber] || loadingPages.has(pageNumber) || !document) return;

    setLoadingPages(prev => new Set(prev).add(pageNumber));

    try {
      const pdfDocument = await loadPDFDocument(document.filePath);
      const page = await pdfDocument.getPage(pageNumber);
      const thumbnailUrl = await getPageThumbnail(page, thumbnailScale);
      
      setThumbnails(prev => ({
        ...prev,
        [pageNumber]: thumbnailUrl
      }));
    } catch (error) {
      console.error(`Failed to load thumbnail for page ${pageNumber}:`, error);
    } finally {
      setLoadingPages(prev => {
        const newSet = new Set(prev);
        newSet.delete(pageNumber);
        return newSet;
      });
    }
  }, [document, thumbnailScale, thumbnails, loadingPages]);

  // Load thumbnails for all pages (with throttling)
  useEffect(() => {
    if (!document) return;

    const loadAllThumbnails = async () => {
      setIsLoading(true);
      
      // Load thumbnails in batches to avoid overwhelming the browser
      const batchSize = 5;
      for (let i = 1; i <= document.numPages; i += batchSize) {
        const batch = [];
        for (let j = i; j < Math.min(i + batchSize, document.numPages + 1); j++) {
          batch.push(loadThumbnail(j));
        }
        await Promise.all(batch);
        
        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      setIsLoading(false);
    };

    loadAllThumbnails();
  }, [document, loadThumbnail]);

  // Handle page click
  const handlePageClick = useCallback((pageNumber) => {
    dispatch(updateDocumentState({ 
      fileId, 
      updates: { currentPage: pageNumber } 
    }));
    
    // Scroll to page in main viewer
    if (window.pdfScrollTo) {
      window.pdfScrollTo(pageNumber);
    }
  }, [fileId, dispatch]);

  // Handle thumbnail size change
  const handleThumbnailSizeChange = useCallback((size) => {
    setThumbnailSize(size);
    // Clear existing thumbnails to regenerate with new size
    setThumbnails({});
  }, []);

  // Render thumbnail item
  const renderThumbnailItem = useCallback((pageNumber) => {
    const isCurrentPage = document?.currentPage === pageNumber;
    const pageAnnotations = annotationsByPage[pageNumber] || [];
    const thumbnailUrl = thumbnails[pageNumber];
    const isLoadingThumbnail = loadingPages.has(pageNumber);

    return (
      <div key={pageNumber} className="thumbnail-item-container" data-page={pageNumber}>
        <div
          className={`thumbnail-item ${isCurrentPage ? 'current' : ''} ${viewMode}`}
          onClick={() => handlePageClick(pageNumber)}
        >
          <div className="thumbnail-image-container">
            {isLoadingThumbnail ? (
              <div className="thumbnail-loading">
                <Spinner size={20} />
              </div>
            ) : thumbnailUrl ? (
              <img
                src={thumbnailUrl}
                alt={`Page ${pageNumber}`}
                className="thumbnail-image"
                style={{
                  width: viewMode === 'grid' ? thumbnailSize : thumbnailSize * 0.6,
                  height: 'auto'
                }}
              />
            ) : (
              <div className="thumbnail-placeholder">
                <span>Page {pageNumber}</span>
              </div>
            )}

            {/* Annotation indicators */}
            {pageAnnotations.length > 0 && (
              <div className="annotation-indicators">
                <div className="annotation-count">
                  {pageAnnotations.length}
                </div>
                <div className="annotation-dots">
                  {pageAnnotations.slice(0, 3).map((annotation, idx) => (
                    <div
                      key={annotation.id}
                      className="annotation-dot"
                      style={{ backgroundColor: annotation.color }}
                    />
                  ))}
                  {pageAnnotations.length > 3 && (
                    <div className="annotation-dot more">+</div>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="thumbnail-label">
            <span className="page-number">Page {pageNumber}</span>
            {viewMode === 'list' && pageAnnotations.length > 0 && (
              <span className="annotation-summary">
                {pageAnnotations.length} annotation{pageAnnotations.length !== 1 ? 's' : ''}
              </span>
            )}
          </div>
        </div>
      </div>
    );
  }, [
    document?.currentPage,
    annotationsByPage,
    thumbnails,
    loadingPages,
    viewMode,
    thumbnailSize,
    handlePageClick
  ]);

  // Calculate item size based on view mode and thumbnail size
  const itemSize = useMemo(() => {
    if (viewMode === 'grid') {
      return thumbnailSize + 60; // Add space for label and padding
    } else {
      return 80; // Fixed height for list view
    }
  }, [viewMode, thumbnailSize]);

  // Scroll to current page (simplified)
  useEffect(() => {
    if (document?.currentPage && listRef.current) {
      // Simple scroll to current page
      const currentPageElement = listRef.current.querySelector(`[data-page="${document.currentPage}"]`);
      if (currentPageElement) {
        currentPageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  }, [document?.currentPage]);

  if (!document) {
    return (
      <Card className="thumbnail-panel">
        <NonIdealState
          icon="document"
          title="No Document"
          description="Open a PDF to see thumbnails"
        />
      </Card>
    );
  }

  return (
    <Card className={`thumbnail-panel ${isDarkTheme ? 'dark' : 'light'}`}>
      {/* Header */}
      <div className="thumbnail-header">
        <div className="header-title">
          <span>Pages</span>
          <span className="page-count">({document.numPages})</span>
        </div>
        
        <div className="header-controls">
          <ButtonGroup minimal>
            <Tooltip content="Grid View">
              <Button
                icon={<Grid size={14} />}
                active={viewMode === 'grid'}
                onClick={() => setViewMode('grid')}
                small
              />
            </Tooltip>
            <Tooltip content="List View">
              <Button
                icon={<ListIcon size={14} />}
                active={viewMode === 'list'}
                onClick={() => setViewMode('list')}
                small
              />
            </Tooltip>
          </ButtonGroup>
        </div>
      </div>

      {/* Size Control */}
      <div className="size-control">
        <Button
          minimal
          small
          icon={<ZoomOut size={12} />}
          onClick={() => handleThumbnailSizeChange(Math.max(100, thumbnailSize - 25))}
          disabled={thumbnailSize <= 100}
        />
        <Slider
          min={100}
          max={300}
          stepSize={25}
          value={thumbnailSize}
          onChange={handleThumbnailSizeChange}
          showTrackFill={false}
          className="size-slider"
        />
        <Button
          minimal
          small
          icon={<ZoomIn size={12} />}
          onClick={() => handleThumbnailSizeChange(Math.min(300, thumbnailSize + 25))}
          disabled={thumbnailSize >= 300}
        />
      </div>

      {/* Thumbnails List */}
      <div className="thumbnails-container">
        {isLoading && Object.keys(thumbnails).length === 0 ? (
          <div className="loading-container">
            <Spinner />
            <p>Loading thumbnails...</p>
          </div>
        ) : (
          <div
            ref={listRef}
            className={`thumbnails-list ${viewMode}`}
            style={{ height: '400px', overflowY: 'auto' }}
          >
            {Array.from({ length: document.numPages }, (_, index) =>
              renderThumbnailItem(index + 1)
            )}
          </div>
        )}
      </div>

      {/* Loading indicator for additional thumbnails */}
      {isLoading && Object.keys(thumbnails).length > 0 && (
        <div className="loading-more">
          <Spinner size={16} />
          <span>Loading more thumbnails...</span>
        </div>
      )}
    </Card>
  );
};

export default ThumbnailPanel;
