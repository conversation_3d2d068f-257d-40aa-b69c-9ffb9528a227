import React, { useState, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Card,
  Button,
  FormGroup,
  InputGroup,
  HTMLSelect,
  Switch,
  Slider,
  Divider,
  Tag,
  Collapse,
  Icon
} from '@blueprintjs/core';
import { <PERSON>, Settings, FileText, Palette, Grid, Eye } from 'lucide-react';

// Redux actions
import {
  updateViewerSettings,
  updateUIState,
  selectActiveDocument,
  selectDocumentAnnotations
} from '../redux/ultimatePdfViewerSlice';

// Utilities
import { getAnnotationStats, formatDate } from '../utils/annotationUtils';

const PropertiesPanel = ({ fileId, isDarkTheme }) => {
  const dispatch = useDispatch();
  const [activeSection, setActiveSection] = useState('document');

  // Redux selectors
  const document = useSelector(selectActiveDocument);
  const annotations = useSelector(state => selectDocumentAnnotations(state, fileId));
  const viewerSettings = useSelector(state => state.ultimatePdfViewer.viewerSettings);
  const ui = useSelector(state => state.ultimatePdfViewer.ui);

  // Calculate annotation statistics
  const annotationStats = getAnnotationStats(annotations);

  // Handle settings changes
  const handleViewerSettingChange = useCallback((setting, value) => {
    dispatch(updateViewerSettings({ [setting]: value }));
  }, [dispatch]);

  const handleUISettingChange = useCallback((setting, value) => {
    dispatch(updateUIState({ [setting]: value }));
  }, [dispatch]);

  // Close panel
  const handleClose = useCallback(() => {
    dispatch(updateUIState({ isPropertiesPanelOpen: false }));
  }, [dispatch]);

  // Render document properties
  const renderDocumentProperties = () => (
    <div className="properties-section">
      <h4>Document Information</h4>
      
      <FormGroup label="File Name">
        <InputGroup value={document?.fileName || ''} readOnly />
      </FormGroup>
      
      <FormGroup label="File Path">
        <InputGroup value={document?.filePath || ''} readOnly />
      </FormGroup>
      
      <FormGroup label="Pages">
        <InputGroup value={document?.numPages?.toString() || '0'} readOnly />
      </FormGroup>
      
      <FormGroup label="Current Page">
        <InputGroup value={document?.currentPage?.toString() || '1'} readOnly />
      </FormGroup>
      
      <FormGroup label="Zoom Level">
        <InputGroup value={`${Math.round((document?.zoom || 1) * 100)}%`} readOnly />
      </FormGroup>
      
      <FormGroup label="Rotation">
        <InputGroup value={`${document?.rotation || 0}°`} readOnly />
      </FormGroup>
      
      <FormGroup label="Last Viewed">
        <InputGroup 
          value={document?.lastViewed ? formatDate(document.lastViewed) : 'Never'} 
          readOnly 
        />
      </FormGroup>
    </div>
  );

  // Render annotation statistics
  const renderAnnotationStats = () => (
    <div className="properties-section">
      <h4>Annotation Statistics</h4>
      
      <div className="stats-grid">
        <div className="stat-item">
          <span className="stat-label">Total Annotations</span>
          <Tag intent="primary">{annotationStats.total}</Tag>
        </div>
        
        <div className="stat-item">
          <span className="stat-label">Pages with Annotations</span>
          <Tag>{Object.keys(annotationStats.byPage).length}</Tag>
        </div>
      </div>
      
      <Divider />
      
      <h5>By Type</h5>
      <div className="type-stats">
        {Object.entries(annotationStats.byType).map(([type, count]) => (
          <div key={type} className="type-stat">
            <span className="type-name">{type.replace('_', ' ').toUpperCase()}</span>
            <Tag minimal>{count}</Tag>
          </div>
        ))}
      </div>
      
      <Divider />
      
      <h5>By Author</h5>
      <div className="author-stats">
        {Object.entries(annotationStats.byAuthor).map(([author, count]) => (
          <div key={author} className="author-stat">
            <span className="author-name">{author}</span>
            <Tag minimal>{count}</Tag>
          </div>
        ))}
      </div>
      
      {annotationStats.dateRange && (
        <>
          <Divider />
          <h5>Date Range</h5>
          <div className="date-range">
            <div>First: {formatDate(annotationStats.dateRange.start)}</div>
            <div>Latest: {formatDate(annotationStats.dateRange.end)}</div>
          </div>
        </>
      )}
    </div>
  );

  // Render viewer settings
  const renderViewerSettings = () => (
    <div className="properties-section">
      <h4>Viewer Settings</h4>
      
      <FormGroup label="Theme">
        <HTMLSelect
          value={viewerSettings.theme}
          onChange={(e) => handleViewerSettingChange('theme', e.target.value)}
        >
          <option value="dark">Dark</option>
          <option value="light">Light</option>
          <option value="auto">Auto</option>
        </HTMLSelect>
      </FormGroup>
      
      <FormGroup label="Default Zoom">
        <Slider
          min={0.1}
          max={3.0}
          stepSize={0.1}
          value={viewerSettings.defaultZoom}
          onChange={(value) => handleViewerSettingChange('defaultZoom', value)}
          labelRenderer={(value) => `${Math.round(value * 100)}%`}
        />
      </FormGroup>
      
      <FormGroup label="Page Spacing">
        <Slider
          min={0}
          max={50}
          stepSize={5}
          value={viewerSettings.pageSpacing}
          onChange={(value) => handleViewerSettingChange('pageSpacing', value)}
          labelRenderer={(value) => `${value}px`}
        />
      </FormGroup>
      
      <Divider />
      
      <Switch
        checked={viewerSettings.fitToWidth}
        onChange={(e) => handleViewerSettingChange('fitToWidth', e.target.checked)}
        label="Fit to Width"
      />
      
      <Switch
        checked={viewerSettings.showThumbnails}
        onChange={(e) => handleViewerSettingChange('showThumbnails', e.target.checked)}
        label="Show Thumbnails"
      />
      
      <Switch
        checked={viewerSettings.showAnnotations}
        onChange={(e) => handleViewerSettingChange('showAnnotations', e.target.checked)}
        label="Show Annotations"
      />
      
      <Switch
        checked={viewerSettings.enableVirtualization}
        onChange={(e) => handleViewerSettingChange('enableVirtualization', e.target.checked)}
        label="Enable Virtualization"
      />
      
      <Switch
        checked={viewerSettings.enableSearch}
        onChange={(e) => handleViewerSettingChange('enableSearch', e.target.checked)}
        label="Enable Search"
      />
      
      <Switch
        checked={viewerSettings.enableTextSelection}
        onChange={(e) => handleViewerSettingChange('enableTextSelection', e.target.checked)}
        label="Enable Text Selection"
      />
    </div>
  );

  // Render UI settings
  const renderUISettings = () => (
    <div className="properties-section">
      <h4>Interface Settings</h4>
      
      <Switch
        checked={ui.showGrid}
        onChange={(e) => handleUISettingChange('showGrid', e.target.checked)}
        label="Show Grid"
      />
      
      <Switch
        checked={ui.showRulers}
        onChange={(e) => handleUISettingChange('showRulers', e.target.checked)}
        label="Show Rulers"
      />
      
      <Divider />
      
      <FormGroup label="Annotation Explorer Width">
        <Slider
          min={200}
          max={600}
          stepSize={25}
          value={ui.annotationExplorerWidth}
          onChange={(value) => handleUISettingChange('annotationExplorerWidth', value)}
          labelRenderer={(value) => `${value}px`}
        />
      </FormGroup>
      
      <FormGroup label="Thumbnail Panel Width">
        <Slider
          min={150}
          max={400}
          stepSize={25}
          value={ui.thumbnailPanelWidth}
          onChange={(value) => handleUISettingChange('thumbnailPanelWidth', value)}
          labelRenderer={(value) => `${value}px`}
        />
      </FormGroup>
    </div>
  );

  // Section navigation
  const sections = [
    { id: 'document', label: 'Document', icon: <FileText size={16} /> },
    { id: 'annotations', label: 'Annotations', icon: <Palette size={16} /> },
    { id: 'viewer', label: 'Viewer', icon: <Eye size={16} /> },
    { id: 'interface', label: 'Interface', icon: <Grid size={16} /> }
  ];

  return (
    <div className="properties-panel-overlay">
      <Card className={`properties-panel ${isDarkTheme ? 'dark' : 'light'}`}>
        {/* Header */}
        <div className="panel-header">
          <div className="header-title">
            <Settings size={16} />
            <span>Properties</span>
          </div>
          <Button
            minimal
            small
            icon={<X size={16} />}
            onClick={handleClose}
          />
        </div>

        {/* Section Navigation */}
        <div className="section-nav">
          {sections.map(section => (
            <Button
              key={section.id}
              minimal
              small
              active={activeSection === section.id}
              onClick={() => setActiveSection(section.id)}
              icon={section.icon}
              text={section.label}
            />
          ))}
        </div>

        <Divider />

        {/* Content */}
        <div className="panel-content">
          {activeSection === 'document' && renderDocumentProperties()}
          {activeSection === 'annotations' && renderAnnotationStats()}
          {activeSection === 'viewer' && renderViewerSettings()}
          {activeSection === 'interface' && renderUISettings()}
        </div>
      </Card>
    </div>
  );
};

export default PropertiesPanel;
