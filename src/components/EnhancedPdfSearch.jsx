import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Input, Radio, Switch, Tooltip, Button, Badge } from 'antd';
import { SearchOutlined, SettingOutlined, CloseOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Pop<PERSON>, Spinner, Card } from '@blueprintjs/core';
import Draggable from 'react-draggable';
import { searchModes } from '../utils/pdfSearchUtils';

const { Search } = Input;
const { Group: RadioGroup } = Radio;

/**
 * Enhanced PDF Search component with advanced search capabilities
 */
const EnhancedPdfSearch = ({
  isOpen,
  onClose,
  searchQuery,
  onSearchChange,
  onSearch,
  searchResults,
  currentSearchIndex,
  onNavigateSearch,
  isDarkTheme,
  totalOccurrences,
  isBusy = false,
  searchMode = 'STANDARD',
  onSearchModeChange,
  caseSensitive = false,
  onCaseSensitiveChange,
  wholeWord = false,
  onWholeWordChange
}) => {
  const [showSettings, setShowSettings] = useState(false);
  const searchInputRef = useRef(null);
  
  // Focus the search input when opened
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    }
  }, [isOpen]);
  
  // Settings panel content
  const renderSettingsContent = () => (
    <div style={{ 
      padding: '10px', 
      width: '280px',
      color: isDarkTheme ? '#F5F8FA' : '#182026'
    }}>
      <h4 style={{ marginTop: 0, marginBottom: '10px' }}>Search Options</h4>
      
      <div style={{ marginBottom: '15px' }}>
        <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>Search Mode:</div>
        <RadioGroup 
          onChange={(e) => onSearchModeChange(e.target.value)} 
          value={searchMode}
          style={{ width: '100%' }}
        >
          {Object.keys(searchModes).map(mode => (
            <Radio key={mode} value={mode}>
              <Tooltip title={searchModes[mode].description}>
                <span>{searchModes[mode].name}</span>
              </Tooltip>
            </Radio>
          ))}
        </RadioGroup>
      </div>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>Case Sensitive</span>
          <Switch 
            size="small" 
            checked={caseSensitive} 
            onChange={onCaseSensitiveChange} 
          />
        </div>
        
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <span>Whole Word Only</span>
          <Switch 
            size="small" 
            checked={wholeWord} 
            onChange={onWholeWordChange} 
          />
        </div>
      </div>
    </div>
  );
  
  if (!isOpen) return null;

  return (
    <Draggable handle=".search-window-header">
      <div className="search-window" style={{
        position: 'absolute',
        top: '50px',
        right: '20px',
        width: '320px',
        backgroundColor: isDarkTheme ? '#2F343C' : '#FFFFFF',
        border: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
        borderRadius: '4px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        zIndex: 1000,
        color: isDarkTheme ? '#F5F8FA' : '#182026',
      }}>
        <div className="search-window-header" style={{
          padding: '5px 10px',
          backgroundColor: isDarkTheme ? '#2F343C' : '#F5F8FA',
          borderBottom: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
          cursor: 'move',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '32px',
        }}>
          <span style={{ fontSize: '14px', fontWeight: 'bold' }}>Search PDF</span>
          <div style={{ display: 'flex', gap: '8px' }}>
            <Popover
              isOpen={showSettings}
              onInteraction={(state) => setShowSettings(state)}
              content={renderSettingsContent()}
              placement="bottom"
            >
              <Button 
                type="text" 
                icon={<SettingOutlined />} 
                size="small" 
                onClick={() => setShowSettings(!showSettings)}
                style={{ color: isDarkTheme ? '#F5F8FA' : '#182026' }}
              />
            </Popover>
            <Button 
              type="text" 
              icon={<CloseOutlined />} 
              size="small" 
              onClick={onClose}
              style={{ color: isDarkTheme ? '#F5F8FA' : '#182026' }}
            />
          </div>
        </div>
        
        <div style={{ padding: '12px' }}>
          <Search
            ref={searchInputRef}
            placeholder="Enter search term"
            value={searchQuery}
            onChange={onSearchChange}
            onSearch={onSearch}
            style={{ marginBottom: "12px" }}
            loading={isBusy}
            enterButton={<Button icon={<SearchOutlined />}>Search</Button>}
          />
          
          {isBusy && (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Spinner size={20} />
              <div style={{ marginTop: '8px', fontSize: '12px' }}>
                Searching document...
              </div>
            </div>
          )}
          
          {!isBusy && searchResults.length > 0 && (
            <Card style={{ 
              backgroundColor: isDarkTheme ? '#394B59' : '#F5F8FA',
              padding: '12px',
              boxShadow: 'none',
              border: `1px solid ${isDarkTheme ? '#5C7080' : '#ddd'}`
            }}>
              <div style={{ marginBottom: '14px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <Badge 
                    count={currentSearchIndex + 1} 
                    style={{ backgroundColor: isDarkTheme ? '#2D72D2' : '#106BA3' }} 
                    showZero={false} 
                    overflowCount={9999}
                  >
                    <span style={{ fontSize: '13px', marginRight: '10px' }}>Current result</span>
                  </Badge>
                  
                  <Badge 
                    count={searchResults.length} 
                    style={{ backgroundColor: isDarkTheme ? '#2D72D2' : '#106BA3' }} 
                    showZero={false} 
                    overflowCount={9999}
                  >
                    <span style={{ fontSize: '13px', marginRight: '10px' }}>Pages</span>
                  </Badge>
                </div>
                
                <div style={{ 
                  fontSize: '13px', 
                  marginBottom: '5px', 
                  display: 'flex', 
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <span>Total occurrences:</span> 
                  <Badge 
                    count={totalOccurrences} 
                    style={{ backgroundColor: isDarkTheme ? '#0E5A8A' : '#2D72D2' }} 
                    showZero={false}
                    overflowCount={9999}
                  />
                </div>
                
                <div style={{ fontSize: '12px', color: isDarkTheme ? '#E1E8ED' : '#5C7080', marginTop: '5px' }}>
                  {searchMode === 'FUZZY' && 'Using fuzzy search to find similar patterns'}
                  {searchMode === 'CROSS_LINE' && 'Searching across line breaks'}
                  {searchMode === 'EXACT' && 'Using exact text matching'}
                </div>
              </div>
              
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button 
                  onClick={() => onNavigateSearch('prev')} 
                  icon={<LeftOutlined />}
                  style={{ flex: 1, marginRight: '4px' }}
                >
                  Previous
                </Button>
                <Button 
                  onClick={() => onNavigateSearch('next')} 
                  icon={<RightOutlined />}
                  style={{ flex: 1, marginLeft: '4px' }}
                >
                  Next
                </Button>
              </div>
            </Card>
          )}
          
          {!isBusy && searchQuery && searchResults.length === 0 && (
            <div style={{ 
              padding: '15px', 
              textAlign: 'center',
              fontSize: '13px',
              color: isDarkTheme ? '#E1E8ED' : '#5C7080',
              backgroundColor: isDarkTheme ? '#394B59' : '#F5F8FA',
              borderRadius: '3px'
            }}>
              No results found for "{searchQuery}"
              
              {searchMode === 'EXACT' && (
                <div style={{ marginTop: '8px', fontSize: '12px' }}>
                  Try using fuzzy search or cross-line search in settings
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Draggable>
  );
};

export default EnhancedPdfSearch;
