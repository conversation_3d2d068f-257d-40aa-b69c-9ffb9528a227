import React, { useCallback, useState, useEffect, useRef } from 'react';
import { Page } from 'react-pdf';
import { createAdvancedTextRenderer, processTextContent } from '../utils/pdfSearchUtils';

/**
 * Enhanced PDF Page renderer with improved text highlighting and search features
 */
const EnhancedPDFPageRenderer = ({
  pageNumber,
  scale,
  rotation,
  width,
  onRenderSuccess,
  searchQuery,
  searchResults,
  searchHighlights,
  highlightColor = 'rgba(255, 255, 0, 0.4)',
  onTextContentExtracted = () => {},
}) => {
  const [textContent, setTextContent] = useState(null);
  const [renderedScale, setRenderedScale] = useState(scale);
  const [isScaleChanging, setIsScaleChanging] = useState(false);
  const [pageSize, setPageSize] = useState({ width: 0, height: 0 });
  const pageRef = useRef(null);
  
  // Handle text extraction on page load
  const handleTextExtract = useCallback(async (page) => {
    try {
      const content = await page.getTextContent();
      const processed = processTextContent(content);
      setTextContent(processed);
      onTextContentExtracted(processed, pageNumber);
      return processed;
    } catch (error) {
      console.error(`Error extracting text from page ${pageNumber}:`, error);
      return null;
    }
  }, [pageNumber, onTextContentExtracted]);

  // Custom text renderer with advanced highlighting
  const textRenderer = useCallback((textItem) => {
    // If we have specific search highlights for this page
    if (searchHighlights && searchHighlights.length > 0) {
      const advancedRenderer = createAdvancedTextRenderer(searchHighlights, highlightColor);
      return advancedRenderer(textItem);
    }
    
    // Fallback to basic highlighting if we only have a search query
    if (searchQuery && searchQuery.trim()) {
      try {
        const regex = new RegExp(`(${searchQuery.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')})`, 'gi');
        return textItem.str.replace(regex, `<mark style="background-color: ${highlightColor}; padding: 0; margin: 0; border-radius: 0 !important; box-shadow: 0 0 0 0 !important; color: inherit;">$1</mark>`);
      } catch (e) {
        // If regex fails (possibly due to invalid user input), return unchanged
        return textItem.str;
      }
    }
    
    return textItem.str;
  }, [searchQuery, searchHighlights, highlightColor]);

  // Handle successful render by measuring dimensions and extracting content
  const handleRenderSuccess = useCallback((page) => {
    const { width, height } = page;
    setPageSize({ width, height });
    
    if (!textContent) {
      handleTextExtract(page._page);
    }
    
    if (onRenderSuccess) {
      onRenderSuccess(page);
    }
  }, [handleTextExtract, onRenderSuccess, textContent]);

  // Detect scale changes for flash scale transitions
  useEffect(() => {
    if (scale !== renderedScale) {
      setIsScaleChanging(true);
    }
  }, [scale, renderedScale]);
  
  // Reset text content only when page number or rotation changes
  // We deliberately don't reset on scale changes to prevent re-rendering during zoom
  useEffect(() => {
    setTextContent(null);
    setRenderedScale(scale); // Reset rendered scale when page or rotation changes
    setIsScaleChanging(false);
  }, [pageNumber, rotation, scale]);

  // Add custom styling for the PDF page container
  const pageStyle = {
    position: 'relative',
    transformOrigin: 'center center',
    willChange: isScaleChanging ? 'transform' : 'auto', // Optimize for transforms only during scale changes
  };

  return (
    <div 
      ref={pageRef}
      data-index={pageNumber - 1}
      data-page-number={pageNumber}
      className="enhanced-pdf-page"
      style={pageStyle}
    >
      {/* Show the previous scale version while new one loads for smooth transitions */}
      {isScaleChanging && (
        <Page
          key={`${pageNumber}@${rotation}@${renderedScale}`}
          className="previous-scale-page"
          pageNumber={pageNumber}
          scale={renderedScale}
          rotate={rotation}
          width={width}
          renderAnnotationLayer={false} // Skip annotation layer on transitional render
          renderTextLayer={false} // Skip text layer on transitional render
          customTextRenderer={textRenderer}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 1,
            opacity: 0.7, // Show previous page with slight transparency
          }}
        />
      )}
      <Page
        key={`${pageNumber}@${rotation}@${scale}`}
        pageNumber={pageNumber}
        scale={scale}
        rotate={rotation}
        width={width}
        renderAnnotationLayer={true}
        renderTextLayer={true}
        customTextRenderer={textRenderer}
        onRenderSuccess={(page) => {
          handleRenderSuccess(page);
          // Update rendered scale and hide previous version
          setRenderedScale(scale);
          setIsScaleChanging(false);
        }}
        style={{ position: 'relative', zIndex: 2 }}
      />
      
      {/* Optional overlay for advanced interaction with search results */}
      {searchHighlights && searchHighlights.length > 0 && textContent && (
        <div 
          className="search-highlight-overlay"
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none',
            zIndex: 2
          }}
        >
          {/* This could be extended to add custom highlight effects or animations */}
        </div>
      )}
    </div>
  );
};

export default React.memo(EnhancedPDFPageRenderer, (prevProps, nextProps) => {
  // Only re-render if these props change
  return (
    prevProps.pageNumber === nextProps.pageNumber &&
    prevProps.rotation === nextProps.rotation &&
    prevProps.width === nextProps.width &&
    prevProps.searchQuery === nextProps.searchQuery &&
    JSON.stringify(prevProps.searchHighlights) === JSON.stringify(nextProps.searchHighlights) &&
    // Allow scale changes without triggering re-render if other props are the same
    (prevProps.scale === nextProps.scale || Math.abs(prevProps.scale - nextProps.scale) < 0.001)
  );
});
