import React, { useEffect, useState, useRef } from 'react';
import { Worker, Viewer, SpecialZoomLevel } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { selectionModePlugin, SelectionMode } from '@react-pdf-viewer/selection-mode';
import { zoomPlugin } from '@react-pdf-viewer/zoom';
import "@blueprintjs/core/lib/css/blueprint.css";
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import '@react-pdf-viewer/selection-mode/lib/styles/index.css';
import '@react-pdf-viewer/zoom/lib/styles/index.css';
import './Styles/pdf-viewer.css';
import './Styles/App.css'

function PDFJsViewer({ fileId, ownerDocument = document }) {
  const viewerRef = useRef(null);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [pdfFile, setPdfFile] = useState(null);
  const [pdfError, setPdfError] = useState('');
  const [selectionMode, setSelectionMode] = useState(SelectionMode.Text);

  const selectionModePluginInstance = selectionModePlugin();
  const { SwitchSelectionMode } = selectionModePluginInstance;

  const zoomPluginInstance = zoomPlugin();
  const { Zoom } = zoomPluginInstance;

  const defaultLayoutPluginInstance = defaultLayoutPlugin();

  useEffect(() => {
    async function fetchFile() {
      setDownloadProgress(0);

      try {
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }

        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          if (contentLength) {
            setDownloadProgress(receivedLength / contentLength);
          }
        }

        const blob = new Blob(chunks);
        const pdfUrl = URL.createObjectURL(blob);
        setPdfError('');
        setPdfFile(pdfUrl);
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
        setPdfError('Failed to fetch the PDF');
        setPdfFile(null);
      }
    }

    fetchFile();

    return () => {
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId]);

  return (
    <div className="container">
      <div className="viewer" ref={viewerRef}>
        {pdfFile ? (
          <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js" ownerDocument={ownerDocument}>
            <div style={{ height: "calc(100vh - 160px)", position: 'relative' }}>
              <Viewer
                theme={'dark'}
                fileUrl={pdfFile}
                defaultScale={SpecialZoomLevel.PageWidth}
                plugins={[defaultLayoutPluginInstance, selectionModePluginInstance, zoomPluginInstance]}
                ownerDocument={ownerDocument}
              />
            </div>
          </Worker>
        ) : (
          <div>{pdfError ? pdfError : 'No file selected'}</div>
        )}
      </div>
    </div>
  );
}

export default PDFJsViewer;
