import React, { useState, useEffect } from 'react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { Button, ButtonGroup, Spinner, SpinnerSize } from '@blueprintjs/core';
import { RotateCcw, RotateCw, ZoomIn, ZoomOut, RefreshCw } from 'lucide-react';

const ImageViewer = ({ fileId, filePath, isDarkTheme }) => {
  const [imageUrl, setImageUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [rotation, setRotation] = useState(0);
  const [hasLoadedBefore, setHasLoadedBefore] = useState(false);

  useEffect(() => {
    const fetchImage = async () => {
      // Skip loading again if we've already loaded this image
      if (hasLoadedBefore && imageUrl) {
        console.log(`Image ${fileId} already loaded, skipping fetch`);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log(`Fetching image file: ${fileId}`);
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'Accept-Encoding': 'gzip, deflate, br',
            'session-id': localStorage.getItem('session_id'),
          },
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
        }
        
        const contentType = response.headers.get('Content-Type');
        if (contentType && !contentType.startsWith('image/')) {
          console.warn(`Received non-image content type: ${contentType}`);
        }
        
        const blob = await response.blob();
        const objectUrl = URL.createObjectURL(blob);
        setImageUrl(objectUrl);
        setHasLoadedBefore(true);
      } catch (err) {
        console.error('Error fetching image:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };
    
    // Only fetch if we don't have an image URL yet or if the fileId changed
    if (!imageUrl || !hasLoadedBefore) {
      fetchImage();
    }
    
    return () => {
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [fileId, hasLoadedBefore, imageUrl]);

  const rotateLeft = () => {
    setRotation(prev => {
      const newRotation = (prev - 90) % 360;
      // Handle negative rotations
      return newRotation < 0 ? newRotation + 360 : newRotation;
    });
  };
  
  const rotateRight = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  if (isLoading) {
    return (
      <div className="loading-container" style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        backgroundColor: isDarkTheme ? '#1C2127' : '#F5F8FA',
        width: '100%'
      }}>
        <Spinner 
          size={SpinnerSize.SMALL}
          intent="primary"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container" style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100%',
        color: isDarkTheme ? '#F5F8FA' : '#1C2127',
        backgroundColor: isDarkTheme ? '#1C2127' : '#F5F8FA'
      }}>
        <p>Error loading image: {error}</p>
      </div>
    );
  }

  return (
    <div style={{ 
      height: '100%', 
      position: 'relative',
      backgroundColor: isDarkTheme ? '#1C2127' : '#F5F8FA'
    }}>
      <div className="tools" style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        zIndex: 999,
        backgroundColor: isDarkTheme ? 'rgba(28, 33, 39, 0.8)' : 'rgba(245, 248, 250, 0.8)',
        borderRadius: '4px',
        padding: '5px',
        boxShadow: isDarkTheme ? '0 0 10px rgba(0, 0, 0, 0.3)' : '0 0 10px rgba(0, 0, 0, 0.1)'
      }}>
        <ButtonGroup>
          <Button 
            icon={<ZoomIn size={16} />} 
            onClick={() => document.querySelector('.transform-component-zoom-in')?.click()} 
            minimal 
            title="Zoom In"
            style={{
              color: isDarkTheme ? '#F5F8FA' : '#1C2127',
            }}
          />
          <Button 
            icon={<ZoomOut size={16} />} 
            onClick={() => document.querySelector('.transform-component-zoom-out')?.click()} 
            minimal 
            title="Zoom Out"
            style={{
              color: isDarkTheme ? '#F5F8FA' : '#1C2127',
            }}
          />
          <Button 
            icon={<RotateCcw size={16} />} 
            onClick={rotateLeft} 
            minimal 
            title="Rotate Left"
            style={{
              color: isDarkTheme ? '#F5F8FA' : '#1C2127',
            }}
          />
          <Button 
            icon={<RotateCw size={16} />} 
            onClick={rotateRight} 
            minimal 
            title="Rotate Right"
            style={{
              color: isDarkTheme ? '#F5F8FA' : '#1C2127',
            }}
          />
          <Button 
            icon={<RefreshCw size={16} />} 
            onClick={() => {
              setRotation(0);
              document.querySelector('.transform-component-reset')?.click();
            }} 
            minimal 
            title="Reset"
            style={{
              color: isDarkTheme ? '#F5F8FA' : '#1C2127',
            }}
          />
        </ButtonGroup>
      </div>
      
      <TransformWrapper
        initialScale={1}
        initialPositionX={0}
        initialPositionY={0}
        minScale={0.1}
        maxScale={8}
        centerOnInit={true}
        doubleClick={{ mode: 'reset' }}
        wheel={{ step: 0.1 }}
        panning={{ disabled: false }}
      >
        <TransformComponent
          wrapperStyle={{
            width: '100%',
            height: '100%',
            cursor: 'grab'
          }}
        >
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            height: '100%'
          }}>
            <div style={{
              transform: `rotate(${rotation}deg)`,
              width: '100%',
              height: '100%',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center'
            }}>
              <img 
                src={imageUrl} 
                alt="Document" 
                style={{ 
                  maxWidth: rotation % 180 !== 0 ? '60vh' : '80%',
                  maxHeight: rotation % 180 !== 0 ? '80%' : '60vh',
                  objectFit: 'contain'
                }} 
              />
            </div>
          </div>
        </TransformComponent>
      </TransformWrapper>
    </div>
  );
};

export default ImageViewer;
