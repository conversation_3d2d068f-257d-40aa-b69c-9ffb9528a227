import React, { useState } from 'react';
import PDFViewer from './PDFViewer';

const App = () => {
  const [pdfUrl, setPdfUrl] = useState(null);
  const [selectedWord, setSelectedWord] = useState('');

  const handleWordClick = (word) => {
    setSelectedWord(word);
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const objectURL = URL.createObjectURL(file);
      setPdfUrl(objectURL);
    }
  };

  return (
    <div>
      <input type="file" onChange={handleFileChange} accept=".pdf" />
      <PDFViewer 
        pdfUrl={pdfUrl} 
        onWordClick={handleWordClick}
      />
      <input value={selectedWord} readOnly />
    </div>
  );
}

export default App;
