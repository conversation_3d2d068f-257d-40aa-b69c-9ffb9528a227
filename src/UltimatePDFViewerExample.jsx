import React, { useState, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { <PERSON><PERSON>, Card, FileInput, NonIdealState } from '@blueprintjs/core';
import { FileText, Upload, Eye } from 'lucide-react';

// Import our Ultimate PDF Viewer
import UltimatePDF<PERSON>iewer from './UltimatePDFViewer';

// Redux actions
import { addDocument, setActiveDocument } from './redux/ultimatePdfViewerSlice';

/**
 * Example component showing how to integrate the Ultimate PDF Viewer
 * This can be used as a FlexLayout tab component
 */
const UltimatePDFViewerExample = ({ isDarkTheme = true }) => {
  const dispatch = useDispatch();
  const [selectedFile, setSelectedFile] = useState(null);
  const [fileUrl, setFileUrl] = useState(null);

  // Redux selectors
  const documents = useSelector(state => state.ultimatePdfViewer.documents);
  const activeDocumentId = useSelector(state => state.ultimatePdfViewer.activeDocumentId);

  // Handle file selection
  const handleFileSelect = useCallback((event) => {
    const file = event.target.files[0];
    if (file && file.type === 'application/pdf') {
      setSelectedFile(file);
      
      // Create blob URL for the file
      const url = URL.createObjectURL(file);
      setFileUrl(url);
      
      // Generate unique file ID
      const fileId = `pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Add document to Redux store
      dispatch(addDocument({
        fileId,
        filePath: url, // Using blob URL as file path
        fileName: file.name,
        numPages: 0 // Will be updated when PDF loads
      }));
      
      // Set as active document
      dispatch(setActiveDocument(fileId));
    }
  }, [dispatch]);

  // Handle opening existing document
  const handleOpenDocument = useCallback((fileId) => {
    dispatch(setActiveDocument(fileId));
  }, [dispatch]);

  // Clean up blob URL when component unmounts
  React.useEffect(() => {
    return () => {
      if (fileUrl) {
        URL.revokeObjectURL(fileUrl);
      }
    };
  }, [fileUrl]);

  // Get active document
  const activeDocument = activeDocumentId ? documents[activeDocumentId] : null;

  return (
    <div className="ultimate-pdf-viewer-example" style={{ height: '100%', width: '100%' }}>
      {activeDocument ? (
        <UltimatePDFViewer
          fileId={activeDocumentId}
          filePath={activeDocument.filePath}
          fileName={activeDocument.fileName}
          isDarkTheme={isDarkTheme}
          onClose={() => dispatch(setActiveDocument(null))}
        />
      ) : (
        <Card className="pdf-viewer-welcome" style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Header */}
          <div style={{ padding: '20px', borderBottom: '1px solid #383E47' }}>
            <h2 style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '8px' }}>
              <FileText size={24} />
              Ultimate PDF Viewer
            </h2>
            <p style={{ margin: '8px 0 0 0', color: '#A7B6C2' }}>
              State-of-the-art PDF viewing with annotations, search, and more
            </p>
          </div>

          {/* Content */}
          <div style={{ flex: 1, padding: '20px', display: 'flex', flexDirection: 'column' }}>
            
            {/* File Upload Section */}
            <div style={{ marginBottom: '32px' }}>
              <h3 style={{ marginBottom: '16px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Upload size={20} />
                Open PDF File
              </h3>
              
              <FileInput
                text={selectedFile ? selectedFile.name : "Choose PDF file..."}
                onInputChange={handleFileSelect}
                inputProps={{
                  accept: '.pdf',
                  style: { width: '100%' }
                }}
                fill
              />
              
              <p style={{ marginTop: '8px', fontSize: '13px', color: '#A7B6C2' }}>
                Select a PDF file from your computer to start viewing and annotating
              </p>
            </div>

            {/* Recent Documents */}
            {Object.keys(documents).length > 0 && (
              <div style={{ marginBottom: '32px' }}>
                <h3 style={{ marginBottom: '16px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <Eye size={20} />
                  Recent Documents
                </h3>
                
                <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  {Object.entries(documents).map(([fileId, doc]) => (
                    <Card
                      key={fileId}
                      interactive
                      onClick={() => handleOpenDocument(fileId)}
                      style={{
                        padding: '12px',
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        border: '1px solid #383E47'
                      }}
                    >
                      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                        <FileText size={16} />
                        <div style={{ flex: 1 }}>
                          <div style={{ fontWeight: 500 }}>{doc.fileName}</div>
                          <div style={{ fontSize: '12px', color: '#A7B6C2' }}>
                            {doc.numPages} pages • Last viewed {new Date(doc.lastViewed).toLocaleDateString()}
                          </div>
                        </div>
                        <Button
                          minimal
                          small
                          text="Open"
                          intent="primary"
                        />
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Features List */}
            <div style={{ flex: 1 }}>
              <h3 style={{ marginBottom: '16px' }}>Features</h3>
              
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '16px' }}>
                <div className="feature-card">
                  <h4>📝 Advanced Annotations</h4>
                  <p>Text highlights, rectangle annotations, comments with full management</p>
                </div>
                
                <div className="feature-card">
                  <h4>🔍 Powerful Search</h4>
                  <p>Full-text search with case sensitivity and whole word options</p>
                </div>
                
                <div className="feature-card">
                  <h4>🖼️ Thumbnail Navigation</h4>
                  <p>Visual page thumbnails with annotation indicators</p>
                </div>
                
                <div className="feature-card">
                  <h4>⚡ Virtualized Performance</h4>
                  <p>Smooth scrolling and rendering for large PDF documents</p>
                </div>
                
                <div className="feature-card">
                  <h4>🎨 Beautiful UI</h4>
                  <p>Dark/light themes with Blueprint.js components</p>
                </div>
                
                <div className="feature-card">
                  <h4>⌨️ Keyboard Shortcuts</h4>
                  <p>Full keyboard navigation and accessibility support</p>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}
      
      <style jsx>{`
        .feature-card {
          padding: 16px;
          background: #252A31;
          border: 1px solid #383E47;
          border-radius: 6px;
          transition: all 0.2s;
        }
        
        .feature-card:hover {
          border-color: #2D72D2;
          transform: translateY(-2px);
        }
        
        .feature-card h4 {
          margin: 0 0 8px 0;
          font-size: 14px;
          font-weight: 600;
        }
        
        .feature-card p {
          margin: 0;
          font-size: 13px;
          color: #A7B6C2;
          line-height: 1.4;
        }
      `}</style>
    </div>
  );
};

export default UltimatePDFViewerExample;
