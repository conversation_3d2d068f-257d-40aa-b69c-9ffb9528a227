import React, { useState, useRef, useCallback } from "react";
import { <PERSON>d<PERSON><PERSON><PERSON><PERSON>, PdfHigh<PERSON><PERSON>, Tip, Highlight, Popup, AreaHighlight } from "react-pdf-highlighter";
import { <PERSON><PERSON>, <PERSON><PERSON>, ButtonGroup, Card } from "@blueprintjs/core";
import { RotateCcw, RotateCw, ZoomIn, ZoomOut, Hand, MousePointer } from "lucide-react";
import "./pdf-viewer.css";

// Unique ID generator for highlights
const getNextId = () => String(Math.random()).slice(2);

// Primary colors for highlights
const PRIMARY_PDF_HIGHLIGHT_COLOR = "rgba(255, 226, 143, 1)";
const PRIMARY_PDF_HIGHLIGHT_COLOR_TRANSPARENT = "rgba(255, 226, 143, 0.4)";

const parseIdFromHash = () => document.location.hash.slice("#highlight-".length);

const resetHash = () => {
  document.location.hash = "";
};

const HighlightPopup = ({ comment }) => {
  return (
    <div className="Highlight__popup">
      {comment ? (
        <div className="Highlight__comment">{comment}</div>
      ) : null}
    </div>
  );
};

const CustomPDFHighlighter = ({ fileId, filePath, isDarkTheme }) => {
  const [url, setUrl] = useState(null);
  const [highlights, setHighlights] = useState([]);
  const [rotation, setRotation] = useState(0);
  const [scale, setScale] = useState(1.0);
  const [isPanMode, setIsPanMode] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const scrollViewerTo = useRef(null);
  const highlighterRef = useRef(null);

  // Fetch PDF file
  React.useEffect(() => {
    const fetchPdf = async () => {
      setLoading(true);
      try {
        let response;
        if (fileId) {
          response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
            headers: {
              'Accept-Encoding': 'gzip, deflate, br',
              'session-id': localStorage.getItem('session_id'),
            },
          });
        } else if (filePath) {
          response = await fetch(filePath);
        } else {
          throw new Error("No file source provided");
        }
        
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        
        const blob = await response.blob();
        const pdfUrl = URL.createObjectURL(blob);
        setUrl(pdfUrl);
        setLoading(false);
      } catch (err) {
        console.error("Failed to fetch PDF:", err);
        setError(err.message);
        setLoading(false);
      }
    };
    
    fetchPdf();
    
    return () => {
      if (url) {
        URL.revokeObjectURL(url);
      }
    };
  }, [fileId, filePath]);

  // Handle highlight adding
  const addHighlight = useCallback((highlight) => {
    console.log("Adding highlight", highlight);
    setHighlights((prev) => [...prev, { ...highlight, id: getNextId() }]);
  }, []);

  // Handle highlight updating
  const updateHighlight = useCallback((highlightId, position, content) => {
    console.log("Updating highlight", highlightId, position, content);
    setHighlights((prev) =>
      prev.map((h) => {
        const {
          id,
          position: originalPosition,
          content: originalContent,
          ...rest
        } = h;
        return id === highlightId
          ? {
              id,
              position: { ...originalPosition, ...position },
              content: { ...originalContent, ...content },
              ...rest,
            }
          : h;
      })
    );
  }, []);

  // Handle text selection
  const getHighlightAt = useCallback((x, y) => {
    if (highlighterRef.current) {
      return highlighterRef.current.getHighlightAt(x, y);
    }
    return null;
  }, []);

  // Handle zoom in/out
  const zoomIn = useCallback(() => {
    setScale((prev) => Math.min(prev + 0.1, 3));
  }, []);

  const zoomOut = useCallback(() => {
    setScale((prev) => Math.max(prev - 0.1, 0.5));
  }, []);

  // Handle rotation
  const rotateLeft = useCallback(() => {
    setRotation((prev) => (prev - 90) % 360);
  }, []);

  const rotateRight = useCallback(() => {
    setRotation((prev) => (prev + 90) % 360);
  }, []);

  // Toggle pan mode
  const togglePanMode = useCallback(() => {
    setIsPanMode((prev) => !prev);
  }, []);

  // Loading state
  if (loading) {
    return (
      <div className="pdf-viewer-container" style={{ 
        height: '100%', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        backgroundColor: isDarkTheme ? '#293742' : '#f5f8fa'
      }}>
        <Spinner size={30} intent="primary" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="pdf-viewer-container" style={{ 
        height: '100%', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        backgroundColor: isDarkTheme ? '#293742' : '#f5f8fa'
      }}>
        <Card elevation={2} style={{ maxWidth: 400, textAlign: 'center' }}>
          <h3>Error Loading PDF</h3>
          <p>{error}</p>
        </Card>
      </div>
    );
  }

  return (
    <div className={`pdf-viewer-container ${isDarkTheme ? 'bp5-dark' : ''}`} style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Toolbar */}
      <div className="pdf-toolbar" style={{ 
        padding: '8px', 
        borderBottom: '1px solid #ddd',
        display: 'flex',
        justifyContent: 'space-between',
        backgroundColor: isDarkTheme ? '#30404D' : '#f5f8fa'
      }}>
        <div className="toolbar-left">
          <ButtonGroup>
            <Button icon={<ZoomOut size={16} />} onClick={zoomOut} />
            <Button minimal>{Math.round(scale * 100)}%</Button>
            <Button icon={<ZoomIn size={16} />} onClick={zoomIn} />
          </ButtonGroup>
          
          <ButtonGroup style={{ marginLeft: 10 }}>
            <Button icon={<RotateCcw size={16} />} onClick={rotateLeft} />
            <Button icon={<RotateCw size={16} />} onClick={rotateRight} />
          </ButtonGroup>
          
          <ButtonGroup style={{ marginLeft: 10 }}>
            <Button 
              icon={isPanMode ? <Hand size={16} /> : <MousePointer size={16} />} 
              active={isPanMode}
              onClick={togglePanMode} 
            />
          </ButtonGroup>
        </div>
      </div>
      
      {/* PDF Viewer */}
      <div 
        className="pdf-content"
        style={{ 
          flex: 1,
          overflow: 'auto',
          backgroundColor: isDarkTheme ? '#293742' : '#fff',
          cursor: isPanMode ? 'grab' : 'default'
        }}
      >
        {url && (
          <PdfLoader url={url} beforeLoad={<Spinner />}>
            {(pdfDocument) => (
              <PdfHighlighter
                ref={highlighterRef}
                pdfDocument={pdfDocument}
                enableAreaSelection={(event) => event.altKey}
                onScrollChange={resetHash}
                scrollRef={(scrollTo) => {
                  scrollViewerTo.current = scrollTo;
                }}
                onSelectionFinished={(
                  position,
                  content,
                  hideTipAndSelection,
                  transformSelection
                ) => (
                  <Tip
                    onOpen={transformSelection}
                    onConfirm={(comment) => {
                      addHighlight({ content, position, comment });
                      hideTipAndSelection();
                    }}
                  />
                )}
                highlightTransform={(
                  highlight,
                  index,
                  setTip,
                  hideTip,
                  viewportToScaled,
                  screenshot,
                  isScrolledTo
                ) => {
                  const isTextHighlight = !Boolean(
                    highlight.content && highlight.content.image
                  );

                  const component = isTextHighlight ? (
                    <Highlight
                      isScrolledTo={isScrolledTo}
                      position={highlight.position}
                      comment={highlight.comment}
                    />
                  ) : (
                    <AreaHighlight
                      isScrolledTo={isScrolledTo}
                      highlight={highlight}
                      onChange={(boundingRect) => {
                        updateHighlight(
                          highlight.id,
                          { boundingRect: viewportToScaled(boundingRect) },
                          { image: screenshot(boundingRect) }
                        );
                      }}
                    />
                  );

                  return (
                    <Popup
                      popupContent={<HighlightPopup {...highlight} />}
                      onMouseOver={(popupContent) =>
                        setTip(highlight, (highlight) => popupContent)
                      }
                      onMouseOut={hideTip}
                      key={index}
                    >
                      {component}
                    </Popup>
                  );
                }}
                highlights={highlights}
                pdfScaleValue={scale}
                rotation={rotation}
              />
            )}
          </PdfLoader>
        )}
      </div>
    </div>
  );
};

export default CustomPDFHighlighter;
