import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>s, Page, CanvasLayer, TextLayer } from "@anaralabs/lector";
import { Spinner } from "@blueprintjs/core";

// Import PDF.js directly
import * as pdfjsLib from 'pdfjs-dist/build/pdf';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';

// Set up the worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

const MinimalLectorViewer = ({ fileId, isDarkTheme }) => {
  const [pdfData, setPdfData] = useState(null);
  const [pdfUrl, setPdfUrl] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadProgress, setDownloadProgress] = useState(0);

  // Fetch PDF
  useEffect(() => {
    if (!fileId) {
      setError("No file ID provided");
      setLoading(false);
      return;
    }

    let isMounted = true;

    const fetchPdf = async () => {
      try {
        setLoading(true);
        setError(null);
        setPdfData(null);
        setDownloadProgress(0);

        console.log(`Fetching PDF for fileId: ${fileId}`);

        // Use the fetch API with a reader to track progress
        const response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
          headers: {
            'session-id': localStorage.getItem('session_id'),
          },
        });

        if (!response.ok) {
          throw new Error(`Network response was not ok: ${response.status} ${response.statusText}`);
        }

        // Get content length for progress tracking
        const contentLength = response.headers.get('Content-Length');
        const reader = response.body.getReader();
        let receivedLength = 0;
        const chunks = [];

        // Read the response in chunks to track progress
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          chunks.push(value);
          receivedLength += value.length;

          // Update progress if content length is available
          if (contentLength && isMounted) {
            setDownloadProgress(receivedLength / parseInt(contentLength, 10));
          }
        }

        if (!isMounted) return;

        // Create a blob from all chunks with the correct MIME type
        const blob = new Blob(chunks, { type: 'application/pdf' });
        console.log(`Created PDF blob: ${blob.size} bytes, type: ${blob.type}`);

        // Create an ArrayBuffer from the blob for PDF.js
        const arrayBuffer = await blob.arrayBuffer();

        // Load the PDF document using PDF.js directly to validate it
        try {
          // Create a temporary URL for validation
          const tempUrl = URL.createObjectURL(blob);
          console.log("Created temporary URL for validation:", tempUrl);

          // Use the URL for validation
          const loadingTask = pdfjsLib.getDocument(tempUrl);
          const pdfDocument = await loadingTask.promise;
          console.log(`PDF document loaded with ${pdfDocument.numPages} pages`);

          // Clean up the temporary URL
          URL.revokeObjectURL(tempUrl);

          // If we get here, the PDF is valid
          if (isMounted) {
            setPdfData(blob);
            setLoading(false);
          }
        } catch (pdfError) {
          console.error("Error loading PDF with PDF.js:", pdfError);
          throw new Error(`Invalid PDF: ${pdfError.message}`);
        }
      } catch (err) {
        console.error("Failed to load PDF:", err);
        if (isMounted) {
          setError(err.message || "Failed to load PDF");
          setLoading(false);
        }
      }
    };

    fetchPdf();

    return () => {
      isMounted = false;
    };
  }, [fileId]);

  // Loading state
  if (loading) {
    return (
      <div style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
        backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa',
        color: isDarkTheme ? '#f5f8fa' : '#182026'
      }}>
        <Spinner size={50} />
        <div style={{ marginTop: '20px' }}>Loading PDF...</div>
        {downloadProgress > 0 && downloadProgress < 1 && (
          <div style={{ width: '300px', marginTop: '20px' }}>
            <div style={{
              height: '6px',
              width: `${Math.round(downloadProgress * 100)}%`,
              backgroundColor: isDarkTheme ? '#48AFF0' : '#2B95D6',
              borderRadius: '3px',
              transition: 'width 0.3s ease'
            }} />
            <div style={{ textAlign: 'center', marginTop: '5px', fontSize: '12px' }}>
              {Math.round(downloadProgress * 100)}%
            </div>
          </div>
        )}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div style={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        height: "100%",
        backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa',
        color: isDarkTheme ? '#f5f8fa' : '#182026'
      }}>
        <div style={{ color: 'red', fontSize: '20px', marginBottom: '10px' }}>Error Loading PDF</div>
        <div>{error}</div>
      </div>
    );
  }

  // Create a blob URL when pdfData changes
  useEffect(() => {
    if (pdfData) {
      try {
        const url = URL.createObjectURL(pdfData);
        console.log("Created blob URL:", url);
        setPdfUrl(url);

        // Clean up the URL when component unmounts or pdfData changes
        return () => {
          console.log("Revoking blob URL:", url);
          URL.revokeObjectURL(url);
        };
      } catch (err) {
        console.error("Error creating blob URL:", err);
      }
    }
  }, [pdfData]);

  // Render PDF
  return (
    <div style={{
      height: "100%",
      width: "100%",
      backgroundColor: isDarkTheme ? '#30404d' : '#f5f8fa',
      color: isDarkTheme ? '#f5f8fa' : '#182026'
    }}>
      {pdfUrl && (
        <Root
          source={pdfUrl}
          pdfLib={pdfjsLib}
          style={{ height: '100%' }}
          onLoad={() => console.log("Lector successfully loaded PDF")}
          onError={(err) => {
            console.error("Lector error:", err);
            // Don't set error state here to avoid re-render loop
            // Just log the error for debugging
          }}
        >
          <Pages>
            <Page>
              <CanvasLayer />
              <TextLayer />
            </Page>
          </Pages>
        </Root>
      )}
    </div>
  );
};

export default MinimalLectorViewer;
