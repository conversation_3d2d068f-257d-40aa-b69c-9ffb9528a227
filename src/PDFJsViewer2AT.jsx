import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { useVirtualizer } from "@tanstack/react-virtual";
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> as B<PERSON><PERSON><PERSON><PERSON>, ButtonGroup, HTMLSelect } from "@blueprintjs/core";
import { Input, InputNumber } from 'antd';
import Draggable from 'react-draggable';
import { RotateCc<PERSON>, RotateCw, MousePointer, Hand, ZoomIn, ZoomOut, Search, ChevronDown, X, ArrowRight } from "lucide-react";
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const ZOOM_LEVELS = [0.1, 0.2, 0.3, 0.4, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3, 3.5, 4];
const PAGE_PADDING = 10;
const DEFAULT_HEIGHT = 600;
const THRESHOLD = [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1];
const INDEX_ATTRIBUTE = "data-index";

const SearchWindow = ({ isOpen, onClose, searchQuery, onSearchChange, onSearch, searchResults, currentSearchIndex, onNavigateSearch, isDarkTheme, totalOccurrences }) => {
  if (!isOpen) return null;

  return (
    <Draggable handle=".search-window-header">
      <div className="search-window" style={{
        position: 'absolute',
        top: '50px',
        right: '20px',
        width: '300px',
        backgroundColor: '#2F343C',
        border: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
        borderRadius: '4px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        zIndex: 1000,
        color: '#F5F8FA',
      }}>
        <div className="search-window-header" style={{
          padding: '5px 10px',
          backgroundColor: '#2F343C',
          borderBottom: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
          cursor: 'move',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '30px',
        }}>
          <span style={{ fontSize: '14px' }}>Search PDF</span>
          <BpButton icon={<X size={14} />} minimal={true} small={true} onClick={onClose} />
        </div>
        <div style={{ padding: '10px' }}>
          <Input.Search
            placeholder="Enter search term"
            value={searchQuery}
            onChange={onSearchChange}
            onSearch={onSearch}
            style={{ marginBottom: "16px" }}
          />
          {searchResults.length > 0 && (
            <div>
              <p style={{ fontSize: '12px', marginBottom: '8px' }}>
                Result {currentSearchIndex + 1} of {searchResults.length}
              </p>
              <p style={{ fontSize: '12px', marginBottom: '8px' }}>
                Found on {searchResults.length} page(s)
              </p>
              <p style={{ fontSize: '12px', marginBottom: '8px' }}>
                Total occurrences: {totalOccurrences}
              </p>
              <BpButton onClick={() => onNavigateSearch('prev')} style={{ marginRight: "8px" }} small={true}>Previous</BpButton>
              <BpButton onClick={() => onNavigateSearch('next')} small={true}>Next</BpButton>
            </div>
          )}
        </div>
      </div>
    </Draggable>
  );
};

const GoToWindow = ({ isOpen, onClose, onGoTo, totalPages, isDarkTheme }) => {
  const [pageNumber, setPageNumber] = useState(1);

  if (!isOpen) return null;

  const handleGoTo = () => {
    onGoTo(pageNumber);
    onClose();
  };

  return (
    <Draggable handle=".goto-window-header">
      <div className="goto-window" style={{
        position: 'absolute',
        top: '50px',
        left: '20px',
        width: '250px',
        backgroundColor: '#2F343C',
        border: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
        borderRadius: '4px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        zIndex: 1000,
        color: '#F5F8FA',
      }}>
        <div className="goto-window-header" style={{
          padding: '5px 10px',
          backgroundColor: '#2F343C',
          borderBottom: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
          cursor: 'move',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '30px',
        }}>
          <span style={{ fontSize: '14px' }}>Go to Page</span>
          <BpButton icon={<X size={14} />} minimal={true} small={true} onClick={onClose} />
        </div>
        <div style={{ padding: '10px' }}>
          <InputNumber
            min={1}
            max={totalPages}
            value={pageNumber}
            onChange={(value) => setPageNumber(value)}
            style={{ marginBottom: '10px', width: '100%' }}
          />
          <BpButton onClick={handleGoTo} icon={<ArrowRight size={16} />} small={true}>
            Go to Page
          </BpButton>
        </div>
      </div>
    </Draggable>
  );
};

const highlightPattern = (text, pattern, highlightColor) => {
  if (!pattern) return text;
  const regex = new RegExp(`(${pattern})`, 'gi');
  return text.replace(regex, `<mark style="background-color: ${highlightColor};">$1</mark>`);
};

const PDFPageRenderer = React.memo(({ pageNumber, scale, rotation, width, onRenderSuccess, searchQuery, highlightColor }) => {
  const textRenderer = useCallback(
    (textItem) => highlightPattern(textItem.str, searchQuery, highlightColor),
    [searchQuery, highlightColor]
  );

  return (
    <div data-index={pageNumber - 1}>
      <Page
        key={`${pageNumber}@${scale}`}
        pageNumber={pageNumber}
        scale={scale}
        rotate={rotation}
        width={width}
        renderAnnotationLayer={true}
        renderTextLayer={true}
        customTextRenderer={textRenderer}
        onRenderSuccess={onRenderSuccess}
      />
    </div>
  );
});

const usePageObserver = ({
  parentRef,
  setCurrentPage,
  numPages,
}) => {
  const [visibilities, setVisibilities] = useState(() =>
    Array(numPages).fill(-1)
  );

  const pageObserver = useMemo(() => {
    if (typeof IntersectionObserver === 'undefined') return null;

    return new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const ratio = entry.isIntersecting ? entry.intersectionRatio : -1;
          const target = entry.target;
          const indexAttribute = target.getAttribute(INDEX_ATTRIBUTE);
          if (!indexAttribute) {
            return;
          }
          const index = parseInt(indexAttribute, 10);
          if (0 <= index && index < numPages) {
            setVisibilities((old) => {
              const newVisibilities = [...old];
              newVisibilities[index] = ratio;
              return newVisibilities;
            });
          }
        });
      },
      {
        root: parentRef.current,
        threshold: THRESHOLD,
      }
    );
  }, [parentRef, numPages]);

  useEffect(() => {
    if (!numPages) return;
    setVisibilities(Array(numPages).fill(-1));
  }, [numPages]);

  useEffect(() => {
    const maxVisibilityIndex = visibilities.indexOf(Math.max(...visibilities));
    if (maxVisibilityIndex !== -1) {
      setCurrentPage(maxVisibilityIndex + 1);
    }
  }, [visibilities, setCurrentPage]);

  useEffect(() => {
    return () => {
      if (pageObserver) {
        pageObserver.disconnect();
      }
    };
  }, [pageObserver]);

  return { pageObserver };
};

const PDFJsViewer = ({ fileId, isDarkTheme }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [containerWidth, setContainerWidth] = useState(0);
  const [isPanMode, setIsPanMode] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isGoToOpen, setIsGoToOpen] = useState(false);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [startPanPosition, setStartPanPosition] = useState({ x: 0, y: 0 });
  const [totalOccurrences, setTotalOccurrences] = useState(0);

  const parentRef = useRef(null);
  const pdfContainerRef = useRef(null);
  const parentClassName = isDarkTheme ? "bp5-dark" : "";

  const { pageObserver } = usePageObserver({
    parentRef,
    setCurrentPage: setPageNumber,
    numPages,
  });

  const estimateSize = useCallback(() => DEFAULT_HEIGHT * scale, [scale]);

  const virtualizer = useVirtualizer({
    count: numPages,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 5,
  });

  const scrollToPage = useCallback((page) => {
    virtualizer.scrollToIndex(page - 1, { align: 'start', behavior: 'smooth' });
  }, [virtualizer]);

  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();

    const fetchFile = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: {
              "session-id": localStorage.getItem("session_id"),
            },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        if (isMounted) {
          const pdfUrl = URL.createObjectURL(response.data);
          setPdfFile(pdfUrl);
          setLoading(false);
        }
      } catch (error) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(error)) {
            console.log("Request canceled:", error.message);
          } else {
            console.error("Failed to fetch the PDF:", error);
            setError("Failed to load the PDF. Please try again.");
          }
        }
      }
    };

    if (fileId) {
      fetchFile();
    }

    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId]);

  useEffect(() => {
    const updateContainerWidth = () => {
      if (pdfContainerRef.current) {
        const newWidth = pdfContainerRef.current.clientWidth;
        setContainerWidth(newWidth);
      }
    };

    const resizeObserver = new ResizeObserver(updateContainerWidth);

    if (pdfContainerRef.current) {
      resizeObserver.observe(pdfContainerRef.current);
    }

    updateContainerWidth();

    return () => resizeObserver.disconnect();
  }, []);

  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    setNumPages(numPages);
    setPageNumber(1);
    setLoading(false);
  }, []);

  const onDocumentLoadError = useCallback((error) => {
    console.error("Error loading PDF:", error);
    setError("Failed to load the PDF. Please check if the file is corrupted or try again.");
    setLoading(false);
  }, []);

  const updateZoom = useCallback((newZoomLevel) => {
    setZoomLevel(newZoomLevel);
    setScale(newZoomLevel);
  }, []);

  const handleZoomChange = useCallback((event) => {
    updateZoom(parseFloat(event.target.value));
  }, [updateZoom]);

  const zoomIn = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex < ZOOM_LEVELS.length - 1) {
      updateZoom(ZOOM_LEVELS[currentIndex + 1]);
    }
  }, [zoomLevel, updateZoom]);

  const zoomOut = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex > 0) {
      updateZoom(ZOOM_LEVELS[currentIndex - 1]);
    }
  }, [zoomLevel, updateZoom]);

  const rotateLeft = useCallback(() => {
    setRotation((prevRotation) => (prevRotation - 90 + 360) % 360);
  }, []);

  const rotateRight = useCallback(() => {
    setRotation((prevRotation) => (prevRotation + 90) % 360);
  }, []);

  const resetView = useCallback(() => {
    updateZoom(1.0);
    setRotation(0);
    setIsPanMode(false);
    setPanPosition({ x: 0, y: 0 });
  }, [updateZoom]);

  const togglePanMode = useCallback(() => {
    setIsPanMode((prev) => !prev);
  }, []);

  const handleSearch = useCallback(async () => {
    if (!pdfFile || !searchQuery) return;

    const pdf = await pdfjs.getDocument(pdfFile).promise;
    const results = [];
    let occurrences = 0;

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const text = textContent.items.map((item) => item.str).join(' ');

      const regex = new RegExp(searchQuery, 'gi');
      const matches = text.match(regex);

      if (matches) {
        results.push(i);
        occurrences += matches.length;
      }
    }

    setSearchResults(results);
    setTotalOccurrences(occurrences);
    setCurrentSearchIndex(results.length > 0 ? 0 : -1);
    if (results.length > 0) {
      setPageNumber(results[0]);
      scrollToPage(results[0]);
    }
    setIsSearchOpen(true);
  }, [pdfFile, searchQuery, scrollToPage]);

  const navigateSearch = useCallback((direction) => {
    if (searchResults.length === 0) return;

    const newIndex = direction === 'next'
      ? (currentSearchIndex + 1) % searchResults.length
      : (currentSearchIndex - 1 + searchResults.length) % searchResults.length;

    setCurrentSearchIndex(newIndex);
    setPageNumber(searchResults[newIndex]);
    scrollToPage(searchResults[newIndex]);
  }, [searchResults, currentSearchIndex, scrollToPage]);

  const handleSearchInputChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleMouseDown = useCallback((e) => {
    if (isPanMode) {
      setIsDragging(true);
      setStartPanPosition({ x: e.clientX - panPosition.x, y: e.clientY - panPosition.y });
    }
  }, [isPanMode, panPosition]);

  const handleMouseMove = useCallback((e) => {
    if (isPanMode && isDragging) {
      const newX = e.clientX - startPanPosition.x;
      const newY = e.clientY - startPanPosition.y;
      setPanPosition({ x: newX, y: newY });
    }
  }, [isPanMode, isDragging, startPanPosition]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  const handleGoTo = useCallback((page) => {
    if (page >= 1 && page <= numPages) {
      setPageNumber(page);
      scrollToPage(page);
    }
  }, [numPages, scrollToPage]);

  if (loading) {
    return (
      <div style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <Spinner intent="primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
        <p>{error}</p>
        <BpButton onClick={() => window.location.reload()}>Retry</BpButton>
      </div>
    );
  }

  return (
    <div className={parentClassName} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      <Card
        style={{
          padding: "4px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          background: isDarkTheme ? "#1C2127" : "white",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "10px", flex: "1 1 25%" }}>
          <Input
            placeholder="Search..."
            value={searchQuery}
            onChange={handleSearchInputChange}
            onPressEnter={handleSearch}
            style={{ width: "200px" }}
            className={isDarkTheme ? "bp5-dark" : ""}
          />
          <BpButton
            icon={<Search size={16} color={isDarkTheme ? "white" : "black"} />}
            onClick={handleSearch}
            minimal={true}
            style={{ color: isDarkTheme ? "white" : "black" }}
          />
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "5px", flex: "1 1 50%", justifyContent: "center" }}>
          <ButtonGroup minimal={true}>
            <BpButton icon={<RotateCcw size={16} />} onClick={rotateLeft} className={isDarkTheme ? "bp5-dark" : ""} />
            <BpButton icon={<RotateCw size={16} />} onClick={rotateRight} className={isDarkTheme ? "bp5-dark" : ""} />
          </ButtonGroup>
          <ButtonGroup>
            <BpButton
              icon={<MousePointer size={16} />}
              onClick={() => setIsPanMode(false)}
              intent={!isPanMode ? "primary" : "none"}
              className={isDarkTheme ? "bp5-dark" : ""}
              minimal={isPanMode}
            />
            <BpButton
              icon={<Hand size={16} />}
              onClick={togglePanMode}
              intent={isPanMode ? "primary" : "none"}
              className={isDarkTheme ? "bp5-dark" : ""}
              minimal={!isPanMode}
            />
          </ButtonGroup>
          <ButtonGroup minimal={true}>
            <BpButton icon={<ZoomOut size={16} />} onClick={zoomOut} className={isDarkTheme ? "bp5-dark" : ""} />
            <BpButton icon={<ZoomIn size={16} />} onClick={zoomIn} className={isDarkTheme ? "bp5-dark" : ""} />
            <HTMLSelect
              options={ZOOM_LEVELS.map(level => ({ label: `${level * 100}%`, value: level }))}
              value={zoomLevel}
              onChange={handleZoomChange}
              style={{ width: '100px' }}
              minimal={true}
              icon={<ChevronDown size={14} />}
              className={isDarkTheme ? "bp5-dark" : ""}
            />
          </ButtonGroup>
          <BpButton text="Reset" onClick={resetView} minimal={true} small={true} className={isDarkTheme ? "bp5-dark" : ""} />
          <BpButton
            text="Go to"
            onClick={() => setIsGoToOpen(true)}
            minimal={true}
            small={true}
            className={isDarkTheme ? "bp5-dark" : ""}
          />
        </div>
        <div style={{ flex: "1 1 25%", textAlign: "right", paddingRight: "20px" }}>
          <span>
            Page {pageNumber} of {numPages}
          </span>
        </div>
      </Card>

      <div
        ref={pdfContainerRef}
        className="pdf-container"
        style={{
          flex: 1,
          overflow: "hidden",
          position: "relative",
        }}
      >
        {pdfFile && (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={<Spinner />}
          >
            <div
              ref={parentRef}
              style={{
                height: "100%",
                width: "100%",
                overflow: "auto",
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
              }}
              onMouseDown={handleMouseDown}
            >
              <div
                style={{
                  height: `${virtualizer.getTotalSize()}px`,
                  width: "100%",
                  position: "relative",
                  transform: `translate(${panPosition.x}px, ${panPosition.y}px)`,
                  cursor: isPanMode ? (isDragging ? 'grabbing' : 'grab') : 'default',
                }}
              >
                {virtualizer.getVirtualItems().map((virtualItem) => (
                  <div
                    key={virtualItem.key}
                    data-index={virtualItem.index}
                    ref={(el) => {
                      virtualizer.measureElement(el);
                      if (el && pageObserver) {
                        pageObserver.observe(el);
                      }
                    }}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                  >
                    <div 
                      style={{ 
                        marginBottom: `${PAGE_PADDING}px`, 
                        display: 'flex', 
                        justifyContent: 'center',
                        userSelect: isPanMode ? 'none' : 'text',
                      }}
                    >
                      <PDFPageRenderer
                        pageNumber={virtualItem.index + 1}
                        scale={scale}
                        rotation={rotation}
                        width={containerWidth}
                        onRenderSuccess={() => {}}
                        searchQuery={searchQuery}
                        highlightColor="#FBB360"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Document>
        )}
      </div>

      <SearchWindow
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
        searchQuery={searchQuery}
        onSearchChange={handleSearchInputChange}
        onSearch={handleSearch}
        searchResults={searchResults}
        currentSearchIndex={currentSearchIndex}
        onNavigateSearch={navigateSearch}
        isDarkTheme={isDarkTheme}
        totalOccurrences={totalOccurrences}
      />

      <GoToWindow
        isOpen={isGoToOpen}
        onClose={() => setIsGoToOpen(false)}
        onGoTo={handleGoTo}
        totalPages={numPages}
        isDarkTheme={isDarkTheme}
      />
    </div>
  );
};

export default PDFJsViewer;

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import axios from 'axios';
import { Document, Page, pdfjs } from 'react-pdf';
import { useVirtualizer } from "@tanstack/react-virtual";
import { Card, Spinner, Button, ButtonGroup, HTMLSelect } from "@blueprintjs/core";
import { Input, InputNumber } from 'antd';
import Draggable from 'react-draggable';
import { RotateCcw, RotateCw, MousePointer, Hand, ZoomIn, ZoomOut, Search, ChevronDown, X, ArrowRight } from "lucide-react";
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const ZOOM_LEVELS = [0.1, 0.2, 0.3, 0.4, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3, 3.5, 4];
const PAGE_PADDING = 10;
const DEFAULT_HEIGHT = 600;
const THRESHOLD = [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1];
const INDEX_ATTRIBUTE = "data-index";
const ZOOM_FACTOR = 0.1;
const ZOOM_DELAY = 200;

const SearchWindow = ({ isOpen, onClose, searchQuery, onSearchChange, onSearch, searchResults, currentSearchIndex, onNavigateSearch, isDarkTheme, totalOccurrences }) => {
  if (!isOpen) return null;

  return (
    <Draggable handle=".search-window-header">
      <div className="search-window" style={{
        position: 'absolute',
        top: '50px',
        right: '20px',
        width: '300px',
        backgroundColor: '#2F343C',
        border: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
        borderRadius: '4px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        zIndex: 1000,
        color: '#F5F8FA',
      }}>
        <div className="search-window-header" style={{
          padding: '5px 10px',
          backgroundColor: '#2F343C',
          borderBottom: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
          cursor: 'move',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '30px',
        }}>
          <span style={{ fontSize: '14px' }}>Search PDF</span>
          <Button icon={<X size={14} />} minimal={true} small={true} onClick={onClose} />
        </div>
        <div style={{ padding: '10px' }}>
          <Input.Search
            placeholder="Enter search term"
            value={searchQuery}
            onChange={onSearchChange}
            onSearch={onSearch}
            style={{ marginBottom: "16px" }}
          />
          {searchResults.length > 0 && (
            <div>
              <p style={{ fontSize: '12px', marginBottom: '8px' }}>
                Result {currentSearchIndex + 1} of {searchResults.length}
              </p>
              <p style={{ fontSize: '12px', marginBottom: '8px' }}>
                Found on {searchResults.length} page(s)
              </p>
              <p style={{ fontSize: '12px', marginBottom: '8px' }}>
                Total occurrences: {totalOccurrences}
              </p>
              <Button onClick={() => onNavigateSearch('prev')} style={{ marginRight: "8px" }} small={true}>Previous</Button>
              <Button onClick={() => onNavigateSearch('next')} small={true}>Next</Button>
            </div>
          )}
        </div>
      </div>
    </Draggable>
  );
};

const GoToWindow = ({ isOpen, onClose, onGoTo, totalPages, isDarkTheme }) => {
  const [pageNumber, setPageNumber] = useState(1);

  if (!isOpen) return null;

  const handleGoTo = () => {
    onGoTo(pageNumber);
    onClose();
  };

  return (
    <Draggable handle=".goto-window-header">
      <div className="goto-window" style={{
        position: 'absolute',
        top: '50px',
        left: '20px',
        width: '250px',
        backgroundColor: '#2F343C',
        border: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
        borderRadius: '4px',
        boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
        zIndex: 1000,
        color: '#F5F8FA',
      }}>
        <div className="goto-window-header" style={{
          padding: '5px 10px',
          backgroundColor: '#2F343C',
          borderBottom: `1px solid ${isDarkTheme ? '#5C7080' : '#ccc'}`,
          cursor: 'move',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          height: '30px',
        }}>
          <span style={{ fontSize: '14px' }}>Go to Page</span>
          <Button icon={<X size={14} />} minimal={true} small={true} onClick={onClose} />
        </div>
        <div style={{ padding: '10px' }}>
          <InputNumber
            min={1}
            max={totalPages}
            value={pageNumber}
            onChange={(value) => setPageNumber(value)}
            style={{ marginBottom: '10px', width: '100%' }}
          />
          <Button onClick={handleGoTo} icon={<ArrowRight size={16} />} small={true}>
            Go to Page
          </Button>
        </div>
      </div>
    </Draggable>
  );
};

const highlightPattern = (text, pattern, highlightColor) => {
  if (!pattern) return text;
  const regex = new RegExp(`(${pattern})`, 'gi');
  return text.replace(regex, `<mark style="background-color: ${highlightColor};">$1</mark>`);
};

const PDFPageRenderer = React.memo(({ pageNumber, scale, rotation, width, onRenderSuccess, searchQuery, highlightColor }) => {
  const textRenderer = useCallback(
    (textItem) => highlightPattern(textItem.str, searchQuery, highlightColor),
    [searchQuery, highlightColor]
  );

  return (
    <div data-index={pageNumber - 1}>
      <Page
        key={`${pageNumber}@${scale}`}
        pageNumber={pageNumber}
        scale={scale}
        rotate={rotation}
        width={width}
        renderAnnotationLayer={true}
        renderTextLayer={true}
        customTextRenderer={textRenderer}
        onRenderSuccess={onRenderSuccess}
      />
    </div>
  );
});

const usePageObserver = ({
  parentRef,
  setCurrentPage,
  numPages,
}) => {
  const [visibilities, setVisibilities] = useState(() =>
    Array(numPages).fill(-1)
  );

  const pageObserver = useMemo(() => {
    if (typeof IntersectionObserver === 'undefined') return null;

    return new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const ratio = entry.isIntersecting ? entry.intersectionRatio : -1;
          const target = entry.target;
          const indexAttribute = target.getAttribute(INDEX_ATTRIBUTE);
          if (!indexAttribute) {
            return;
          }
          const index = parseInt(indexAttribute, 10);
          if (0 <= index && index < numPages) {
            setVisibilities((old) => {
              const newVisibilities = [...old];
              newVisibilities[index] = ratio;
              return newVisibilities;
            });
          }
        });
      },
      {
        root: parentRef.current,
        threshold: THRESHOLD,
      }
    );
  }, [parentRef, numPages]);

  useEffect(() => {
    if (!numPages) return;
    setVisibilities(Array(numPages).fill(-1));
  }, [numPages]);

  useEffect(() => {
    const maxVisibilityIndex = visibilities.indexOf(Math.max(...visibilities));
    if (maxVisibilityIndex !== -1) {
      setCurrentPage(maxVisibilityIndex + 1);
    }
  }, [visibilities, setCurrentPage]);

  useEffect(() => {
    return () => {
      if (pageObserver) {
        pageObserver.disconnect();
      }
    };
  }, [pageObserver]);

  return { pageObserver };
};

const PDFViewer = ({ fileId, isDarkTheme }) => {
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [containerWidth, setContainerWidth] = useState(0);
  const [isPanMode, setIsPanMode] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isGoToOpen, setIsGoToOpen] = useState(false);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [startPanPosition, setStartPanPosition] = useState({ x: 0, y: 0 });
  const [totalOccurrences, setTotalOccurrences] = useState(0);
  const [isZooming, setIsZooming] = useState(false);

  const parentRef = useRef(null);
  const pdfContainerRef = useRef(null);
  const zoomTimeoutRef = useRef(null);
  const parentClassName = isDarkTheme ? "bp5-dark" : "";

  const { pageObserver } = usePageObserver({
    parentRef,
    setCurrentPage: setPageNumber,
    numPages,
  });

  const estimateSize = useCallback(() => DEFAULT_HEIGHT * scale, [scale]);

  const virtualizer = useVirtualizer({
    count: numPages,
    getScrollElement: () => parentRef.current,
    estimateSize,
    overscan: 5,
  });

  const scrollToPage = useCallback((page) => {
    virtualizer.scrollToIndex(page - 1, { align: 'start', behavior: 'smooth' });
  }, [virtualizer]);

  useEffect(() => {
    let isMounted = true;
    const cancelSource = axios.CancelToken.source();

    const fetchFile = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await axios.get(
          `${process.env.REACT_APP_FILES_API}/get-file/${fileId}`,
          {
            headers: {
              "session-id": localStorage.getItem("session_id"),
            },
            responseType: "blob",
            cancelToken: cancelSource.token,
          }
        );

        if (isMounted) {
          const pdfUrl = URL.createObjectURL(response.data);
          setPdfFile(pdfUrl);
          setLoading(false);
        }
      } catch (error) {
        if (isMounted) {
          setLoading(false);
          if (axios.isCancel(error)) {
            console.log("Request canceled:", error.message);
          } else {
            console.error("Failed to fetch the PDF:", error);
            setError("Failed to load the PDF. Please try again.");
          }
        }
      }
    };

    if (fileId) {
      fetchFile();
    }

    return () => {
      isMounted = false;
      cancelSource.cancel("Operation canceled by the user.");
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId]);

  useEffect(() => {
    const updateContainerWidth = () => {
      if (pdfContainerRef.current) {
        const newWidth = pdfContainerRef.current.clientWidth;
        setContainerWidth(newWidth);
      }
    };

    const resizeObserver = new ResizeObserver(updateContainerWidth);

    if (pdfContainerRef.current) {
      resizeObserver.observe(pdfContainerRef.current);
    }

    updateContainerWidth();

    return () => resizeObserver.disconnect();
  }, []);

  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    setNumPages(numPages);
    setPageNumber(1);
    setLoading(false);
  }, []);

  const onDocumentLoadError = useCallback((error) => {
    console.error("Error loading PDF:", error);
    setError("Failed to load the PDF. Please check if the file is corrupted or try again.");
    setLoading(false);
  }, []);

  const updateZoom = useCallback((newZoomLevel) => {
    setZoomLevel(newZoomLevel);
    setScale(newZoomLevel);
    setIsZooming(true);
    if (zoomTimeoutRef.current) {
      clearTimeout(zoomTimeoutRef.current);
    }
    zoomTimeoutRef.current = setTimeout(() => {
      setIsZooming(false);
    }, ZOOM_DELAY);
  }, []);

  const handleZoomChange = useCallback((event) => {
    updateZoom(parseFloat(event.target.value));
  }, [updateZoom]);

  const zoomIn = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex < ZOOM_LEVELS.length - 1) {
      updateZoom(ZOOM_LEVELS[currentIndex + 1]);
    }
  }, [zoomLevel, updateZoom]);

  const zoomOut = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(zoomLevel);
    if (currentIndex > 0) {
      updateZoom(ZOOM_LEVELS[currentIndex - 1]);
    }
  }, [zoomLevel, updateZoom]);

  const rotateLeft = useCallback(() => {
    setRotation((prevRotation) => (prevRotation - 90 + 360) % 360);
  }, []);

  const rotateRight = useCallback(() => {
    setRotation((prevRotation) => (prevRotation + 90) % 360);
  }, []);

  const resetView = useCallback(() => {
    updateZoom(1.0);
    setRotation(0);
    setIsPanMode(false);
    setPanPosition({ x: 0, y: 0 });
  }, [updateZoom]);

  const togglePanMode = useCallback(() => {
    setIsPanMode((prev) => !prev);
  }, []);

  const handleSearch = useCallback(async () => {
    if (!pdfFile || !searchQuery) return;

    const pdf = await pdfjs.getDocument(pdfFile).promise;
    const results = [];
    let occurrences = 0;

    for (let i = 1; i <= pdf.numPages; i++) {
      const page = await pdf.getPage(i);
      const textContent = await page.getTextContent();
      const text = textContent.items.map((item) => item.str).join(' ');

      const regex = new RegExp(searchQuery, 'gi');
      const matches = text.match(regex);

      if (matches) {
        results.push(i);
        occurrences += matches.length;
      }
    }

    setSearchResults(results);
    setTotalOccurrences(occurrences);
    setCurrentSearchIndex(results.length > 0 ? 0 : -1);
    if (results.length > 0) {
      setPageNumber(results[0]);
      scrollToPage(results[0]);
    }
    setIsSearchOpen(true);
  }, [pdfFile, searchQuery, scrollToPage]);

  const navigateSearch = useCallback((direction) => {
    if (searchResults.length === 0) return;

    const newIndex = direction === 'next'
      ? (currentSearchIndex + 1) % searchResults.length
      : (currentSearchIndex - 1 + searchResults.length) % searchResults.length;

    setCurrentSearchIndex(newIndex);
    setPageNumber(searchResults[newIndex]);
    scrollToPage(searchResults[newIndex]);
  }, [searchResults, currentSearchIndex, scrollToPage]);

  const handleSearchInputChange = useCallback((e) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleMouseDown = useCallback((e) => {
    if (isPanMode) {
      setIsDragging(true);
      setStartPanPosition({ x: e.clientX - panPosition.x, y: e.clientY - panPosition.y });
    }
  }, [isPanMode, panPosition]);

  const handleMouseMove = useCallback((e) => {
    if (isPanMode && isDragging) {
      const newX = e.clientX - startPanPosition.x;
      const newY = e.clientY - startPanPosition.y;
      setPanPosition({ x: newX, y: newY });
    }
  }, [isPanMode, isDragging, startPanPosition]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [handleMouseMove, handleMouseUp]);

  const handleGoTo = useCallback((page) => {
    if (page >= 1 && page <= numPages) {
      setPageNumber(page);
      scrollToPage(page);
    }
  }, [numPages, scrollToPage]);

  const handleWheel = useCallback((e) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      const delta = e.deltaY < 0 ? ZOOM_FACTOR : -ZOOM_FACTOR;
      const newScale = Math.min(Math.max(scale + delta, ZOOM_LEVELS[0]), ZOOM_LEVELS[ZOOM_LEVELS.length - 1]);
      updateZoom(newScale);
    }
  }, [scale, updateZoom]);

  useEffect(() => {
    const container = parentRef.current;
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });
    }
    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheel);
      }
    };
  }, [handleWheel]);

  if (loading) {
    return (
      <div style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center" }}>
        <Spinner intent="primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ height: "100vh", display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
        <p>{error}</p>
        <Button onClick={() => window.location.reload()}>Retry</Button>
      </div>
    );
  }

  return (
    <div className={parentClassName} style={{ height: "100vh", display: "flex", flexDirection: "column" }}>
      <Card
        style={{
          padding: "4px",
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          background: isDarkTheme ? "#1C2127" : "white",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "10px", flex: "1 1 25%" }}>
          <Input
            placeholder="Search..."
            value={searchQuery}
            onChange={handleSearchInputChange}
            onPressEnter={handleSearch}
            style={{ width: "200px" }}
            className={isDarkTheme ? "bp5-dark" : ""}
          />
          <Button
            icon={<Search size={16} color={isDarkTheme ? "white" : "black"} />}
            onClick={handleSearch}
            minimal={true}
            style={{ color: isDarkTheme ? "white" : "black" }}
          />
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "5px", flex: "1 1 50%", justifyContent: "center" }}>
          <ButtonGroup minimal={true}>
            <Button icon={<RotateCcw size={16} />} onClick={rotateLeft} className={isDarkTheme ? "bp5-dark" : ""} />
            <Button icon={<RotateCw size={16} />} onClick={rotateRight} className={isDarkTheme ? "bp5-dark" : ""} />
          </ButtonGroup>
          <ButtonGroup>
            <Button
              icon={<MousePointer size={16} />}
              onClick={() => setIsPanMode(false)}
              intent={!isPanMode ? "primary" : "none"}
              className={isDarkTheme ? "bp5-dark" : ""}
              minimal={isPanMode}
            />
            <Button
              icon={<Hand size={16} />}
              onClick={togglePanMode}
              intent={isPanMode ? "primary" : "none"}
              className={isDarkTheme ? "bp5-dark" : ""}
              minimal={!isPanMode}
            />
          </ButtonGroup>
          <ButtonGroup minimal={true}>
            <Button icon={<ZoomOut size={16} />} onClick={zoomOut} className={isDarkTheme ? "bp5-dark" : ""} />
            <Button icon={<ZoomIn size={16} />} onClick={zoomIn} className={isDarkTheme ? "bp5-dark" : ""} />
            <HTMLSelect
              options={ZOOM_LEVELS.map(level => ({ label: `${level * 100}%`, value: level }))}
              value={zoomLevel}
              onChange={handleZoomChange}
              style={{ width: '100px' }}
              minimal={true}
              icon={<ChevronDown size={14} />}
              className={isDarkTheme ? "bp5-dark" : ""}
            />
          </ButtonGroup>
          <Button text="Reset" onClick={resetView} minimal={true} small={true} className={isDarkTheme ? "bp5-dark" : ""} />
          <Button
            text="Go to"
            onClick={() => setIsGoToOpen(true)}
            minimal={true}
            small={true}
            className={isDarkTheme ? "bp5-dark" : ""}
          />
        </div>
        <div style={{ flex: "1 1 25%", textAlign: "right", paddingRight: "20px" }}>
          <span>
            Page {pageNumber} of {numPages}
          </span>
        </div>
      </Card>

      <div
        ref={pdfContainerRef}
        className="pdf-container"
        style={{
          flex: 1,
          overflow: "hidden",
          position: "relative",
        }}
      >
        {pdfFile && (
          <Document
            file={pdfFile}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading={<Spinner />}
          >
            <div
              ref={parentRef}
              style={{
                height: "100%",
                width: "100%",
                overflow: "auto",
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
              }}
              onMouseDown={handleMouseDown}
            >
              <div
                style={{
                  height: `${virtualizer.getTotalSize()}px`,
                  width: "100%",
                  position: "relative",
                  transform: `translate(${panPosition.x}px, ${panPosition.y}px)`,
                  cursor: isPanMode ? (isDragging ? 'grabbing' : 'grab') : 'default',
                }}
              >
                {virtualizer.getVirtualItems().map((virtualItem) => (
                  <div
                    key={virtualItem.key}
                    data-index={virtualItem.index}
                    ref={(el) => {
                      virtualizer.measureElement(el);
                      if (el && pageObserver) {
                        pageObserver.observe(el);
                      }
                    }}
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                  >
                    <div 
                      style={{ 
                        marginBottom: `${PAGE_PADDING}px`, 
                        display: 'flex', 
                        justifyContent: 'center',
                        userSelect: isPanMode ? 'none' : 'text',
                      }}
                    >
                      <PDFPageRenderer
                        pageNumber={virtualItem.index + 1}
                        scale={scale}
                        rotation={rotation}
                        width={containerWidth}
                        onRenderSuccess={() => {}}
                        searchQuery={searchQuery}
                        highlightColor="#FBB360"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Document>
        )}
        {isZooming && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1000,
          }}>
            <Spinner intent="primary" />
          </div>
        )}
      </div>

      <SearchWindow
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
        searchQuery={searchQuery}
        onSearchChange={handleSearchInputChange}
        onSearch={handleSearch}
        searchResults={searchResults}
        currentSearchIndex={currentSearchIndex}
        onNavigateSearch={navigateSearch}
        isDarkTheme={isDarkTheme}
        totalOccurrences={totalOccurrences}
      />

      <GoToWindow
        isOpen={isGoToOpen}
        onClose={() => setIsGoToOpen(false)}
        onGoTo={handleGoTo}
        totalPages={numPages}
        isDarkTheme={isDarkTheme}
      />
    </div>
  );
};

export default PDFViewer;