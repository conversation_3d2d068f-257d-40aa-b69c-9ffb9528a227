export const getColorForBlockType = (blockType) => {
    switch(blockType) {
      case "PAGE":
        return "rgba(255, 0, 0, 0.3)"; // Red
      case "LINE":
        return "rgba(0, 255, 0, 0.3)"; // Green
      case "WORD":
        return "rgba(0, 102, 202, 0.3)"; // Blue (As specified)
      case "SELECTION_ELEMENT":
        return "rgba(255, 165, 0, 0.3)"; // Orange
      case "TABLE":
        return "rgba(128, 0, 128, 0.3)"; // Purple
      case "CELL":
        return "rgba(255, 192, 203, 0.3)"; // Pink
      case "MERGED_CELL":
        return "rgba(255, 255, 0, 0.3)"; // Yellow
      case "TABLE_TITLE":
        return "rgba(0, 128, 128, 0.3)"; // Teal
      case "KEY_VALUE_SET":
        return "rgba(128, 128, 0, 0.3)"; // Olive
      case "TABLE_FOOTER":
        return "rgba(0, 0, 128, 0.3)"; // Navy
      default:
        return "rgba(0, 102, 202, 0.3)"; // Default to <PERSON>
    }
  };