import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Document, Page } from 'react-pdf';
import { useVirtualizer } from "@tanstack/react-virtual";
import { <PERSON>, Spinner, <PERSON><PERSON>, ButtonGroup } from "@blueprintjs/core";
import { TransformWrapper, TransformComponent } from "react-zoom-pan-pinch";
import { <PERSON>otateCcw, RotateCw, MousePointer, Hand, ZoomIn, ZoomOut, Search, ChevronDown } from "lucide-react";
import Draggable from 'react-draggable';
import "@blueprintjs/core/lib/css/blueprint.css";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import "react-pdf/dist/esm/Page/TextLayer.css";
import { debounce } from 'lodash';
import './pdf-viewer.css';

// Import our custom PDF.js patch
import pdfjsModule from './pdfjs-patch';

// Use the patched pdfjs module
const pdfjs = pdfjsModule;

const ZOOM_LEVELS = [0.1, 0.2, 0.3, 0.4, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2, 2.5, 3, 3.5, 4];
const PAGE_PADDING = 10;

// Highlight types
const HIGHLIGHT_TYPES = {
  TEXT: 'TEXT',
  AREA: 'AREA'
};

// Custom highlight component for text highlights
const TextHighlight = ({ highlight, isScrolledTo }) => {
  const { position, content } = highlight;
  
  return (
    <div
      className={`text-highlight ${isScrolledTo ? 'scrolled-to' : ''}`}
      style={{
        position: 'absolute',
        background: 'rgba(255, 226, 143, 0.4)',
        border: isScrolledTo ? '2px solid rgba(255, 226, 143, 1)' : 'none'
      }}
    >
      {position.rects.map((rect, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: rect.x1,
            top: rect.y1,
            width: rect.x2 - rect.x1,
            height: rect.y2 - rect.y1,
            background: 'rgba(255, 226, 143, 0.4)',
            borderRadius: '2px'
          }}
        />
      ))}
    </div>
  );
};

// Custom highlight component for area highlights
const AreaHighlight = ({ highlight, isScrolledTo }) => {
  const { position, content } = highlight;
  const rect = position.boundingRect;
  
  return (
    <div
      className={`area-highlight ${isScrolledTo ? 'scrolled-to' : ''}`}
      style={{
        position: 'absolute',
        left: rect.x1,
        top: rect.y1,
        width: rect.x2 - rect.x1,
        height: rect.y2 - rect.y1,
        background: 'rgba(255, 226, 143, 0.3)',
        border: '2px dashed rgba(255, 226, 143, 0.8)',
        borderRadius: '3px'
      }}
    />
  );
};

// Highlight container that renders the appropriate highlight type
const HighlightContainer = ({ highlight, isScrolledTo, onClick }) => {
  const isTextHighlight = highlight.type === HIGHLIGHT_TYPES.TEXT;
  
  return (
    <div 
      className="highlight-container"
      onClick={() => onClick(highlight)}
    >
      {isTextHighlight ? (
        <TextHighlight highlight={highlight} isScrolledTo={isScrolledTo} />
      ) : (
        <AreaHighlight highlight={highlight} isScrolledTo={isScrolledTo} />
      )}
    </div>
  );
};

// PDF Page renderer with highlight support
const PDFPageRenderer = React.memo(({ 
  pageNumber, 
  scale, 
  rotation, 
  width, 
  highlights = [], 
  onSelectionFinished,
  currentlyScrolledToHighlightId
}) => {
  const pageRef = useRef(null);
  const [pageSize, setPageSize] = useState(null);
  
  const onRenderSuccess = useCallback((page) => {
    const viewport = page.getViewport({ scale, rotation });
    setPageSize({
      width: viewport.width,
      height: viewport.height
    });
  }, [scale, rotation]);
  
  const onMouseUp = useCallback((e) => {
    const selection = window.getSelection();
    const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
    
    if (range && !range.collapsed && pageRef.current) {
      const pageRect = pageRef.current.getBoundingClientRect();
      const rects = [];
      
      // Get client rects for the selection
      const clientRects = range.getClientRects();
      for (let i = 0; i < clientRects.length; i++) {
        const rect = clientRects[i];
        if (rect.width > 0 && rect.height > 0) {
          rects.push({
            x1: rect.left - pageRect.left,
            y1: rect.top - pageRect.top,
            x2: rect.right - pageRect.left,
            y2: rect.bottom - pageRect.top,
            width: rect.width,
            height: rect.height
          });
        }
      }
      
      if (rects.length > 0) {
        // Calculate bounding rect
        const boundingRect = {
          x1: Math.min(...rects.map(r => r.x1)),
          y1: Math.min(...rects.map(r => r.y1)),
          x2: Math.max(...rects.map(r => r.x2)),
          y2: Math.max(...rects.map(r => r.y2)),
        };
        
        // Create highlight object
        const highlight = {
          id: `highlight-${Date.now()}`,
          type: HIGHLIGHT_TYPES.TEXT,
          page: pageNumber,
          position: {
            boundingRect,
            rects,
            pageNumber
          },
          content: {
            text: selection.toString()
          },
          comment: ''
        };
        
        onSelectionFinished(highlight);
      }
    }
  }, [pageNumber, onSelectionFinished]);
  
  // Filter highlights for this page
  const pageHighlights = useMemo(() => {
    return highlights.filter(h => h.page === pageNumber);
  }, [highlights, pageNumber]);
  
  return (
    <div 
      ref={pageRef}
      className="pdf-page"
      style={{ position: 'relative' }}
      onMouseUp={onMouseUp}
    >
      <Page
        pageNumber={pageNumber}
        scale={scale}
        rotate={rotation}
        width={width}
        renderAnnotationLayer={true}
        renderTextLayer={true}
        onRenderSuccess={onRenderSuccess}
      />
      
      {pageSize && pageHighlights.map((highlight) => (
        <HighlightContainer
          key={highlight.id}
          highlight={highlight}
          isScrolledTo={currentlyScrolledToHighlightId === highlight.id}
          onClick={() => {}}
        />
      ))}
    </div>
  );
});

// Main PDF Viewer component
const HighlightablePDFViewer = ({ fileId, filePath, isDarkTheme }) => {
  // State for PDF document
  const [pdfFile, setPdfFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for viewer settings
  const [scale, setScale] = useState(1);
  const [rotation, setRotation] = useState(0);
  const [containerWidth, setContainerWidth] = useState(800);
  
  // State for highlights
  const [highlights, setHighlights] = useState([]);
  const [currentHighlightId, setCurrentHighlightId] = useState(null);
  
  // Refs
  const containerRef = useRef(null);
  const parentRef = useRef(null);
  
  // Fetch PDF file
  useEffect(() => {
    async function fetchFile() {
      setLoading(true);
      setPdfFile(null);
      
      try {
        let response;
        if (fileId) {
          response = await fetch(`${process.env.REACT_APP_FILES_API}/get-file/${fileId}`, {
            headers: {
              'Accept-Encoding': 'gzip, deflate, br',
              'session-id': localStorage.getItem('session_id'),
            },
          });
        } else if (filePath) {
          response = await fetch(filePath);
        } else {
          throw new Error("No file source provided");
        }
        
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        
        const blob = await response.blob();
        const pdfUrl = URL.createObjectURL(blob);
        setPdfFile(pdfUrl);
        setLoading(false);
      } catch (error) {
        console.error("Failed to fetch the PDF:", error);
        setError(error.message);
        setLoading(false);
      }
    }
    
    fetchFile();
    
    return () => {
      if (pdfFile) {
        URL.revokeObjectURL(pdfFile);
      }
    };
  }, [fileId, filePath]);
  
  // Update container width on resize
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };
    
    updateWidth();
    
    const debouncedUpdateWidth = debounce(updateWidth, 100);
    window.addEventListener('resize', debouncedUpdateWidth);
    
    return () => {
      window.removeEventListener('resize', debouncedUpdateWidth);
    };
  }, []);
  
  // Handle document load success
  const onDocumentLoadSuccess = useCallback((pdf) => {
    setNumPages(pdf.numPages);
    setLoading(false);
  }, []);
  
  // Handle document load error
  const onDocumentLoadError = useCallback((error) => {
    console.error("Error loading PDF:", error);
    setError("Failed to load PDF document");
    setLoading(false);
  }, []);
  
  // Handle zoom in/out
  const zoomIn = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(scale);
    if (currentIndex < ZOOM_LEVELS.length - 1) {
      setScale(ZOOM_LEVELS[currentIndex + 1]);
    }
  }, [scale]);
  
  const zoomOut = useCallback(() => {
    const currentIndex = ZOOM_LEVELS.indexOf(scale);
    if (currentIndex > 0) {
      setScale(ZOOM_LEVELS[currentIndex - 1]);
    }
  }, [scale]);
  
  // Handle rotation
  const rotateLeft = useCallback(() => {
    setRotation((prev) => (prev - 90) % 360);
  }, []);
  
  const rotateRight = useCallback(() => {
    setRotation((prev) => (prev + 90) % 360);
  }, []);
  
  // Handle highlight creation
  const addHighlight = useCallback((highlight) => {
    setHighlights((prev) => [...prev, highlight]);
    setCurrentHighlightId(highlight.id);
  }, []);
  
  // Scroll to highlight
  const scrollToHighlight = useCallback((highlightId) => {
    setCurrentHighlightId(highlightId);
    
    const highlight = highlights.find(h => h.id === highlightId);
    if (highlight) {
      setCurrentPage(highlight.page);
      
      // Scroll to the highlight position
      setTimeout(() => {
        const highlightElement = document.querySelector(`.highlight-container[data-id="${highlightId}"]`);
        if (highlightElement) {
          highlightElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    }
  }, [highlights]);
  
  // Setup virtualizer for efficient page rendering
  const virtualizer = useVirtualizer({
    count: numPages || 0,
    getScrollElement: () => parentRef.current,
    estimateSize: useCallback(() => 
      Math.ceil((containerWidth / scale) * 1.4) + PAGE_PADDING, 
    [containerWidth, scale]),
    overscan: 2,
  });
  
  // Loading state
  if (loading) {
    return (
      <div className="pdf-viewer-container" style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Spinner size={30} intent="primary" />
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="pdf-viewer-container" style={{ height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <Card elevation={2} style={{ maxWidth: 400, textAlign: 'center' }}>
          <h3>Error Loading PDF</h3>
          <p>{error}</p>
        </Card>
      </div>
    );
  }
  
  return (
    <div 
      ref={containerRef} 
      className={`pdf-viewer-container ${isDarkTheme ? 'bp5-dark' : ''}`}
      style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
    >
      {/* Toolbar */}
      <div className="pdf-toolbar" style={{ 
        padding: '8px', 
        borderBottom: '1px solid #ddd',
        display: 'flex',
        justifyContent: 'space-between',
        backgroundColor: isDarkTheme ? '#30404D' : '#f5f8fa'
      }}>
        <div className="toolbar-left">
          <ButtonGroup>
            <Button icon={<ZoomOut size={16} />} onClick={zoomOut} />
            <Button>
              {Math.round(scale * 100)}%
            </Button>
            <Button icon={<ZoomIn size={16} />} onClick={zoomIn} />
          </ButtonGroup>
          
          <ButtonGroup style={{ marginLeft: 10 }}>
            <Button icon={<RotateCcw size={16} />} onClick={rotateLeft} />
            <Button icon={<RotateCw size={16} />} onClick={rotateRight} />
          </ButtonGroup>
        </div>
        
        <div className="toolbar-center">
          <span>Page {currentPage} of {numPages}</span>
        </div>
        
        <div className="toolbar-right">
          <ButtonGroup>
            <Button icon={<Search size={16} />}>Search</Button>
          </ButtonGroup>
        </div>
      </div>
      
      {/* PDF Viewer */}
      <div 
        ref={parentRef}
        className="pdf-content"
        style={{ 
          flex: 1,
          overflow: 'auto',
          backgroundColor: isDarkTheme ? '#293742' : '#fff'
        }}
      >
        {pdfFile && (
          <TransformWrapper
            initialScale={1}
            minScale={0.1}
            maxScale={4}
            wheel={{ step: 0.1 }}
          >
            <TransformComponent
              wrapperStyle={{ width: '100%', height: '100%' }}
              contentStyle={{ width: '100%', height: '100%' }}
            >
              <Document
                file={pdfFile}
                onLoadSuccess={onDocumentLoadSuccess}
                onLoadError={onDocumentLoadError}
                loading={<Spinner size={30} intent="primary" />}
                error={<div>Failed to load PDF</div>}
              >
                <div
                  style={{
                    height: `${virtualizer.getTotalSize()}px`,
                    width: '100%',
                    position: 'relative',
                  }}
                >
                  {virtualizer.getVirtualItems().map((virtualItem) => (
                    <div
                      key={virtualItem.key}
                      data-index={virtualItem.index}
                      ref={(el) => virtualizer.measureElement(el)}
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        transform: `translateY(${virtualItem.start}px)`,
                      }}
                    >
                      <div style={{ 
                        marginBottom: `${PAGE_PADDING}px`, 
                        display: 'flex', 
                        justifyContent: 'center' 
                      }}>
                        <PDFPageRenderer
                          pageNumber={virtualItem.index + 1}
                          scale={scale}
                          rotation={rotation}
                          width={containerWidth}
                          highlights={highlights}
                          onSelectionFinished={addHighlight}
                          currentlyScrolledToHighlightId={currentHighlightId}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </Document>
            </TransformComponent>
          </TransformWrapper>
        )}
      </div>
    </div>
  );
};

export default HighlightablePDFViewer;
