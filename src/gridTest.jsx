// Import necessary libraries and styles
import React, { useState, useEffect } from "react";
import { AgGridReact } from "ag-grid-react";
import { useHotkeys } from "react-hotkeys-hook";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-alpine.css";

const MyGrid = () => {
  const [highlightedCells, setHighlightedCells] = useState(new Set());
  const [isMouseDown, setIsMouseDown] = useState(false);
  const [initialCell, setInitialCell] = useState(null);
  const [currentCell, setCurrentCell] = useState(null);
  const [focusCell, setFocusCell] = useState(null); // New state variable
  const [currentCellInfo, setCurrentCellInfo] = useState({header: '', value: ''});

  const updateHighlightedCells = () => {
    if (initialCell && currentCell) {
      const [initialRow, initialCol] = initialCell.split("-").map(Number);
      const [currentRow, currentCol] = currentCell.split("-").map(Number);
      const minRow = Math.min(initialRow, currentRow);
      const maxRow = Math.max(initialRow, currentRow);
      const minCol = Math.min(initialCol, currentCol);
      const maxCol = Math.max(initialCol, currentCol);

      const newHighlightedCells = new Set();
      for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
          newHighlightedCells.add(`${row}-${col}`);
        }
      }
      setHighlightedCells(newHighlightedCells);
    }
  };

  const rowData = [
    { a: "1", b: "2", c: "3" },
    { a: "4", b: "5", c: "6" },
    { a: "7", b: "8", c: "9" },
    { a: "6", b: "7", c: "1" },
  ];

  const onCellMouseDown = (params) => {
    const key = `${params.rowIndex}-${params.column.colId}`;
    setInitialCell(key);
    setCurrentCell(key);
    setFocusCell(key); // Update the focus cell
    setIsMouseDown(true);
  };

  const onCellMouseOver = (params) => {
    if (isMouseDown) {
      const key = `${params.rowIndex}-${params.column.colId}`;
      setCurrentCell(key);
    }
  };

  const onCellMouseUp = () => {
    setIsMouseDown(false);
  };

  const onEscape = (event) => {
    if (event.key === "Escape") {
      setHighlightedCells(new Set());
      setInitialCell(null);
      setCurrentCell(null);
      setFocusCell(null); // Reset the focus cell
    }
  };

  useEffect(() => {
    updateHighlightedCells();
  }, [initialCell, currentCell]);

  useEffect(() => {
    window.addEventListener("keyup", onEscape);
    return () => {
      window.removeEventListener("keyup", onEscape);
    };
  }, []);

  const getBorderStyle = (rowIndex, colId) => {
    const [initialRow, initialCol] = initialCell.split("-").map(Number);
    const [currentRow, currentCol] = currentCell.split("-").map(Number);
    const minRow = Math.min(initialRow, currentRow);
    const maxRow = Math.max(initialRow, currentRow);
    const minCol = Math.min(initialCol, currentCol);
    const maxCol = Math.max(initialCol, currentCol);
    colId = Number(colId); // Convert colId to a number for comparison

    let borderStyle = {};
    if (
      rowIndex === minRow ||
      (rowIndex === initialRow && colId === initialCol)
    ) {
      borderStyle.borderTop = "2px solid #137CBD";
    }
    if (
      rowIndex === maxRow ||
      (rowIndex === initialRow && colId === initialCol)
    ) {
      borderStyle.borderBottom = "2px solid #137CBD";
    }
    if (colId === minCol || (rowIndex === initialRow && colId === initialCol)) {
      borderStyle.borderLeft = "2px solid #137CBD";
    }
    if (colId === maxCol || (rowIndex === initialRow && colId === initialCol)) {
      borderStyle.borderRight = "2px solid #137CBD";
    }
    return borderStyle;
  };

  const cellStyle = (params) => {
    const key = `${params.rowIndex}-${params.column.colId}`;
    if (highlightedCells.has(key)) {
      const borderStyle = getBorderStyle(params.rowIndex, params.column.colId);
      return {
        backgroundColor: "rgba(19, 124, 189, 0.1)", // Updated opacity to 0.1
        ...borderStyle,
      };
    }
    return null;
  };

  const columnDefs = [
    { colId: "0", field: "a",editable: true },
    { colId: "1", field: "b",  cellStyle,resizable: true,editable:true },  // Set editable to true
    { colId: "2", field: "c",cellStyle, editable: true,resizable: true, },];




    
  const moveSelection = (rowOffset, colOffset) => {
    if (focusCell) {
      console.log("moving focus cell");
      const [currentRow, currentCol] = focusCell.split("-").map(Number);
      let newRowIndex = currentRow + rowOffset;
      let newColId = currentCol + colOffset;

      // Ensure the new cell indices are within the bounds of the grid
      newRowIndex = Math.max(0, Math.min(2, newRowIndex));
      newColId = Math.max(0, Math.min(2, newColId));

      const newKey = `${newRowIndex}-${newColId}`;
      setFocusCell(newKey); // Update the focus cell
      setInitialCell(newKey);
      setCurrentCell(newKey);

        // Get cell info
      const header = columnDefs[newColId].field;
      const value = rowData[newRowIndex][header];
        
        setCurrentCellInfo({ header, value });

      const newHighlightedCells = new Set();
      newHighlightedCells.add(newKey);
      setHighlightedCells(newHighlightedCells);
    }
  };

  useHotkeys("arrow-up", () => moveSelection(-1, 0));
  useHotkeys("arrow-down", () => moveSelection(1, 0));
  useHotkeys("arrow-left", () => moveSelection(0, -1));
  useHotkeys("arrow-right", () => moveSelection(0, 1));

  return (
    <div
      className="ag-theme-alpine"
      style={{ height: "500px", width: "1000px" }}
      onMouseUp={onCellMouseUp}
      tabIndex={0} // This allows the div to receive keyboard focus
      onKeyDown={(e) => {
        switch (e.key) {
          case "ArrowUp":
            moveSelection(-1, 0);
            break;
          case "ArrowDown":
            moveSelection(1, 0);
            break;
          case "ArrowLeft":
            moveSelection(0, -1);
            break;
          case "ArrowRight":
            moveSelection(0, 1);
            break;
          default:
            break;
        }
      }}
    >
      <AgGridReact
        suppressCellFocus={true}
        onCellMouseDown={onCellMouseDown}
        onCellMouseOver={onCellMouseOver}
        columnDefs={columnDefs}
        rowData={rowData}
        editType="fullRow"
      />
       <div>
                <strong>Current Cell:</strong> {currentCellInfo.header} : {currentCellInfo.value}
            </div>
    </div>
  );
};

function App() {
  return (
    <div className="App">
      <MyGrid />
    </div>
  );
}

export default App;
