/**
 * Enhanced PDF search utilities
 * Provides advanced text processing and search capabilities for PDF documents
 */

import Fuse from 'fuse.js';

/**
 * Process text content from PDF pages to handle line breaks intelligently
 * @param {Object} textContent - The text content from PDF.js getTextContent()
 * @returns {Object} Processed text with line information
 */
export const processTextContent = (textContent) => {
  if (!textContent || !textContent.items) return { text: '', lines: [] };

  const items = textContent.items;
  const lines = [];
  let currentLine = { text: '', items: [], y: null };
  let fullText = '';
  
  // First pass: group items by vertical position (y-coordinate) to identify lines
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    // Handle the first item
    if (currentLine.y === null) {
      currentLine.y = item.transform[5]; // y-coordinate from transform matrix
      currentLine.items.push(item);
      currentLine.text += item.str;
      continue;
    }
    
    // Y-position tolerance (adjust as needed based on font size/document)
    const yTolerance = 3;
    
    // Check if we're on the same line based on y-coordinate
    if (Math.abs(item.transform[5] - currentLine.y) <= yTolerance) {
      // Same line - check for word spacing
      const lastItem = currentLine.items[currentLine.items.length - 1];
      
      // Add a space if needed between words
      // This uses the x-position to determine proper spacing
      if (lastItem && item.transform[4] > lastItem.transform[4] + lastItem.width && !currentLine.text.endsWith(' ')) {
        currentLine.text += ' ';
      }
      
      currentLine.items.push(item);
      currentLine.text += item.str;
    } else {
      // New line detected
      lines.push({...currentLine});
      fullText += currentLine.text + '\n';
      
      // Start a new line
      currentLine = { 
        text: item.str, 
        items: [item], 
        y: item.transform[5]
      };
    }
  }
  
  // Don't forget the last line
  if (currentLine.text) {
    lines.push(currentLine);
    fullText += currentLine.text;
  }
  
  return { text: fullText, lines };
};

/**
 * Performs a fuzzy search with configurable options for matching across line breaks
 * @param {string} query - The search query
 * @param {Object} processedText - Processed text from processTextContent()
 * @param {Object} options - Search options
 * @returns {Array} Array of search results with page and position information
 */
export const performFuzzySearch = (query, processedText, options = {}) => {
  if (!query || !processedText) return [];
  
  const defaultOptions = {
    ignoreCase: true,
    ignoreLineBreaks: true,
    fuzzyThreshold: 0.3, // Lower = more strict matching
    includeScore: true
  };
  
  const searchOptions = { ...defaultOptions, ...options };
  const fuseOptions = {
    includeScore: true,
    threshold: searchOptions.fuzzyThreshold,
    ignoreLocation: searchOptions.ignoreLineBreaks,
    keys: ['text']
  };
  
  // Create a dataset that includes both full text and individual lines
  const searchDataset = [
    // Full text search (across line breaks)
    { id: 'fulltext', text: processedText.text },
    
    // Line-by-line search
    ...processedText.lines.map((line, index) => ({
      id: `line-${index}`,
      text: line.text,
      lineIndex: index,
      lineInfo: line
    }))
  ];
  
  const fuse = new Fuse(searchDataset, fuseOptions);
  const results = fuse.search(query);
  
  return results.map(result => ({
    text: result.item.text,
    score: result.score,
    isFullText: result.item.id === 'fulltext',
    lineIndex: result.item.lineIndex,
    lineInfo: result.item.lineInfo
  }));
};

/**
 * Process search results to highlight text accurately, handling line breaks
 * @param {Array} searchResults - Results from performFuzzySearch()
 * @param {string} query - Original search query
 * @param {string} highlightColor - CSS color for highlighting
 * @returns {Object} Processed highlights with positions for rendering
 */
export const processHighlights = (searchResults, query, highlightColor) => {
  if (!searchResults || searchResults.length === 0) return [];
  
  const highlights = [];
  
  searchResults.forEach(result => {
    if (result.isFullText) {
      // Process multi-line matches
      const regex = new RegExp(query.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), 'gi');
      let match;
      
      while ((match = regex.exec(result.text)) !== null) {
        highlights.push({
          text: match[0],
          index: match.index,
          isMultiLine: true,
          color: highlightColor
        });
      }
    } else if (result.lineInfo) {
      // Process single-line matches
      const regex = new RegExp(query.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), 'gi');
      let match;
      
      while ((match = regex.exec(result.lineInfo.text)) !== null) {
        highlights.push({
          text: match[0],
          index: match.index,
          lineIndex: result.lineIndex,
          lineInfo: result.lineInfo,
          isMultiLine: false,
          color: highlightColor
        });
      }
    }
  });
  
  return highlights;
};

/**
 * Custom renderer for PDF.js text layer that handles advanced highlighting
 * @param {Object} highlights - Processed highlights from processHighlights()
 * @param {string} defaultHighlightColor - Default highlight color
 * @returns {Function} Text renderer function compatible with PDF.js
 */
export const createAdvancedTextRenderer = (highlights, defaultHighlightColor = 'rgba(255, 255, 0, 0.4)') => {
  return (textItem) => {
    if (!highlights || highlights.length === 0) return textItem.str;
    
    // Simple highlighting for basic cases
    let highlighted = textItem.str;
    
    // Check if this text item should be highlighted based on position and content
    highlights.forEach(highlight => {
      if (textItem.str.toLowerCase().includes(highlight.text.toLowerCase())) {
        const regex = new RegExp(`(${highlight.text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')})`, 'gi');
        highlighted = highlighted.replace(regex, `<mark style="background-color: ${highlight.color || defaultHighlightColor}; padding: 0; margin: 0; border-radius: 0 !important; box-shadow: 0 0 0 0 !important; color: inherit;">$1</mark>`);
      }
    });
    
    return highlighted;
  };
};

/**
 * Enhanced search function that combines processing and searching
 * @param {Object} pdf - The loaded PDF.js document
 * @param {string} query - Search query
 * @param {Object} options - Search options
 * @returns {Promise<Object>} Search results with page numbers and positions
 */
export const enhancedPdfSearch = async (pdf, query, options = {}) => {
  if (!pdf || !query) return { results: [], totalOccurrences: 0 };
  
  const results = [];
  let totalOccurrences = 0;
  
  // Process each page
  for (let i = 1; i <= pdf.numPages; i++) {
    const page = await pdf.getPage(i);
    const textContent = await page.getTextContent();
    
    // Process text to handle line breaks
    const processedText = processTextContent(textContent);
    
    // Perform the search
    const searchResults = performFuzzySearch(query, processedText, options);
    
    if (searchResults.length > 0) {
      // Count occurrences
      const exactRegex = new RegExp(query.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&'), 'gi');
      const matches = processedText.text.match(exactRegex);
      const pageMatches = matches ? matches.length : 0;
      
      results.push({
        pageNumber: i,
        matches: pageMatches,
        searchResults,
        text: processedText.text,
        highlights: processHighlights(searchResults, query, options.highlightColor || 'rgba(255, 255, 0, 0.4)')
      });
      
      totalOccurrences += pageMatches;
    }
  }
  
  return { 
    results, 
    totalOccurrences,
    pageCount: results.length,
  };
};

// Export an object with the scoring options for different types of searches
export const searchModes = {
  EXACT: { 
    fuzzyThreshold: 0, 
    name: 'Exact Match', 
    description: 'Only find exact text matches'
  },
  STANDARD: { 
    fuzzyThreshold: 0.3, 
    name: 'Standard', 
    description: 'Balance between exact and fuzzy matching' 
  },
  FUZZY: { 
    fuzzyThreshold: 0.6, 
    name: 'Fuzzy', 
    description: 'Find similar text patterns, good for OCR errors' 
  },
  CROSS_LINE: { 
    fuzzyThreshold: 0.4, 
    ignoreLineBreaks: true, 
    name: 'Cross-line', 
    description: 'Search across line breaks' 
  }
};
