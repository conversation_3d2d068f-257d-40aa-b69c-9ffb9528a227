import * as pdfjsLib from 'pdfjs-dist/build/pdf';
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';
import axios from 'axios';

// Set up PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;

/**
 * Load a PDF document from a file path
 * @param {string} filePath - Path to the PDF file
 * @returns {Promise<PDFDocumentProxy>} PDF document proxy
 */
export const loadPDFDocument = async (filePath) => {
  try {
    // Create URL for the PDF file
    const pdfUrl = await createPDFUrl(filePath);
    
    // Load the PDF document
    const loadingTask = pdfjsLib.getDocument({
      url: pdfUrl,
      cMapUrl: 'https://unpkg.com/pdfjs-dist@4.4.168/cmaps/',
      cMapPacked: true,
      enableXfa: true,
      verbosity: 0 // Reduce console output
    });

    const pdfDocument = await loadingTask.promise;
    return pdfDocument;
  } catch (error) {
    console.error('Error loading PDF document:', error);
    throw new Error(`Failed to load PDF: ${error.message}`);
  }
};

/**
 * Create a URL for accessing a PDF file
 * @param {string} filePath - Path to the PDF file
 * @returns {Promise<string>} URL for the PDF file
 */
export const createPDFUrl = async (filePath) => {
  try {
    // Check if it's already a URL
    if (filePath.startsWith('http://') || filePath.startsWith('https://') || filePath.startsWith('blob:')) {
      return filePath;
    }

    // For local files, we need to fetch them through our API
    const response = await axios.get(`${process.env.REACT_APP_DATA_API}/get-file/`, {
      params: { file_path: filePath },
      headers: {
        'session-id': localStorage.getItem('session_id'),
      },
      responseType: 'blob'
    });

    // Create blob URL
    const blob = new Blob([response.data], { type: 'application/pdf' });
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('Error creating PDF URL:', error);
    throw new Error(`Failed to create PDF URL: ${error.message}`);
  }
};

/**
 * Extract text content from a PDF page
 * @param {PDFPageProxy} page - PDF page proxy
 * @returns {Promise<string>} Extracted text content
 */
export const extractPageText = async (page) => {
  try {
    const textContent = await page.getTextContent();
    return textContent.items.map(item => item.str).join(' ');
  } catch (error) {
    console.error('Error extracting page text:', error);
    return '';
  }
};

/**
 * Search for text in a PDF document
 * @param {PDFDocumentProxy} pdfDocument - PDF document proxy
 * @param {string} searchQuery - Text to search for
 * @param {Object} options - Search options
 * @returns {Promise<Array>} Array of search results
 */
export const searchInPDF = async (pdfDocument, searchQuery, options = {}) => {
  const {
    caseSensitive = false,
    wholeWords = false,
    maxResults = 100
  } = options;

  const results = [];
  const query = caseSensitive ? searchQuery : searchQuery.toLowerCase();

  try {
    for (let pageNum = 1; pageNum <= pdfDocument.numPages && results.length < maxResults; pageNum++) {
      const page = await pdfDocument.getPage(pageNum);
      const textContent = await page.getTextContent();
      
      let pageText = textContent.items.map(item => item.str).join(' ');
      if (!caseSensitive) {
        pageText = pageText.toLowerCase();
      }

      // Find all occurrences in the page
      let index = 0;
      while (index < pageText.length && results.length < maxResults) {
        const foundIndex = pageText.indexOf(query, index);
        if (foundIndex === -1) break;

        // Check for whole words if required
        if (wholeWords) {
          const beforeChar = foundIndex > 0 ? pageText[foundIndex - 1] : ' ';
          const afterChar = foundIndex + query.length < pageText.length ? pageText[foundIndex + query.length] : ' ';
          
          if (!/\s/.test(beforeChar) || !/\s/.test(afterChar)) {
            index = foundIndex + 1;
            continue;
          }
        }

        // Find the text item that contains this match
        let charCount = 0;
        let matchingItem = null;
        
        for (const item of textContent.items) {
          const itemLength = item.str.length;
          if (charCount <= foundIndex && foundIndex < charCount + itemLength) {
            matchingItem = item;
            break;
          }
          charCount += itemLength + 1; // +1 for space
        }

        if (matchingItem) {
          results.push({
            pageNumber: pageNum,
            text: searchQuery,
            context: pageText.substring(Math.max(0, foundIndex - 50), foundIndex + query.length + 50),
            position: {
              x: matchingItem.transform[4],
              y: matchingItem.transform[5],
              width: matchingItem.width,
              height: matchingItem.height
            },
            index: foundIndex
          });
        }

        index = foundIndex + 1;
      }
    }
  } catch (error) {
    console.error('Error searching in PDF:', error);
  }

  return results;
};

/**
 * Get thumbnail image for a PDF page
 * @param {PDFPageProxy} page - PDF page proxy
 * @param {number} scale - Scale factor for the thumbnail
 * @returns {Promise<string>} Data URL of the thumbnail image
 */
export const getPageThumbnail = async (page, scale = 0.2) => {
  try {
    const viewport = page.getViewport({ scale });
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    const renderContext = {
      canvasContext: context,
      viewport: viewport
    };

    await page.render(renderContext).promise;
    return canvas.toDataURL();
  } catch (error) {
    console.error('Error generating page thumbnail:', error);
    return null;
  }
};

/**
 * Convert PDF coordinates to screen coordinates
 * @param {Object} pdfCoords - PDF coordinates
 * @param {Object} viewport - PDF viewport
 * @param {number} containerWidth - Container width
 * @param {number} containerHeight - Container height
 * @returns {Object} Screen coordinates
 */
export const pdfToScreenCoords = (pdfCoords, viewport, containerWidth, containerHeight) => {
  const scaleX = containerWidth / viewport.width;
  const scaleY = containerHeight / viewport.height;
  
  return {
    x: pdfCoords.x * scaleX,
    y: (viewport.height - pdfCoords.y) * scaleY, // PDF Y is bottom-up
    width: pdfCoords.width * scaleX,
    height: pdfCoords.height * scaleY
  };
};

/**
 * Convert screen coordinates to PDF coordinates
 * @param {Object} screenCoords - Screen coordinates
 * @param {Object} viewport - PDF viewport
 * @param {number} containerWidth - Container width
 * @param {number} containerHeight - Container height
 * @returns {Object} PDF coordinates
 */
export const screenToPdfCoords = (screenCoords, viewport, containerWidth, containerHeight) => {
  const scaleX = viewport.width / containerWidth;
  const scaleY = viewport.height / containerHeight;
  
  return {
    x: screenCoords.x * scaleX,
    y: viewport.height - (screenCoords.y * scaleY), // PDF Y is bottom-up
    width: screenCoords.width * scaleX,
    height: screenCoords.height * scaleY
  };
};

/**
 * Validate annotation position
 * @param {Object} position - Annotation position
 * @param {Object} viewport - PDF viewport
 * @returns {boolean} Whether the position is valid
 */
export const validateAnnotationPosition = (position, viewport) => {
  if (!position || !viewport) return false;
  
  const { x, y, width, height } = position;
  
  return (
    x >= 0 && 
    y >= 0 && 
    x + width <= viewport.width && 
    y + height <= viewport.height &&
    width > 0 && 
    height > 0
  );
};

/**
 * Generate unique annotation ID
 * @returns {string} Unique annotation ID
 */
export const generateAnnotationId = () => {
  return `annotation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Export annotations to JSON
 * @param {Array} annotations - Array of annotations
 * @param {Object} documentInfo - Document information
 * @returns {string} JSON string of annotations
 */
export const exportAnnotationsToJSON = (annotations, documentInfo) => {
  const exportData = {
    document: documentInfo,
    annotations: annotations,
    exportedAt: new Date().toISOString(),
    version: '1.0'
  };
  
  return JSON.stringify(exportData, null, 2);
};

/**
 * Import annotations from JSON
 * @param {string} jsonString - JSON string of annotations
 * @returns {Object} Parsed annotation data
 */
export const importAnnotationsFromJSON = (jsonString) => {
  try {
    const data = JSON.parse(jsonString);
    
    // Validate the structure
    if (!data.annotations || !Array.isArray(data.annotations)) {
      throw new Error('Invalid annotation format');
    }
    
    return data;
  } catch (error) {
    console.error('Error importing annotations:', error);
    throw new Error(`Failed to import annotations: ${error.message}`);
  }
};

/**
 * Clean up blob URLs to prevent memory leaks
 * @param {string} url - Blob URL to clean up
 */
export const cleanupBlobUrl = (url) => {
  if (url && url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
};

/**
 * Debounce function for performance optimization
 * @param {Function} func - Function to debounce
 * @param {number} wait - Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * Throttle function for performance optimization
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @returns {Function} Throttled function
 */
export const throttle = (func, limit) => {
  let inThrottle;
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
