/**
 * Utility functions for annotation management
 */

/**
 * Format date for display
 * @param {number} timestamp - Unix timestamp
 * @returns {string} Formatted date string
 */
export const formatDate = (timestamp) => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now - date;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    return 'Yesterday';
  } else if (diffDays < 7) {
    return `${diffDays} days ago`;
  } else {
    return date.toLocaleDateString();
  }
};

/**
 * Group annotations by page number
 * @param {Array} annotations - Array of annotations
 * @returns {Object} Grouped annotations
 */
export const groupAnnotationsByPage = (annotations) => {
  return annotations.reduce((groups, annotation) => {
    const pageKey = `Page ${annotation.pageNumber}`;
    if (!groups[pageKey]) groups[pageKey] = [];
    groups[pageKey].push(annotation);
    return groups;
  }, {});
};

/**
 * Group annotations by type
 * @param {Array} annotations - Array of annotations
 * @returns {Object} Grouped annotations
 */
export const groupAnnotationsByType = (annotations) => {
  return annotations.reduce((groups, annotation) => {
    const typeKey = annotation.type.replace('_', ' ').toUpperCase();
    if (!groups[typeKey]) groups[typeKey] = [];
    groups[typeKey].push(annotation);
    return groups;
  }, {});
};

/**
 * Group annotations by author
 * @param {Array} annotations - Array of annotations
 * @returns {Object} Grouped annotations
 */
export const groupAnnotationsByAuthor = (annotations) => {
  return annotations.reduce((groups, annotation) => {
    const author = annotation.author || 'Unknown';
    if (!groups[author]) groups[author] = [];
    groups[author].push(annotation);
    return groups;
  }, {});
};

/**
 * Group annotations by date
 * @param {Array} annotations - Array of annotations
 * @returns {Object} Grouped annotations
 */
export const groupAnnotationsByDate = (annotations) => {
  return annotations.reduce((groups, annotation) => {
    const date = new Date(annotation.createdAt).toDateString();
    if (!groups[date]) groups[date] = [];
    groups[date].push(annotation);
    return groups;
  }, {});
};

/**
 * Filter annotations by criteria
 * @param {Array} annotations - Array of annotations
 * @param {Object} filters - Filter criteria
 * @returns {Array} Filtered annotations
 */
export const filterAnnotations = (annotations, filters) => {
  return annotations.filter(annotation => {
    // Type filter
    if (filters.types && filters.types.length > 0) {
      if (!filters.types.includes(annotation.type)) return false;
    }
    
    // Color filter
    if (filters.colors && filters.colors.length > 0) {
      if (!filters.colors.includes(annotation.color)) return false;
    }
    
    // Author filter
    if (filters.authors && filters.authors.length > 0) {
      if (!filters.authors.includes(annotation.author)) return false;
    }
    
    // Date range filter
    if (filters.dateRange) {
      const { start, end } = filters.dateRange;
      if (annotation.createdAt < start || annotation.createdAt > end) return false;
    }
    
    // Search query filter
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase();
      const content = (annotation.content?.text || annotation.content?.comment || annotation.comment || '').toLowerCase();
      const author = (annotation.author || '').toLowerCase();
      const type = annotation.type.toLowerCase();
      
      if (!content.includes(query) && !author.includes(query) && !type.includes(query)) {
        return false;
      }
    }
    
    // Page filter
    if (filters.pageNumbers && filters.pageNumbers.length > 0) {
      if (!filters.pageNumbers.includes(annotation.pageNumber)) return false;
    }
    
    return true;
  });
};

/**
 * Sort annotations by criteria
 * @param {Array} annotations - Array of annotations
 * @param {string} sortBy - Sort criteria
 * @param {string} sortOrder - Sort order ('asc' or 'desc')
 * @returns {Array} Sorted annotations
 */
export const sortAnnotations = (annotations, sortBy, sortOrder = 'desc') => {
  const sorted = [...annotations];
  
  sorted.sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'created':
        comparison = a.createdAt - b.createdAt;
        break;
      case 'updated':
        comparison = a.updatedAt - b.updatedAt;
        break;
      case 'page':
        comparison = a.pageNumber - b.pageNumber;
        break;
      case 'type':
        comparison = a.type.localeCompare(b.type);
        break;
      case 'author':
        comparison = (a.author || '').localeCompare(b.author || '');
        break;
      case 'color':
        comparison = (a.color || '').localeCompare(b.color || '');
        break;
      default:
        comparison = 0;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
  
  return sorted;
};

/**
 * Get annotation statistics
 * @param {Array} annotations - Array of annotations
 * @returns {Object} Statistics object
 */
export const getAnnotationStats = (annotations) => {
  const stats = {
    total: annotations.length,
    byType: {},
    byAuthor: {},
    byPage: {},
    byColor: {},
    dateRange: null
  };
  
  if (annotations.length === 0) return stats;
  
  let minDate = Infinity;
  let maxDate = -Infinity;
  
  annotations.forEach(annotation => {
    // Count by type
    stats.byType[annotation.type] = (stats.byType[annotation.type] || 0) + 1;
    
    // Count by author
    const author = annotation.author || 'Unknown';
    stats.byAuthor[author] = (stats.byAuthor[author] || 0) + 1;
    
    // Count by page
    stats.byPage[annotation.pageNumber] = (stats.byPage[annotation.pageNumber] || 0) + 1;
    
    // Count by color
    stats.byColor[annotation.color] = (stats.byColor[annotation.color] || 0) + 1;
    
    // Track date range
    if (annotation.createdAt < minDate) minDate = annotation.createdAt;
    if (annotation.createdAt > maxDate) maxDate = annotation.createdAt;
  });
  
  stats.dateRange = { start: minDate, end: maxDate };
  
  return stats;
};

/**
 * Validate annotation data
 * @param {Object} annotation - Annotation object
 * @returns {Object} Validation result
 */
export const validateAnnotation = (annotation) => {
  const errors = [];
  
  if (!annotation.type) {
    errors.push('Annotation type is required');
  }
  
  if (!annotation.pageNumber || annotation.pageNumber < 1) {
    errors.push('Valid page number is required');
  }
  
  if (!annotation.position) {
    errors.push('Annotation position is required');
  } else {
    const { x, y, width, height } = annotation.position;
    if (typeof x !== 'number' || typeof y !== 'number' || 
        typeof width !== 'number' || typeof height !== 'number') {
      errors.push('Position coordinates must be numbers');
    }
    if (width <= 0 || height <= 0) {
      errors.push('Position width and height must be positive');
    }
  }
  
  if (!annotation.content && !annotation.comment) {
    errors.push('Annotation must have content or comment');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Merge overlapping annotations
 * @param {Array} annotations - Array of annotations
 * @param {number} threshold - Overlap threshold (0-1)
 * @returns {Array} Merged annotations
 */
export const mergeOverlappingAnnotations = (annotations, threshold = 0.8) => {
  const merged = [];
  const processed = new Set();
  
  annotations.forEach((annotation, index) => {
    if (processed.has(index)) return;
    
    const overlapping = [annotation];
    processed.add(index);
    
    // Find overlapping annotations
    for (let i = index + 1; i < annotations.length; i++) {
      if (processed.has(i)) continue;
      
      const other = annotations[i];
      if (annotation.pageNumber === other.pageNumber && 
          annotation.type === other.type &&
          calculateOverlap(annotation.position, other.position) > threshold) {
        overlapping.push(other);
        processed.add(i);
      }
    }
    
    if (overlapping.length > 1) {
      // Merge annotations
      const mergedAnnotation = {
        ...annotation,
        id: `merged_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: {
          text: overlapping.map(a => a.content?.text || a.comment).filter(Boolean).join(' '),
          merged: true,
          originalIds: overlapping.map(a => a.id)
        },
        position: calculateBoundingBox(overlapping.map(a => a.position))
      };
      merged.push(mergedAnnotation);
    } else {
      merged.push(annotation);
    }
  });
  
  return merged;
};

/**
 * Calculate overlap between two rectangles
 * @param {Object} rect1 - First rectangle
 * @param {Object} rect2 - Second rectangle
 * @returns {number} Overlap ratio (0-1)
 */
const calculateOverlap = (rect1, rect2) => {
  const x1 = Math.max(rect1.x, rect2.x);
  const y1 = Math.max(rect1.y, rect2.y);
  const x2 = Math.min(rect1.x + rect1.width, rect2.x + rect2.width);
  const y2 = Math.min(rect1.y + rect1.height, rect2.y + rect2.height);
  
  if (x2 <= x1 || y2 <= y1) return 0;
  
  const overlapArea = (x2 - x1) * (y2 - y1);
  const area1 = rect1.width * rect1.height;
  const area2 = rect2.width * rect2.height;
  const unionArea = area1 + area2 - overlapArea;
  
  return overlapArea / unionArea;
};

/**
 * Calculate bounding box for multiple rectangles
 * @param {Array} rectangles - Array of rectangle objects
 * @returns {Object} Bounding box
 */
const calculateBoundingBox = (rectangles) => {
  if (rectangles.length === 0) return null;
  
  let minX = Infinity, minY = Infinity;
  let maxX = -Infinity, maxY = -Infinity;
  
  rectangles.forEach(rect => {
    minX = Math.min(minX, rect.x);
    minY = Math.min(minY, rect.y);
    maxX = Math.max(maxX, rect.x + rect.width);
    maxY = Math.max(maxY, rect.y + rect.height);
  });
  
  return {
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY
  };
};

/**
 * Export annotations to various formats
 * @param {Array} annotations - Array of annotations
 * @param {string} format - Export format ('json', 'csv', 'txt')
 * @returns {string} Exported data
 */
export const exportAnnotations = (annotations, format = 'json') => {
  switch (format.toLowerCase()) {
    case 'json':
      return JSON.stringify(annotations, null, 2);
      
    case 'csv':
      const headers = ['ID', 'Type', 'Page', 'Content', 'Author', 'Created', 'Color'];
      const rows = annotations.map(a => [
        a.id,
        a.type,
        a.pageNumber,
        a.content?.text || a.comment || '',
        a.author || '',
        new Date(a.createdAt).toISOString(),
        a.color || ''
      ]);
      return [headers, ...rows].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');
      
    case 'txt':
      return annotations.map(a => 
        `Page ${a.pageNumber} - ${a.type.toUpperCase()}\n` +
        `${a.content?.text || a.comment || ''}\n` +
        `By: ${a.author || 'Unknown'} on ${new Date(a.createdAt).toLocaleString()}\n` +
        '---\n'
      ).join('\n');
      
    default:
      throw new Error(`Unsupported export format: ${format}`);
  }
};

/**
 * Import annotations from various formats
 * @param {string} data - Import data
 * @param {string} format - Import format ('json', 'csv')
 * @returns {Array} Imported annotations
 */
export const importAnnotations = (data, format = 'json') => {
  switch (format.toLowerCase()) {
    case 'json':
      return JSON.parse(data);
      
    case 'csv':
      const lines = data.split('\n');
      const headers = lines[0].split(',').map(h => h.replace(/"/g, ''));
      return lines.slice(1).map(line => {
        const values = line.split(',').map(v => v.replace(/"/g, ''));
        const annotation = {};
        headers.forEach((header, index) => {
          annotation[header.toLowerCase()] = values[index];
        });
        return annotation;
      });
      
    default:
      throw new Error(`Unsupported import format: ${format}`);
  }
};
