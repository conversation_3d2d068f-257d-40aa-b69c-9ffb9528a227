/**
 * FlexLayout integration utilities for Ultimate PDF Viewer
 * This file provides helper functions to integrate the PDF viewer with FlexLayout
 */

import { Model } from 'flexlayout-react';
import UltimatePDFViewerExample from '../UltimatePDFViewerExample';

/**
 * Create a FlexLayout tab configuration for the Ultimate PDF Viewer
 * @param {string} fileId - Unique identifier for the PDF file
 * @param {string} fileName - Display name for the tab
 * @param {Object} options - Additional options
 * @returns {Object} FlexLayout tab configuration
 */
export const createPDFViewerTab = (fileId, fileName, options = {}) => {
  const {
    enableClose = true,
    enableDrag = true,
    enableRename = false,
    icon = 'document',
    ...otherOptions
  } = options;

  return {
    type: 'tab',
    id: `pdf-viewer-${fileId}`,
    name: fileName,
    component: 'ultimatePDFViewer',
    config: {
      fileId,
      fileName,
      ...otherOptions
    },
    enableClose,
    enableDrag,
    enableRename,
    icon,
    ...otherOptions
  };
};

/**
 * Add a PDF viewer tab to an existing FlexLayout model
 * @param {Model} model - FlexLayout model
 * @param {string} fileId - Unique identifier for the PDF file
 * @param {string} fileName - Display name for the tab
 * @param {string} targetTabsetId - ID of the tabset to add the tab to (optional)
 * @param {Object} options - Additional options
 * @returns {Model} Updated FlexLayout model
 */
export const addPDFViewerToLayout = (model, fileId, fileName, targetTabsetId = null, options = {}) => {
  const tabConfig = createPDFViewerTab(fileId, fileName, options);
  
  if (targetTabsetId) {
    // Add to specific tabset
    model.doAction({
      type: 'add_node',
      json: tabConfig,
      toNode: targetTabsetId
    });
  } else {
    // Add to the first available tabset or create a new one
    const rootNode = model.getRoot();
    const firstTabset = findFirstTabset(rootNode);
    
    if (firstTabset) {
      model.doAction({
        type: 'add_node',
        json: tabConfig,
        toNode: firstTabset.getId()
      });
    } else {
      // Create a new tabset if none exists
      model.doAction({
        type: 'add_node',
        json: {
          type: 'tabset',
          children: [tabConfig]
        },
        toNode: rootNode.getId()
      });
    }
  }
  
  return model;
};

/**
 * Find the first tabset in a FlexLayout node tree
 * @param {Node} node - FlexLayout node
 * @returns {Node|null} First tabset node or null
 */
const findFirstTabset = (node) => {
  if (node.getType() === 'tabset') {
    return node;
  }
  
  const children = node.getChildren();
  for (const child of children) {
    const result = findFirstTabset(child);
    if (result) return result;
  }
  
  return null;
};

/**
 * Create a factory function for FlexLayout components
 * This should be used in your main FlexLayout factory
 * @param {Object} additionalComponents - Additional components to include
 * @returns {Function} Factory function
 */
export const createPDFViewerFactory = (additionalComponents = {}) => {
  return (node) => {
    const component = node.getComponent();
    const config = node.getConfig() || {};
    
    switch (component) {
      case 'ultimatePDFViewer':
        return <UltimatePDFViewerExample {...config} />;
      
      // Add your existing components here
      ...Object.entries(additionalComponents).map(([key, Component]) => ({
        [key]: <Component {...config} />
      })),
      
      default:
        return <div>Unknown component: {component}</div>;
    }
  };
};

/**
 * Create a default layout model with PDF viewer support
 * @param {Object} options - Layout options
 * @returns {Model} FlexLayout model
 */
export const createDefaultLayoutWithPDFViewer = (options = {}) => {
  const {
    enablePDFViewer = true,
    defaultPDFFile = null,
    ...layoutOptions
  } = options;

  const layoutConfig = {
    global: {
      tabEnableClose: true,
      tabEnableRename: false,
      tabEnableDrag: true,
      ...layoutOptions.global
    },
    layout: {
      type: 'row',
      children: [
        {
          type: 'tabset',
          weight: 70,
          children: enablePDFViewer ? [
            defaultPDFFile ? 
              createPDFViewerTab(defaultPDFFile.id, defaultPDFFile.name, defaultPDFFile) :
              {
                type: 'tab',
                component: 'ultimatePDFViewer',
                name: 'PDF Viewer',
                enableClose: false
              }
          ] : []
        },
        {
          type: 'tabset',
          weight: 30,
          children: [
            {
              type: 'tab',
              component: 'dataCollection',
              name: 'Data Collection',
              enableClose: false
            }
          ]
        }
      ]
    }
  };

  return Model.fromJson(layoutConfig);
};

/**
 * Handle PDF file opening in FlexLayout
 * @param {Model} model - FlexLayout model
 * @param {File} file - PDF file object
 * @param {Function} dispatch - Redux dispatch function
 * @returns {Promise<string>} File ID of the opened PDF
 */
export const handlePDFFileOpen = async (model, file, dispatch) => {
  // Generate unique file ID
  const fileId = `pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Create blob URL
  const fileUrl = URL.createObjectURL(file);
  
  // Add to Redux store (you'll need to import the action)
  // dispatch(addDocument({
  //   fileId,
  //   filePath: fileUrl,
  //   fileName: file.name,
  //   numPages: 0
  // }));
  
  // Add tab to layout
  addPDFViewerToLayout(model, fileId, file.name, null, {
    filePath: fileUrl,
    fileSize: file.size,
    lastModified: file.lastModified
  });
  
  return fileId;
};

/**
 * Close a PDF viewer tab and clean up resources
 * @param {Model} model - FlexLayout model
 * @param {string} fileId - File ID to close
 * @param {Function} dispatch - Redux dispatch function
 */
export const closePDFViewer = (model, fileId, dispatch) => {
  const tabId = `pdf-viewer-${fileId}`;
  const node = model.getNodeById(tabId);
  
  if (node) {
    // Close the tab
    model.doAction({
      type: 'delete_tab',
      node: tabId
    });
    
    // Clean up Redux state (you'll need to import the action)
    // dispatch(removeDocument({ fileId }));
    
    // Clean up blob URL if it exists
    const config = node.getConfig();
    if (config && config.filePath && config.filePath.startsWith('blob:')) {
      URL.revokeObjectURL(config.filePath);
    }
  }
};

/**
 * Get all open PDF viewer tabs
 * @param {Model} model - FlexLayout model
 * @returns {Array} Array of PDF viewer tab nodes
 */
export const getOpenPDFViewers = (model) => {
  const pdfTabs = [];
  
  const visitNode = (node) => {
    if (node.getType() === 'tab' && node.getComponent() === 'ultimatePDFViewer') {
      pdfTabs.push(node);
    }
    
    const children = node.getChildren();
    if (children) {
      children.forEach(visitNode);
    }
  };
  
  visitNode(model.getRoot());
  return pdfTabs;
};

/**
 * Switch to a specific PDF viewer tab
 * @param {Model} model - FlexLayout model
 * @param {string} fileId - File ID to switch to
 */
export const switchToPDFViewer = (model, fileId) => {
  const tabId = `pdf-viewer-${fileId}`;
  const node = model.getNodeById(tabId);
  
  if (node) {
    model.doAction({
      type: 'select_tab',
      tabNode: tabId
    });
  }
};

/**
 * Save the current layout state including PDF viewer tabs
 * @param {Model} model - FlexLayout model
 * @returns {Object} Serializable layout state
 */
export const saveLayoutState = (model) => {
  const layoutJson = model.toJson();
  
  // Add metadata about PDF viewers
  const pdfViewers = getOpenPDFViewers(model).map(node => ({
    id: node.getId(),
    fileId: node.getConfig()?.fileId,
    fileName: node.getName(),
    config: node.getConfig()
  }));
  
  return {
    layout: layoutJson,
    pdfViewers,
    timestamp: Date.now()
  };
};

/**
 * Restore layout state including PDF viewer tabs
 * @param {Object} savedState - Previously saved layout state
 * @param {Function} dispatch - Redux dispatch function
 * @returns {Model} Restored FlexLayout model
 */
export const restoreLayoutState = (savedState, dispatch) => {
  const model = Model.fromJson(savedState.layout);
  
  // Restore PDF viewer state to Redux if needed
  if (savedState.pdfViewers) {
    savedState.pdfViewers.forEach(pdfViewer => {
      if (pdfViewer.config && pdfViewer.fileId) {
        // dispatch(addDocument({
        //   fileId: pdfViewer.fileId,
        //   filePath: pdfViewer.config.filePath,
        //   fileName: pdfViewer.fileName,
        //   numPages: pdfViewer.config.numPages || 0
        // }));
      }
    });
  }
  
  return model;
};
